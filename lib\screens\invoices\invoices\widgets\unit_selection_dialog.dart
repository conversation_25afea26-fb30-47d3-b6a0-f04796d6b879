import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/unit_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:provider/provider.dart';

/// Shows a dialog to select a unit for a product
Future<ItemPriceDTO?> showUnitSelectionDialog({
  required BuildContext context,
  required ProductDTO product,
}) async {
  // Get all units from the UnitController
  final unitController = Provider.of<UnitController>(context, listen: false);
  final invoiceSettingsController =
      Provider.of<InvoiceSettingsController>(context, listen: false);

  // Make sure units are loaded
  if (unitController.units.isEmpty) {
    await unitController.fetchUnits();
  }

  // Create a list of ItemPriceDTO from the units
  List<ItemPriceDTO> allUnits = [];

  // Add all units from the controller
  for (var unit in unitController.units) {
    allUnits.add(ItemPriceDTO(
      unitID: unit.id,
      unitName: unit.name,
      salesPrice: product.price, // Keep the same price
    ));
  }

  // If there's a default unit set and the product doesn't have a specific unit,
  // we can auto-select the default unit
  if (product.uniteId == null &&
      invoiceSettingsController.defaultUnitId != null) {
    // Find the default unit in our list
    final defaultUnit = allUnits.firstWhere(
      (unit) => unit.unitID == invoiceSettingsController.defaultUnitId,
      orElse: () => allUnits.first,
    );

    // Return the default unit without showing the dialog
    return defaultUnit;
  }

  // If there are no units, show an error message and return null
  if (allUnits.isEmpty) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(T('No units available')),
        backgroundColor: Colors.red,
      ),
    );
    return null;
  }

  // Show the dialog
  return showDialog<ItemPriceDTO>(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(T('Select Unit'),
            style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
        content: SizedBox(
          width: double.maxFinite,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // List of units
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.5,
                ),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: allUnits.length,
                  itemBuilder: (context, index) {
                    final unit = allUnits[index];
                    final isSelected = unit.unitID == product.uniteId;

                    return ListTile(
                      title: Text(unit.unitName ?? 'Unknown'),
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? context.newPrimaryColor
                              : context.newPrimaryColor.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.straighten,
                          color: isSelected
                              ? Colors.white
                              : context.newPrimaryColor,
                          size: 20,
                        ),
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check_circle,
                              color: context.newPrimaryColor,
                            )
                          : null,
                      onTap: () {
                        Navigator.of(context).pop(unit);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(T('Cancel')),
          ),
          // Sync units button
          TextButton(
            onPressed: () async {
              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (BuildContext context) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                },
              );

              try {
                // Sync units
                await Provider.of<UnitController>(context, listen: false)
                    .fetchUnitsFromServer();

                // Close loading indicator
                Navigator.of(context).pop();

                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(T('Units synchronized successfully')),
                    backgroundColor: Colors.green,
                  ),
                );
              } catch (e) {
                // Close loading indicator
                Navigator.of(context).pop();

                // Show error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(T('Failed to synchronize units')),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: Text(T('Sync Units')),
          ),
        ],
      );
    },
  );
}
