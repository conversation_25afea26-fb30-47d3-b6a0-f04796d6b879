class LogInDto {
  int? id;
  String? usernameOrEmail;
  String? password;
  String? token;
  int? currentBranchID;
  String? currentBranchName;

  LogInDto(
      {this.id,
      this.usernameOrEmail,
      this.password,
      this.token,
      this.currentBranchID,
      this.currentBranchName});

  LogInDto.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    usernameOrEmail = json['usernameOrEmail'];
    password = json['password'];
    currentBranchID = json['Current_Branch_ID'];
    token = json['token'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['usernameOrEmail'] = usernameOrEmail;
    data['password'] = password;
    data['token'] = token;
    data['Current_Branch_ID'] = currentBranchID;

    return data;
  }
}
