import 'package:flutter/material.dart';
import '../../../models/warehouse_planner/warehouse_layout.dart';

/// لوحة قوالب الخزائن
class ShelfPalette extends StatelessWidget {
  final String? selectedTemplateId;
  final Function(String) onTemplateSelected;

  const ShelfPalette({
    Key? key,
    this.selectedTemplateId,
    required this.onTemplateSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'قوالب الخزائن',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: ShelfTemplate.predefined.length,
              itemBuilder: (context, index) {
                final template = ShelfTemplate.predefined[index];
                return _buildTemplateCard(template);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateCard(ShelfTemplate template) {
    final isSelected = selectedTemplateId == template.name;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: isSelected
            ? LinearGradient(
                colors: [template.color, template.color.withOpacity(0.8)],
              )
            : null,
        color: isSelected ? null : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? template.color : Colors.grey.shade300,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: template.color.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => onTemplateSelected(template.name),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس الكارت
                Row(
                  children: [
                    // أيقونة نوع الخزانة
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Colors.white.withOpacity(0.2)
                            : template.color.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getTypeIcon(template.type),
                        color: isSelected ? Colors.white : template.color,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),

                    // اسم القالب
                    Expanded(
                      child: Text(
                        template.name,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color:
                              isSelected ? Colors.white : Colors.grey.shade800,
                        ),
                      ),
                    ),

                    // إشارة التحديد
                    if (isSelected)
                      Icon(
                        Icons.check_circle,
                        color: Colors.white,
                        size: 20,
                      ),
                  ],
                ),

                const SizedBox(height: 12),

                // معاينة مرئية للخزانة
                _buildShelfPreview(template, isSelected),

                const SizedBox(height: 12),

                // معلومات القالب
                _buildTemplateInfo(template, isSelected),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildShelfPreview(ShelfTemplate template, bool isSelected) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: isSelected ? Colors.white.withOpacity(0.1) : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isSelected ? Colors.white.withOpacity(0.3) : Colors.grey.shade200,
        ),
      ),
      child: CustomPaint(
        painter: ShelfPreviewPainter(
          template: template,
          isSelected: isSelected,
        ),
        size: const Size(double.infinity, 60),
      ),
    );
  }

  Widget _buildTemplateInfo(ShelfTemplate template, bool isSelected) {
    final textColor = isSelected ? Colors.white : Colors.grey.shade600;
    final valueColor = isSelected ? Colors.white : Colors.grey.shade800;

    return Column(
      children: [
        // الأبعاد
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الأبعاد:',
              style: TextStyle(
                fontSize: 12,
                color: textColor,
              ),
            ),
            Text(
              '${template.width.toInt()}×${template.depth.toInt()}×${template.height.toInt()} سم',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: valueColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 4),

        // المستويات والخانات
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'التقسيم:',
              style: TextStyle(
                fontSize: 12,
                color: textColor,
              ),
            ),
            Text(
              '${template.levels} مستوى، ${template.slotsPerLevel} خانة/مستوى',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: valueColor,
              ),
            ),
          ],
        ),

        const SizedBox(height: 4),

        // نوع الخزانة
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'النوع:',
              style: TextStyle(
                fontSize: 12,
                color: textColor,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.white.withOpacity(0.2)
                    : template.color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                _getTypeLabel(template.type),
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.white : template.color,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  IconData _getTypeIcon(ShelfType type) {
    switch (type) {
      case ShelfType.standard:
        return Icons.inventory_2;
      case ShelfType.refrigerated:
        return Icons.ac_unit;
      case ShelfType.controlled:
        return Icons.thermostat;
      case ShelfType.hazardous:
        return Icons.warning;
      case ShelfType.narcotics:
        return Icons.security;
    }
  }

  String _getTypeLabel(ShelfType type) {
    switch (type) {
      case ShelfType.standard:
        return 'عادية';
      case ShelfType.refrigerated:
        return 'مبردة';
      case ShelfType.controlled:
        return 'محكمة';
      case ShelfType.hazardous:
        return 'خطرة';
      case ShelfType.narcotics:
        return 'مخدرات';
    }
  }
}

/// رسام معاينة الخزانة
class ShelfPreviewPainter extends CustomPainter {
  final ShelfTemplate template;
  final bool isSelected;

  ShelfPreviewPainter({
    required this.template,
    required this.isSelected,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = isSelected
          ? Colors.white.withOpacity(0.8)
          : template.color.withOpacity(0.8)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = isSelected ? Colors.white : template.color
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;

    final linePaint = Paint()
      ..color = isSelected
          ? Colors.white.withOpacity(0.5)
          : template.color.withOpacity(0.5)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    // حساب نسبة الرسم
    final margin = 8.0;
    final drawWidth = size.width - 2 * margin;
    final drawHeight = size.height - 2 * margin;

    // رسم مستطيل الخزانة
    final rect = Rect.fromLTWH(
      margin,
      margin,
      drawWidth,
      drawHeight,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      paint,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(rect, const Radius.circular(4)),
      borderPaint,
    );

    // رسم الأرفف الداخلية
    final levelHeight = drawHeight / template.levels;
    final slotWidth = drawWidth / template.slotsPerLevel;

    // الخطوط الأفقية (المستويات)
    for (int level = 1; level < template.levels; level++) {
      final y = margin + level * levelHeight;
      canvas.drawLine(
        Offset(margin + 2, y),
        Offset(margin + drawWidth - 2, y),
        linePaint,
      );
    }

    // الخطوط العمودية (الخانات)
    for (int slot = 1; slot < template.slotsPerLevel; slot++) {
      final x = margin + slot * slotWidth;
      canvas.drawLine(
        Offset(x, margin + 2),
        Offset(x, margin + drawHeight - 2),
        linePaint,
      );
    }

    // رسم النقاط الصغيرة كأدوية مصغرة
    final dotPaint = Paint()
      ..color = isSelected
          ? Colors.white.withOpacity(0.6)
          : template.color.withOpacity(0.6)
      ..style = PaintingStyle.fill;

    for (int level = 0; level < template.levels; level++) {
      for (int slot = 0; slot < template.slotsPerLevel; slot++) {
        // نقاط عشوائية لمحاكاة الأدوية
        if ((level + slot) % 3 == 0) {
          final x = margin + slot * slotWidth + slotWidth / 2;
          final y = margin + level * levelHeight + levelHeight / 2;
          canvas.drawCircle(Offset(x, y), 2, dotPaint);
        }
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
