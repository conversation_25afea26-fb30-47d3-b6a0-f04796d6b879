import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/device_user_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/server/product_sales_server_dto.dart';
import 'package:inventory_application/models/dto/reports/server/product_sales_report_dto.dart'
    as ServerReportDTO;
import 'package:inventory_application/models/dto/device_dto.dart';
import 'package:inventory_application/models/dto/user_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/services/server_reports_service.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

enum DateRangeType {
  daily, // يومي
  monthly, // شهري
  yearly, // سنوي
  custom // تاريخ مخصص
}

class ProductSalesReportScreen extends StatefulWidget {
  const ProductSalesReportScreen({Key? key}) : super(key: key);

  @override
  State<ProductSalesReportScreen> createState() =>
      _ProductSalesReportScreenState();
}

class _ProductSalesReportScreenState extends State<ProductSalesReportScreen>
    with TickerProviderStateMixin {
  ServerReportDTO.ProductSalesServerReportDTO? _reportData;
  bool _isLoading = false;
  DateTime? _fromDate;
  DateTime? _toDate;
  DateRangeType? _selectedDateRangeType;

  final ServerReportsService _reportsService = ServerReportsService();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final deviceController = context.read<DeviceUserController>();
      deviceController.loadDevices();
      _loadReport();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final deviceController = context.read<DeviceUserController>();

      final String? deviceId = deviceController.selectedDevice?.id;
      final int? userId = deviceController.selectedUser?.id;

      final data = await _reportsService.getProductSalesReport(
        deviceId: deviceId,
        userId: userId,
        fromDate: _fromDate,
        toDate: _toDate,
      );

      if (data != null) {
        setState(() {
          _reportData =
              ServerReportDTO.ProductSalesServerReportDTO.fromProductsList(
            data,
            fromDate: _fromDate,
            toDate: _toDate,
          );
        });
        _animationController.forward();
      } else {
        errorSnackBar(message: 'فشل في جلب بيانات التقرير');
      }
    } catch (e) {
      print('Error loading product sales report: $e');
      errorSnackBar(
        message: 'حدث خطأ أثناء جلب البيانات\n'
            'جرب تعديل الفترة الزمنية أو تحقق من الاتصال',
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _showFiltersDialog() async {
    await showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        initialFromDate: _fromDate,
        initialToDate: _toDate,
        initialDateRangeType: _selectedDateRangeType,
        onApplyFilters: (fromDate, toDate, dateRangeType) {
          setState(() {
            _fromDate = fromDate;
            _toDate = toDate;
            _selectedDateRangeType = dateRangeType;
          });
          _loadReport();
        },
      ),
    );
  }

  void _applyDateRangeType(DateRangeType type) {
    final now = DateTime.now();
    setState(() {
      _selectedDateRangeType = type;
      switch (type) {
        case DateRangeType.daily:
          _fromDate = DateTime(now.year, now.month, now.day);
          _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
          break;
        case DateRangeType.monthly:
          _fromDate = DateTime(now.year, now.month, 1);
          _toDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
          break;
        case DateRangeType.yearly:
          _fromDate = DateTime(now.year, 1, 1);
          _toDate = DateTime(now.year, 12, 31, 23, 59, 59);
          break;
        case DateRangeType.custom:
          // سيتم اختيار التاريخ يدوياً
          break;
      }
    });
  }

  String _getDateRangeTypeDisplayName(DateRangeType type) {
    switch (type) {
      case DateRangeType.daily:
        return 'يومي';
      case DateRangeType.monthly:
        return 'شهري';
      case DateRangeType.yearly:
        return 'سنوي';
      case DateRangeType.custom:
        return 'تاريخ مخصص';
    }
  }

  double get _totalQuantity {
    return _reportData?.totalQuantity ?? 0.0;
  }

  double get _totalSales {
    return _reportData?.totalSales ?? 0.0;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'تقرير مبيعات المنتجات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF6366F1),
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFiltersDialog,
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReport,
          ),
        ],
      ),
      body: Column(
        children: [
          // Summary Cards
          _buildSummaryCards(),

          // Applied Filters Display
          _buildFiltersDisplay(),

          // Products List
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFF6366F1),
                    ),
                  )
                : (_reportData?.products.isEmpty ?? true)
                    ? _buildEmptyState()
                    : _buildProductsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              'إجمالي الكمية',
              _totalQuantity.toStringAsFixed(2),
              Icons.inventory_2,
              Colors.blue,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: _buildSummaryCard(
              'إجمالي المبيعات',
              ' ' + NumberFormat("#,##0.00").format(_totalSales),
              Icons.monetization_on,
              Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
      String title, String value, IconData icon, Color color) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withOpacity(0.1),
              color.withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withOpacity(0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: color,
                    size: 20,
                  ),
                ),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersDisplay() {
    final deviceController = context.watch<DeviceUserController>();
    final hasFilters = deviceController.selectedDevice != null ||
        deviceController.selectedUser != null ||
        _fromDate != null ||
        _toDate != null ||
        _selectedDateRangeType != null;

    if (!hasFilters) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF6366F1).withOpacity(0.1),
                const Color(0xFF6366F1).withOpacity(0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF6366F1).withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.filter_list,
                    color: const Color(0xFF6366F1),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'الفلاتر المطبقة',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF6366F1),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  if (deviceController.selectedDevice != null)
                    _buildFilterChip(
                      'الجهاز: ${deviceController.selectedDevice!.name}',
                      Icons.device_hub,
                    ),
                  if (deviceController.selectedUser != null)
                    _buildFilterChip(
                      'المستخدم: ${deviceController.selectedUser!.name}',
                      Icons.person,
                    ),
                  if (_selectedDateRangeType != null)
                    _buildFilterChip(
                      'الفترة الزمنية: ${_getDateRangeTypeDisplayName(_selectedDateRangeType!)}',
                      Icons.date_range,
                    ),
                  if (_fromDate != null && _toDate != null)
                    _buildFilterChip(
                      'من ${DateFormat('dd/MM/yyyy').format(_fromDate!)} إلى ${DateFormat('dd/MM/yyyy').format(_toDate!)}',
                      Icons.calendar_today,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFilterChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: const Color(0xFF6366F1).withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: const Color(0xFF6366F1),
          ),
          const SizedBox(width: 6),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF6366F1),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.inventory_2_outlined,
                size: 64,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد بيانات مبيعات',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'لم يتم العثور على مبيعات للمنتجات في الفترة المحددة',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _showFiltersDialog,
              icon: const Icon(Icons.filter_list),
              label: const Text('تعديل الفلاتر'),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF6366F1),
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductsList() {
    final products = _reportData?.products ?? [];

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: products.length,
        itemBuilder: (context, index) {
          final product = products[index];
          return _buildProductCard(product, index);
        },
      ),
    );
  }

  Widget _buildProductCard(ProductSalesServerDTO product, int index) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        final slideAnimation = Tween<Offset>(
          begin: const Offset(0, 0.3),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _animationController,
          curve: Interval(
            (index * 0.1).clamp(0.0, 1.0),
            ((index * 0.1) + 0.3).clamp(0.0, 1.0),
            curve: Curves.easeOutCubic,
          ),
        ));

        return SlideTransition(
          position: slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.only(bottom: 12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
                border: Border.all(
                  color: Colors.grey.withOpacity(0.1),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: const Color(0xFF6366F1).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Icon(
                            Icons.inventory_2,
                            color: Color(0xFF6366F1),
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                product.productName ?? 'منتج غير محدد',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black87,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'كود المنتج: ${product.productId ?? 'غير محدد'}',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: _buildProductMetric(
                            'الكمية المباعة',
                            (product.quantitySold ?? 0).toStringAsFixed(2),
                            Icons.shopping_cart,
                            Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildProductMetric(
                            'إجمالي المبيعات',
                            ' ' +
                                NumberFormat("#,##0.00")
                                    .format(product.totalSales ?? 0),
                            Icons.monetization_on,
                            Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildProductMetric(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.1),
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

// Filter Dialog (same as in SalesTotalReportScreen but for ProductSalesReportScreen)
class _FilterDialog extends StatefulWidget {
  final DateTime? initialFromDate;
  final DateTime? initialToDate;
  final DateRangeType? initialDateRangeType;
  final Function(DateTime?, DateTime?, DateRangeType?) onApplyFilters;

  const _FilterDialog({
    Key? key,
    this.initialFromDate,
    this.initialToDate,
    this.initialDateRangeType,
    required this.onApplyFilters,
  }) : super(key: key);

  @override
  State<_FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<_FilterDialog> {
  DateTime? _fromDate;
  DateTime? _toDate;
  DateRangeType? _selectedDateRangeType;

  @override
  void initState() {
    super.initState();
    _fromDate = widget.initialFromDate;
    _toDate = widget.initialToDate;
    _selectedDateRangeType = widget.initialDateRangeType;
  }

  List<ComboBoxDataModel> _getDevicesData() {
    final deviceController = context.read<DeviceUserController>();
    List<ComboBoxDataModel> data = [
      ComboBoxDataModel(id: 0, name: 'جميع الأجهزة'),
    ];
    for (var device in deviceController.devices) {
      if (device.id != null && device.name != null) {
        data.add(ComboBoxDataModel(
            id: int.tryParse(device.id!) ?? 0, name: device.name!));
      }
    }
    return data;
  }

  List<ComboBoxDataModel> _getUsersData() {
    final deviceController = context.read<DeviceUserController>();
    List<ComboBoxDataModel> data = [
      ComboBoxDataModel(id: 0, name: 'جميع المستخدمين'),
    ];
    for (var user in deviceController.users) {
      if (user.id != null && user.name != null) {
        data.add(ComboBoxDataModel(id: user.id!, name: user.name!));
      }
    }
    return data;
  }

  List<ComboBoxDataModel> _getDateRangeTypesData() {
    return [
      ComboBoxDataModel(id: 0, name: 'جميع الفترات'),
      ComboBoxDataModel(id: 1, name: 'يومي'),
      ComboBoxDataModel(id: 2, name: 'شهري'),
      ComboBoxDataModel(id: 3, name: 'سنوي'),
      ComboBoxDataModel(id: 4, name: 'تاريخ مخصص'),
    ];
  }

  DateRangeType? _getDateRangeTypeFromId(int id) {
    switch (id) {
      case 1:
        return DateRangeType.daily;
      case 2:
        return DateRangeType.monthly;
      case 3:
        return DateRangeType.yearly;
      case 4:
        return DateRangeType.custom;
      default:
        return null;
    }
  }

  int _getIdFromDateRangeType(DateRangeType? type) {
    switch (type) {
      case DateRangeType.daily:
        return 1;
      case DateRangeType.monthly:
        return 2;
      case DateRangeType.yearly:
        return 3;
      case DateRangeType.custom:
        return 4;
      default:
        return 0;
    }
  }

  void _applyDateRangeType(DateRangeType type) {
    final now = DateTime.now();
    setState(() {
      _selectedDateRangeType = type;
      switch (type) {
        case DateRangeType.daily:
          _fromDate = DateTime(now.year, now.month, now.day);
          _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
          break;
        case DateRangeType.monthly:
          _fromDate = DateTime(now.year, now.month, 1);
          _toDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
          break;
        case DateRangeType.yearly:
          _fromDate = DateTime(now.year, 1, 1);
          _toDate = DateTime(now.year, 12, 31, 23, 59, 59);
          break;
        case DateRangeType.custom:
          // سيتم اختيار التاريخ يدوياً
          break;
      }
    });
  }

  String _getDateRangeTypeDisplayName(DateRangeType type) {
    switch (type) {
      case DateRangeType.daily:
        return 'يومي';
      case DateRangeType.monthly:
        return 'شهري';
      case DateRangeType.yearly:
        return 'سنوي';
      case DateRangeType.custom:
        return 'تاريخ مخصص';
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceController = context.watch<DeviceUserController>();

    return AlertDialog(
      title: const Text(
        'فلاتر التقرير',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Device Selection
            if (AuthController.getIsMainAdmin())
              if (!deviceController.isLoadingDevices)
                MyComboBox(
                  caption:
                      deviceController.selectedDevice?.name ?? 'جميع الأجهزة',
                  labelText: 'الجهاز',
                  selectedValue: deviceController.selectedDevice != null
                      ? int.tryParse(deviceController.selectedDevice!.id!) ?? 0
                      : 0,
                  modalTitle: 'اختر الجهاز',
                  data: _getDevicesData(),
                  height: 60,
                  isShowLabel: false,
                  onSelect: (id, name) {
                    deviceController.selectedDevice?.id = id.toString();
                    deviceController.selectedDevice?.name = name;
                    deviceController.notifyListeners();
                  },
                )
              else
                Container(
                  height: 60,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(child: CircularProgressIndicator()),
                ),

            const SizedBox(height: 16),

            // User Selection
            if (AuthController.getIsMainAdmin())
              if (!deviceController.isLoadingUsers)
                MyComboBox(
                  caption:
                      deviceController.selectedUser?.name ?? 'جميع المستخدمين',
                  labelText: 'المستخدم',
                  selectedValue: deviceController.selectedUser?.id ?? 0,
                  modalTitle: 'اختر المستخدم',
                  data: _getUsersData(),
                  height: 60,
                  isShowLabel: false,
                  onSelect: (id, name) {
                    deviceController.selectedUser?.id = id;
                    deviceController.selectedUser?.name = name;
                    deviceController.notifyListeners();
                  },
                )
              else
                Container(
                  height: 60,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(child: CircularProgressIndicator()),
                ),

            const SizedBox(height: 16),

            // Date Range Type Selection
            MyComboBox(
              caption: _selectedDateRangeType != null
                  ? _getDateRangeTypeDisplayName(_selectedDateRangeType!)
                  : 'اختر نوع الفترة',
              labelText: 'نوع الفترة الزمنية',
              selectedValue: _getIdFromDateRangeType(_selectedDateRangeType),
              modalTitle: 'اختر نوع الفترة',
              data: _getDateRangeTypesData(),
              height: 60,
              isShowLabel: false,
              onSelect: (id, name) {
                final dateRangeType = _getDateRangeTypeFromId(id);
                if (dateRangeType != null) {
                  _applyDateRangeType(dateRangeType);
                } else {
                  setState(() {
                    _selectedDateRangeType = null;
                    _fromDate = null;
                    _toDate = null;
                  });
                }
              },
            ),

            const SizedBox(height: 16),

            // Custom Date Range Selection (only for custom type)
            if (_selectedDateRangeType == DateRangeType.custom) ...[
              const Text(
                'الفترة الزمنية المخصصة',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _fromDate != null && _toDate != null
                                ? '${DateFormat('dd/MM/yyyy').format(_fromDate!)} - ${DateFormat('dd/MM/yyyy').format(_toDate!)}'
                                : 'اختر الفترة الزمنية',
                            style: TextStyle(
                              color: _fromDate != null && _toDate != null
                                  ? Colors.black87
                                  : Colors.grey[600],
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.date_range),
                          onPressed: _selectDateRange,
                        ),
                      ],
                    ),
                    if (_fromDate != null || _toDate != null)
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () {
                            setState(() {
                              _fromDate = null;
                              _toDate = null;
                            });
                          },
                          child: const Text('مسح الفترة الزمنية'),
                        ),
                      ),
                  ],
                ),
              ),
            ] else if (_selectedDateRangeType != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle,
                        color: Colors.green.shade600, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _fromDate != null && _toDate != null
                            ? '${DateFormat('dd/MM/yyyy').format(_fromDate!)} - ${DateFormat('dd/MM/yyyy').format(_toDate!)}'
                            : 'تم تطبيق الفترة ${_getDateRangeTypeDisplayName(_selectedDateRangeType!)}',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApplyFilters(_fromDate, _toDate, _selectedDateRangeType);
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF6366F1),
            foregroundColor: Colors.white,
          ),
          child: const Text('تطبيق'),
        ),
      ],
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _fromDate != null && _toDate != null
          ? DateTimeRange(start: _fromDate!, end: _toDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: const Color(0xFF6366F1),
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
      });
    }
  }
}
