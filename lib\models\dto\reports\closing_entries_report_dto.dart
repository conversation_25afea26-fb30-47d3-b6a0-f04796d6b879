class ClosingEntriesReportDTO {
  FinancialSummaryDTO? financialSummary;
  DetailedSummariesDTO? detailedSummaries;
  TransactionDetailsDTO? transactionDetails;

  ClosingEntriesReportDTO({
    this.financialSummary,
    this.detailedSummaries,
    this.transactionDetails,
  });

  factory ClosingEntriesReportDTO.fromJson(Map<String, dynamic> json) {
    return ClosingEntriesReportDTO(
      financialSummary: json['financialSummary'] != null
          ? FinancialSummaryDTO.fromJson(json['financialSummary'])
          : null,
      detailedSummaries: json['detailedSummaries'] != null
          ? DetailedSummariesDTO.fromJson(json['detailedSummaries'])
          : null,
      transactionDetails: json['transactionDetails'] != null
          ? TransactionDetailsDTO.fromJson(json['transactionDetails'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'financialSummary': financialSummary?.toJson(),
      'detailedSummaries': detailedSummaries?.toJson(),
      'transactionDetails': transactionDetails?.toJson(),
    };
  }
}

class FinancialSummaryDTO {
  RechargeSummaryDTO? rechargeSummary;
  SalesSummaryDTO? salesSummary;
  double? netAmount;
  double? totalCurrentBalance;
  int? totalTransactions;

  FinancialSummaryDTO({
    this.rechargeSummary,
    this.salesSummary,
    this.netAmount,
    this.totalCurrentBalance,
    this.totalTransactions,
  });

  factory FinancialSummaryDTO.fromJson(Map<String, dynamic> json) {
    return FinancialSummaryDTO(
      rechargeSummary: json['rechargeSummary'] != null
          ? RechargeSummaryDTO.fromJson(json['rechargeSummary'])
          : null,
      salesSummary: json['salesSummary'] != null
          ? SalesSummaryDTO.fromJson(json['salesSummary'])
          : null,
      netAmount: json['netAmount']?.toDouble(),
      totalCurrentBalance: json['totalCurrentBalance']?.toDouble(),
      totalTransactions: json['totalTransactions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rechargeSummary': rechargeSummary?.toJson(),
      'salesSummary': salesSummary?.toJson(),
      'netAmount': netAmount,
      'totalCurrentBalance': totalCurrentBalance,
      'totalTransactions': totalTransactions,
    };
  }
}

class RechargeSummaryDTO {
  double? totalRechargeAmount;
  double? totalRechargeAmountCard;
  double? totalRechargeAmountCash;
  double? totalGiftAmount;
  double? totalFeesAmount;
  double? totalFeesAmountCard;
  double? totalFeesAmountCash;
  double? totalAmount;
  double? totalAmountCard;
  double? totalAmountCash;
  double? totalParentFeeAmount;
  double? totalParentFeeAmountCard;
  double? totalParentFeeAmountCash;
  int? transactionCount;
  int? uniqueMembers;

  RechargeSummaryDTO({
    this.totalRechargeAmount,
    this.totalRechargeAmountCard,
    this.totalRechargeAmountCash,
    this.totalGiftAmount,
    this.totalFeesAmount,
    this.totalFeesAmountCard,
    this.totalFeesAmountCash,
    this.totalAmount,
    this.totalAmountCard,
    this.totalAmountCash,
    this.transactionCount,
    this.totalParentFeeAmount,
    this.totalParentFeeAmountCard,
    this.totalParentFeeAmountCash,
    this.uniqueMembers,
  });

  factory RechargeSummaryDTO.fromJson(Map<String, dynamic> json) {
    return RechargeSummaryDTO(
      totalRechargeAmount: json['totalRechargeAmount']?.toDouble(),
      totalRechargeAmountCard: json['totalRechargeAmountCard']?.toDouble(),
      totalRechargeAmountCash: json['totalRechargeAmountCash']?.toDouble(),
      totalGiftAmount: json['totalGiftAmount']?.toDouble(),
      totalFeesAmount: json['totalFeesAmount']?.toDouble(),
      totalFeesAmountCard: json['totalFeesAmountCard']?.toDouble(),
      totalFeesAmountCash: json['totalFeesAmountCash']?.toDouble(),
      totalAmount: json['totalAmount']?.toDouble(),
      totalAmountCard: json['totalAmountCard']?.toDouble(),
      totalAmountCash: json['totalAmountCash']?.toDouble(),
      transactionCount: json['transactionCount'],
      uniqueMembers: json['uniqueMembers'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalRechargeAmount': totalRechargeAmount,
      'totalRechargeAmountCard': totalRechargeAmountCard,
      'totalRechargeAmountCash': totalRechargeAmountCash,
      'totalGiftAmount': totalGiftAmount,
      'totalFeesAmount': totalFeesAmount,
      'totalFeesAmountCard': totalFeesAmountCard,
      'totalFeesAmountCash': totalFeesAmountCash,
      'totalAmount': totalAmount,
      'totalAmountCard': totalAmountCard,
      'totalAmountCash': totalAmountCash,
      'transactionCount': transactionCount,
      'uniqueMembers': uniqueMembers,
      'totalParentFeeAmount': totalParentFeeAmount,
      'totalParentFeeAmountCard': totalParentFeeAmountCard,
      'totalParentFeeAmountCash': totalParentFeeAmountCash,
    };
  }
}

class SalesSummaryDTO {
  double? totalSalesAmount;
  int? transactionCount;
  int? uniqueBills;
  int? uniqueMembers;

  SalesSummaryDTO({
    this.totalSalesAmount,
    this.transactionCount,
    this.uniqueBills,
    this.uniqueMembers,
  });

  factory SalesSummaryDTO.fromJson(Map<String, dynamic> json) {
    return SalesSummaryDTO(
      totalSalesAmount: json['totalSalesAmount']?.toDouble(),
      transactionCount: json['transactionCount'],
      uniqueBills: json['uniqueBills'],
      uniqueMembers: json['uniqueMembers'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalSalesAmount': totalSalesAmount,
      'transactionCount': transactionCount,
      'uniqueBills': uniqueBills,
      'uniqueMembers': uniqueMembers,
    };
  }
}

class DetailedSummariesDTO {
  List<CompanySummaryDTO>? byCompany;
  List<AdminSummaryDTO>? byAdmin;

  DetailedSummariesDTO({
    this.byCompany,
    this.byAdmin,
  });

  factory DetailedSummariesDTO.fromJson(Map<String, dynamic> json) {
    return DetailedSummariesDTO(
      byCompany: json['byCompany'] != null
          ? (json['byCompany'] as List)
              .map((item) => CompanySummaryDTO.fromJson(item))
              .toList()
          : null,
      byAdmin: json['byAdmin'] != null
          ? (json['byAdmin'] as List)
              .map((item) => AdminSummaryDTO.fromJson(item))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'byCompany': byCompany?.map((item) => item.toJson()).toList(),
      'byAdmin': byAdmin?.map((item) => item.toJson()).toList(),
    };
  }
}

class CompanySummaryDTO {
  int? companyId;
  String? companyName;
  double? totalRecharge;
  int? transactionCount;
  int? memberCount;

  CompanySummaryDTO({
    this.companyId,
    this.companyName,
    this.totalRecharge,
    this.transactionCount,
    this.memberCount,
  });

  factory CompanySummaryDTO.fromJson(Map<String, dynamic> json) {
    return CompanySummaryDTO(
      companyId: json['companyId'],
      companyName: json['companyName'],
      totalRecharge: json['totalRecharge']?.toDouble(),
      transactionCount: json['transactionCount'],
      memberCount: json['memberCount'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'companyId': companyId,
      'companyName': companyName,
      'totalRecharge': totalRecharge,
      'transactionCount': transactionCount,
      'memberCount': memberCount,
    };
  }
}

class AdminSummaryDTO {
  int? adminId;
  String? adminName;
  String? adminAccount;
  double? totalAmount;
  double? totalAmountCard;
  double? totalAmountCash;
  double? totalRecharge;
  double? totalRechargeCard;
  double? totalRechargeCash;
  double? totalFeesAmount;
  double? totalFeesAmountCard;
  double? totalFeesAmountCash;
  int? transactionCount;
  int? memberCount;

  AdminSummaryDTO({
    this.adminId,
    this.adminName,
    this.adminAccount,
    this.totalAmount,
    this.totalAmountCard,
    this.totalAmountCash,
    this.totalRecharge,
    this.totalRechargeCard,
    this.totalRechargeCash,
    this.totalFeesAmount,
    this.totalFeesAmountCard,
    this.totalFeesAmountCash,
    this.transactionCount,
    this.memberCount,
  });

  factory AdminSummaryDTO.fromJson(Map<String, dynamic> json) {
    return AdminSummaryDTO(
      adminId: json['adminId'],
      adminName: json['adminName'],
      adminAccount: json['adminAccount'],
      totalAmount: json['totalAmount']?.toDouble(),
      totalAmountCard: json['totalAmountCard']?.toDouble(),
      totalAmountCash: json['totalAmountCash']?.toDouble(),
      totalRecharge: json['totalRecharge']?.toDouble(),
      totalRechargeCard: json['totalRechargeCard']?.toDouble(),
      totalRechargeCash: json['totalRechargeCash']?.toDouble(),
      totalFeesAmount: json['totalFeesAmount']?.toDouble(),
      totalFeesAmountCard: json['totalFeesAmountCard']?.toDouble(),
      totalFeesAmountCash: json['totalFeesAmountCash']?.toDouble(),
      transactionCount: json['transactionCount'],
      memberCount: json['memberCount'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'adminId': adminId,
      'adminName': adminName,
      'adminAccount': adminAccount,
      'totalAmount': totalAmount,
      'totalAmountCard': totalAmountCard,
      'totalAmountCash': totalAmountCash,
      'totalRecharge': totalRecharge,
      'totalRechargeCard': totalRechargeCard,
      'totalRechargeCash': totalRechargeCash,
      'totalFeesAmount': totalFeesAmount,
      'totalFeesAmountCard': totalFeesAmountCard,
      'totalFeesAmountCash': totalFeesAmountCash,
      'transactionCount': transactionCount,
      'memberCount': memberCount,
    };
  }
}

class TransactionDetailsDTO {
  List<RechargeTransactionDTO>? recharges;
  List<SalesTransactionDTO>? sales;
  int? totalRechargeRecords;
  int? totalSalesRecords;

  TransactionDetailsDTO({
    this.recharges,
    this.sales,
    this.totalRechargeRecords,
    this.totalSalesRecords,
  });

  factory TransactionDetailsDTO.fromJson(Map<String, dynamic> json) {
    return TransactionDetailsDTO(
      recharges: json['recharges'] != null
          ? (json['recharges'] as List)
              .map((item) => RechargeTransactionDTO.fromJson(item))
              .toList()
          : null,
      sales: json['sales'] != null
          ? (json['sales'] as List)
              .map((item) => SalesTransactionDTO.fromJson(item))
              .toList()
          : null,
      totalRechargeRecords: json['totalRechargeRecords'],
      totalSalesRecords: json['totalSalesRecords'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'recharges': recharges?.map((item) => item.toJson()).toList(),
      'sales': sales?.map((item) => item.toJson()).toList(),
      'totalRechargeRecords': totalRechargeRecords,
      'totalSalesRecords': totalSalesRecords,
    };
  }
}

class RechargeTransactionDTO {
  int? rechargeId;
  String? date;
  int? companyId;
  String? companyName;
  int? memberId;
  String? cardNumber;
  String? memberName;
  double? rechargeAmount;
  double? giftAmount;
  double? totalAmount;
  double? totalFees;
  double? balanceAfter;
  int? adminId;
  String? adminName;
  int? rechargeType;
  int? paymentTypeId;

  RechargeTransactionDTO({
    this.rechargeId,
    this.date,
    this.companyId,
    this.companyName,
    this.memberId,
    this.cardNumber,
    this.memberName,
    this.rechargeAmount,
    this.giftAmount,
    this.totalAmount,
    this.totalFees,
    this.balanceAfter,
    this.adminId,
    this.adminName,
    this.rechargeType,
    this.paymentTypeId,
  });

  factory RechargeTransactionDTO.fromJson(Map<String, dynamic> json) {
    return RechargeTransactionDTO(
      rechargeId: json['rechargeId'],
      date: json['date'],
      companyId: json['companyId'],
      companyName: json['companyName'],
      memberId: json['memberId'],
      cardNumber: json['cardNumber'],
      memberName: json['memberName'],
      rechargeAmount: json['rechargeAmount']?.toDouble(),
      giftAmount: json['giftAmount']?.toDouble(),
      totalAmount: json['totalAmount']?.toDouble(),
      totalFees: json['totalFees']?.toDouble(),
      balanceAfter: json['balanceAfter']?.toDouble(),
      adminId: json['adminId'],
      adminName: json['adminName'],
      rechargeType: json['rechargeType'],
      paymentTypeId: json['paymentTypeId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rechargeId': rechargeId,
      'date': date,
      'companyId': companyId,
      'companyName': companyName,
      'memberId': memberId,
      'cardNumber': cardNumber,
      'memberName': memberName,
      'rechargeAmount': rechargeAmount,
      'giftAmount': giftAmount,
      'totalAmount': totalAmount,
      'totalFees': totalFees,
      'balanceAfter': balanceAfter,
      'adminId': adminId,
      'adminName': adminName,
      'rechargeType': rechargeType,
      'paymentTypeId': paymentTypeId,
    };
  }
}

class SalesTransactionDTO {
  int? salesId;
  String? date;
  int? companyId;
  String? companyName;
  int? memberId;
  String? cardNumber;
  String? memberName;
  double? salesAmount;
  double? discountAmount;
  double? totalAmount;
  double? balanceAfter;
  int? adminId;
  int? salesType;

  SalesTransactionDTO({
    this.salesId,
    this.date,
    this.companyId,
    this.companyName,
    this.memberId,
    this.cardNumber,
    this.memberName,
    this.salesAmount,
    this.discountAmount,
    this.totalAmount,
    this.balanceAfter,
    this.adminId,
    this.salesType,
  });

  factory SalesTransactionDTO.fromJson(Map<String, dynamic> json) {
    return SalesTransactionDTO(
      salesId: json['salesId'],
      date: json['date'],
      companyId: json['companyId'],
      companyName: json['companyName'],
      memberId: json['memberId'],
      cardNumber: json['cardNumber'],
      memberName: json['memberName'],
      salesAmount: json['salesAmount']?.toDouble(),
      discountAmount: json['discountAmount']?.toDouble(),
      totalAmount: json['totalAmount']?.toDouble(),
      balanceAfter: json['balanceAfter']?.toDouble(),
      adminId: json['adminId'],
      salesType: json['salesType'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'salesId': salesId,
      'date': date,
      'companyId': companyId,
      'companyName': companyName,
      'memberId': memberId,
      'cardNumber': cardNumber,
      'memberName': memberName,
      'salesAmount': salesAmount,
      'discountAmount': discountAmount,
      'totalAmount': totalAmount,
      'balanceAfter': balanceAfter,
      'adminId': adminId,
      'salesType': salesType,
    };
  }
}
