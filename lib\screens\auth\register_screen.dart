import 'package:flutter/material.dart';
import 'package:flutter_pw_validator/flutter_pw_validator.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/screens/auth/%C4%B0magebgWidget.dart';
import 'package:inventory_application/screens/auth/widget/custom_input_field.dart';

class SignUpScreen extends StatefulWidget {
  const SignUpScreen({super.key});

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final GlobalKey<FlutterPwValidatorState> validatorKey =
      GlobalKey<FlutterPwValidatorState>();
  bool isRememberMeChecked = false;
  bool passwordValid = false;
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF56A6ED), Color(0xFF895FF9)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Column(
          children: [
            ImageBgWidget(
              height: 180,
            ),
            const SizedBox(
              height: 30,
            ),
            Text(
              "قم بتسجيل حساب جديد",
              style: TextStyle(
                  color: context.backgroundColor,
                  fontWeight: FontWeight.bold,
                  fontSize: 28),
            ),
            const SizedBox(
              height: 45,
            ),
            CustomInputField(
              controller: usernameController,
              icon: Icons.person,
              hintText: "اسم المستخدم",
              onChange: (value) {},
            ),
            const SizedBox(
              height: 10,
            ),
            CustomInputField(
              icon: Icons.email,
              hintText: "البريد الالكتروني",
              isPassword: false,
              controller: emailController,
              onChange: (value) {},
            ),
            const SizedBox(
              height: 10,
            ),
            CustomInputField(
              icon: Icons.lock,
              hintText: "كلمة المرور",
              isPassword: true,
              controller: passwordController,
              onChange: (value) {},
            ),
            // Container(
            //   alignment:
            //       context.width > 450 ? Alignment.centerLeft : Alignment.center,
            //   margin: const EdgeInsets.only(top: 2),
            //   child: FlutterPwValidator(
            //     strings: PwValidatorStrings(),
            //     key: validatorKey,
            //     controller: passwordController,
            //     minLength: 8,
            //     uppercaseCharCount: 1,
            //     numericCharCount: 3,
            //     specialCharCount: 1,
            //     normalCharCount: 3,
            //     width: context.width > 450 ? 350 : context.width - 80,
            //     height: context.width > 450 ? 160 : 130,
            //     onSuccess: () {
            //       setState(() {
            //         passwordValid = true;
            //       });
            //     },
            //     onFail: () {
            //       setState(() {
            //         passwordValid = false;
            //       });
            //     },
            //   ),
            // ),
            const SizedBox(
              height: 10,
            ),
            CustomInputField(
              icon: Icons.lock,
              hintText: " تأكيد كلمة المرور",
              isPassword: true,
              controller: confirmPasswordController,
              onChange: (value) {},
            ),
            const SizedBox(
              height: 45,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
                width: context.width,
                decoration: BoxDecoration(
                  color: Colors.blue.shade900,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.4),
                      spreadRadius: 2,
                      blurRadius: 5,
                      offset: const Offset(0, 3),
                    ),
                  ],
                  borderRadius: BorderRadius.circular(25), // Rounded corners
                ),
                child: const Center(
                  child: Text(
                    "قم بتسجيل حساب جديد",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            const Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                      text: " لديك حساب؟ ",
                      style: TextStyle(color: Colors.white)),
                  TextSpan(
                    text: "قم بتسجيل الدخول",
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
