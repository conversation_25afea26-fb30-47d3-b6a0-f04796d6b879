import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';

class InventoryOperationNoProductsWidget extends StatefulWidget {
  const InventoryOperationNoProductsWidget({super.key});

  @override
  State<InventoryOperationNoProductsWidget> createState() =>
      _InventoryOperationNoProductsWidgetState();
}

class _InventoryOperationNoProductsWidgetState
    extends State<InventoryOperationNoProductsWidget> {
  @override
  Widget build(BuildContext context) {
    return
        // Empty State
        Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            T('No products added yet'),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T('Add products to start the incoming operation'),
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }
}
