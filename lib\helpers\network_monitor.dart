import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
// ignore: depend_on_referenced_packages
import 'package:http/http.dart' as http;
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/main.dart';
import 'package:provider/provider.dart';

class NetworkMonitor {
  // Timer to periodically check internet status
  Timer? _internetCheckTimer;

  bool hasSyncedOnce = false;
  // Duration between each internet connectivity check
  final Duration _checkInterval = const Duration(seconds: 5);
//-------------------------------------------------------------
  void startMonitoring() {
    // Listen for network changes (WiFi or mobile data state changes)
    Connectivity()
        .onConnectivityChanged
        .listen((List<ConnectivityResult> result) {
      _handleNetworkChange(result);
    });

    // Start periodic internet connectivity check
    _startPeriodicInternetCheck();
  }

//-------------------------------------------------------------
  Future<void> _handleNetworkChange(List<ConnectivityResult> result) async {
    if (result.contains(ConnectivityResult.mobile) ||
        result.contains(ConnectivityResult.wifi)) {
      bool isConnected = await hasActiveInternetConnection();
      _updateInternetStatus(isConnected);
    } else {
      _updateInternetStatus(false);
    }
  }

//-------------------------------------------------------------
  Future<bool> hasActiveInternetConnection() async {
    try {
      var baseUrl = AppController.getBaseUrlFromShared();
      String requestUrl = '$baseUrl/Secure/ConnectionTest';

      var result = await http.get(Uri.parse(requestUrl)).timeout(
            const Duration(seconds: 10),
          );

      return result.statusCode == 200;
    } catch (e) {
      return false;
    }
  }

  void _startPeriodicInternetCheck() {
    _internetCheckTimer = Timer.periodic(_checkInterval, (timer) async {
      bool isConnected = await hasActiveInternetConnection();
      _updateInternetStatus(isConnected);
    });
  }

//-------------------------------------------------------------
  void _updateInternetStatus(bool isConnected) {
    if (isConnected && !hasSyncedOnce) {
      AppController().setInternetStatus(true);
      // Execute this block only once after internet becomes available
      // ignore: avoid_print
      print("Internet is available");

      // Perform your actions
      Provider.of<AppController>(navigatorKey.currentContext as BuildContext,
              listen: false)
          .setInternetStatus(true);
      Provider.of<CustomerController>(
              navigatorKey.currentContext as BuildContext,
              listen: false)
          .syncUnsyncedCustomers();
      Provider.of<InvoiceController>(
              navigatorKey.currentContext as BuildContext,
              listen: false)
          .syncInvoicesWithSever();

      navigatorKey.currentContext
          ?.read<ProductController>()
          .fetchProductAfterCertineId();

      // Set the flag to prevent multiple calls
      hasSyncedOnce = true;
    } else if (!isConnected) {
      AppController().setInternetStatus(false);
      // ignore: avoid_print
      print("No internet connection available");

      // Reset the flag when the connection is lost
      hasSyncedOnce = false;

      Provider.of<AppController>(navigatorKey.currentContext as BuildContext,
              listen: false)
          .setInternetStatus(false);
    }
  }

//-------------------------------------------------------------
  void stopMonitoring() {
    _internetCheckTimer?.cancel();
  }
}
