class AccountsVoucharsModel {
  String? code;
  double? exchangeRate;
  int? currencyId;
  int? accountIdDebit;
  int? accountIdCredit;
  double? voucharAmount;
  String? voucharDateFormated;
  String? currencyName;
  String? accountsCreditName;
  String? drawOnFormated;
  String? customerName;
  String? receiptType;
  String? invoiceNo;
  int? receiptTypeInt;
  String? note;
  int? reservationInvoiceId;
  int? paymentTypeId;
  bool? canEditCurrency;
  bool? canEditDate;
  bool? isInventory;
  String? sourceReference1;

  // Constructor with all fields
  AccountsVoucharsModel({
    this.code,
    this.exchangeRate,
    this.currencyId,
    this.accountIdDebit,
    this.accountIdCredit,
    this.voucharAmount,
    this.voucharDateFormated,
    this.currencyName,
    this.accountsCreditName,
    this.drawOnFormated,
    this.customerName,
    this.receiptType,
    this.invoiceNo,
    this.receiptTypeInt,
    this.note,
    this.reservationInvoiceId,
    this.paymentTypeId,
    this.canEditCurrency,
    this.canEditDate,
    this.isInventory,
    this.sourceReference1,
  });

  AccountsVoucharsModel.fromJson(Map<String, dynamic> json) {
    code = json['Code'];
    exchangeRate = json['Exchange_Rate']?.toDouble();
    currencyId = json['Currency_ID'];
    accountIdDebit = json['Account_ID_Debit'];
    accountIdCredit = json['Account_ID_Credit'];
    voucharAmount = json['Vouchar_Amount']?.toDouble();
    voucharDateFormated = json['Vouchar_Date_Formated'];
    currencyName = json['Currency_Name'];
    accountsCreditName = json['Accounts_Credit_Name'];
    drawOnFormated = json['Draw_On_Formated'];
    customerName = json['Customer_Name'];
    receiptType = json['Receipt_Type'];
    invoiceNo = json['Invoice_No'];
    receiptTypeInt = json['Receipt_TypeInt'];
    note = json['Note'];
    reservationInvoiceId = json['ReservationInvoice_ID'];
    paymentTypeId = json['Payment_Type_ID'];
    canEditCurrency = json['CanEditCurrency'];
    canEditDate = json['CanEditDate'];
    isInventory = json['IsInventory'];
    sourceReference1 = json['Source_Reference_1'];
  }

  Map<String, dynamic> toJson() {
    return {
      'Code': code,
      'Exchange_Rate': exchangeRate,
      'Currency_ID': currencyId,
      'Account_ID_Debit': accountIdDebit,
      'Account_ID_Credit': accountIdCredit,
      'Vouchar_Amount': voucharAmount,
      'Vouchar_Date_Formated': voucharDateFormated,
      'Currency_Name': currencyName,
      'Accounts_Credit_Name': accountsCreditName,
      'Draw_On_Formated': drawOnFormated,
      'Customer_Name': customerName,
      'Receipt_Type': receiptType,
      'Invoice_No': invoiceNo,
      'Receipt_TypeInt': receiptTypeInt,
      'Note': note,
      'ReservationInvoice_ID': reservationInvoiceId,
      'Payment_Type_ID': paymentTypeId,
      'CanEditCurrency': canEditCurrency,
      'CanEditDate': canEditDate,
      'IsInventory': isInventory,
      'Source_Reference_1': sourceReference1,
    };
  }
}
