import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/branch_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/models/dto/reports/inventory_attributes_report_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/services/server_reports_service.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

class InventoryAttributesReportScreen extends StatefulWidget {
  const InventoryAttributesReportScreen({Key? key}) : super(key: key);

  @override
  State<InventoryAttributesReportScreen> createState() =>
      _InventoryAttributesReportScreenState();
}

class _InventoryAttributesReportScreenState
    extends State<InventoryAttributesReportScreen>
    with TickerProviderStateMixin {
  // Animation Controllers
  late AnimationController _animationController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Data
  List<InventoryAttributesReportDTO>? _reportData;
  Map<String, List<InventoryAttributesReportDTO>> _groupedData = {};
  Map<int, String> _branchNames = {};
  List<ProductDTO> _products = [];
  List<ProductDTO> _filteredProducts = [];

  // Loading states
  bool _isLoading = false;
  bool _isLoadingProducts = false;

  // Filter parameters
  ProductDTO? _selectedProduct;
  final TextEditingController _productSearchController =
      TextEditingController();
  DateTime? _fromDate;
  DateTime? _toDate;
  int? _selectedStoreId;
  int? _selectedBranchId;
  String? _selectedCombination;
  List<ComboBoxDataModel> _branchesComboData = [];
  List<ComboBoxDataModel> _storesComboData = [];
  List<ComboBoxDataModel> _combinationsComboData = [];

  // Services
  final ServerReportsService _reportsService = ServerReportsService();

  // Helper methods for responsive design
  bool get isDesktop => MediaQuery.of(context).size.width > 900;
  bool get isTablet =>
      MediaQuery.of(context).size.width > 600 &&
      MediaQuery.of(context).size.width <= 900;
  bool get isMobile => MediaQuery.of(context).size.width <= 600;

  double get screenWidth => MediaQuery.of(context).size.width;
  double get contentPadding => isDesktop ? 24.0 : (isTablet ? 20.0 : 16.0);
  double get cardPadding => isDesktop ? 20.0 : (isTablet ? 16.0 : 12.0);
  double get fontSize => isDesktop ? 1.0 : (isTablet ? 0.9 : 0.8);

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadProducts();
    _loadBranchesAndStores();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _slideController.dispose();
    _productSearchController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
    _slideController.forward();
  }

  Future<void> _loadProducts() async {
    setState(() {
      _isLoadingProducts = true;
    });

    try {
      // Load all products directly from database without pagination
      await _loadAllProductsFromDatabase();
    } catch (e) {
      _showSnackBar(T('فشل في تحميل قائمة المنتجات'), isError: true);
      print('Error loading products: $e');
    } finally {
      setState(() {
        _isLoadingProducts = false;
      });
    }
  }

  Future<void> _loadBranchesAndStores() async {
    try {
      // Load branches
      final branchController =
          Provider.of<BranchController>(context, listen: false);
      await branchController.getBranches();

      // Convert branches to ComboBoxDataModel
      setState(() {
        _branchesComboData = branchController.branches
            .map((branch) => ComboBoxDataModel(
                  id: branch.id ?? 0,
                  name: branch.name ?? '',
                ))
            .toList();
      });

      // Load warehouses
      final warehouseController =
          Provider.of<WarehouseController>(context, listen: false);
      await warehouseController.getWarehouses();

      // Convert warehouses to ComboBoxDataModel
      setState(() {
        _storesComboData = warehouseController.warehouses
            .map((warehouse) => ComboBoxDataModel(
                  id: warehouse.id ?? 0,
                  name: warehouse.name ?? '',
                ))
            .toList();
      });
    } catch (e) {
      print('Error loading branches and stores: $e');
    }
  }

  Future<void> _loadAllProductsFromDatabase() async {
    try {
      final db = await DatabaseHelper().database;

      // Build a query for the local database without pagination
      List<String> whereClauses = [];
      List<dynamic> whereArgs = [];

      if (!AppController.isSharedProducts) {
        whereClauses.add('BranchId = ?');
        whereArgs.add(AppController.currentBranchId);
      }

      String? whereClause =
          whereClauses.isNotEmpty ? whereClauses.join(' AND ') : null;
      List<dynamic>? queryArgs = whereArgs.isNotEmpty ? whereArgs : null;

      // Query for ALL products without limit
      List<Map<String, dynamic>> products = await db.query(
        'ProductModel',
        where: whereClause,
        whereArgs: queryArgs,
        orderBy: 'Name ASC', // Order by name for better UX
      );

      List<ProductDTO> loadedProducts = [];

      if (products.isNotEmpty) {
        for (var productData in products) {
          var productModel = ProductModel.fromJson(productData);

          // Simplified loading - just basic product info for selection
          var productDto = ProductDTO(
            id: productModel.iD,
            title: productModel.name,
            code: productModel.code,
            mainImageUrl: productModel.mainImageUrl,
          );

          loadedProducts.add(productDto);
        }
      }

      setState(() {
        _products = loadedProducts;
        _filteredProducts = loadedProducts;
      });

      print('Loaded ${loadedProducts.length} products for selection');
    } catch (e) {
      print('Exception in _loadAllProductsFromDatabase: $e');
      rethrow;
    }
  }

  bool _hasActiveFilters() {
    return _fromDate != null ||
        _toDate != null ||
        _selectedBranchId != null ||
        _selectedStoreId != null ||
        (_selectedCombination != null && _selectedCombination!.isNotEmpty);
  }

  Widget _buildCombinationDropdown() {
    final selectedCombinationName =
        _selectedCombination != null && _selectedCombination!.isNotEmpty
            ? _selectedCombination!
            : T('كافة التركيبات');

    return MyComboBox(
      caption: selectedCombinationName,
      labelText: T('التركيبة'),
      modalTitle: T('اختيار التركيبة'),
      selectedValue: _selectedCombination?.hashCode,
      data: [
        ComboBoxDataModel(id: 0, name: T('كافة التركيبات')),
        ..._combinationsComboData,
      ],
      onSelect: (id, name) {
        setState(() {
          _selectedCombination = id == 0 ? null : name;
          _groupData(); // Re-group data with new filter
        });
      },
      onRefresh: () {
        // Re-extract combinations from current data
        if (_reportData != null && _reportData!.isNotEmpty) {
          _extractCombinationsFromData(_reportData!);
        }
      },
      height: cardPadding * 2.5,
      fontSize: (14 * fontSize).clamp(12, 16),
    );
  }

  void _filterProducts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredProducts = _products;
      } else {
        _filteredProducts = _products.where((product) {
          final productName = product.title?.toLowerCase() ?? '';
          final productCode = product.code?.toLowerCase() ?? '';
          final searchLower = query.toLowerCase();

          // Enhanced search: search in both name and code, and also search for Arabic and English
          return productName.contains(searchLower) ||
              productCode.contains(searchLower) ||
              // Allow searching with partial matches
              productName
                  .split(' ')
                  .any((word) => word.startsWith(searchLower)) ||
              (product.code != null &&
                  product.code!.toLowerCase().startsWith(searchLower));
        }).toList();
      }
    });
  }

  Future<void> _loadReport() async {
    if (_selectedProduct == null) {
      _showSnackBar(T('يرجى اختيار منتج'), isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final data = await _reportsService.getInventoryAttributesReport(
        itemId: _selectedProduct!.id!,
        fromDate: _fromDate,
        toDate: _toDate,
        storeId: _selectedStoreId,
        branchId: _selectedBranchId,
      );

      if (data != null && data.isNotEmpty) {
        setState(() {
          _reportData = data;
          _groupData();
          // Extract combinations from loaded data
          _extractCombinationsFromData(data);
        });
        _showSnackBar(T('تم تحميل التقرير بنجاح'));
      } else {
        setState(() {
          _reportData = [];
          _groupedData.clear();
          _combinationsComboData.clear();
          _selectedCombination = null;
        });
        _showSnackBar(T('لا توجد بيانات للمنتج المحدد'), isError: false);
      }
    } catch (e) {
      _showSnackBar(T('حدث خطأ أثناء تحميل التقرير'), isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _extractCombinationsFromData(List<InventoryAttributesReportDTO> data) {
    // Get unique combinations from the data
    Set<String> uniqueCombinations = {};
    for (var item in data) {
      if (item.attribute != null && item.attribute!.isNotEmpty) {
        uniqueCombinations.add(item.attribute!);
      }
    }

    // Convert to ComboBoxDataModel list
    _combinationsComboData = uniqueCombinations
        .map((combination) => ComboBoxDataModel(
              id: combination.hashCode, // Use hashCode as unique id
              name: combination,
            ))
        .toList();

    // Sort alphabetically
    _combinationsComboData.sort((a, b) => a.name.compareTo(b.name));
  }

  void _groupData() {
    _groupedData.clear();
    _branchNames.clear();

    if (_reportData != null) {
      // Filter data based on selected combination
      List<InventoryAttributesReportDTO> filteredData = _reportData!;

      if (_selectedCombination != null && _selectedCombination!.isNotEmpty) {
        filteredData = _reportData!
            .where((item) => item.attribute == _selectedCombination)
            .toList();
      }

      for (var item in filteredData) {
        final key = item.attribute;
        if (!_groupedData.containsKey(key)) {
          _groupedData[key] = [];
        }
        _groupedData[key]!.add(item);

        // Store branch names (you might want to fetch actual branch names from API)
        _branchNames[item.branchId] = 'فرع ${item.branchId}';
      }
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            fontSize: 14 * fontSize,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor:
            isError ? const Color(0xFFDC2626) : const Color(0xFF059669),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        margin: EdgeInsets.all(contentPadding),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF1F5F9),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: Column(
            children: [
              _buildModernAppBar(),
              Expanded(
                child: _isLoading ? _buildLoadingWidget() : _buildMainContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF1E3A8A), Color(0xFF3B82F6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding:
              EdgeInsets.symmetric(horizontal: contentPadding, vertical: 12),
          child: Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  iconSize: isDesktop ? 24 : 20,
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
              ),
              SizedBox(width: contentPadding * 0.7),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      T('تقرير خصائص المخزون'),
                      style: TextStyle(
                        fontSize: (20 * fontSize).clamp(16, 24),
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    if (!isMobile) ...[
                      const SizedBox(height: 4),
                      Text(
                        T('تقرير مفصل للخصائص والحركات'),
                        style: TextStyle(
                          fontSize: (14 * fontSize).clamp(12, 16),
                          color: Colors.white.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: IconButton(
                  iconSize: isDesktop ? 24 : 20,
                  icon: const Icon(Icons.refresh, color: Colors.white),
                  onPressed: _selectedProduct != null ? _loadReport : null,
                  tooltip: T('تحديث'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (isDesktop) {
          return _buildDesktopLayout();
        } else {
          return _buildMobileLayout();
        }
      },
    );
  }

  Widget _buildDesktopLayout() {
    return SingleChildScrollView(
      child: Column(
        // crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Left Panel - Product Selection
          Container(
            margin: EdgeInsets.all(contentPadding),
            child: Column(
              children: [
                _buildProductSelector(),
                if (_reportData != null && _reportData!.isNotEmpty) ...[
                  const SizedBox(height: 24),
                  _buildSummaryCards(),
                ],
              ],
            ),
          ),
          // Right Panel - Report Content
          Container(
            margin: EdgeInsets.only(
              top: contentPadding,
              right: contentPadding,
              bottom: contentPadding,
            ),
            child: _buildReportContentArea(),
          ),
        ],
      ),
    );
  }

  Widget _buildMobileLayout() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(contentPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildProductSelector(),
          const SizedBox(height: 24),
          if (_reportData != null) ...[
            if (_reportData!.isNotEmpty) ...[
              _buildSummaryCards(),
              const SizedBox(height: 24),
              _buildReportContent(),
            ] else
              _buildEmptyState(),
          ],
        ],
      ),
    );
  }

  Widget _buildReportContentArea() {
    if (_reportData == null) {
      return _buildWelcomeState();
    } else if (_reportData!.isEmpty) {
      return _buildEmptyState();
    } else {
      return _buildReportContent();
    }
  }

  Widget _buildWelcomeState() {
    return Center(
      child: Container(
        padding: EdgeInsets.all(cardPadding * 2),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(cardPadding),
              decoration: BoxDecoration(
                color: const Color(0xFF3B82F6).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.analytics,
                size: isDesktop ? 64 : 48,
                color: const Color(0xFF3B82F6),
              ),
            ),
            SizedBox(height: cardPadding),
            Text(
              T('اختر منتجاً لعرض التقرير'),
              style: TextStyle(
                fontSize: (18 * fontSize).clamp(16, 22),
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            SizedBox(height: cardPadding * 0.5),
            Text(
              T('قم باختيار منتج من القائمة على اليمين لعرض تقرير خصائص المخزون'),
              style: TextStyle(
                fontSize: (14 * fontSize).clamp(12, 16),
                color: const Color(0xFF6B7280),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductSelector() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(cardPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.search,
                    color: const Color(0xFF3B82F6),
                    size: isDesktop ? 20 : 18,
                  ),
                ),
                SizedBox(width: cardPadding * 0.6),
                Text(
                  T('اختيار المنتج'),
                  style: TextStyle(
                    fontSize: (18 * fontSize).clamp(16, 20),
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF1F2937),
                  ),
                ),
              ],
            ),
            // SizedBox(height: cardPadding * 0.8),
            _buildProductDropdown(),
            if (_selectedProduct != null) ...[
              _buildFiltersSection(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildProductDropdown() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('البحث واختيار المنتج'),
          style: TextStyle(
            fontSize: (14 * fontSize).clamp(12, 16),
            fontWeight: FontWeight.w500,
            color: const Color(0xFF6B7280),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFD1D5DB)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            onTap: _isLoadingProducts ? null : _showProductSelectionDialog,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: EdgeInsets.all(cardPadding * 0.8),
              child: Row(
                children: [
                  Icon(
                    Icons.inventory_2,
                    color: _selectedProduct != null
                        ? const Color(0xFF3B82F6)
                        : const Color(0xFF9CA3AF),
                    size: isDesktop ? 20 : 18,
                  ),
                  SizedBox(width: cardPadding * 0.6),
                  Expanded(
                    child: Text(
                      _selectedProduct?.title ?? T('اختر منتج من القائمة'),
                      style: TextStyle(
                        fontSize: (16 * fontSize).clamp(14, 18),
                        color: _selectedProduct != null
                            ? const Color(0xFF1F2937)
                            : const Color(0xFF9CA3AF),
                        fontWeight: _selectedProduct != null
                            ? FontWeight.w500
                            : FontWeight.normal,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (_isLoadingProducts)
                    SizedBox(
                      width: isDesktop ? 20 : 18,
                      height: isDesktop ? 20 : 18,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(Color(0xFF3B82F6)),
                      ),
                    )
                  else
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_products.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: const Color(0xFF3B82F6).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              '${_products.length}',
                              style: const TextStyle(
                                fontSize: 10,
                                color: Color(0xFF3B82F6),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        const SizedBox(width: 4),
                        Icon(
                          Icons.arrow_drop_down,
                          color: const Color(0xFF9CA3AF),
                          size: isDesktop ? 24 : 20,
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFiltersSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Range Section
          _buildDateRangeSection(),

          SizedBox(height: cardPadding * 0.8),

          // Branch and Store Selection
          _buildBranchStoreSection(),

          SizedBox(height: cardPadding * 0.8),

          // Combination Filter (only show if data is loaded)
          if (_combinationsComboData.isNotEmpty) ...[
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: const Color(0xFF10B981),
                  size: isDesktop ? 18 : 16,
                ),
                SizedBox(width: cardPadding * 0.3),
                Text(
                  T('فلترة حسب التركيبة'),
                  style: TextStyle(
                    fontSize: (14 * fontSize).clamp(12, 16),
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF374151),
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: const Color(0xFF10B981).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_combinationsComboData.length}',
                    style: TextStyle(
                      fontSize: (10 * fontSize).clamp(8, 12),
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF10B981),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: cardPadding * 0.5),
            _buildCombinationDropdown(),
            SizedBox(height: cardPadding * 0.8),
          ],

          const SizedBox(height: 15),
          SizedBox(
            width: context.width - 40, // set your desired width
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _loadReport,
              icon: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(Icons.analytics, size: isDesktop ? 18 : 16),
              label: Text(
                _isLoading ? T('تحميل...') : T('إنشاء التقرير'),
                style: TextStyle(
                  fontSize: (14 * fontSize).clamp(12, 16),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3B82F6),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: cardPadding * 0.8,
                  vertical: cardPadding * 0.6,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: T('من تاريخ'),
                date: _fromDate,
                onTap: () => _selectDate(true),
              ),
            ),
            SizedBox(width: cardPadding * 0.5),
            Expanded(
              child: _buildDateField(
                label: T('إلى تاريخ'),
                date: _toDate,
                onTap: () => _selectDate(false),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: (12 * fontSize).clamp(10, 14),
            fontWeight: FontWeight.w500,
            color: const Color(0xFF6B7280),
          ),
        ),
        const SizedBox(height: 6),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: EdgeInsets.all(cardPadding * 0.6),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFD1D5DB)),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: isDesktop ? 16 : 14,
                  color: const Color(0xFF6B7280),
                ),
                SizedBox(width: cardPadding * 0.4),
                Expanded(
                  child: Text(
                    date != null
                        ? DateFormat('yyyy-MM-dd').format(date)
                        : T('اختر التاريخ'),
                    style: TextStyle(
                      fontSize: (14 * fontSize).clamp(12, 16),
                      color: date != null
                          ? const Color(0xFF1F2937)
                          : const Color(0xFF9CA3AF),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildBranchStoreSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isMobile) ...[
          // Mobile: Stack vertically
          _buildBranchDropdown(),
          SizedBox(height: cardPadding * 0.5),
          _buildStoreDropdown(),
        ] else ...[
          // Desktop/Tablet: Side by side
          Row(
            children: [
              Expanded(
                child: _buildBranchDropdown(),
              ),
              SizedBox(width: cardPadding * 0.5),
              Expanded(
                child: _buildStoreDropdown(),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildBranchDropdown() {
    final selectedBranchName = _branchesComboData.isNotEmpty
        ? _branchesComboData
            .firstWhere(
              (branch) => branch.id == _selectedBranchId,
              orElse: () => ComboBoxDataModel(id: 0, name: T('كافة الفروع')),
            )
            .name
        : T('كافة الفروع');

    return MyComboBox(
      caption: selectedBranchName,
      labelText: T('الفرع'),
      modalTitle: T('اختيار الفرع'),
      selectedValue: _selectedBranchId,
      data: [
        ComboBoxDataModel(id: 0, name: T('كافة الفروع')),
        ..._branchesComboData,
      ],
      onSelect: (id, name) {
        setState(() {
          _selectedBranchId = id == 0 ? null : id;
        });
      },
      onRefresh: () {
        _loadBranchesAndStores();
      },
      height: cardPadding * 2.5,
      fontSize: (14 * fontSize).clamp(12, 16),
    );
  }

  Widget _buildStoreDropdown() {
    final selectedStoreName = _storesComboData.isNotEmpty
        ? _storesComboData
            .firstWhere(
              (store) => store.id == _selectedStoreId,
              orElse: () => ComboBoxDataModel(id: 0, name: T('كافة المخازن')),
            )
            .name
        : T('كافة المخازن');

    return MyComboBox(
      caption: selectedStoreName,
      labelText: T('المخزن'),
      modalTitle: T('اختيار المخزن'),
      selectedValue: _selectedStoreId,
      data: [
        ComboBoxDataModel(id: 0, name: T('كافة المخازن')),
        ..._storesComboData,
      ],
      onSelect: (id, name) {
        setState(() {
          _selectedStoreId = id == 0 ? null : id;
        });
      },
      onRefresh: () {
        _loadBranchesAndStores();
      },
      height: cardPadding * 2.5,
      fontSize: (14 * fontSize).clamp(12, 16),
    );
  }

  Future<void> _selectDate(bool isFromDate) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: isFromDate
          ? (_fromDate ?? DateTime.now())
          : (_toDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF3B82F6),
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Color(0xFF1F2937),
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = pickedDate;
          // Ensure toDate is not before fromDate
          if (_toDate != null && _toDate!.isBefore(_fromDate!)) {
            _toDate = _fromDate;
          }
        } else {
          _toDate = pickedDate;
          // Ensure fromDate is not after toDate
          if (_fromDate != null && _fromDate!.isAfter(_toDate!)) {
            _fromDate = _toDate;
          }
        }
      });
    }
  }

  void _showProductSelectionDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => StatefulBuilder(
        builder: (context, setModalState) => Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: Column(
            children: [
              // Header
              Container(
                padding: EdgeInsets.all(cardPadding),
                decoration: const BoxDecoration(
                  color: Color(0xFFF8FAFC),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: const Color(0xFFD1D5DB),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    SizedBox(height: cardPadding * 0.8),
                    Row(
                      children: [
                        Icon(
                          Icons.inventory_2,
                          color: const Color(0xFF3B82F6),
                          size: isDesktop ? 24 : 20,
                        ),
                        SizedBox(width: cardPadding * 0.6),
                        Expanded(
                          child: Text(
                            T('اختيار المنتج'),
                            style: TextStyle(
                              fontSize: (18 * fontSize).clamp(16, 20),
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF1F2937),
                            ),
                          ),
                        ),
                        IconButton(
                          onPressed: () {
                            _productSearchController.clear();
                            Navigator.pop(context);
                          },
                          icon: const Icon(Icons.close),
                          style: IconButton.styleFrom(
                            backgroundColor: const Color(0xFFE5E7EB),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: cardPadding * 0.8),
                    // Search Field
                    TextField(
                      controller: _productSearchController,
                      decoration: InputDecoration(
                        hintText: T('البحث عن المنتج...'),
                        hintStyle:
                            TextStyle(fontSize: (14 * fontSize).clamp(12, 16)),
                        prefixIcon: Icon(
                          Icons.search,
                          color: const Color(0xFF9CA3AF),
                          size: isDesktop ? 20 : 18,
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide:
                              const BorderSide(color: Color(0xFFD1D5DB)),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide:
                              const BorderSide(color: Color(0xFFD1D5DB)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: const BorderSide(
                              color: Color(0xFF3B82F6), width: 2),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: EdgeInsets.all(cardPadding * 0.8),
                      ),
                      onChanged: (value) {
                        _filterProducts(value);
                        setModalState(() {});
                      },
                    ),
                  ],
                ),
              ),
              // Products List
              Expanded(
                child: _filteredProducts.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.search_off,
                              size: 48,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _productSearchController.text.isNotEmpty
                                  ? T('لا توجد منتجات تطابق البحث')
                                  : T('لا توجد منتجات متاحة'),
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                            if (_productSearchController.text.isNotEmpty) ...[
                              const SizedBox(height: 8),
                              Text(
                                T('جرب البحث بكلمات مختلفة'),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ],
                        ),
                      )
                    : ListView.builder(
                        padding: EdgeInsets.all(cardPadding),
                        itemCount: _filteredProducts.length,
                        itemBuilder: (context, index) {
                          final product = _filteredProducts[index];
                          final isSelected = _selectedProduct?.id == product.id;

                          return Container(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: InkWell(
                              onTap: () {
                                setState(() {
                                  _selectedProduct = product;
                                });
                                Navigator.pop(context);
                              },
                              borderRadius: BorderRadius.circular(12),
                              child: Container(
                                padding: EdgeInsets.all(cardPadding * 0.8),
                                decoration: BoxDecoration(
                                  color: isSelected
                                      ? const Color(0xFF3B82F6).withOpacity(0.1)
                                      : Colors.white,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: isSelected
                                        ? const Color(0xFF3B82F6)
                                        : const Color(0xFFE5E7EB),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Container(
                                      width: isDesktop ? 48 : 40,
                                      height: isDesktop ? 48 : 40,
                                      decoration: BoxDecoration(
                                        color: const Color(0xFFF3F4F6),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Icon(
                                        Icons.inventory_2,
                                        color: isSelected
                                            ? const Color(0xFF3B82F6)
                                            : const Color(0xFF9CA3AF),
                                        size: isDesktop ? 24 : 20,
                                      ),
                                    ),
                                    SizedBox(width: cardPadding * 0.6),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            product.title ?? '',
                                            style: TextStyle(
                                              fontSize:
                                                  (16 * fontSize).clamp(14, 18),
                                              fontWeight: FontWeight.w600,
                                              color: isSelected
                                                  ? const Color(0xFF3B82F6)
                                                  : const Color(0xFF1F2937),
                                            ),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          if (product.code != null) ...[
                                            const SizedBox(height: 4),
                                            Text(
                                              '${T('الكود')}: ${product.code}',
                                              style: TextStyle(
                                                fontSize: (14 * fontSize)
                                                    .clamp(12, 16),
                                                color: const Color(0xFF6B7280),
                                              ),
                                            ),
                                          ],
                                        ],
                                      ),
                                    ),
                                    if (isSelected)
                                      Container(
                                        padding: const EdgeInsets.all(4),
                                        decoration: const BoxDecoration(
                                          color: Color(0xFF3B82F6),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: isDesktop ? 16 : 14,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(cardPadding),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                SizedBox(
                  width: isDesktop ? 40 : 32,
                  height: isDesktop ? 40 : 32,
                  child: const CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF3B82F6)),
                  ),
                ),
                SizedBox(height: cardPadding * 0.8),
                Text(
                  T('جاري تحميل التقرير...'),
                  style: TextStyle(
                    fontSize: (16 * fontSize).clamp(14, 18),
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Container(
        padding: EdgeInsets.all(cardPadding * 1.6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(cardPadding),
              decoration: BoxDecoration(
                color: const Color(0xFFF3F4F6),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.inventory_2_outlined,
                size: isDesktop ? 48 : 40,
                color: const Color(0xFF9CA3AF),
              ),
            ),
            SizedBox(height: cardPadding),
            Text(
              T('لا توجد بيانات للعرض'),
              style: TextStyle(
                fontSize: (18 * fontSize).clamp(16, 20),
                fontWeight: FontWeight.w600,
                color: const Color(0xFF1F2937),
              ),
            ),
            SizedBox(height: cardPadding * 0.4),
            Text(
              T('لا توجد حركات مخزون لهذا المنتج'),
              style: TextStyle(
                fontSize: (14 * fontSize).clamp(12, 16),
                color: const Color(0xFF6B7280),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalItems = _reportData
        ?.map((item) => item.balance)
        .toList()
        .fold(0.0, (sum, quantity) => (sum + quantity));
    final totalAttributes = _groupedData.keys.length;
    final totalBranches = _branchNames.keys.length;

    // Responsive grid columns
    List<Widget> summaryCards = [
      _buildSummaryCard(
        context: context,
        title: T('إجمالي العدد'),
        value: totalItems.toString(),
        icon: Icons.list_alt,
        gradient: const LinearGradient(
          colors: [Color(0xFF3B82F6), Color(0xFF1E40AF)],
        ),
      ),
      _buildSummaryCard(
        context: context,
        title: T('عدد الخصائص'),
        value: totalAttributes.toString(),
        icon: Icons.category,
        gradient: const LinearGradient(
          colors: [Color(0xFF10B981), Color(0xFF047857)],
        ),
      ),
      _buildSummaryCard(
        context: context,
        title: T('عدد الفروع'),
        value: totalBranches.toString(),
        icon: Icons.store,
        gradient: const LinearGradient(
          colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
        ),
      ),
    ];

    // Add combination filter card if active
    if (_selectedCombination != null && _selectedCombination!.isNotEmpty) {
      summaryCards.add(
        _buildSummaryCard(
          context: context,
          title: T('فلتر التركيبة'),
          value: _selectedCombination!,
          icon: Icons.tune,
          gradient: const LinearGradient(
            colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: summaryCards,
    );
  }

  Widget _buildSummaryCard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required LinearGradient gradient,
  }) {
    return Container(
      padding: EdgeInsets.all(cardPadding),
      width: context.width / 3 - 40,
      margin: const EdgeInsets.symmetric(horizontal: 10),
      decoration: BoxDecoration(
        gradient: gradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: gradient.colors.first.withOpacity(0.3),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: isMobile
          ?
          // Mobile: Horizontal layout
          Row(
              children: [
                Icon(icon, color: Colors.white, size: 24),
                SizedBox(width: cardPadding * 0.8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        value,
                        style: TextStyle(
                          fontSize: (20 * fontSize).clamp(18, 24),
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: (12 * fontSize).clamp(10, 14),
                          color: Colors.white.withOpacity(0.9),
                          fontWeight: FontWeight.w500,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            )
          :
          // Desktop/Tablet: Vertical layout
          Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, color: Colors.white, size: isDesktop ? 28 : 24),
                SizedBox(height: cardPadding * 0.6),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: (24 * fontSize).clamp(20, 28),
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                SizedBox(height: cardPadding * 0.2),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: (12 * fontSize).clamp(10, 14),
                    color: Colors.white.withOpacity(0.9),
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
    );
  }

  Widget _buildReportContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: _groupedData.keys
          .map((attribute) => _buildAttributeSection(attribute))
          .toList(),
    );
  }

  Widget _buildAttributeSection(String attribute) {
    final items = _groupedData[attribute]!;

    return Container(
      margin: EdgeInsets.only(bottom: cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(cardPadding),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF3B82F6).withOpacity(0.1),
                  const Color(0xFF1E40AF).withOpacity(0.05),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.category,
                    color: Colors.white,
                    size: isDesktop ? 20 : 18,
                  ),
                ),
                SizedBox(width: cardPadding * 0.6),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${T('الخاصية')}: $attribute',
                        style: TextStyle(
                          fontSize: (18 * fontSize).clamp(16, 20),
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF1F2937),
                        ),
                      ),
                      Text(
                        '${items.length} ${T('فرع')}',
                        style: TextStyle(
                          fontSize: (14 * fontSize).clamp(12, 16),
                          color: const Color(0xFF6B7280),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: cardPadding * 0.6,
                      vertical: cardPadding * 0.3),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3B82F6),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${items.length}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: (14 * fontSize).clamp(12, 16),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.all(cardPadding * 0.8),
            child: Column(
              children: items.map((item) => _buildItemCard(item)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemCard(InventoryAttributesReportDTO item) {
    final numberFormat = NumberFormat('#,##0.00');

    return Container(
      margin: EdgeInsets.only(bottom: cardPadding * 0.8),
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFFF8FAFC),
            Color(0xFFF1F5F9),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6B7280).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.store,
                  color: const Color(0xFF6B7280),
                  size: isDesktop ? 18 : 16,
                ),
              ),
              SizedBox(width: cardPadding * 0.6),
              Expanded(
                child: Text(
                  'الفرع: ${Provider.of<BranchController>(context).branches.firstWhere((element) => element.id == item.branchId).name}',
                  style: TextStyle(
                    fontSize: (16 * fontSize).clamp(14, 18),
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF374151),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                    horizontal: cardPadding * 0.6, vertical: cardPadding * 0.3),
                decoration: BoxDecoration(
                  color: item.balance >= 0
                      ? const Color(0xFF059669)
                      : const Color(0xFFDC2626),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      item.balance >= 0
                          ? Icons.trending_up
                          : Icons.trending_down,
                      color: Colors.white,
                      size: isDesktop ? 16 : 14,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      isMobile
                          ? numberFormat.format(item.balance)
                          : '${T('الرصيد')}: ${numberFormat.format(item.balance)}',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: (12 * fontSize).clamp(10, 14),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: cardPadding),
          _buildDataGrid(item, numberFormat),
        ],
      ),
    );
  }

  Widget _buildDataGrid(
      InventoryAttributesReportDTO item, NumberFormat numberFormat) {
    final List<Map<String, dynamic>> data = [
      {
        'label': T('الافتتاحي'),
        'value': item.opening,
        'icon': Icons.play_arrow,
        'color': const Color(0xFF6366F1)
      },
      {
        'label': T('المشتريات'),
        'value': item.purchases,
        'icon': Icons.shopping_cart,
        'color': const Color(0xFF059669)
      },
      {
        'label': T('المبيعات'),
        'value': item.sales,
        'icon': Icons.sell,
        'color': const Color(0xFFDC2626)
      },
      {
        'label': T('اذونات الصرف'),
        'value': item.deliveryNote,
        'icon': Icons.local_shipping,
        'color': const Color(0xFF7C3AED)
      },
      {
        'label': T('مرتجع مبيعات'),
        'value': item.salesReturns,
        'icon': Icons.keyboard_return,
        'color': const Color(0xFF059669)
      },
      {
        'label': T('مرتجع مشتريات'),
        'value': item.purchasesReturns,
        'icon': Icons.undo,
        'color': const Color(0xFFDC2626)
      },
      {
        'label': T('التالف'),
        'value': item.damaged,
        'icon': Icons.warning,
        'color': const Color(0xFFEA580C)
      },
      // {
      //   'label': T('صادرة'),
      //   'value': item.outgoingItems,
      //   'icon': Icons.logout,
      //   'color': const Color(0xFFDC2626)
      // },
      {
        'label': T('الصادرات'),
        'value': item.outgoing,
        'icon': Icons.output,
        'color': const Color(0xFFDC2626)
      },
      {
        'label': T('الواردات'),
        'value': item.incoming,
        'icon': Icons.input,
        'color': const Color(0xFF059669)
      },
      {
        'label': T('تحويل'),
        'value': item.itemsTransfer,
        'icon': Icons.transform,
        'color': const Color(0xFF0891B2)
      },

      {
        'label': T('النقص'),
        'value': item.shortage,
        'icon': Icons.remove,
        'color': const Color(0xFFEA580C)
      },
      {
        'label': T('الفائض'),
        'value': item.surplus,
        'icon': Icons.add,
        'color': const Color(0xFF059669)
      },
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: data.map((dataItem) {
          final value = dataItem['value'] as double;
          final color = dataItem['color'] as Color;

          return Container(
            width: isDesktop ? 100 : (isTablet ? 85 : 75),
            margin: EdgeInsets.only(right: cardPadding * 0.4),
            padding: EdgeInsets.all(cardPadding * 0.4),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: color.withOpacity(0.2)),
              boxShadow: [
                BoxShadow(
                  color: color.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: EdgeInsets.all(cardPadding * 0.2),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    dataItem['icon'],
                    size: isDesktop ? 14 : 12,
                    color: color,
                  ),
                ),
                SizedBox(height: cardPadding * 0.25),
                Text(
                  dataItem['label'],
                  style: TextStyle(
                    fontSize: (9 * fontSize).clamp(8, 11),
                    color: const Color(0xFF6B7280),
                    fontWeight: FontWeight.w500,
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                ),
                SizedBox(height: cardPadding * 0.15),
                Text(
                  numberFormat.format(value),
                  style: TextStyle(
                    fontSize: (11 * fontSize).clamp(9, 13),
                    fontWeight: FontWeight.bold,
                    color: value == 0 ? const Color(0xFF9CA3AF) : color,
                  ),
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }
}
