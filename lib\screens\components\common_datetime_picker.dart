import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// ignore: depend_on_referenced_packages
import 'package:intl/intl.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/theme/app_theme_light.dart';

class MyDateTimePicker extends StatefulWidget {
  const MyDateTimePicker(
      {Key? key,
      required this.initialVal,
      required this.onSave,
      required this.caption,
      required this.backColor,
      this.fontSize,
      this.onChange})
      : super(key: key);
  final DateTime initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final double? fontSize;
  final String caption;
  @override
  // ignore: library_private_types_in_public_api
  _MyDateTimePickerState createState() => _MyDateTimePickerState();
}

class _MyDateTimePickerState extends State<MyDateTimePicker> {
  // DateTime selectedDate =widget.initialVal;
  TextEditingController _dateController = TextEditingController();

  @override
  void didChangeDependencies() {
    _dateController = TextEditingController(
        text: DateFormat('yyyy-MM-dd hh:mm a', 'en').format(widget.initialVal));
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _selectDate(context);
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5),
            color: widget.backColor,
            border: const Border(
              bottom: BorderSide(color: Colors.black, width: 2),
            )),
        padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 8),
        child: Text(
          widget.caption,
          style: TextStyle(fontSize: widget.fontSize ?? 14),
        ),
      ),
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .35,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        // Provide a background color for the popup.
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: SafeArea(
          top: false,
          child: child,
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    if (Platform.isIOS) {
      // IOS
      _showDialog(CupertinoDatePicker(
        initialDateTime: DateTime.now().add(const Duration(days: 2)),
        mode: CupertinoDatePickerMode.date,
        onDateTimeChanged: (value) {
          setState(() {
            _dateController.text =
                DateFormat('yyyy-MM-dd hh:mm a', 'en').format(value);
          });
          if (widget.onChange != null) widget.onChange!(value);
        },
      ));
    } else {
      // Android and Windows use Material Design
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(_dateController.text),
        initialDatePickerMode: DatePickerMode.day,
        firstDate: DateTime(2015),
        lastDate: DateTime(2101),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ColorScheme.light(
                primary: context.primaryColor, // <-- SEE HERE
                onPrimary: Colors.white, // <-- SEE HERE
                onSurface: Colors.black, // <-- SEE HERE
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  foregroundColor: context.primaryColor, // button text color
                ),
              ),
            ),
            child: child!,
          );
        },
      );
      if (picked != null) {
        setState(() {
          _dateController.text =
              DateFormat('yyyy-MM-dd hh:mm a', 'en').format(picked);
        });
      }
      if (widget.onChange != null) widget.onChange!(picked);
    }
  }
}

//================//================//================//================//================
//================//================//================//================//================

class MyDatePicker extends StatefulWidget {
  const MyDatePicker(
      {Key? key,
      required this.initialVal,
      required this.backColor,
      required this.onSave,
      required this.caption,
      this.fontSize,
      this.isDisabled,
      this.onChange,
      this.showLabel,
      this.width})
      : super(key: key);
  final DateTime initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final String caption;
  final double? fontSize;
  final bool? isDisabled;
  final double? width;
  final bool? showLabel;
  @override
  _MyDatePickerState createState() => _MyDatePickerState();
}

class _MyDatePickerState extends State<MyDatePicker> {
  TextEditingController _dateController = TextEditingController();
  String selectedDateText = "الرجاء اختيار تاريخ";

  @override
  void didChangeDependencies() {
    _dateController = TextEditingController(
        text: DateFormat('yyyy-MM-dd', 'en').format(widget.initialVal));
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.isDisabled == true) {
          return;
        }
        _selectDate(context);
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          widget.showLabel == false
              ? const SizedBox.shrink()
              : Container(
                  alignment: Alignment.center,
                  width: widget.width ?? context.width / 4,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(
                        color: AppThemeLight.instance.theme.colorScheme.primary,
                        width: 1.2,
                      )),
                  child: Text(
                    _dateController.text,
                    style: TextStyle(
                      color: AppThemeLight.instance.theme.colorScheme.primary,
                    ),
                  ),
                ),
        ],
      ),
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .35,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: SafeArea(
          top: false,
          child: child,
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    if (Platform.isIOS) {
      // IOS
      _showDialog(CupertinoDatePicker(
        initialDateTime: DateTime.now().add(const Duration(days: 2)),
        mode: CupertinoDatePickerMode.date,
        onDateTimeChanged: (value) {
          setState(() {
            selectedDateText = DateFormat('yyyy-MM-dd', 'en').format(value);
            _dateController.text = selectedDateText;
          });
          if (widget.onChange != null) widget.onChange!(value);
        },
      ));
    } else {
      // Android and Windows use Material Design
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate: DateTime.parse(_dateController.text),
        initialDatePickerMode: DatePickerMode.day,
        firstDate: DateTime(2015),
        lastDate: DateTime(2101),
        builder: (context, child) {
          return Theme(
            data: Theme.of(context).copyWith(
              colorScheme: ColorScheme.light(
                primary: context.primaryColor, // <-- SEE HERE
                onPrimary: Colors.white, // <-- SEE HERE
                onSurface: Colors.black, // <-- SEE HERE
              ),
              textButtonTheme: TextButtonThemeData(
                style: TextButton.styleFrom(
                  foregroundColor: context.primaryColor, // button text color
                ),
              ),
            ),
            child: child!,
          );
        },
      );
      if (picked != null) {
        setState(() {
          selectedDateText = DateFormat('yyyy-MM-dd', 'en').format(picked);
          _dateController.text = selectedDateText;
        });
      }
      if (widget.onChange != null) widget.onChange!(picked);
    }
  }
}

class MyTimePicker extends StatefulWidget {
  const MyTimePicker(
      {Key? key,
      required this.initialVal,
      required this.backColor,
      required this.onSave,
      this.isDisabled,
      required this.caption,
      this.fontSize,
      this.onChange})
      : super(key: key);
  final TimeOfDay initialVal;
  final Function onSave;
  final Function? onChange;
  final Color backColor;
  final String caption;
  final double? fontSize;
  final bool? isDisabled;

  @override
  State<MyTimePicker> createState() => _MyTimePickerState();
}

class _MyTimePickerState extends State<MyTimePicker> {
  TextEditingController _dateController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.isDisabled == true) {
          return;
        }
        _selectTime(context);
      },
      child: Container(
        width: context.width - 20,
        decoration: BoxDecoration(
          color: widget.backColor,
          border: const Border(
            bottom: BorderSide(
              width: 1,
              color: Colors.black,
            ),
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 15),
        child: Text(
          widget.caption,
          textAlign: TextAlign.center,
          softWrap: true,
          overflow: TextOverflow.ellipsis,
          style: TextStyle(
            fontSize: widget.fontSize ?? 16,
            fontWeight: FontWeight.bold,
            color: context.primaryColor,
          ),
        ),
      ),
    );
  }

  void _showDialog(Widget child) {
    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) => Container(
        height: MediaQuery.of(context).size.height * .35,
        padding: const EdgeInsets.only(top: 6.0),
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        // Provide a background color for the popup.
        color: CupertinoColors.systemBackground.resolveFrom(context),
        child: SafeArea(
          top: false,
          child: child,
        ),
      ),
    );
  }

  Future<void> _selectTime(BuildContext context) async {
    DateTime minDate = DateTime.now().add(const Duration(hours: 23));
    DateTime maxDate = DateTime.now().add(const Duration(days: 60));
    if (Platform.isIOS) {
      // IOS
      _showDialog(CupertinoDatePicker(
        initialDateTime: DateTime.now().add(const Duration(days: 2)),
        mode: CupertinoDatePickerMode.time,
        use24hFormat: true,
        minimumDate: minDate,
        maximumDate: maxDate,
        onDateTimeChanged: (value) {
          setState(() {
            TimeOfDay picked =
                TimeOfDay(hour: value.hour, minute: value.minute);
            _dateController.text = picked.format(context);
            widget.onSave(picked);
          });
          if (widget.onChange != null) widget.onChange!(value);
        },
      ));
    } else {
      // Android and Windows use Material Design
      final TimeOfDay? picked = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.now(),
      );
      if (picked != null) {
        setState(() {
          _dateController.text = picked.format(context);
          widget.onSave(picked);
        });
      }
      if (widget.onChange != null) widget.onChange!(DateTime.now());
    }
  }
}
