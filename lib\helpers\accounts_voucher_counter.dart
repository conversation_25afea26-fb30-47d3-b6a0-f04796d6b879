import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/models/dto/counter/device_counter_dto.dart';

enum AccountsVoucharType {
  ReceiptVoucher(1), // سند قبض
  PaymentVoucher(2), // سند دفع
  SalesInvoiceReceiptVouchar(3), // سند قبض فاتورة مبيعات
  GeneralVouchar(4), // سند عام
  CreditNote(5); // مذكرة ائتمان

  final int value;
  const AccountsVoucharType(this.value);
}

class AccountsVoucherCounterGenerator {
  //---------------------------------------------------------
  static Future<int> getCounterByType(
      {required AccountsVoucharType type}) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());

    return currentCounter;
  }

  //------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> setCounterByTypeAuto(
      {required AccountsVoucharType type}) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());
    currentCounter += 1;

    await LocaleManager.instance
        .setStringValueByStringKey(type.toString(), currentCounter.toString());
  }

  //---------------------------------------------------------
  static Future<bool> setCounterByType(
      {required AccountsVoucharType type, required int counter}) async {
    if (counter == 0) {
      initializeCounterByType(type);
    }
    var oldCounter = await getCounterByType(type: type);
    if (oldCounter > counter) {
      await setAccountsVoucherCounterInServer();
      return false;
    }
    counter = counter++;
    LocaleManager.instance.removeKeyByStringKey(type.toString());
    await LocaleManager.instance
        .setStringValueByStringKey(type.toString(), counter.toString());

    return true;
  }

  //------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> getAccountsVoucherCounterFromServer() async {
    try {
      var types = [
        AccountsVoucharType.ReceiptVoucher,
        AccountsVoucharType.PaymentVoucher,
        AccountsVoucharType.SalesInvoiceReceiptVouchar,
        AccountsVoucharType.GeneralVouchar,
        AccountsVoucharType.CreditNote,
      ];

      String deviceId = LocaleManager.instance
          .getStringValue(PreferencesKeys.DeviceCode)
          .toString();
      for (var type in types) {
        var model = ApiDeviceCounterDTO(
          counterType: type.index + 1, // +1 because enum starts from 1 in C#
          deviceCode: deviceId,
          referenceType: ApiDeviceCounterTypeEnum.vouchars,
        );
        var url = '/Counter/GetCodeByTypes';
        var result = await Api.getWithBody(action: url, body: model.toJson());
        if (result?.isSuccess != null && result?.isSuccess == true) {
          setCounterByType(type: type, counter: result?.data ?? 0);
        } else {
          await initializeCounterByType(type);
        }
      }
    } catch (e) {
      print('Error fetching accounts voucher counter from server: $e');
    }
  }

//------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> setAccountsVoucherCounterInServer() async {
    try {
      var types = [
        AccountsVoucharType.ReceiptVoucher,
        AccountsVoucharType.PaymentVoucher,
        AccountsVoucharType.SalesInvoiceReceiptVouchar,
        AccountsVoucharType.GeneralVouchar,
        AccountsVoucharType.CreditNote,
      ];
      String deviceId = LocaleManager.instance
          .getStringValue(PreferencesKeys.DeviceCode)
          .toString();
      for (var type in types) {
        var counter = await getCounterByType(type: type);
        var model = ApiDeviceCounterDTO(
          counter: counter,
          counterType: type.index + 1, // +1 because enum starts from 1 in C#
          deviceCode: deviceId,
          referenceType: ApiDeviceCounterTypeEnum.vouchars,
        );
        var url = '/Counter/SetCodeByTypes';
        await Api.post(action: url, body: model.toJson());
      }
    } catch (e) {
      print('Error setting accounts voucher counter in server: $e');
    }
  }

  //---------------------------------------------------------
  static Future<void> initializeCounterByType(AccountsVoucharType type) async {
    if (!await LocaleManager.instance.checkKeyByStringKey(type.toString())) {
      await LocaleManager.instance
          .setStringValueByStringKey(type.toString(), 1.toString());
      // Initial counter value
    }
  }

//--------------------------------------------------------------------------
  static Future<String> getCurrentCounterByType(
      AccountsVoucharType type) async {
    var formattedCounter = "1";
    var currentCounter = LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString();

    if (currentCounter.isNotEmpty) {
      formattedCounter = currentCounter.toString();
    }

    return formattedCounter;
  }

//--------------------------------------------------------------------------
  // Generates the next counter number in the format for each voucher type
  static Future<String> getNextCounterByType(AccountsVoucharType type) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());
    String deviceId = LocaleManager.instance
        .getStringValue(PreferencesKeys.DeviceCode)
        .toString();
    String formattedCounter = currentCounter.toString().padLeft(5, '0');
    String yearSuffix = DateTime.now().year.toString().substring(2);

    switch (type) {
      case AccountsVoucharType.ReceiptVoucher:
        return 'RV$yearSuffix-$deviceId-$formattedCounter'; // Receipt Voucher

      case AccountsVoucharType.PaymentVoucher:
        return 'PV$yearSuffix-$deviceId-$formattedCounter'; // Payment Voucher

      case AccountsVoucharType.SalesInvoiceReceiptVouchar:
        return 'SIR$yearSuffix-$deviceId-$formattedCounter'; // Sales Invoice Receipt

      case AccountsVoucharType.GeneralVouchar:
        return 'GV$yearSuffix-$deviceId-$formattedCounter'; // General Voucher

      case AccountsVoucharType.CreditNote:
        return 'CRN$yearSuffix-$deviceId-$formattedCounter'; // Credit Note
    }
  }

  //--------------------------------------------------------------------------
  // Get voucher type display name in Arabic
  static String getVoucherTypeDisplayName(AccountsVoucharType type) {
    switch (type) {
      case AccountsVoucharType.ReceiptVoucher:
        return 'سند قبض';
      case AccountsVoucharType.PaymentVoucher:
        return 'سند دفع';
      case AccountsVoucharType.SalesInvoiceReceiptVouchar:
        return 'سند قبض فاتورة مبيعات';
      case AccountsVoucharType.GeneralVouchar:
        return 'سند عام';
      case AccountsVoucharType.CreditNote:
        return 'مذكرة ائتمان';
    }
  }

  //--------------------------------------------------------------------------
  // Checks if a voucher counter has been used (for validation)
  static Future<bool> checkIfCounterUsed(
      AccountsVoucharType type, String code) async {
    try {
      final parts = code.split('-');
      if (parts.length != 3) return false;

      final counterString = parts.last; // e.g., "00023"
      final codeCounter = int.tryParse(counterString);
      if (codeCounter == null) return false;

      // Get current counter from preferences
      final currentCounterString = LocaleManager.instance
          .getStringValueByStringKey(type.toString())
          .toString();

      final currentCounter = int.tryParse(currentCounterString);
      if (currentCounter == null) return false;

      // Compare
      return codeCounter < currentCounter;
    } catch (e) {
      print("Error checking voucher counter: $e");
      return false;
    }
  }

  //--------------------------------------------------------------------------
  // Initialize all voucher types counters
  static Future<void> initializeAllCounters() async {
    for (var type in AccountsVoucharType.values) {
      await initializeCounterByType(type);
    }
  }

  //--------------------------------------------------------------------------
  // Get all counters status for debugging
  static Future<Map<AccountsVoucharType, int>> getAllCounters() async {
    Map<AccountsVoucharType, int> counters = {};
    for (var type in AccountsVoucharType.values) {
      try {
        counters[type] = await getCounterByType(type: type);
      } catch (e) {
        await initializeCounterByType(type);
        counters[type] = 1;
      }
    }
    return counters;
  }
}
