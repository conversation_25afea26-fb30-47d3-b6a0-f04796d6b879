enum SupplierSyncStatus { pending, syncing, synced }

class SupplierModel {
  String? code;
  String? name;
  String? nameEn;

  int? iD;
  int? localId;

  SupplierModel({
    this.code,
    this.name,
    this.nameEn,
    this.iD,
    this.localId,
  });

  SupplierModel.fromJson(Map<String, dynamic> json) {
    code = json['Code'];

    name = json['Name'];
    nameEn = json['Name_En'];

    iD = json['ID'];
    localId = json['local_Id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Code'] = code;

    data['Name'] = name;
    data['Name_En'] = nameEn;

    data['local_Id'] = localId;
    data['ID'] = iD;

    return data;
  }
}
