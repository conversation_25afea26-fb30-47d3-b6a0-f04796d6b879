import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/delivery_note_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/components/common_datetime_picker.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:provider/provider.dart';

// ignore: must_be_immutable
class CreateDeliveryNoteDialogWidget extends StatefulWidget {
  String? title;
  CreateDeliveryNoteDialogWidget({super.key, this.title});

  @override
  State<CreateDeliveryNoteDialogWidget> createState() =>
      _CreateDeliveryNoteDialogWidgetState();
}

class _CreateDeliveryNoteDialogWidgetState
    extends State<CreateDeliveryNoteDialogWidget> {
  var invoiceCodeController = TextEditingController(text: "");

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DeliveryNoteController>(context, listen: false)
          .getDeliveryNoteNumber();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final textProvider = Provider.of<DeliveryNoteController>(context).invoice;

    invoiceCodeController.text =
        (textProvider.appReferanceCode ?? "").toString();
  }

  @override
  Widget build(BuildContext context) {
    var data = Provider.of<DeliveryNoteController>(context).invoice;
    return WillPopScope(
      onWillPop: () async {
        if (data.customerId == null) {
          errorSnackBar(
              message:
                  T("You must select a customer to start the delivery note"));
          return false;
        }
        return true;
      },
      child: Dialog(
        backgroundColor: Colors.transparent,
        insetPadding: const EdgeInsets.symmetric(horizontal: 16),
        child: Container(
          width: double.infinity,
          constraints: BoxConstraints(
            maxWidth: context.width > 800 ? 800 : context.width - 32,
            maxHeight: context.height * 0.85,
          ),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: context.newSecondaryColor,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.local_shipping,
                      color: Colors.white,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        widget.title ?? T("Create Delivery Note"),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content area
              Flexible(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Date and invoice number section
                        _buildSectionContainer(
                          context,
                          title: T("Delivery Note Information"),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  // Date picker
                                  Expanded(
                                    child: MyDatePicker(
                                      width: double.infinity,
                                      onSave: (date) {
                                        data.invoiceDate = date;
                                      },
                                      initialVal:
                                          data.invoiceDate ?? DateTime.now(),
                                      caption: ''.myDateFormatter(
                                          data.invoiceDate ?? DateTime.now(),
                                          isShowTime: false),
                                      backColor: Colors.white,
                                    ),
                                  ),
                                  const SizedBox(width: 12),

                                  // Invoice number
                                  Expanded(
                                    child: CommonTextField(
                                      controller: invoiceCodeController,
                                      enabled: false,
                                      floatingLabelBehavior:
                                          FloatingLabelBehavior.never,
                                      label: T("Delivery Note Number"),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Customer section
                        _buildSectionContainer(
                          context,
                          title: T("Customer Information"),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              MyComboBox(
                                onRefresh: () {
                                  Provider.of<CustomerController>(context,
                                          listen: false)
                                      .fetchCustomersFromServer();
                                  Navigator.of(context).pop();
                                },
                                selectedValue: data.customerId,
                                caption:
                                    data.custoemrName ?? T("Customer Name"),
                                height: 50,
                                onSelect: (int id, String name) {
                                  data.customerId = id;
                                  data.custoemrName = name;
                                  setState(() {});
                                },
                                modalTitle: T("Customer Name"),
                                data: Provider.of<CustomerController>(context)
                                    .customers
                                    .map(
                                      (e) => ComboBoxDataModel(
                                          id: e.iD ?? 0, name: e.name ?? ""),
                                    )
                                    .toList(),
                                isShowLabel: false,
                                labelText: "",
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Warehouse section
                        _buildSectionContainer(
                          context,
                          title: T("Warehouse Information"),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              MyComboBox(
                                caption: data.warehouseName ?? T("Warehouse"),
                                height: 50,
                                selectedValue: data.warehouseId,
                                onSelect: (int id, String name) {
                                  data.warehouseId = id;
                                  data.warehouseName = name;
                                  setState(() {});
                                },
                                onRefresh: () {
                                  Provider.of<WarehouseController>(context,
                                          listen: false)
                                      .fetchWarehouses();
                                  Navigator.of(context).pop();
                                },
                                modalTitle: T("Warehouse"),
                                data: Provider.of<WarehouseController>(context)
                                    .warehouses,
                                isShowLabel: false,
                                labelText: "",
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(height: 16),

                        // Notes section
                        _buildSectionContainer(
                          context,
                          title: T("Additional Information"),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 10),
                              CommonTextField(
                                initialValue: data.note ?? "",
                                label: T("Notes"),
                                onChanged: (value) {
                                  data.note = value;
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Actions section
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: context.newBackgroundColor.withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                  border: Border(
                    top: BorderSide(
                      color: context.newPrimaryColor.withOpacity(0.1),
                    ),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: ElevatedButton.icon(
                        icon: const Icon(Icons.check_circle_outline, size: 18),
                        label: Text(T("Create Delivery Note")),
                        onPressed: () {
                          if (data.customerId == null) {
                            errorSnackBar(
                                message: T(
                                    "You must select a customer to start the delivery note"));
                            return;
                          }
                          Navigator.of(context).pop();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: context.newSecondaryColor,
                          foregroundColor: Colors.white,
                          elevation: 0,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 10),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionContainer(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.newBackgroundColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: context.newPrimaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: context.newPrimaryColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(9),
                topRight: Radius.circular(9),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.arrow_right,
                  color: context.newSecondaryColor,
                  size: 20,
                ),
                const SizedBox(width: 4),
                Text(
                  title,
                  style: TextStyle(
                    color: context.newSecondaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(12),
            child: child,
          ),
        ],
      ),
    );
  }
}
