import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/helpers/function.dart';

class InternetConnectionStatusWidget extends StatelessWidget {
  const InternetConnectionStatusWidget({super.key});

  @override
  Widget build(BuildContext context) {
    var status = AppController.isThereConnection;
    return Container(
      alignment: Alignment.center,
      width: context.width,
      height: 30,
      decoration: BoxDecoration(
        color: status == true ? context.colors.onPrimary : context.colors.error,
      ),
      child: Text(
        status == true ? T("Online") : T("Offline"),
        style: TextStyle(
          color: context.backgroundColor,
        ),
      ),
    );
  }
}
