import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/controllers/warehouse_3d_controller.dart';
import 'package:inventory_application/models/model/warehouse_3d_model.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/warehouse_3d/widgets/warehouse_3d_viewer_widget.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';

class Warehouse3DSearchScreen extends StatefulWidget {
  const Warehouse3DSearchScreen({Key? key}) : super(key: key);

  @override
  State<Warehouse3DSearchScreen> createState() =>
      _Warehouse3DSearchScreenState();
}

class _Warehouse3DSearchScreenState extends State<Warehouse3DSearchScreen> {
  late Warehouse3DController _warehouse3DController;

  final _searchController = TextEditingController();
  List<ProductLocation3D> _searchResults = [];
  ProductLocation3D? _selectedProductLocation;
  bool _isSearching = false;
  bool _showSearchResults = false;

  // للتحكم في العرض ثلاثي الأبعاد
  Warehouse3D? _currentWarehouse;
  bool _highlightMode = false;

  @override
  void initState() {
    super.initState();
    _warehouse3DController =
        Provider.of<Warehouse3DController>(context, listen: false);

    _loadWarehouses();
  }

  Future<void> _loadWarehouses() async {
    await _warehouse3DController.fetchWarehouses3D();
    if (_warehouse3DController.warehouses3D.isNotEmpty) {
      setState(() {
        _currentWarehouse = _warehouse3DController.warehouses3D.first;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: Text(
          T('البحث المرئي في المستودع'),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2C3E50),
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _highlightMode ? Icons.visibility_off : Icons.visibility,
              color: Colors.white,
            ),
            onPressed: _toggleHighlightMode,
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          _buildSearchBar(),

          // نتائج البحث
          if (_showSearchResults) _buildSearchResults(),

          // العارض ثلاثي الأبعاد
          Expanded(
            child: _buildWarehouse3DViewer(),
          ),

          // معلومات المنتج المحدد
          if (_selectedProductLocation != null) _buildProductInfoPanel(),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: T('ابحث عن منتج، كود، أو رقم الدفعة...'),
                prefixIcon: const Icon(Icons.search, color: Color(0xFF3498DB)),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: _onSearchChanged,
              onSubmitted: _performSearch,
            ),
          ),
          if (_isSearching)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.search, color: Color(0xFF3498DB)),
              onPressed: () => _performSearch(_searchController.text),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return Container(
      height: 200,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  T('نتائج البحث'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                const Spacer(),
                Text(
                  '${_searchResults.length} ${T("نتيجة")}',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () {
                    setState(() {
                      _showSearchResults = false;
                      _selectedProductLocation = null;
                    });
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final productLocation = _searchResults[index];
                return _buildSearchResultItem(productLocation);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResultItem(ProductLocation3D productLocation) {
    final isSelected = _selectedProductLocation?.id == productLocation.id;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? const Color(0xFF3498DB).withOpacity(0.1)
            : Colors.transparent,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? const Color(0xFF3498DB) : Colors.transparent,
          width: 2,
        ),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: const Color(0xFF3498DB).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.inventory_2,
            color: Color(0xFF3498DB),
          ),
        ),
        title: Text(
          productLocation.productName ?? 'منتج غير محدد',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${T("الكود")}: ${productLocation.productCode ?? "غير محدد"}',
              style: const TextStyle(fontSize: 12),
            ),
            if (productLocation.batchNumber != null)
              Text(
                '${T("رقم الدفعة")}: ${productLocation.batchNumber}',
                style: const TextStyle(fontSize: 12),
              ),
            Text(
              '${T("الكمية")}: ${productLocation.quantity?.toStringAsFixed(0) ?? "0"}',
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF27AE60),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.location_on, color: Color(0xFFE74C3C), size: 16),
            Text(
              '${T("خزانة")} ${_getCabinetName(productLocation.cabinetId)}',
              style: const TextStyle(fontSize: 10),
            ),
            Text(
              '${T("رف")} ${_getShelfName(productLocation.shelfId)}',
              style: const TextStyle(fontSize: 10),
            ),
          ],
        ),
        onTap: () => _selectProductLocation(productLocation),
      ),
    );
  }

  Widget _buildWarehouse3DViewer() {
    if (_currentWarehouse == null) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warehouse,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'لا توجد مستودعات ثلاثية الأبعاد',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: Stack(
          children: [
            Warehouse3DViewerWidget(
              warehouse: _currentWarehouse!,
              onCabinetTap: _onCabinetTap,
              onShelfTap: _onShelfTap,
            ),

            // طبقة التمييز للمنتج المحدد
            if (_selectedProductLocation != null && _highlightMode)
              _buildHighlightOverlay(),

            // مؤشر الموقع
            if (_selectedProductLocation != null) _buildLocationIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _buildHighlightOverlay() {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFE74C3C).withOpacity(0.3),
        borderRadius: BorderRadius.circular(16),
      ),
      child: const Center(
        child: Icon(
          Icons.location_on,
          size: 48,
          color: Color(0xFFE74C3C),
        ),
      ),
    );
  }

  Widget _buildLocationIndicator() {
    return Positioned(
      top: 16,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: const Color(0xFFE74C3C),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.location_on, color: Colors.white, size: 16),
            const SizedBox(width: 4),
            Text(
              T('موقع المنتج'),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductInfoPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // معلومات المنتج
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: const Color(0xFF3498DB).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.inventory_2,
                  color: Color(0xFF3498DB),
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _selectedProductLocation!.productName ?? 'منتج غير محدد',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${T("الكود")}: ${_selectedProductLocation!.productCode ?? "غير محدد"}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                    if (_selectedProductLocation!.batchNumber != null)
                      Text(
                        '${T("رقم الدفعة")}: ${_selectedProductLocation!.batchNumber}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFF27AE60).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_selectedProductLocation!.quantity?.toStringAsFixed(0) ?? "0"}',
                  style: const TextStyle(
                    color: Color(0xFF27AE60),
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // معلومات الموقع
          Row(
            children: [
              Expanded(
                child: _buildLocationInfo(
                  T('المستودع'),
                  _getWarehouseName(_selectedProductLocation!.warehouseId),
                  Icons.warehouse,
                  const Color(0xFF9B59B6),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildLocationInfo(
                  T('الخزانة'),
                  _getCabinetName(_selectedProductLocation!.cabinetId),
                  Icons.inbox,
                  const Color(0xFF3498DB),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildLocationInfo(
                  T('الرف'),
                  _getShelfName(_selectedProductLocation!.shelfId),
                  Icons.shelves,
                  const Color(0xFF27AE60),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // أزرار العمليات
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _navigateToProduct,
                  icon: const Icon(Icons.directions),
                  label: Text(T('انتقل للموقع')),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF3498DB),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showProductDetails,
                  icon: const Icon(Icons.info),
                  label: Text(T('تفاصيل المنتج')),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLocationInfo(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _onSearchChanged(String value) {
    // البحث المباشر أثناء الكتابة
    if (value.length >= 2) {
      _performSearch(value);
    } else if (value.isEmpty) {
      setState(() {
        _searchResults.clear();
        _showSearchResults = false;
        _selectedProductLocation = null;
      });
    }
  }

  Future<void> _performSearch(String searchTerm) async {
    if (searchTerm.trim().isEmpty) return;

    setState(() {
      _isSearching = true;
    });

    try {
      final results =
          await _warehouse3DController.searchProduct(searchTerm.trim());
      setState(() {
        _searchResults = results;
        _showSearchResults = results.isNotEmpty;
        _isSearching = false;
      });

      if (results.isEmpty) {
        errorSnackBar(
          message: T('لم يتم العثور على نتائج'),
          context: context,
        );
      }
    } catch (e) {
      setState(() {
        _isSearching = false;
      });
      errorSnackBar(
        message: T('خطأ في البحث'),
        context: context,
      );
    }
  }

  void _selectProductLocation(ProductLocation3D productLocation) {
    setState(() {
      _selectedProductLocation = productLocation;
    });

    // تحديد المستودع المناسب إذا لم يكن محدداً
    if (_currentWarehouse?.id != productLocation.warehouseId) {
      final warehouse = _warehouse3DController.warehouses3D
          .where((w) => w.id == productLocation.warehouseId)
          .firstOrNull;
      if (warehouse != null) {
        setState(() {
          _currentWarehouse = warehouse;
        });
      }
    }
  }

  void _onCabinetTap(Cabinet3D cabinet) {
    // يمكن إضافة منطق إضافي عند النقر على الخزانة
  }

  void _onShelfTap(Shelf3D shelf) {
    // يمكن إضافة منطق إضافي عند النقر على الرف
  }

  void _toggleHighlightMode() {
    setState(() {
      _highlightMode = !_highlightMode;
    });
  }

  void _navigateToProduct() {
    if (_selectedProductLocation != null) {
      // TODO: تطبيق الانتقال للموقع الفعلي في العرض ثلاثي الأبعاد
      successSnackBar(
        message: T('تم تحديد موقع المنتج'),
        context: context,
      );
    }
  }

  void _showProductDetails() {
    if (_selectedProductLocation != null) {
      // TODO: عرض تفاصيل المنتج
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(T('تفاصيل المنتج')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  '${T("الاسم")}: ${_selectedProductLocation!.productName ?? "غير محدد"}'),
              Text(
                  '${T("الكود")}: ${_selectedProductLocation!.productCode ?? "غير محدد"}'),
              Text(
                  '${T("الكمية")}: ${_selectedProductLocation!.quantity?.toStringAsFixed(2) ?? "0"}'),
              if (_selectedProductLocation!.batchNumber != null)
                Text(
                    '${T("رقم الدفعة")}: ${_selectedProductLocation!.batchNumber}'),
              if (_selectedProductLocation!.expiryDate != null)
                Text(
                    '${T("تاريخ الانتهاء")}: ${_selectedProductLocation!.expiryDate.toString().split(' ')[0]}'),
              Text(
                  '${T("آخر تحديث")}: ${_selectedProductLocation!.lastUpdated?.toString().split(' ')[0] ?? "غير محدد"}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(T('إغلاق')),
            ),
          ],
        ),
      );
    }
  }

  String _getWarehouseName(int? warehouseId) {
    if (warehouseId == null) return 'غير محدد';
    final warehouse = _warehouse3DController.warehouses3D
        .where((w) => w.id == warehouseId)
        .firstOrNull;
    return warehouse?.name ?? 'مستودع $warehouseId';
  }

  String _getCabinetName(int? cabinetId) {
    if (cabinetId == null) return 'غير محدد';

    for (final warehouse in _warehouse3DController.warehouses3D) {
      final cabinet =
          warehouse.cabinets?.where((c) => c.id == cabinetId).firstOrNull;
      if (cabinet != null) {
        return cabinet.name ?? 'خزانة $cabinetId';
      }
    }
    return 'خزانة $cabinetId';
  }

  String _getShelfName(int? shelfId) {
    if (shelfId == null) return 'غير محدد';

    for (final warehouse in _warehouse3DController.warehouses3D) {
      for (final cabinet in warehouse.cabinets ?? []) {
        final shelf =
            cabinet.shelves?.where((s) => s.id == shelfId).firstOrNull;
        if (shelf != null) {
          return shelf.name ?? 'رف $shelfId';
        }
      }
    }
    return 'رف $shelfId';
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
