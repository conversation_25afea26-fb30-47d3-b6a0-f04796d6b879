import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';

class InventoryDetailsBaseInfoWidget extends StatelessWidget {
  const InventoryDetailsBaseInfoWidget({
    super.key,
    required this.data,
  });

  final InventoryOperationModel? data;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(15),
      child: Column(
        children: [
          // Invoice code and date section
          _buildInfoSection(
            context,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                    decoration: BoxDecoration(
                      color: context.newPrimaryColor,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      data?.entryDateFormated ?? "",
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Icon(Icons.receipt_outlined,
                            size: 18, color: context.newSecondaryColor),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            "${T("Inventory Code")}: ${T(data?.code ?? "")}",
                            style: TextStyle(
                              color: context.newTextColor,
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Row(
                children: [
                  Icon(Icons.numbers_outlined,
                      size: 18, color: context.newSecondaryColor),
                  const SizedBox(width: 8),
                  Text(
                    "${T("Local Code")}: ${T(data?.aPPReferanceCode ?? "")}",
                    style: TextStyle(
                      color: context.newTextColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Customer and warehouse information
          _buildInfoSection(
            context,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.warehouse_outlined,
                      size: 18, color: context.newSecondaryColor),
                  const SizedBox(width: 8),
                  Text(
                    "${T("Warehouses")}: ",
                    style: TextStyle(
                      color: context.newTextColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      T(data?.storeName ?? ""),
                      style: TextStyle(
                        color: context.newSecondaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  // Expanded(
                  //   child: Text(
                  //     T(data?.salesItems
                  //             ?.map(
                  //               (e) => e.warehouseName,
                  //             )
                  //             .join(", ") ??
                  //         ""),
                  //     style: TextStyle(
                  //       color: context.newTextColor,
                  //     ),
                  //   ),
                  // ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 12),
        ],
      ),
    );
  }

  Widget _buildInfoSection(BuildContext context,
      {required List<Widget> children}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: context.newPrimaryColor.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: children,
      ),
    );
  }

  Widget _buildFinancialRow(BuildContext context,
      {required String label, required String value, bool isTotal = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: isTotal ? 16 : 14,
            fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
            color: context.newTextColor,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isTotal ? 18 : 14,
            fontWeight: FontWeight.bold,
            color: isTotal ? context.newSecondaryColor : context.newTextColor,
          ),
        ),
      ],
    );
  }
}
