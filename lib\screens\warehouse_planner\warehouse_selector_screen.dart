import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/warehouse_planner_provider.dart';

class WarehouseSelectorScreen extends StatefulWidget {
  const WarehouseSelectorScreen({Key? key}) : super(key: key);

  @override
  State<WarehouseSelectorScreen> createState() =>
      _WarehouseSelectorScreenState();
}

class _WarehouseSelectorScreenState extends State<WarehouseSelectorScreen> {
  List<WarehouseOption> _warehouses = [];
  WarehouseOption? _selectedWarehouse;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWarehouses();
  }

  Future<void> _loadWarehouses() async {
    // في الوضع الحقيقي، ستأتي هذه البيانات من API
    // لكن الآن سنضع بيانات تجريبية
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _warehouses = [
        WarehouseOption(
          id: 1,
          name: 'مستودع الأدوية الرئيسي',
          description: 'المستودع الأساسي للأدوية والمستلزمات الطبية',
          location: 'الطابق الأول - القسم الشرقي',
          capacity: 1000,
          currentStock: 750,
        ),
        WarehouseOption(
          id: 2,
          name: 'مستودع الأدوية المبردة',
          description: 'مخصص للأدوية التي تحتاج تبريد',
          location: 'الطابق الأرضي - القسم الشمالي',
          capacity: 300,
          currentStock: 180,
        ),
        WarehouseOption(
          id: 3,
          name: 'مستودع المخدرات',
          description: 'مستودع آمن للأدوية المخدرة والمحكمة',
          location: 'الطابق الثاني - غرفة مؤمنة',
          capacity: 100,
          currentStock: 45,
        ),
        WarehouseOption(
          id: 4,
          name: 'مستودع المستلزمات الطبية',
          description: 'للأدوات والمستلزمات الطبية غير الدوائية',
          location: 'الطابق الأول - القسم الغربي',
          capacity: 800,
          currentStock: 520,
        ),
      ];
      _isLoading = false;
    });
  }

  void _selectWarehouse(WarehouseOption warehouse) {
    setState(() {
      _selectedWarehouse = warehouse;
    });
  }

  void _createWarehousePlan() {
    if (_selectedWarehouse == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار مستودع أولاً')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => _CreatePlanDialog(
        warehouse: _selectedWarehouse!,
        onConfirm: (name, description) {
          final provider =
              Provider.of<WarehousePlannerProvider>(context, listen: false);
          provider.createNewWarehouse(
            name: name,
            description: description,
            linkedWarehouseId: _selectedWarehouse!.id,
            linkedWarehouseName: _selectedWarehouse!.name,
          );
          Navigator.of(context).pop(); // إغلاق الحوار
          Navigator.of(context).pop(); // العودة للشاشة الرئيسية
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختر المستودع'),
        backgroundColor: const Color(0xFF3498DB),
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: const Color(0xFFE9ECEF)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info_outline, color: Color(0xFF3498DB)),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: const [
                            Text(
                              'اختر المستودع المراد إنشاء مخطط له',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'سيتم ربط المخطط بالمستودع المحدد لإدارة الأدوية والمنتجات',
                              style: TextStyle(
                                color: Color(0xFF6C757D),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: _warehouses.length,
                    itemBuilder: (context, index) {
                      final warehouse = _warehouses[index];
                      final isSelected = _selectedWarehouse?.id == warehouse.id;

                      return Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? const Color(0xFF3498DB)
                                : const Color(0xFFE9ECEF),
                            width: isSelected ? 2 : 1,
                          ),
                          color: isSelected
                              ? const Color(0xFFF8F9FA)
                              : Colors.white,
                        ),
                        child: ListTile(
                          contentPadding: const EdgeInsets.all(16),
                          leading: CircleAvatar(
                            backgroundColor: isSelected
                                ? const Color(0xFF3498DB)
                                : const Color(0xFF6C757D),
                            child: Icon(
                              Icons.warehouse,
                              color: Colors.white,
                            ),
                          ),
                          title: Text(
                            warehouse.name,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: isSelected
                                  ? const Color(0xFF3498DB)
                                  : Colors.black,
                            ),
                          ),
                          subtitle: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const SizedBox(height: 4),
                              Text(warehouse.description),
                              const SizedBox(height: 8),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.location_on,
                                    size: 16,
                                    color: Color(0xFF6C757D),
                                  ),
                                  const SizedBox(width: 4),
                                  Expanded(
                                    child: Text(
                                      warehouse.location,
                                      style: const TextStyle(
                                        color: Color(0xFF6C757D),
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 4),
                              Row(
                                children: [
                                  const Icon(
                                    Icons.inventory,
                                    size: 16,
                                    color: Color(0xFF6C757D),
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    'المخزون: ${warehouse.currentStock}/${warehouse.capacity}',
                                    style: const TextStyle(
                                      color: Color(0xFF6C757D),
                                      fontSize: 12,
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  Container(
                                    width: 100,
                                    height: 4,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(2),
                                      color: const Color(0xFFE9ECEF),
                                    ),
                                    child: FractionallySizedBox(
                                      alignment: Alignment.centerLeft,
                                      widthFactor: warehouse.currentStock /
                                          warehouse.capacity,
                                      child: Container(
                                        decoration: BoxDecoration(
                                          borderRadius:
                                              BorderRadius.circular(2),
                                          color: warehouse.currentStock /
                                                      warehouse.capacity >
                                                  0.8
                                              ? Colors.red
                                              : warehouse.currentStock /
                                                          warehouse.capacity >
                                                      0.6
                                                  ? Colors.orange
                                                  : Colors.green,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          trailing: isSelected
                              ? const Icon(
                                  Icons.check_circle,
                                  color: Color(0xFF3498DB),
                                  size: 24,
                                )
                              : const Icon(
                                  Icons.radio_button_unchecked,
                                  color: Color(0xFF6C757D),
                                  size: 24,
                                ),
                          onTap: () => _selectWarehouse(warehouse),
                        ),
                      );
                    },
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(16),
                  child: SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: _selectedWarehouse != null
                          ? _createWarehousePlan
                          : null,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF3498DB),
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'إنشاء مخطط للمستودع',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
    );
  }
}

class WarehouseOption {
  final int id;
  final String name;
  final String description;
  final String location;
  final int capacity;
  final int currentStock;

  WarehouseOption({
    required this.id,
    required this.name,
    required this.description,
    required this.location,
    required this.capacity,
    required this.currentStock,
  });
}

class _CreatePlanDialog extends StatefulWidget {
  final WarehouseOption warehouse;
  final Function(String name, String description) onConfirm;

  const _CreatePlanDialog({
    required this.warehouse,
    required this.onConfirm,
  });

  @override
  State<_CreatePlanDialog> createState() => __CreatePlanDialogState();
}

class __CreatePlanDialogState extends State<_CreatePlanDialog> {
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _nameController.text = 'مخطط ${widget.warehouse.name}';
    _descriptionController.text =
        'مخطط ثنائي الأبعاد لـ ${widget.warehouse.name}';
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إنشاء مخطط جديد'),
      content: SizedBox(
        width: 400,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المستودع المحدد: ${widget.warehouse.name}',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Color(0xFF3498DB),
              ),
            ),
            const SizedBox(height: 16),
            const Text('اسم المخطط:'),
            const SizedBox(height: 8),
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'أدخل اسم المخطط',
              ),
            ),
            const SizedBox(height: 16),
            const Text('الوصف:'),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              maxLines: 3,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'أدخل وصف المخطط',
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_nameController.text.trim().isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('يرجى إدخال اسم المخطط')),
              );
              return;
            }
            widget.onConfirm(
              _nameController.text.trim(),
              _descriptionController.text.trim(),
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF3498DB),
            foregroundColor: Colors.white,
          ),
          child: const Text('إنشاء'),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
