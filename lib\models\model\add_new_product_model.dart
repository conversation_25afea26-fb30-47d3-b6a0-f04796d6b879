class addNewProduct {
  int? categoryID;
  String? code;
  String? name;
  int? iD;
  String? nameEn;
  String? nameTr;
  int? codingMode;
  Null? salesDiscount;
  String? nameLocalized;
  int? salesPrice1;
  int? purchasePrice1;
  int? unitID;
  int? branchID;
  List<ItemsUnitsModel>? itemsUnitsModel;

  addNewProduct(
      {this.categoryID,
      this.code,
      this.name,
      this.iD,
      this.nameEn,
      this.nameTr,
      this.codingMode,
      this.salesDiscount,
      this.nameLocalized,
      this.salesPrice1,
      this.purchasePrice1,
      this.unitID,
      this.branchID,
      this.itemsUnitsModel});

  addNewProduct.fromJson(Map<String, dynamic> json) {
    categoryID = json['Category_ID'];
    code = json['Code'];
    name = json['Name'];
    iD = json['ID'];
    nameEn = json['Name_En'];
    nameTr = json['Name_Tr'];
    codingMode = json['Coding_Mode'];
    salesDiscount = json['Sales_Discount'];
    nameLocalized = json['Name_Localized'];
    salesPrice1 = json['Sales_Price_1'];
    purchasePrice1 = json['Purchase_Price_1'];
    unitID = json['Unit_ID'];
    branchID = json['Branch_ID'];
    if (json['ItemsUnitsModel'] != null) {
      itemsUnitsModel = <ItemsUnitsModel>[];
      json['ItemsUnitsModel'].forEach((v) {
        itemsUnitsModel!.add(new ItemsUnitsModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Category_ID'] = this.categoryID;
    data['Code'] = this.code;
    data['Name'] = this.name;
    data['ID'] = this.iD;
    data['Name_En'] = this.nameEn;
    data['Name_Tr'] = this.nameTr;
    data['Coding_Mode'] = this.codingMode;
    data['Sales_Discount'] = this.salesDiscount;
    data['Name_Localized'] = this.nameLocalized;
    data['Sales_Price_1'] = this.salesPrice1;
    data['Purchase_Price_1'] = this.purchasePrice1;
    data['Unit_ID'] = this.unitID;
    data['Branch_ID'] = this.branchID;
    if (this.itemsUnitsModel != null) {
      data['ItemsUnitsModel'] =
          this.itemsUnitsModel!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ItemsUnitsModel {
  int? itemID;
  bool? isMainUnit;
  int? itemUnitID;
  int? salesPrice1;
  int? purchasePrice1;
  int? purchasePrice2;
  int? purchasePrice3;
  int? salesPrice2;
  int? salesPrice3;
  int? salesPrice4;
  int? salesPrice5;
  int? unitWeight;
  int? unitFactorToMainUnit;
  int? unitID;

  ItemsUnitsModel(
      {this.itemID,
      this.isMainUnit,
      this.itemUnitID,
      this.salesPrice1,
      this.purchasePrice1,
      this.purchasePrice2,
      this.purchasePrice3,
      this.salesPrice2,
      this.salesPrice3,
      this.salesPrice4,
      this.salesPrice5,
      this.unitWeight,
      this.unitFactorToMainUnit,
      this.unitID});

  ItemsUnitsModel.fromJson(Map<String, dynamic> json) {
    itemID = json['Item_ID'];
    isMainUnit = json['Is_Main_Unit'];
    itemUnitID = json['ItemUnit_ID'];
    salesPrice1 = json['Sales_Price_1'];
    purchasePrice1 = json['Purchase_Price_1'];
    purchasePrice2 = json['Purchase_Price_2'];
    purchasePrice3 = json['Purchase_Price_3'];
    salesPrice2 = json['Sales_Price_2'];
    salesPrice3 = json['Sales_Price_3'];
    salesPrice4 = json['Sales_Price_4'];
    salesPrice5 = json['Sales_Price_5'];
    unitWeight = json['Unit_Weight'];
    unitFactorToMainUnit = json['Unit_Factor_To_Main_Unit'];
    unitID = json['Unit_ID'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Item_ID'] = this.itemID;
    data['Is_Main_Unit'] = this.isMainUnit;
    data['ItemUnit_ID'] = this.itemUnitID;
    data['Sales_Price_1'] = this.salesPrice1;
    data['Purchase_Price_1'] = this.purchasePrice1;
    data['Purchase_Price_2'] = this.purchasePrice2;
    data['Purchase_Price_3'] = this.purchasePrice3;
    data['Sales_Price_2'] = this.salesPrice2;
    data['Sales_Price_3'] = this.salesPrice3;
    data['Sales_Price_4'] = this.salesPrice4;
    data['Sales_Price_5'] = this.salesPrice5;
    data['Unit_Weight'] = this.unitWeight;
    data['Unit_Factor_To_Main_Unit'] = this.unitFactorToMainUnit;
    data['Unit_ID'] = this.unitID;
    return data;
  }
}