import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class CommonHeaderDelegate extends SliverPersistentHeaderDelegate {
  const CommonHeaderDelegate(this.context,
      {required this.imagePath, required this.title});
  final String imagePath;
  final String title;
  final BuildContext context;
  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        padding: const EdgeInsets.only(right: 30),
        width: context.width - 20,
        height: 85,
        decoration: BoxDecoration(
          color: context.colors.outline,
          borderRadius: BorderRadius.circular(15),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 20),
            ),
            Row(
              children: [
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: Image.asset(
                    imagePath,
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    height: 120,
                    decoration: BoxDecoration(
                      color: context.colors.outline,
                      borderRadius: const BorderRadius.only(
                        bottomLeft: Radius.circular(15),
                        topLeft: Radius.circular(15),
                      ),
                    ),
                    child: Icon(
                      Icons.arrow_right_sharp,
                      color: context.surfaceColor,
                      size: 35,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  double get maxExtent => 85.0;

  @override
  double get minExtent => 85.0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}
