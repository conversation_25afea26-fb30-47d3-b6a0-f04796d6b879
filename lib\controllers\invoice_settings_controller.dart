import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:shared_preferences/shared_preferences.dart';

class InvoiceSettingsController with ChangeNotifier {
  // Default customer settings
  int? _defaultCustomerId;
  String? _defaultCustomerName;

  // Default warehouse settings
  int? _defaultWarehouseId;
  String? _defaultWarehouseName;

  // Default unit settings
  int? _defaultUnitId;
  String? _defaultUnitName;

  // Getters
  int? get defaultCustomerId => _defaultCustomerId;
  String? get defaultCustomerName => _defaultCustomerName;
  int? get defaultWarehouseId => _defaultWarehouseId;
  String? get defaultWarehouseName => _defaultWarehouseName;
  int? get defaultUnitId => _defaultUnitId;
  String? get defaultUnitName => _defaultUnitName;

  // Constructor
  InvoiceSettingsController() {
    loadSettings();
  }

  // Helper method to get branch-specific keys
  String _getBranchKey(String baseKey) {
    return '${baseKey}_branch_${AppController.currentBranchId}';
  }

  // Load settings from SharedPreferences
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load customer settings with branch-specific keys
      _defaultCustomerId = prefs.getInt(_getBranchKey('defaultCustomerId'));
      _defaultCustomerName =
          prefs.getString(_getBranchKey('defaultCustomerName'));

      // Load warehouse settings with branch-specific keys
      _defaultWarehouseId = prefs.getInt(_getBranchKey('defaultWarehouseId'));
      _defaultWarehouseName =
          prefs.getString(_getBranchKey('defaultWarehouseName'));

      // Load unit settings with branch-specific keys
      _defaultUnitId = prefs.getInt(_getBranchKey('defaultUnitId'));
      _defaultUnitName = prefs.getString(_getBranchKey('defaultUnitName'));

      notifyListeners();
    } catch (e) {
      print("Error loading invoice settings: $e");
    }
  }

  // Save customer settings
  Future<void> setDefaultCustomer(int customerId, String customerName) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save to SharedPreferences with branch-specific keys
      await prefs.setInt(_getBranchKey('defaultCustomerId'), customerId);
      await prefs.setString(_getBranchKey('defaultCustomerName'), customerName);

      // Update controller state
      _defaultCustomerId = customerId;
      _defaultCustomerName = customerName;

      notifyListeners();
      successSnackBar(
          message: 'Default customer saved successfully for current branch');
    } catch (e) {
      print("Error saving default customer: $e");
      errorSnackBar(message: 'Failed to save default customer');
    }
  }

  // Save warehouse settings
  Future<void> setDefaultWarehouse(
      int warehouseId, String warehouseName) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save to SharedPreferences with branch-specific keys
      await prefs.setInt(_getBranchKey('defaultWarehouseId'), warehouseId);
      await prefs.setString(
          _getBranchKey('defaultWarehouseName'), warehouseName);

      // Update controller state
      _defaultWarehouseId = warehouseId;
      _defaultWarehouseName = warehouseName;

      notifyListeners();
      successSnackBar(
          message: 'Default warehouse saved successfully for current branch');
    } catch (e) {
      print("Error saving default warehouse: $e");
      errorSnackBar(message: 'Failed to save default warehouse');
    }
  }

  // Clear customer settings
  Future<void> clearDefaultCustomer() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove from SharedPreferences with branch-specific keys
      await prefs.remove(_getBranchKey('defaultCustomerId'));
      await prefs.remove(_getBranchKey('defaultCustomerName'));

      // Update controller state
      _defaultCustomerId = null;
      _defaultCustomerName = null;

      notifyListeners();
      successSnackBar(message: 'Default customer cleared for current branch');
    } catch (e) {
      print("Error clearing default customer: $e");
      errorSnackBar(message: 'Failed to clear default customer');
    }
  }

  // Clear warehouse settings
  Future<void> clearDefaultWarehouse() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove from SharedPreferences with branch-specific keys
      await prefs.remove(_getBranchKey('defaultWarehouseId'));
      await prefs.remove(_getBranchKey('defaultWarehouseName'));

      // Update controller state
      _defaultWarehouseId = null;
      _defaultWarehouseName = null;

      notifyListeners();
      successSnackBar(message: 'Default warehouse cleared for current branch');
    } catch (e) {
      print("Error clearing default warehouse: $e");
      errorSnackBar(message: 'Failed to clear default warehouse');
    }
  }

  // Save unit settings
  Future<void> setDefaultUnit(int unitId, String unitName) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save to SharedPreferences with branch-specific keys
      await prefs.setInt(_getBranchKey('defaultUnitId'), unitId);
      await prefs.setString(_getBranchKey('defaultUnitName'), unitName);

      // Update controller state
      _defaultUnitId = unitId;
      _defaultUnitName = unitName;

      notifyListeners();
      successSnackBar(
          message: 'Default unit saved successfully for current branch');
    } catch (e) {
      print("Error saving default unit: $e");
      errorSnackBar(message: 'Failed to save default unit');
    }
  }

  // Clear unit settings
  Future<void> clearDefaultUnit() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove from SharedPreferences with branch-specific keys
      await prefs.remove(_getBranchKey('defaultUnitId'));
      await prefs.remove(_getBranchKey('defaultUnitName'));

      // Update controller state
      _defaultUnitId = null;
      _defaultUnitName = null;

      notifyListeners();
      successSnackBar(message: 'Default unit cleared for current branch');
    } catch (e) {
      print("Error clearing default unit: $e");
      errorSnackBar(message: 'Failed to clear default unit');
    }
  }

  // Method to reload settings when branch changes
  Future<void> reloadSettingsForCurrentBranch() async {
    await loadSettings();
  }
}
