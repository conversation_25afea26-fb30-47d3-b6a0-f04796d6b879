import 'dart:async';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_item_details_dialog_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/unit_selection_dialog.dart';
import 'package:inventory_application/base/extension/double_extension.dart';
import 'package:intl/intl.dart';

class PurchaseProductListWidget extends StatefulWidget {
  const PurchaseProductListWidget({
    super.key,
    required this.id,
    required this.selectedInvoiceProduct,
    required this.onUpdateQuantity,
    required this.onUpdatePrice,
    required this.onDeleteProduct,
    required this.onChangeWarehouse,
    required this.barcode,
    this.virtualProductId,
    required this.onUpdateUnit,
    this.onUpdateBatchNumber,
    this.onUpdateSerialNumber,
    this.onUpdateExpirationDate,
    this.onUpdateUnitCost,
    this.onUpdateVatPercent,
  });

  final int id;
  final String? barcode;
  final String? virtualProductId;
  final List<ProductDTO> selectedInvoiceProduct;
  final Function onUpdateQuantity;
  final Function onUpdatePrice;
  final Function onDeleteProduct;
  final Function onChangeWarehouse;
  final Function onUpdateUnit;
  final Function? onUpdateBatchNumber;
  final Function? onUpdateSerialNumber;
  final Function? onUpdateExpirationDate;
  final Function? onUpdateUnitCost;
  final Function? onUpdateVatPercent;

  @override
  State<PurchaseProductListWidget> createState() =>
      _PurchaseProductListWidgetState();
}

class _PurchaseProductListWidgetState extends State<PurchaseProductListWidget> {
  late TextEditingController quantityController;
  late TextEditingController priceController;
  late TextEditingController totalController;
  late TextEditingController batchNumberController;
  late TextEditingController serialNumberController;
  late TextEditingController unitCostController;
  late TextEditingController vatPercentController;

  Timer? _quantityUpdateTimer;
  bool _isExpanded = false;

  // Modern color scheme
  final Color primaryBlue = const Color(0xFF2196F3);
  final Color accentBlue = const Color(0xFF1976D2);
  final Color lightBlue = const Color(0xFFE3F2FD);
  final Color successGreen = const Color(0xFF4CAF50);
  final Color warningOrange = const Color(0xFFFF9800);
  final Color errorRed = const Color(0xFFF44336);
  final Color backgroundGrey = const Color(0xFFF8F9FA);
  final Color borderGrey = const Color(0xFFE0E0E0);
  final Color textDark = const Color(0xFF212121);
  final Color textMedium = const Color(0xFF757575);
  final Color textLight = const Color(0xFF9E9E9E);

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    ProductDTO model = _findProduct();

    quantityController =
        TextEditingController(text: (model.quantity ?? 1).toString());
    priceController = TextEditingController(
        text: (model.price?.covertDoubleToMoneyReturnString(model.price ?? 0) ??
                0)
            .toString());
    totalController = TextEditingController(
        text: (model.total?.covertDoubleToMoneyReturnString(model.total ?? 0) ??
                0)
            .toString());
    batchNumberController =
        TextEditingController(text: model.batchNumber ?? '');
    serialNumberController =
        TextEditingController(text: model.serialNumber ?? '');
    unitCostController = TextEditingController(
        text: (model.unitCost
                    ?.covertDoubleToMoneyReturnString(model.unitCost ?? 0) ??
                0)
            .toString());
    vatPercentController =
        TextEditingController(text: (model.vatPercent ?? 0).toString());
  }

  ProductDTO _findProduct() {
    if (widget.virtualProductId != null) {
      return widget.selectedInvoiceProduct.firstWhere(
        (element) => element.virtualProductId == widget.virtualProductId,
      );
    } else {
      return widget.selectedInvoiceProduct.firstWhere((element) =>
          element.id == widget.id && element.barcode == widget.barcode);
    }
  }

  @override
  void didUpdateWidget(PurchaseProductListWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.selectedInvoiceProduct != oldWidget.selectedInvoiceProduct) {
      ProductDTO currentProduct = _findProduct();
      quantityController.text = (currentProduct.quantity ?? 1).toString();
      totalController.text = (currentProduct.total
                  ?.covertDoubleToMoneyReturnString(
                      currentProduct.total ?? 0) ??
              0)
          .toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    ProductDTO model = _findProduct();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
        border: Border.all(
          color: borderGrey.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main product header
          _buildProductHeader(model),

          // Main fields row (always visible)
          _buildMainFieldsRow(model),

          // Expandable additional fields
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _isExpanded ? null : 0,
            child: _isExpanded
                ? _buildExpandedFields(model)
                : const SizedBox.shrink(),
          ),

          // Expand/Collapse button
          _buildExpandButton(),
        ],
      ),
    );
  }

  Widget _buildProductHeader(ProductDTO model) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            primaryBlue.withOpacity(0.1),
            accentBlue.withOpacity(0.05),
          ],
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          // Product icon with gradient
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [primaryBlue, accentBlue],
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: primaryBlue.withOpacity(0.3),
                  spreadRadius: 0,
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: const Icon(
              Icons.inventory_2_rounded,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),

          // Product info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Product title with attributes
                Text(
                  '${model.title} ${_formatAttributes(model)}',
                  style: TextStyle(
                    color: textDark,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    height: 1.2,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),

                // Product code and warehouse
                Row(
                  children: [
                    // Product code
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: primaryBlue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: primaryBlue.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        model.code ?? T("No Code"),
                        style: TextStyle(
                          color: primaryBlue,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Warehouse
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: successGreen.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: successGreen.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.warehouse_rounded,
                            color: successGreen,
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            model.warehouseName ?? T("No Warehouse"),
                            style: TextStyle(
                              color: successGreen,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Unit and options
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              // Unit selector
              InkWell(
                onTap: () => _showUnitDialog(model),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: primaryBlue.withOpacity(0.3),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        spreadRadius: 0,
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.straighten_rounded,
                        color: primaryBlue,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        model.uniteName ?? T("Unit"),
                        style: TextStyle(
                          color: primaryBlue,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Icon(
                        Icons.keyboard_arrow_down_rounded,
                        color: primaryBlue,
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 8),

              // Options menu
              InkWell(
                onTap: () => _showOptionsDialog(model),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: borderGrey,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.more_vert_rounded,
                    color: textMedium,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMainFieldsRow(ProductDTO model) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // Quantity
          Expanded(
            flex: 2,
            child: _buildFieldCard(
              label: T("Quantity"),
              child: _buildQuantityField(),
              color: primaryBlue,
            ),
          ),
          const SizedBox(width: 8),

          // Price
          Expanded(
            flex: 2,
            child: _buildFieldCard(
              label: T("Price"),
              child: _buildPriceField(),
              color: warningOrange,
            ),
          ),
          const SizedBox(width: 8),

          // Total
          Expanded(
            flex: 2,
            child: _buildFieldCard(
              label: T("Total"),
              child: _buildTotalField(),
              color: successGreen,
              isReadOnly: true,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpandedFields(ProductDTO model) {
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 0, 12, 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section title
          Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: Row(
              children: [
                Container(
                  width: 4,
                  height: 20,
                  decoration: BoxDecoration(
                    color: primaryBlue,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Text(
                  T("Additional Details"),
                  style: TextStyle(
                    color: textDark,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // First row: Batch Number, Serial Number
          Row(
            children: [
              Expanded(
                child: _buildFieldCard(
                  label: T("Batch Number"),
                  child: _buildTextField(
                    controller: batchNumberController,
                    onChanged: (value) => _updateBatchNumber(value),
                  ),
                  color: accentBlue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFieldCard(
                  label: T("Serial Number"),
                  child: _buildTextField(
                    controller: serialNumberController,
                    onChanged: (value) => _updateSerialNumber(value),
                  ),
                  color: accentBlue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Second row: Unit Cost, Unit Cost with Expenses
          Row(
            children: [
              Expanded(
                child: _buildFieldCard(
                  label: T("Unit Cost"),
                  child: _buildTextField(
                    controller: unitCostController,
                    keyboardType: TextInputType.number,
                    onChanged: (value) => _updateUnitCost(value),
                  ),
                  color: warningOrange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFieldCard(
                  label: T("Unit Cost with Expenses"),
                  child: _buildTextField(
                    controller: TextEditingController(
                        text: (model.unitCostWithExpenses
                                    ?.covertDoubleToMoneyReturnString(
                                        model.unitCostWithExpenses ?? 0) ??
                                0)
                            .toString()),
                    keyboardType: TextInputType.number,
                    isReadOnly: true,
                  ),
                  color: errorRed,
                  isReadOnly: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Third row: VAT Percent, Expiration Date
          Row(
            children: [
              Expanded(
                child: _buildFieldCard(
                  label: T("VAT %"),
                  child: _buildTextField(
                    controller: vatPercentController,
                    keyboardType: TextInputType.number,
                    onChanged: (value) => _updateVatPercent(value),
                    suffix: "%",
                  ),
                  color: primaryBlue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildFieldCard(
                  label: T("Expiration Date"),
                  child: _buildDateField(model),
                  color: errorRed,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFieldCard({
    required String label,
    required Widget child,
    required Color color,
    bool isReadOnly = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: isReadOnly ? backgroundGrey : Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isReadOnly ? borderGrey : color.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.03),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Label
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: isReadOnly
                  ? borderGrey.withOpacity(0.5)
                  : color.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(7),
                topRight: Radius.circular(7),
              ),
            ),
            child: Text(
              label,
              style: TextStyle(
                color: isReadOnly ? textMedium : color,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),

          // Field
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            child: child,
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityField() {
    return Row(
      children: [
        // Minus button
        InkWell(
          onTap: () => _decreaseQuantity(),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: primaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: primaryBlue.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.remove_rounded,
              color: primaryBlue,
              size: 16,
            ),
          ),
        ),

        // Quantity input
        Expanded(
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            child: _buildTextField(
              controller: quantityController,
              keyboardType: TextInputType.number,
              textAlign: TextAlign.center,
              onChanged: (value) => _updateQuantity(value),
            ),
          ),
        ),

        // Plus button
        InkWell(
          onTap: () => _increaseQuantity(),
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: primaryBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: primaryBlue.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.add_rounded,
              color: primaryBlue,
              size: 16,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPriceField() {
    return _buildTextField(
      controller: priceController,
      keyboardType: TextInputType.number,
      onChanged: (value) => _updatePrice(value),
    );
  }

  Widget _buildTotalField() {
    return _buildTextField(
      controller: totalController,
      isReadOnly: true,
    );
  }

  Widget _buildDateField(ProductDTO model) {
    return InkWell(
      onTap: () => _selectExpirationDate(model),
      child: Container(
        height: 32, // Reduced height to match text fields
        padding: const EdgeInsets.symmetric(horizontal: 8), // Reduced padding
        decoration: BoxDecoration(
          color: backgroundGrey,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: borderGrey,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                model.expirationDate != null
                    ? DateFormat('yyyy/MM/dd').format(model.expirationDate!)
                    : T("Select Date"),
                style: TextStyle(
                  color: model.expirationDate != null ? textDark : textLight,
                  fontSize: 12, // Smaller font size
                ),
              ),
            ),
            Icon(
              Icons.calendar_today_rounded,
              color: textMedium,
              size: 14, // Smaller icon
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    TextInputType keyboardType = TextInputType.text,
    TextAlign textAlign = TextAlign.start,
    String? suffix,
    bool isReadOnly = false,
    Function(String)? onChanged,
  }) {
    return SizedBox(
      height: 32, // Fixed compact height
      child: TextField(
        controller: controller,
        keyboardType: keyboardType,
        textAlign: textAlign,
        readOnly: isReadOnly,
        onChanged: onChanged,
        style: TextStyle(
          color: isReadOnly ? textMedium : textDark,
          fontSize: 12, // Smaller font size
          fontWeight: FontWeight.w500,
        ),
        decoration: InputDecoration(
          border: InputBorder.none,
          contentPadding:
              const EdgeInsets.symmetric(vertical: 4), // Reduced padding
          suffixText: suffix,
          suffixStyle: TextStyle(
            color: textMedium,
            fontSize: 10, // Smaller suffix text
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildExpandButton() {
    return InkWell(
      onTap: () => setState(() => _isExpanded = !_isExpanded),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 8),
        decoration: BoxDecoration(
          color: backgroundGrey,
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(16),
            bottomRight: Radius.circular(16),
          ),
          border: Border(
            top: BorderSide(
              color: borderGrey.withOpacity(0.5),
              width: 1,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _isExpanded ? T("Show Less") : T("Show More Details"),
              style: TextStyle(
                color: primaryBlue,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            AnimatedRotation(
              turns: _isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                Icons.keyboard_arrow_down_rounded,
                color: primaryBlue,
                size: 20,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatAttributes(ProductDTO model) {
    if (model.attribute == null || model.attribute!.isEmpty) return '';
    return model.attribute!
        .map((attr) => attr.itemsAttributeOptions
            ?.map((option) => option.optionName)
            .join(','))
        .where((attr) => attr != null && attr.isNotEmpty)
        .join(' / ');
  }

  // Event handlers
  void _updateQuantity(String value) {
    if (value.isNotEmpty) {
      double quantity = double.tryParse(value) ?? 0;
      double price = double.tryParse(priceController.text) ?? 0;
      double total = quantity * price;

      totalController.text =
          total.covertDoubleToMoneyReturnString(total).toString();

      _quantityUpdateTimer?.cancel();
      _quantityUpdateTimer = Timer(const Duration(milliseconds: 500), () {
        if (widget.virtualProductId != null) {
          widget.onUpdateQuantity(
              _findProduct().id ?? 0, quantity, widget.virtualProductId);
        } else {
          widget.onUpdateQuantity(_findProduct().id ?? 0, quantity);
        }
      });
    }
  }

  void _updatePrice(String value) {
    if (value.isNotEmpty) {
      double price = double.tryParse(value) ?? 0;
      double quantity = double.tryParse(quantityController.text) ?? 0;
      double total = quantity * price;

      if (widget.virtualProductId != null) {
        widget.onUpdatePrice(
            _findProduct().id ?? 0, price, widget.virtualProductId);
      } else {
        widget.onUpdatePrice(_findProduct().id ?? 0, price);
      }

      setState(() {
        totalController.text =
            total.covertDoubleToMoneyReturnString(total).toString();
      });
    }
  }

  void _decreaseQuantity() {
    double currentQty = double.tryParse(quantityController.text) ?? 0;
    if (currentQty > 1) {
      double newQty = currentQty - 1;
      quantityController.text = newQty.toString();
      _updateQuantity(newQty.toString());
    }
  }

  void _increaseQuantity() {
    double currentQty = double.tryParse(quantityController.text) ?? 0;
    double newQty = currentQty + 1;
    quantityController.text = newQty.toString();
    _updateQuantity(newQty.toString());
  }

  void _updateBatchNumber(String value) {
    if (widget.onUpdateBatchNumber != null) {
      if (widget.virtualProductId != null) {
        widget.onUpdateBatchNumber!(
            _findProduct().id ?? 0, value, widget.virtualProductId);
      } else {
        widget.onUpdateBatchNumber!(_findProduct().id ?? 0, value);
      }
    }
  }

  void _updateSerialNumber(String value) {
    if (widget.onUpdateSerialNumber != null) {
      if (widget.virtualProductId != null) {
        widget.onUpdateSerialNumber!(
            _findProduct().id ?? 0, value, widget.virtualProductId);
      } else {
        widget.onUpdateSerialNumber!(_findProduct().id ?? 0, value);
      }
    }
  }

  void _updateUnitCost(String value) {
    if (widget.onUpdateUnitCost != null && value.isNotEmpty) {
      double unitCost = double.tryParse(value) ?? 0;
      if (widget.virtualProductId != null) {
        widget.onUpdateUnitCost!(
            _findProduct().id ?? 0, unitCost, widget.virtualProductId);
      } else {
        widget.onUpdateUnitCost!(_findProduct().id ?? 0, unitCost);
      }
    }
  }

  void _updateVatPercent(String value) {
    if (widget.onUpdateVatPercent != null && value.isNotEmpty) {
      double vatPercent = double.tryParse(value) ?? 0;
      if (widget.virtualProductId != null) {
        widget.onUpdateVatPercent!(
            _findProduct().id ?? 0, vatPercent, widget.virtualProductId);
      } else {
        widget.onUpdateVatPercent!(_findProduct().id ?? 0, vatPercent);
      }
    }
  }

  Future<void> _selectExpirationDate(ProductDTO model) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          model.expirationDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: primaryBlue,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: textDark,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && widget.onUpdateExpirationDate != null) {
      if (widget.virtualProductId != null) {
        widget.onUpdateExpirationDate!(
            _findProduct().id ?? 0, picked, widget.virtualProductId);
      } else {
        widget.onUpdateExpirationDate!(_findProduct().id ?? 0, picked);
      }
    }
  }

  Future<void> _showUnitDialog(ProductDTO model) async {
    if (widget.onUpdateUnit != null) {
      final selectedUnit = await showUnitSelectionDialog(
        context: context,
        product: model,
      );

      if (selectedUnit != null) {
        if (widget.virtualProductId != null) {
          widget.onUpdateUnit(
              model.id ?? 0, selectedUnit, widget.virtualProductId);
        } else {
          widget.onUpdateUnit(model.id ?? 0, selectedUnit);
        }

        setState(() {
          priceController.text = (selectedUnit.salesPrice ?? 0).toString();
          double quantity = double.tryParse(quantityController.text) ?? 0;
          double price = selectedUnit.salesPrice ?? 0;
          double total = quantity * price;
          totalController.text =
              total.covertDoubleToMoneyReturnString(total).toString();
        });
      }
    }
  }

  void _showOptionsDialog(ProductDTO model) {
    invoiceListItemDetailsDialog(
      context: context,
      id: model.id ?? 0,
      barcode: widget.barcode,
      virtualProductId: widget.virtualProductId,
      onDelete: () {
        Navigator.of(context).pop();
        if (widget.virtualProductId != null) {
          widget.onDeleteProduct(model.id ?? 0, widget.virtualProductId);
        } else if (model.virtualProductId != null) {
          widget.onDeleteProduct(model.id ?? 0, model.virtualProductId);
        } else {
          widget.onDeleteProduct(model.id ?? 0);
        }
      },
      onSelect: (id, name) {
        Navigator.of(context).pop();
        if (widget.virtualProductId != null) {
          widget.onChangeWarehouse(
              model.id ?? 0, id, name, widget.virtualProductId);
        } else if (model.virtualProductId != null) {
          widget.onChangeWarehouse(
              model.id ?? 0, id, name, model.virtualProductId);
        } else {
          widget.onChangeWarehouse(model.id ?? 0, id, name);
        }
      },
      selectedInvoiceProduct: widget.selectedInvoiceProduct,
    );
  }

  @override
  void dispose() {
    _quantityUpdateTimer?.cancel();
    quantityController.dispose();
    priceController.dispose();
    totalController.dispose();
    batchNumberController.dispose();
    serialNumberController.dispose();
    unitCostController.dispose();
    vatPercentController.dispose();
    super.dispose();
  }
}
