import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/accounts_vouchers_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/accounts_voucher_counter.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class VouchersListScreen extends StatefulWidget {
  const VouchersListScreen({super.key});

  @override
  State<VouchersListScreen> createState() => _VouchersListScreenState();
}

class _VouchersListScreenState extends State<VouchersListScreen> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  List<AccountsVoucherDtoWithLiteId> _vouchers = [];
  List<AccountsVoucherDtoWithLiteId> _filteredVouchers = [];
  String _selectedFilter = "All";
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadVouchers();
  }

  Future<void> _loadVouchers() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final controller =
          Provider.of<AccountsVouchersController>(context, listen: false);
      _vouchers = await controller.fetchLocalVouchers();
      _applyFilter();
    } catch (e) {
      print('Error loading vouchers: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _applyFilter() {
    setState(() {
      if (_selectedFilter == "All") {
        _filteredVouchers = _vouchers;
      } else {
        _filteredVouchers = _vouchers.where((voucher) {
          switch (_selectedFilter) {
            case "Receipt":
              return voucher.data?.receiptTypeInt ==
                  AccountsVoucharType.ReceiptVoucher.value;
            case "Payment":
              return voucher.data?.receiptTypeInt ==
                  AccountsVoucharType.PaymentVoucher.value;
            default:
              return true;
          }
        }).toList();
      }
    });
  }

  void _onRefresh() async {
    await _loadVouchers();
    _refreshController.refreshCompleted();
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Scaffold(
        backgroundColor: const Color(0xFFF8F9FD),
        body: Column(
          children: [
            // Header
            CommonHeader(
              icon: Icons.receipt_long,
              title: T("Vouchers List"),
              subtitle: T("View all vouchers"),
            ),

            // Filter Section
            _buildFilterSection(),

            // Content
            Expanded(
              child: SmartRefresher(
                controller: _refreshController,
                onRefresh: _onRefresh,
                header: const WaterDropHeader(),
                child: _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredVouchers.isEmpty
                        ? _buildEmptyState()
                        : _buildVouchersList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.filter_list, color: context.primaryColor, size: 20),
              const SizedBox(width: 8),
              Text(
                T("Filter by Type"),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            children: [
              _buildFilterChip("All", T("All")),
              _buildFilterChip("Receipt", T("Receipt Vouchers")),
              _buildFilterChip("Payment", T("Payment Vouchers")),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String value, String label) {
    final isSelected = _selectedFilter == value;
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _selectedFilter = value;
          });
          _applyFilter();
        }
      },
      backgroundColor: Colors.grey[100],
      selectedColor: context.primaryColor.withOpacity(0.2),
      checkmarkColor: context.primaryColor,
      labelStyle: TextStyle(
        color: isSelected ? context.primaryColor : Colors.grey[700],
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            T("No vouchers found"),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T("Create your first voucher to get started"),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildVouchersList() {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _filteredVouchers.length,
      itemBuilder: (context, index) {
        final voucher = _filteredVouchers[index];
        return _buildVoucherCard(voucher);
      },
    );
  }

  Widget _buildVoucherCard(AccountsVoucherDtoWithLiteId voucher) {
    final isReceiptVoucher = voucher.data?.receiptTypeInt ==
        AccountsVoucharType.ReceiptVoucher.value;
    final cardColor =
        isReceiptVoucher ? context.primaryColor : const Color(0xFFE53E3E);
    final iconData =
        isReceiptVoucher ? Icons.receipt_outlined : Icons.payment_outlined;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _showVoucherDetails(voucher),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header Row
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: cardColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      iconData,
                      color: cardColor,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          voucher.data?.code ?? T("Unknown"),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          voucher.data?.receiptType ?? T("Unknown Type"),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: cardColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      "\$${voucher.data?.voucharAmount?.toStringAsFixed(2) ?? '0.00'}",
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Details Row
              Row(
                children: [
                  _buildDetailItem(Icons.calendar_today,
                      voucher.data?.voucharDateFormated ?? ""),
                  const SizedBox(width: 16),
                  if (voucher.data?.customerName != null &&
                      voucher.data!.customerName!.isNotEmpty)
                    _buildDetailItem(Icons.person, voucher.data!.customerName!),
                ],
              ),

              if (voucher.data?.note != null &&
                  voucher.data!.note!.isNotEmpty) ...[
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    voucher.data!.note!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _showVoucherDetails(AccountsVoucherDtoWithLiteId voucher) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildVoucherDetailsSheet(voucher),
    );
  }

  Widget _buildVoucherDetailsSheet(AccountsVoucherDtoWithLiteId voucher) {
    final isReceiptVoucher = voucher.data?.receiptTypeInt ==
        AccountsVoucharType.ReceiptVoucher.value;
    final cardColor =
        isReceiptVoucher ? context.primaryColor : const Color(0xFFE53E3E);

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: cardColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  isReceiptVoucher
                      ? Icons.receipt_outlined
                      : Icons.payment_outlined,
                  color: cardColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      voucher.data?.code ?? T("Unknown"),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      voucher.data?.receiptType ?? T("Unknown Type"),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 20),

          // Amount
          _buildDetailRow(T("Amount"),
              "\$${voucher.data?.voucharAmount?.toStringAsFixed(2) ?? '0.00'}"),
          _buildDetailRow(T("Date"), voucher.data?.voucharDateFormated ?? ""),

          if (voucher.data?.customerName != null &&
              voucher.data!.customerName!.isNotEmpty)
            _buildDetailRow(T("Customer"), voucher.data!.customerName!),

          if (voucher.data?.invoiceNo != null &&
              voucher.data!.invoiceNo!.isNotEmpty)
            _buildDetailRow(T("Invoice No"), voucher.data!.invoiceNo!),

          if (voucher.data?.note != null && voucher.data!.note!.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              T("Notes"),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                voucher.data!.note!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
              ),
            ),
          ],

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }
}
