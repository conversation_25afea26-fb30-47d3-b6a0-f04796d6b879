import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/controllers/accounting_report_helper_controller.dart';
import 'package:inventory_application/controllers/printer_settings_controller.dart';
import 'package:inventory_application/services/report_printer_service.dart';
import 'package:inventory_application/models/dto/reports/card_detail_report_dto.dart';
import 'package:intl/intl.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:inventory_application/helpers/arabic_font_loader.dart';

class CardDetailReportScreen extends StatefulWidget {
  const CardDetailReportScreen({Key? key}) : super(key: key);

  @override
  State<CardDetailReportScreen> createState() => _CardDetailReportScreenState();
}

class _CardDetailReportScreenState extends State<CardDetailReportScreen> {
  final TextEditingController _cardNumberController = TextEditingController();

  // Sorting variables for different tables
  int _paymentSortColumnIndex = 0;
  bool _paymentSortAscending = true;
  int _rechargeSortColumnIndex = 0;
  bool _rechargeSortAscending = true;
  int _combinedSortColumnIndex = 0;
  bool _combinedSortAscending = true;

  // Pagination variables
  int _paymentCurrentPage = 0;
  int _rechargeCurrentPage = 0;
  int _combinedCurrentPage = 0;
  int _itemsPerPage = 10;
  final List<int> _itemsPerPageOptions = [
    10,
    20,
    50,
    100,
    -1
  ]; // -1 means show all

  // Sorted and paginated lists
  List<dynamic> _sortedPayments = [];
  List<dynamic> _sortedRecharges = [];
  List<dynamic> _paginatedPayments = [];
  List<dynamic> _paginatedRecharges = [];

  // Combined transactions for unified timeline
  List<Map<String, dynamic>> _sortedCombinedTransactions = [];
  List<Map<String, dynamic>> _paginatedCombinedTransactions = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _cardNumberController.dispose();
    super.dispose();
  }

  Future<void> _loadReport() async {
    if (_cardNumberController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.error_outline, color: Colors.white),
              SizedBox(width: 8),
              Text('يرجى إدخال رقم الكارت'),
            ],
          ),
          backgroundColor: Colors.red[600],
          behavior: SnackBarBehavior.floating,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
      return;
    }

    final controller = context.read<AccountingReportHelperController>();
    await controller.getCardDetailReport(_cardNumberController.text.trim());

    // Reset pagination and sort data
    setState(() {
      _paymentCurrentPage = 0;
      _rechargeCurrentPage = 0;
      _combinedCurrentPage = 0;
      _sortAndPaginateData();
    });
  }

  void _sortAndPaginateData() {
    final controller = context.read<AccountingReportHelperController>();
    final report = controller.cardDetailReport;

    if (report != null) {
      // Sort payments
      _sortedPayments = List.from(report.paymentDetails ?? []);
      _sortPayments();

      // Sort recharges
      _sortedRecharges = List.from(report.rechargeDetails ?? []);
      _sortRecharges();

      // Combine and sort transactions
      _combineCombinedTransactions();
    }
  }

  void _combineCombinedTransactions() {
    final controller = context.read<AccountingReportHelperController>();
    final report = controller.cardDetailReport;

    _sortedCombinedTransactions.clear();

    if (report != null) {
      // Add payment transactions
      for (var payment in report.paymentDetails ?? []) {
        _sortedCombinedTransactions.add({
          'type': 'payment',
          'date': payment.paymentDate ?? DateTime(1900),
          'amount': payment.paymentAmount ?? 0.0,
          'balanceAfter': payment.balanceAfter ?? 0.0,
          'description': payment.deviceName ?? 'غير محدد',
          'store': payment.storeName ?? '',
          'company': payment.companyName ?? '',
          'data': payment,
        });
      }

      // Add recharge transactions
      for (var recharge in report.rechargeDetails ?? []) {
        _sortedCombinedTransactions.add({
          'type': 'recharge',
          'date': recharge.date ?? DateTime(1900),
          'amount': recharge.totalAmount ?? 0.0,
          'balanceAfter': recharge.balanceAfter ?? 0.0,
          'description': 'عملية شحن',
          'rechargeAmount': recharge.rechargeAmount ?? 0.0,
          'giftAmount': recharge.giftAmount ?? 0.0,
          'admin': recharge.adminName ?? '',
          'data': recharge,
        });
      }

      _sortCombinedTransactions();
    }
  }

  void _sortPayments() {
    switch (_paymentSortColumnIndex) {
      case 0: // Date
        _sortedPayments.sort((a, b) {
          final dateA = a.paymentDate ?? DateTime(1900);
          final dateB = b.paymentDate ?? DateTime(1900);
          return _paymentSortAscending
              ? dateA.compareTo(dateB)
              : dateB.compareTo(dateA);
        });
        break;
      case 1: // Device
        _sortedPayments.sort((a, b) {
          final deviceA = a.deviceName ?? '';
          final deviceB = b.deviceName ?? '';
          return _paymentSortAscending
              ? deviceA.compareTo(deviceB)
              : deviceB.compareTo(deviceA);
        });
        break;
      case 2: // Amount
        _sortedPayments.sort((a, b) {
          final amountA = a.paymentAmount ?? 0.0;
          final amountB = b.paymentAmount ?? 0.0;
          return _paymentSortAscending
              ? amountA.compareTo(amountB)
              : amountB.compareTo(amountA);
        });
        break;
    }
    _updatePaymentPagination();
  }

  void _sortRecharges() {
    switch (_rechargeSortColumnIndex) {
      case 0: // Date
        _sortedRecharges.sort((a, b) {
          final dateA = a.date ?? DateTime(1900);
          final dateB = b.date ?? DateTime(1900);
          return _rechargeSortAscending
              ? dateA.compareTo(dateB)
              : dateB.compareTo(dateA);
        });
        break;
      case 1: // Recharge Amount
        _sortedRecharges.sort((a, b) {
          final amountA = a.rechargeAmount ?? 0.0;
          final amountB = b.rechargeAmount ?? 0.0;
          return _rechargeSortAscending
              ? amountA.compareTo(amountB)
              : amountB.compareTo(amountA);
        });
        break;
      case 2: // Total Amount
        _sortedRecharges.sort((a, b) {
          final amountA = a.totalAmount ?? 0.0;
          final amountB = b.totalAmount ?? 0.0;
          return _rechargeSortAscending
              ? amountA.compareTo(amountB)
              : amountB.compareTo(amountA);
        });
        break;
    }
    _updateRechargePagination();
  }

  void _sortCombinedTransactions() {
    switch (_combinedSortColumnIndex) {
      case 0: // Date
        _sortedCombinedTransactions.sort((a, b) {
          final dateA = a['date'] as DateTime;
          final dateB = b['date'] as DateTime;
          return _combinedSortAscending
              ? dateA.compareTo(dateB)
              : dateB.compareTo(dateA);
        });
        break;
      case 1: // Type
        _sortedCombinedTransactions.sort((a, b) {
          final typeA = a['type'] as String;
          final typeB = b['type'] as String;
          return _combinedSortAscending
              ? typeA.compareTo(typeB)
              : typeB.compareTo(typeA);
        });
        break;
      case 2: // Amount
        _sortedCombinedTransactions.sort((a, b) {
          final amountA = a['amount'] as double;
          final amountB = b['amount'] as double;
          return _combinedSortAscending
              ? amountA.compareTo(amountB)
              : amountB.compareTo(amountA);
        });
        break;
      case 3: // Balance After
        _sortedCombinedTransactions.sort((a, b) {
          final balanceA = a['balanceAfter'] as double;
          final balanceB = b['balanceAfter'] as double;
          return _combinedSortAscending
              ? balanceA.compareTo(balanceB)
              : balanceB.compareTo(balanceA);
        });
        break;
    }
    _updateCombinedPagination();
  }

  void _updatePaymentPagination() {
    if (_itemsPerPage == -1) {
      _paginatedPayments = List.from(_sortedPayments);
    } else {
      final startIndex = _paymentCurrentPage * _itemsPerPage;
      final endIndex =
          (startIndex + _itemsPerPage).clamp(0, _sortedPayments.length);
      _paginatedPayments = _sortedPayments.sublist(startIndex, endIndex);
    }
  }

  void _updateRechargePagination() {
    if (_itemsPerPage == -1) {
      _paginatedRecharges = List.from(_sortedRecharges);
    } else {
      final startIndex = _rechargeCurrentPage * _itemsPerPage;
      final endIndex =
          (startIndex + _itemsPerPage).clamp(0, _sortedRecharges.length);
      _paginatedRecharges = _sortedRecharges.sublist(startIndex, endIndex);
    }
  }

  void _updateCombinedPagination() {
    if (_itemsPerPage == -1) {
      _paginatedCombinedTransactions = List.from(_sortedCombinedTransactions);
    } else {
      final startIndex = _combinedCurrentPage * _itemsPerPage;
      final endIndex = (startIndex + _itemsPerPage)
          .clamp(0, _sortedCombinedTransactions.length);
      _paginatedCombinedTransactions =
          _sortedCombinedTransactions.sublist(startIndex, endIndex);
    }
  }

  void _onPaymentSort(int columnIndex, bool ascending) {
    setState(() {
      _paymentSortColumnIndex = columnIndex;
      _paymentSortAscending = ascending;
      _paymentCurrentPage = 0;
      _sortPayments();
    });
  }

  void _onRechargeSort(int columnIndex, bool ascending) {
    setState(() {
      _rechargeSortColumnIndex = columnIndex;
      _rechargeSortAscending = ascending;
      _rechargeCurrentPage = 0;
      _sortRecharges();
    });
  }

  void _onCombinedSort(int columnIndex, bool ascending) {
    setState(() {
      _combinedSortColumnIndex = columnIndex;
      _combinedSortAscending = ascending;
      _combinedCurrentPage = 0;
      _sortCombinedTransactions();
    });
  }

  String _getMemberStateText(int? memberState) {
    switch (memberState) {
      case 1:
        return 'نشط';
      case 0:
        return 'غير نشط';
      case 2:
        return 'محظور';
      default:
        return 'غير محدد';
    }
  }

  double _calculateTotalPayments(List<PaymentDetailDTO>? payments) {
    if (payments == null) return 0.0;
    return payments.fold(0.0, (sum, payment) {
      return sum + (payment.paymentAmount ?? 0.0);
    });
  }

  double _calculateTotalRecharges(List<RechargeDetailDTO>? recharges) {
    if (recharges == null) return 0.0;
    return recharges.fold(0.0, (sum, recharge) {
      return sum + (recharge.totalAmount ?? 0.0);
    });
  }

  // دالة معاينة وطباعة التقرير
  Future<void> _printReport() async {
    final controller = context.read<AccountingReportHelperController>();

    if (controller.cardDetailReport == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد بيانات للطباعة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // عرض خيارات المعاينة والطباعة
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.print, color: Colors.blue),
              SizedBox(width: 8),
              Text('طباعة التقرير'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('اختر العملية المطلوبة:'),
              SizedBox(height: 8),
              Text(
                '• المعاينة: لعرض التقرير قبل الطباعة\n• الطباعة المباشرة: للطباعة فوراً',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.cancel, color: Colors.grey),
              label: const Text('إلغاء'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _previewReport();
              },
              icon: const Icon(Icons.preview),
              label: const Text('معاينة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _directPrint();
              },
              icon: const Icon(Icons.print),
              label: const Text('طباعة مباشرة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  // دالة معاينة التقرير
  Future<void> _previewReport() async {
    final controller = context.read<AccountingReportHelperController>();

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('جاري تحضير معاينة التقرير...'),
            ],
          ),
        );
      },
    );

    try {
      final printerSettings = context.read<PrinterSettingsController>();

      // إنشاء PDF
      final pdf = await _createCardDetailReportPdf(controller, printerSettings);

      Navigator.of(context).pop(); // إغلاق مؤشر التحميل

      // عرض معاينة التقرير
      await Printing.layoutPdf(
        onLayout: (_) async => await pdf.save(),
        name:
            'معاينة تقرير تفاصيل الكارت - ${_cardNumberController.text.trim()}',
        format: PdfPageFormat.a4,
      );

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('تم عرض معاينة التقرير بنجاح'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء معاينة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // دالة الطباعة المباشرة
  Future<void> _directPrint() async {
    final controller = context.read<AccountingReportHelperController>();

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('جاري تحضير التقرير للطباعة...'),
            ],
          ),
        );
      },
    );

    try {
      final printerSettings = context.read<PrinterSettingsController>();

      // إنشاء PDF
      final pdf = await _createCardDetailReportPdf(controller, printerSettings);

      Navigator.of(context).pop(); // إغلاق مؤشر التحميل

      // طباعة التقرير مباشرة
      await _printReportPdf(pdf, printerSettings);
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء طباعة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // إنشاء PDF لتقرير تفاصيل الكارت
  Future<pw.Document> _createCardDetailReportPdf(
    AccountingReportHelperController controller,
    PrinterSettingsController printerSettings,
  ) async {
    final pdf = pw.Document();
    final arabicFont = await _loadArabicFont();
    final pageFormat = PdfPageFormat.a4;
    final report = controller.cardDetailReport!;

    pdf.addPage(
      pw.MultiPage(
        pageFormat: pageFormat,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => [
          // رأس التقرير
          _buildReportHeader(arabicFont, printerSettings),
          pw.SizedBox(height: 15),

          // معلومات الكارت
          _buildCardInfoSectionPdf(arabicFont, report),
          pw.SizedBox(height: 20),

          // ملخص الرصيد
          _buildBalanceSummarySectionPdf(arabicFont, report),
          pw.SizedBox(height: 20),

          // جدول المعاملات المدمج
          ..._buildCombinedTransactionsTable(arabicFont, report),

          pw.SizedBox(height: 20),

          // ذيل التقرير
          _buildReportFooter(arabicFont, printerSettings),
        ],
      ),
    );

    return pdf;
  }

  // بناء رأس التقرير
  pw.Widget _buildReportHeader(
      pw.Font arabicFont, PrinterSettingsController printerSettings) {
    return pw.Column(
      children: [
        // شعار الشركة إذا كان مفعلاً
        if (printerSettings.printLogo && printerSettings.logoPath != null)
          pw.Center(
            child: pw.Container(
              width: 100,
              height: 40,
              decoration: pw.BoxDecoration(
                border: pw.Border.all(width: 1),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Center(
                child: pw.Text(
                  'الشعار',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
            ),
          ),

        if (printerSettings.printLogo) pw.SizedBox(height: 10),

        // رأس مخصص
        if (printerSettings.receiptHeader.isNotEmpty)
          pw.Center(
            child: pw.Text(
              printerSettings.receiptHeader,
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),

        if (printerSettings.receiptHeader.isNotEmpty) pw.SizedBox(height: 8),

        // عنوان التقرير
        pw.Center(
          child: pw.Text(
            'تقرير تفاصيل الكارت',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),

        pw.SizedBox(height: 5),

        // خط فاصل
        pw.Container(
          width: double.infinity,
          height: 1,
          color: PdfColors.grey400,
        ),
      ],
    );
  }

  // بناء قسم معلومات الكارت للطباعة
  pw.Widget _buildCardInfoSectionPdf(
      pw.Font arabicFont, CardDetailReportDTO report) {
    final cardInfo = report.cardInfo;
    if (cardInfo == null) return pw.SizedBox.shrink();

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.indigo50,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.indigo200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'معلومات الكارت',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.indigo700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 12),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.indigo200),
            columnWidths: {
              0: const pw.FlexColumnWidth(1),
              1: const pw.FlexColumnWidth(2),
            },
            children: [
              _buildInfoRow(
                  'رقم الكارت', cardInfo.cardNumber ?? 'غير محدد', arabicFont),
              _buildInfoRow(
                  'اسم العضو', cardInfo.memberName ?? 'غير محدد', arabicFont),
              _buildInfoRow(
                  'رقم الجوال', cardInfo.mobile ?? 'غير محدد', arabicFont),
              _buildInfoRow(
                  'تاريخ التسجيل',
                  cardInfo.memberSince != null
                      ? DateFormat('yyyy-MM-dd').format(cardInfo.memberSince!)
                      : 'غير محدد',
                  arabicFont),
              _buildInfoRow(
                  'الرصيد الحالي',
                  '${cardInfo.currentBalance?.toStringAsFixed(2) ?? '0'}',
                  arabicFont,
                  isAmount: true),
            ],
          ),
        ],
      ),
    );
  }

  // بناء صف المعلومات
  pw.TableRow _buildInfoRow(String label, String value, pw.Font arabicFont,
      {bool isAmount = false}) {
    return pw.TableRow(
      children: [
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            label,
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 11,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.indigo700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
        pw.Padding(
          padding: const pw.EdgeInsets.all(8),
          child: pw.Text(
            value,
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 11,
              fontWeight: isAmount ? pw.FontWeight.bold : pw.FontWeight.normal,
              color: isAmount ? PdfColors.green700 : PdfColors.black,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      ],
    );
  }

  // بناء قسم ملخص الرصيد للطباعة
  pw.Widget _buildBalanceSummarySectionPdf(
      pw.Font arabicFont, CardDetailReportDTO report) {
    final balanceSummary = report.balanceSummary;
    if (balanceSummary == null) return pw.SizedBox.shrink();

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.green50,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.green200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص الرصيد',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.green700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 12),
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.green200),
            columnWidths: {
              0: const pw.FlexColumnWidth(2),
              1: const pw.FlexColumnWidth(1),
              2: const pw.FlexColumnWidth(2),
              3: const pw.FlexColumnWidth(1),
            },
            children: [
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.green100),
                children: [
                  _buildTableCell('إجمالي الشحن', arabicFont, isHeader: true),
                  _buildTableCell(
                      '${balanceSummary.totalRechargeAmount?.toStringAsFixed(2) ?? '0'}',
                      arabicFont,
                      isHeader: true),
                  _buildTableCell('إجمالي الإنفاق', arabicFont, isHeader: true),
                  _buildTableCell(
                      '${balanceSummary.totalSpentAmount?.toStringAsFixed(2) ?? '0'}',
                      arabicFont,
                      isHeader: true),
                ],
              ),
              pw.TableRow(
                children: [
                  _buildTableCell('إجمالي الهدايا', arabicFont),
                  _buildTableCell(
                      '${balanceSummary.totalGiftAmount?.toStringAsFixed(2) ?? '0'}',
                      arabicFont,
                      color: PdfColors.purple700),
                  _buildTableCell('معاملات الشحن', arabicFont),
                  _buildTableCell(
                      '${balanceSummary.totalRechargeTransactions?.toString() ?? '0'}',
                      arabicFont,
                      color: PdfColors.blue700),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء جدول المعاملات المدمج مع تقسيم الصفحات لتجنب المشكلة
  List<pw.Widget> _buildCombinedTransactionsTable(
      pw.Font arabicFont, CardDetailReportDTO report) {
    if (_sortedCombinedTransactions.isEmpty) {
      return [
        pw.Center(
          child: pw.Text(
            'لا توجد معاملات',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 14,
              color: PdfColors.grey600,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      ];
    }

    List<pw.Widget> widgets = [];

    // عنوان القسم
    widgets.add(
      pw.Text(
        'السجل الزمني للمعاملات',
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: 16,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.red700,
        ),
        textDirection: pw.TextDirection.rtl,
      ),
    );

    widgets.add(pw.SizedBox(height: 5));

    // تحديد العدد الأقصى للمعاملات في PDF لتجنب إنشاء صفحات كثيرة
    const int maxTransactionsInPdf = 50;
    final transactionsToShow =
        _sortedCombinedTransactions.length > maxTransactionsInPdf
            ? _sortedCombinedTransactions.take(maxTransactionsInPdf).toList()
            : _sortedCombinedTransactions;

    widgets.add(
      pw.Text(
        _sortedCombinedTransactions.length > maxTransactionsInPdf
            ? 'عرض أحدث $maxTransactionsInPdf معاملة من إجمالي ${_sortedCombinedTransactions.length} معاملة'
            : 'إجمالي المعاملات: ${_sortedCombinedTransactions.length}',
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: 12,
          color: PdfColors.red600,
          fontWeight: pw.FontWeight.bold,
        ),
        textDirection: pw.TextDirection.rtl,
      ),
    );

    widgets.add(pw.SizedBox(height: 10));

    // تقسيم المعاملات إلى مجموعات صغيرة لضمان عدم إنشاء صفحات كثيرة
    const int itemsPerTable = 15;

    for (int i = 0; i < transactionsToShow.length; i += itemsPerTable) {
      final endIndex = (i + itemsPerTable).clamp(0, transactionsToShow.length);
      final chunk = transactionsToShow.sublist(i, endIndex);

      // إضافة عنوان فرعي للمجموعات إذا كان العدد كبيراً
      if (transactionsToShow.length > itemsPerTable && i > 0) {
        widgets.add(pw.SizedBox(height: 15));
        widgets.add(
          pw.Text(
            'المعاملات ${i + 1} - $endIndex',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        );
        widgets.add(pw.SizedBox(height: 8));
      }

      // إنشاء الجدول للمجموعة الحالية
      widgets.add(
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey400),
          columnWidths: {
            0: const pw.FlexColumnWidth(2.5),
            1: const pw.FlexColumnWidth(1.5),
            2: const pw.FlexColumnWidth(1.5),
            3: const pw.FlexColumnWidth(1.5),
            4: const pw.FlexColumnWidth(2.5),
            5: const pw.FlexColumnWidth(2),
          },
          children: [
            // Header row - only for first table or when starting new page
            if (i == 0 || i % (itemsPerTable * 2) == 0)
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.indigo100),
                children: [
                  _buildTableCell('التاريخ', arabicFont, isHeader: true),
                  _buildTableCell('نوع العملية', arabicFont, isHeader: true),
                  _buildTableCell('المبلغ', arabicFont, isHeader: true),
                  _buildTableCell('الرصيد بعد', arabicFont, isHeader: true),
                  _buildTableCell('التفاصيل', arabicFont, isHeader: true),
                  _buildTableCell('معلومات إضافية', arabicFont, isHeader: true),
                ],
              ),
            // Data rows
            ...chunk.map((transaction) {
              final isRecharge = transaction['type'] == 'recharge';
              return pw.TableRow(
                decoration: pw.BoxDecoration(
                  color: isRecharge ? PdfColors.green50 : PdfColors.red50,
                ),
                children: [
                  _buildTableCell(
                    DateFormat('yyyy-MM-dd HH:mm').format(transaction['date']),
                    arabicFont,
                  ),
                  _buildTableCell(
                    isRecharge ? 'شحن' : 'صرف',
                    arabicFont,
                    color: isRecharge ? PdfColors.green700 : PdfColors.red700,
                  ),
                  _buildTableCell(
                    '${transaction['amount'].toStringAsFixed(2)}',
                    arabicFont,
                    color: isRecharge ? PdfColors.green700 : PdfColors.red700,
                  ),
                  _buildTableCell(
                    '${transaction['balanceAfter'].toStringAsFixed(2)}',
                    arabicFont,
                  ),
                  _buildTableCell(
                    transaction['description'] ?? '',
                    arabicFont,
                  ),
                  _buildTableCell(
                    _getAdditionalInfo(transaction),
                    arabicFont,
                    fontSize: 8,
                  ),
                ],
              );
            }),
          ],
        ),
      );
    }

    // إضافة ملاحظة إذا تم تقليص عدد المعاملات
    if (_sortedCombinedTransactions.length > maxTransactionsInPdf) {
      widgets.add(pw.SizedBox(height: 10));
      widgets.add(
        pw.Container(
          padding: const pw.EdgeInsets.all(8),
          decoration: pw.BoxDecoration(
            color: PdfColors.orange50,
            borderRadius: pw.BorderRadius.circular(4),
            border: pw.Border.all(color: PdfColors.orange200),
          ),
          child: pw.Text(
            'ملاحظة: تم عرض أحدث $maxTransactionsInPdf معاملة فقط في هذا التقرير المطبوع. للاطلاع على جميع المعاملات يرجى استخدام التقرير الرقمي.',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 10,
              color: PdfColors.orange700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      );
    }

    return widgets;
  }

  // الحصول على المعلومات الإضافية للمعاملة
  String _getAdditionalInfo(Map<String, dynamic> transaction) {
    final isRecharge = transaction['type'] == 'recharge';
    List<String> info = [];

    if (isRecharge) {
      if (transaction['rechargeAmount'] != null &&
          transaction['rechargeAmount'] > 0) {
        info.add('شحن: ${transaction['rechargeAmount'].toStringAsFixed(2)}');
      }
      if (transaction['giftAmount'] != null && transaction['giftAmount'] > 0) {
        info.add('هدية: ${transaction['giftAmount'].toStringAsFixed(2)}');
      }
      if (transaction['admin'] != null && transaction['admin'].isNotEmpty) {
        info.add('مشرف: ${transaction['admin']}');
      }
    } else {
      if (transaction['store'] != null && transaction['store'].isNotEmpty) {
        info.add('متجر: ${transaction['store']}');
      }
      if (transaction['company'] != null && transaction['company'].isNotEmpty) {
        info.add('شركة: ${transaction['company']}');
      }
    }

    return info.join('\n');
  }

  // بناء خلية الجدول
  pw.Widget _buildTableCell(
    String text,
    pw.Font arabicFont, {
    bool isHeader = false,
    PdfColor? color,
    double? fontSize,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: fontSize ?? (isHeader ? 10 : 9),
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: color ?? (isHeader ? PdfColors.black : PdfColors.black),
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // بناء ذيل التقرير
  pw.Widget _buildReportFooter(
      pw.Font arabicFont, PrinterSettingsController printerSettings) {
    return pw.Column(
      children: [
        pw.Container(
          width: double.infinity,
          height: 1,
          color: PdfColors.grey400,
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'تاريخ الطباعة: ${DateTime.now().toString().split('.')[0]}',
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 10,
            color: PdfColors.grey600,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        if (printerSettings.receiptFooter.isNotEmpty) ...[
          pw.SizedBox(height: 8),
          pw.Center(
            child: pw.Text(
              printerSettings.receiptFooter,
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 10,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),
        ],
        pw.SizedBox(height: 8),
        pw.Center(
          child: pw.Text(
            'شكراً لاستخدام نظامنا Pal4it',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 9,
              color: PdfColors.grey600,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      ],
    );
  }

  // تحميل الخط للتقرير - استخدام خدمة الخطوط الموحدة
  // ✅ هذا الحل يضمن عمل الخطوط العربية في النسخة المُجمعة
  Future<pw.Font> _loadArabicFont() async {
    try {
      // استخدام خدمة تحميل الخطوط العربية الموحدة
      return await ArabicFontLoader.getSafeArabicFont();
    } catch (e) {
      print('⚠️ Arabic font loading failed in CardDetailReport: $e');
      // استخدام الخط الافتراضي كبديل أخير
      return pw.Font.helvetica();
    }
  }

  // الحصول على تنسيق الصفحة

  // طباعة PDF
  Future<void> _printReportPdf(
      pw.Document pdf, PrinterSettingsController printerSettings) async {
    try {
      final bool isMobile =
          !Platform.isWindows && !Platform.isLinux && !Platform.isMacOS;

      // إذا كان على الهاتف وإعدادات الطابعة غير مكونة
      if (isMobile && printerSettings.printerType == null) {
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name: 'تقرير تفاصيل الكارت - ${_cardNumberController.text.trim()}',
          format: PdfPageFormat.a4, // _getPageFormat(printerSettings),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال التقرير للطباعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
        return;
      }

      // طباعة حسب نوع الطابعة
      if (printerSettings.printerType == 'usb') {
        final printers = await Printing.listPrinters();
        final selectedPrinter = printers.firstWhere(
          (printer) => printer.name == printerSettings.selectedPrinterName,
          orElse: () => throw Exception('الطابعة المحددة غير متوفرة'),
        );

        final result = await Printing.directPrintPdf(
          printer: selectedPrinter,
          onLayout: (_) async => await pdf.save(),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  result ? 'تم طباعة التقرير بنجاح' : 'فشل في طباعة التقرير'),
              backgroundColor: result ? Colors.green : Colors.red,
            ),
          );
        }
      } else {
        // طباعة عادية أو شبكة أو بلوتوث
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name: 'تقرير تفاصيل الكارت - ${_cardNumberController.text.trim()}',
          format: PdfPageFormat.a4,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال التقرير للطباعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في طباعة التقرير: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في طباعة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      rethrow;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.credit_card, color: Colors.white),
            SizedBox(width: 8),
            Text('تقرير تفاصيل الكارت'),
          ],
        ),
        backgroundColor: Colors.indigo[700],
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تقرير شامل لتفاصيل الكارت مع سجل المعاملات'),
                  duration: Duration(seconds: 3),
                ),
              );
            },
          ),
          Consumer<AccountingReportHelperController>(
            builder: (context, controller, child) {
              if (controller.cardDetailReport != null) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.preview),
                      tooltip: 'معاينة التقرير',
                      onPressed: _previewReport,
                    ),
                    IconButton(
                      icon: const Icon(Icons.print),
                      tooltip: 'طباعة التقرير',
                      onPressed: _printReport,
                    ),
                  ],
                );
              }
              return const SizedBox.shrink();
            },
          ),
        ],
      ),
      body: Consumer<AccountingReportHelperController>(
        builder: (context, controller, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Search Section
                _buildSearchSection(controller),
                const SizedBox(height: 24),

                // Card Info and Summary Section
                if (controller.cardDetailReport != null) ...[
                  _buildCardInfoSection(controller),
                  const SizedBox(height: 24),

                  _buildBalanceSummarySection(controller),
                  const SizedBox(height: 24),

                  _buildCombinedTransactionsSection(controller),
                  const SizedBox(height: 24),
                  // Payment Details Section
                  // _buildPaymentDetailsSection(controller),
                  // const SizedBox(height: 24),

                  // // Recharge Details Section
                  // _buildRechargeDetailsSection(controller),

                  // Combined Transactions Timeline
                ],

                // Loading indicator
                if (controller.isLoadingCardDetail)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(50),
                      child: CircularProgressIndicator(),
                    ),
                  ),

                // Empty state
                if (!controller.isLoadingCardDetail &&
                    controller.cardDetailReport == null)
                  Center(
                    child: Padding(
                      padding: const EdgeInsets.all(50),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(40),
                            decoration: BoxDecoration(
                              color: Colors.indigo[50],
                              shape: BoxShape.circle,
                            ),
                            child: Icon(Icons.search,
                                size: 80, color: Colors.indigo[300]),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'ابحث عن كارت',
                            style: TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700]),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'أدخل رقم الكارت واضغط بحث لعرض التفاصيل',
                            style: TextStyle(
                                fontSize: 16, color: Colors.grey[500]),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildSearchSection(AccountingReportHelperController controller) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.search, color: Colors.indigo[600], size: 24),
                const SizedBox(width: 8),
                Text(
                  'البحث عن الكارت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _cardNumberController,
                    decoration: InputDecoration(
                      labelText: 'رقم الكارت',
                      prefixIcon:
                          Icon(Icons.credit_card, color: Colors.indigo[600]),
                      border: const OutlineInputBorder(),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.indigo[600]!),
                      ),
                    ),
                    keyboardType: TextInputType.number,
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed:
                      controller.isLoadingCardDetail ? null : _loadReport,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo[600],
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                        horizontal: 24, vertical: 16),
                  ),
                  icon: controller.isLoadingCardDetail
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                              strokeWidth: 2, color: Colors.white),
                        )
                      : const Icon(Icons.search),
                  label: Text(
                      controller.isLoadingCardDetail ? 'جاري البحث...' : 'بحث'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardInfoSection(AccountingReportHelperController controller) {
    final cardInfo = controller.cardDetailReport?.cardInfo;
    if (cardInfo == null) return const SizedBox.shrink();

    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [Colors.indigo[500]!, Colors.indigo[700]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(28),
          child: Column(
            children: [
              // Header with card icon and number
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.credit_card,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'رقم الكارت',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          cardInfo.cardNumber ?? '',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 1.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // Balance section

              _buildCardInfoRow(
                'الرصيد الحالي',
                '${cardInfo.currentBalance?.toStringAsFixed(2) ?? '0'} ',
                Icons.balance,
              ),
              const SizedBox(height: 24),

              // Member information grid
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معلومات العضو',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildCardInfoRow(
                      'اسم العضو',
                      cardInfo.memberName ?? 'غير محدد',
                      Icons.person_outline,
                    ),
                    const SizedBox(height: 12),
                    _buildCardInfoRow(
                      'رقم الجوال',
                      cardInfo.mobile ?? 'غير محدد',
                      Icons.phone,
                    ),
                    const SizedBox(height: 12),
                    _buildCardInfoRow(
                      'تاريخ التسجيل',
                      cardInfo.memberSince != null
                          ? DateFormat('yyyy-MM-dd')
                              .format(cardInfo.memberSince!)
                          : 'غير محدد',
                      Icons.calendar_today,
                    ),
                    // const SizedBox(height: 12),
                    // _buildCardInfoRow(
                    //   'حالة العضو',
                    //   cardInfo.memberState == 1 ? 'نشط ✓' : 'غير نشط ✗',
                    //   cardInfo.memberState == 1
                    //       ? Icons.check_circle
                    //       : Icons.cancel,
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCardInfoRow(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, size: 16, color: Colors.white),
          ),
          const SizedBox(width: 12),
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const Text(
            ': ',
            style:
                TextStyle(color: Colors.white70, fontWeight: FontWeight.w500),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceSummarySection(
      AccountingReportHelperController controller) {
    final balanceSummary = controller.cardDetailReport?.balanceSummary;
    if (balanceSummary == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.green[600], size: 24),
                const SizedBox(width: 8),
                Text(
                  'ملخص الرصيد',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildSummaryCard(
                  title: 'إجمالي الشحن',
                  value:
                      '${balanceSummary.totalRechargeAmount?.toStringAsFixed(2) ?? '0'}  ',
                  icon: Icons.battery_charging_full,
                  color: Colors.green,
                ),
                _buildSummaryCard(
                  title: 'إجمالي الإنفاق',
                  value:
                      '${balanceSummary.totalSpentAmount?.toStringAsFixed(2) ?? '0'}  ',
                  icon: Icons.shopping_cart,
                  color: Colors.red,
                ),
                _buildSummaryCard(
                  title: 'إجمالي الهدايا',
                  value:
                      '${balanceSummary.totalGiftAmount?.toStringAsFixed(2) ?? '0'}  ',
                  icon: Icons.card_giftcard,
                  color: Colors.purple,
                ),
                _buildSummaryCard(
                  title: 'معاملات الشحن',
                  value: balanceSummary.totalRechargeTransactions?.toString() ??
                      '0',
                  icon: Icons.repeat,
                  color: Colors.teal,
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDetailsSection(
      AccountingReportHelperController controller) {
    final payments = controller.cardDetailReport?.paymentDetails ?? [];

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.payment, color: Colors.red[600], size: 24),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل المدفوعات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[700],
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.red[50],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'إجمالي: ${payments.length} عملية',
                    style: TextStyle(
                      color: Colors.red[700],
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (payments.isEmpty)
              _buildEmptyState(
                icon: Icons.payment,
                title: 'لا توجد مدفوعات',
                subtitle: 'لم يتم العثور على أي معاملات دفع لهذا الكارت',
              )
            else ...[
              _buildPaymentDataTable(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRechargeDetailsSection(
      AccountingReportHelperController controller) {
    final recharges = controller.cardDetailReport?.rechargeDetails ?? [];

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.battery_charging_full,
                    color: Colors.green[600], size: 24),
                const SizedBox(width: 8),
                Text(
                  'تفاصيل الشحن',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.green[50],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'إجمالي: ${recharges.length} عملية',
                    style: TextStyle(
                      color: Colors.green[700],
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (recharges.isEmpty)
              _buildEmptyState(
                icon: Icons.battery_charging_full,
                title: 'لا توجد عمليات شحن',
                subtitle: 'لم يتم العثور على أي عمليات شحن لهذا الكارت',
              )
            else ...[
              _buildRechargeDataTable(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCombinedTransactionsSection(
      AccountingReportHelperController controller) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.timeline, color: Colors.indigo[600], size: 24),
                const SizedBox(width: 8),
                Text(
                  'السجل الزمني للمعاملات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo[700],
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.indigo[50],
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'إجمالي: ${_sortedCombinedTransactions.length} حركة',
                    style: TextStyle(
                      color: Colors.indigo[700],
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_sortedCombinedTransactions.isEmpty)
              _buildEmptyState(
                icon: Icons.timeline,
                title: 'لا توجد حركات',
                subtitle: 'لم يتم العثور على أي معاملات لهذا الكارت',
              )
            else ...[
              _buildCombinedDataTable(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentDataTable() {
    return Column(
      children: [
        _buildPaginationControls(_sortedPayments.length, 'payment'),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            sortColumnIndex: _paymentSortColumnIndex,
            sortAscending: _paymentSortAscending,
            headingRowColor:
                MaterialStateProperty.all(Colors.red.withOpacity(0.1)),
            columns: [
              DataColumn(
                label: const Text('التاريخ',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                onSort: _onPaymentSort,
              ),
              DataColumn(
                label: const Text('الجهاز',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                onSort: _onPaymentSort,
              ),
              DataColumn(
                label: const Text('المبلغ',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                numeric: true,
                onSort: _onPaymentSort,
              ),
              const DataColumn(
                label: Text('الرصيد بعد',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                numeric: true,
              ),
              const DataColumn(
                label: Text('المتجر/الشركة',
                    style: TextStyle(fontWeight: FontWeight.bold)),
              ),
            ],
            rows: _paginatedPayments.map((payment) {
              return DataRow(cells: [
                DataCell(Text(
                  payment.paymentDate != null
                      ? DateFormat('yyyy-MM-dd HH:mm')
                          .format(payment.paymentDate!)
                      : '',
                  style: const TextStyle(fontSize: 12),
                )),
                DataCell(Text(payment.deviceName ?? '',
                    style: const TextStyle(fontSize: 12))),
                DataCell(Text(
                  '${payment.paymentAmount?.toStringAsFixed(2) ?? '0'}  ',
                  style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.red),
                )),
                DataCell(Text(
                  '${payment.balanceAfter?.toStringAsFixed(2) ?? '0'}  ',
                  style: const TextStyle(fontSize: 12),
                )),
                DataCell(Text(
                  '${payment.storeName ?? ''} ${payment.companyName ?? ''}',
                  style: const TextStyle(fontSize: 12),
                )),
              ]);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildRechargeDataTable() {
    return Column(
      children: [
        _buildPaginationControls(_sortedRecharges.length, 'recharge'),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            sortColumnIndex: _rechargeSortColumnIndex,
            sortAscending: _rechargeSortAscending,
            headingRowColor:
                MaterialStateProperty.all(Colors.green.withOpacity(0.1)),
            columns: [
              DataColumn(
                label: const Text('التاريخ',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                onSort: _onRechargeSort,
              ),
              DataColumn(
                label: const Text('مبلغ الشحن',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                numeric: true,
                onSort: _onRechargeSort,
              ),
              DataColumn(
                label: const Text('الإجمالي',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                numeric: true,
                onSort: _onRechargeSort,
              ),
              const DataColumn(
                label: Text('الهدية',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                numeric: true,
              ),
              const DataColumn(
                label: Text('الرصيد بعد',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                numeric: true,
              ),
              const DataColumn(
                label: Text('المشرف',
                    style: TextStyle(fontWeight: FontWeight.bold)),
              ),
            ],
            rows: _paginatedRecharges.map((recharge) {
              return DataRow(cells: [
                DataCell(Text(
                  recharge.date != null
                      ? DateFormat('yyyy-MM-dd HH:mm').format(recharge.date!)
                      : '',
                  style: const TextStyle(fontSize: 12),
                )),
                DataCell(Text(
                  '${recharge.rechargeAmount?.toStringAsFixed(2) ?? '0'}  ',
                  style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue),
                )),
                DataCell(Text(
                  '${recharge.totalAmount?.toStringAsFixed(2) ?? '0'}  ',
                  style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.green),
                )),
                DataCell(Text(
                  '${recharge.giftAmount?.toStringAsFixed(2) ?? '0'}  ',
                  style: const TextStyle(fontSize: 12, color: Colors.purple),
                )),
                DataCell(Text(
                  '${recharge.balanceAfter?.toStringAsFixed(2) ?? '0'}  ',
                  style: const TextStyle(fontSize: 12),
                )),
                DataCell(Text(recharge.adminName ?? '',
                    style: const TextStyle(fontSize: 12))),
              ]);
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildCombinedDataTable() {
    return Column(
      children: [
        _buildPaginationControls(
            _sortedCombinedTransactions.length, 'combined'),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            'انقر على رأس العمود للترتيب • جميع المعاملات مرتبة زمنياً',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minWidth: MediaQuery.of(context).size.width - 32,
            ),
            child: DataTable(
              sortColumnIndex: _combinedSortColumnIndex,
              sortAscending: _combinedSortAscending,
              headingRowColor:
                  MaterialStateProperty.all(Colors.indigo.withOpacity(0.1)),
              columnSpacing: 20,
              columns: [
                DataColumn(
                  label: const Text('التاريخ',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onSort: _onCombinedSort,
                ),
                DataColumn(
                  label: const Text('نوع العملية',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  onSort: _onCombinedSort,
                ),
                DataColumn(
                  label: const Text('المبلغ',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  numeric: true,
                  onSort: _onCombinedSort,
                ),
                DataColumn(
                  label: const Text('الرصيد بعد العملية',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  numeric: true,
                  onSort: _onCombinedSort,
                ),
                const DataColumn(
                  label: Text('التفاصيل',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ),
                const DataColumn(
                  label: Text('معلومات إضافية',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                ),
              ],
              rows: _paginatedCombinedTransactions.map((transaction) {
                final isRecharge = transaction['type'] == 'recharge';
                final isPayment = transaction['type'] == 'payment';

                return DataRow(
                  color: MaterialStateProperty.all(isRecharge
                      ? Colors.green.withOpacity(0.05)
                      : Colors.red.withOpacity(0.05)),
                  cells: [
                    DataCell(
                      Text(
                        DateFormat('yyyy-MM-dd HH:mm')
                            .format(transaction['date']),
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    DataCell(
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color:
                              isRecharge ? Colors.green[100] : Colors.red[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              isRecharge
                                  ? Icons.add_circle
                                  : Icons.remove_circle,
                              size: 16,
                              color: isRecharge
                                  ? Colors.green[700]
                                  : Colors.red[700],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              isRecharge ? 'شحن' : 'صرف',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                                color: isRecharge
                                    ? Colors.green[700]
                                    : Colors.red[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    DataCell(
                      Text(
                        '${transaction['amount'].toStringAsFixed(2)}  ',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color:
                              isRecharge ? Colors.green[700] : Colors.red[700],
                        ),
                      ),
                    ),
                    DataCell(
                      Text(
                        '${transaction['balanceAfter'].toStringAsFixed(2)}  ',
                        style: const TextStyle(
                            fontSize: 12, fontWeight: FontWeight.w600),
                      ),
                    ),
                    DataCell(
                      Text(
                        transaction['description'],
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    DataCell(
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (isRecharge) ...[
                            if (transaction['rechargeAmount'] != null &&
                                transaction['rechargeAmount'] > 0)
                              Text(
                                'شحن: ${transaction['rechargeAmount'].toStringAsFixed(2)}  ',
                                style: const TextStyle(
                                    fontSize: 10, color: Colors.blue),
                              ),
                            if (transaction['giftAmount'] != null &&
                                transaction['giftAmount'] > 0)
                              Text(
                                'هدية: ${transaction['giftAmount'].toStringAsFixed(2)}  ',
                                style: const TextStyle(
                                    fontSize: 10, color: Colors.purple),
                              ),
                            if (transaction['admin'] != null &&
                                transaction['admin'].isNotEmpty)
                              Text(
                                'مشرف: ${transaction['admin']}',
                                style: const TextStyle(
                                    fontSize: 10, color: Colors.grey),
                              ),
                          ] else if (isPayment) ...[
                            if (transaction['store'] != null &&
                                transaction['store'].isNotEmpty)
                              Text(
                                'متجر: ${transaction['store']}',
                                style: const TextStyle(
                                    fontSize: 10, color: Colors.orange),
                              ),
                            if (transaction['company'] != null &&
                                transaction['company'].isNotEmpty)
                              Text(
                                'شركة: ${transaction['company']}',
                                style: const TextStyle(
                                    fontSize: 10, color: Colors.teal),
                              ),
                          ],
                        ],
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildPaginationControls(int totalItems, String type) {
    if (_itemsPerPage == -1) return const SizedBox.shrink();

    int currentPage;
    int totalPages = (totalItems / _itemsPerPage).ceil();

    switch (type) {
      case 'payment':
        currentPage = _paymentCurrentPage;
        break;
      case 'recharge':
        currentPage = _rechargeCurrentPage;
        break;
      case 'combined':
        currentPage = _combinedCurrentPage;
        break;
      default:
        currentPage = 0;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          const Text('عرض:',
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14)),
          const SizedBox(width: 8),
          DropdownButton<int>(
            value: _itemsPerPage,
            underline: const SizedBox(),
            items: _itemsPerPageOptions.map((int value) {
              return DropdownMenuItem<int>(
                value: value,
                child: Text(value == -1 ? 'الكل' : value.toString()),
              );
            }).toList(),
            onChanged: (int? newValue) {
              setState(() {
                _itemsPerPage = newValue!;
                _paymentCurrentPage = 0;
                _rechargeCurrentPage = 0;
                _combinedCurrentPage = 0;
                _sortAndPaginateData();
              });
            },
          ),
          const SizedBox(width: 8),
          Text(
            _itemsPerPage == -1
                ? 'عرض جميع العناصر ($totalItems)'
                : 'عرض ${(currentPage * _itemsPerPage) + 1} - ${((currentPage + 1) * _itemsPerPage).clamp(0, totalItems)} من $totalItems',
            style: const TextStyle(fontSize: 13, color: Colors.grey),
          ),
          const Spacer(),
          if (totalPages > 1) ...[
            IconButton(
              onPressed: currentPage > 0
                  ? () {
                      setState(() {
                        switch (type) {
                          case 'payment':
                            _paymentCurrentPage--;
                            _updatePaymentPagination();
                            break;
                          case 'recharge':
                            _rechargeCurrentPage--;
                            _updateRechargePagination();
                            break;
                          case 'combined':
                            _combinedCurrentPage--;
                            _updateCombinedPagination();
                            break;
                        }
                      });
                    }
                  : null,
              icon: const Icon(Icons.chevron_right),
            ),
            Text('${currentPage + 1} من $totalPages'),
            IconButton(
              onPressed: currentPage < totalPages - 1
                  ? () {
                      setState(() {
                        switch (type) {
                          case 'payment':
                            _paymentCurrentPage++;
                            _updatePaymentPagination();
                            break;
                          case 'recharge':
                            _rechargeCurrentPage++;
                            _updateRechargePagination();
                            break;
                          case 'combined':
                            _combinedCurrentPage++;
                            _updateCombinedPagination();
                            break;
                        }
                      });
                    }
                  : null,
              icon: const Icon(Icons.chevron_left),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                shape: BoxShape.circle,
              ),
              child: Icon(icon, size: 64, color: Colors.grey[400]),
            ),
            const SizedBox(height: 16),
            Text(title,
                style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[700])),
            const SizedBox(height: 8),
            Text(subtitle,
                style: TextStyle(fontSize: 14, color: Colors.grey[500]),
                textAlign: TextAlign.center),
          ],
        ),
      ),
    );
  }
}
