// ignore_for_file: constant_identifier_names

enum PreferencesKeys {
  TOKEN,
  UserId,
  EcommerceTOKEN,
  Invoice, // this for Sale invocie but I want to catch with server Enum
  RetrunInvoice,
  OrderInvoice,
  DeviceCode,
  IsRuningFirstTime,
  BaseUrl,
  EcommerceBaseUrl,
  IsUsingEcommerce,
  BackupPath,
  SkylanAccountingHelperURL,
  IsSharedProducts,
  isPharmacy,
}

extension PreferencesKeysExtension on PreferencesKeys {
  String get title {
    switch (this) {
      case PreferencesKeys.OrderInvoice:
        return 'Order';
      case PreferencesKeys.Invoice:
        return 'Invoice';
      case PreferencesKeys.RetrunInvoice:
        return 'RetrunInvoice';

      default:
        return 'غير معروف';
    }
  }
}
