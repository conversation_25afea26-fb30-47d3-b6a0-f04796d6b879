import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class CommonBarcodeScannerDialog extends StatefulWidget {
  const CommonBarcodeScannerDialog({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _CommonBarcodeScannerDialogState createState() =>
      _CommonBarcodeScannerDialogState();
}

class _CommonBarcodeScannerDialogState
    extends State<CommonBarcodeScannerDialog> {
  String? scannedBarcode;
  final MobileScannerController _scannerController = MobileScannerController();
  bool _isFlashOn = false;
  bool _isFrontCamera = false;

  @override
  void dispose() {
    _scannerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Container(
        margin: const EdgeInsets.only(bottom: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              T('Scan Barcode'),
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            Row(
              children: [
                // Flash toggle
                IconButton(
                  icon: Icon(
                    _isFlashOn ? Icons.flash_on : Icons.flash_off,
                    color: context.colors.primary,
                    size: 24,
                  ),
                  onPressed: () {
                    setState(() {
                      _isFlashOn = !_isFlashOn;
                      _scannerController.toggleTorch();
                    });
                  },
                  tooltip: T("Toggle Flash"),
                ),
                // Camera switch
                IconButton(
                  icon: Icon(
                    _isFrontCamera ? Icons.camera_front : Icons.camera_rear,
                    color: context.colors.primary,
                    size: 24,
                  ),
                  onPressed: () {
                    setState(() {
                      _isFrontCamera = !_isFrontCamera;
                      _scannerController.switchCamera();
                    });
                  },
                  tooltip: T("Switch Camera"),
                ),
              ],
            ),
          ],
        ),
      ),
      contentPadding: EdgeInsets.zero, // Remove padding to allow full view
      content: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.4,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // Scanner
              MobileScanner(
                controller: _scannerController,
                onDetect: (barcode) {
                  final String? code = barcode.barcodes.first.rawValue;
                  if (code != null && code.isNotEmpty) {
                    setState(() {
                      scannedBarcode = code;
                    });

                    // Show a brief success indicator
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Row(
                          children: [
                            const Icon(Icons.check_circle, color: Colors.white),
                            const SizedBox(width: 10),
                            Expanded(
                              child: Text(
                                T('Barcode detected: ') + code,
                                style: const TextStyle(color: Colors.white),
                              ),
                            ),
                          ],
                        ),
                        backgroundColor: Colors.green,
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );

                    // Close dialog with result after a brief delay
                    Future.delayed(const Duration(milliseconds: 500), () {
                      Navigator.of(context).pop(scannedBarcode);
                    });
                  }
                },
              ),

              // Scan overlay
              Center(
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Colors.white,
                      width: 2.0,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),

              // Instruction text
              Positioned(
                bottom: 20,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      T('Position the barcode within the frame'),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        CommonMaterialButton(
          label: T("Cancel"),
          backgroundColor: Colors.grey,
          onPressed: () {
            Navigator.of(context)
                .pop("-1"); // Return -1 to indicate cancellation
          },
        ),
        CommonMaterialButton(
          label: T("Done"),
          onPressed: () {
            Navigator.of(context).pop(scannedBarcode);
          },
        ),
      ],
    );
  }
}
