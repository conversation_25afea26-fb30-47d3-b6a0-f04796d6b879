import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/inventory_operation_transfare_to_section_controlle.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20transfare%20to%20section/widgets/inventory_operation_transfare_to_section_base_info_widget.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/InventoryOperation/shared/inventory_operation_no_products_widget.dart';
import 'package:inventory_application/screens/InventoryOperation/shared/inventory_opertaion_bottom_bar_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';

class InventoryOperationTransfareToSectionScreen extends StatefulWidget {
  const InventoryOperationTransfareToSectionScreen({super.key});

  @override
  State<InventoryOperationTransfareToSectionScreen> createState() =>
      _InventoryOperationTransfareToSectionScreenState();
}

class _InventoryOperationTransfareToSectionScreenState
    extends State<InventoryOperationTransfareToSectionScreen> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<InventoryOperationTransfareToSectionController>(context,
              listen: false)
          .getIncomingNumber();
    });
  }

  @override
  Widget build(BuildContext context) {
    var provider =
        Provider.of<InventoryOperationTransfareToSectionController>(context);
    var model =
        Provider.of<InventoryOperationTransfareToSectionController>(context)
            .inventorySectionTransfare;

    return ApplicationLayout(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: [
              CommonHeader(
                icon: Icons.input,
                title: T("Transfare to section operation"),
              ),

              // Base Info Section
              InventoryOperationTransfareToSectionBaseInfoWidget(
                onSelectWarehouse: () {
                  setState(() {});
                },
                onselecteRequest: (id, name) async {
                  var result = await provider.getRequestById(id);
                  if (result.isSuccess) {
                    var data = result.data as InventoryOperationModel;
                    Provider.of<WarehouseController>(context, listen: false)
                        .getWarehousesBySectionIdFormServer(
                            data.secationId ?? 0);
                    Provider.of<InventoryOperationTransfareToSectionController>(
                            context,
                            listen: false)
                        .inventorySectionTransfare
                        .iD = 0;
                    Provider.of<InventoryOperationTransfareToSectionController>(
                            context,
                            listen: false)
                        .inventorySectionTransfare
                        .secationName = Provider.of<WarehouseController>(
                            context,
                            listen: false)
                        .sections
                        .firstWhere((element) => element.id == data.secationId)
                        .name;

                    await Future.delayed(const Duration(milliseconds: 100));

                    Provider.of<InventoryOperationTransfareToSectionController>(
                            context,
                            listen: false)
                        .inventorySectionTransfare
                        .toStoreName = Provider.of<WarehouseController>(context,
                            listen: false)
                        .warehousesBySection
                        .firstWhere((element) => element.id == data.toStoreID)
                        .name;
                    Provider.of<InventoryOperationTransfareToSectionController>(
                            context,
                            listen: false)
                        .inventorySectionTransfare
                        .sourceReference1 = int.parse(id.toString());
                    Provider.of<InventoryOperationTransfareToSectionController>(
                            context,
                            listen: false)
                        .inventorySectionTransfare
                        .sourceReference1Name = name.toString();
                    Provider.of<InventoryOperationTransfareToSectionController>(
                            context,
                            listen: false)
                        .inventorySectionTransfare
                        .sourceID = 10;
                    setState(() {});

                    // setState(() {});
                    // var adksljaskl = "";
                  }
                },
              ),

              // Content Section
              Column(
                children: [
                  // Product Selection Widget
                  InvoiceSelectProductWidget(
                    selectedProducts: provider.selectedSectionProduct,
                    canAdd: model.toStoreID != null,
                    oncannotAddProduct: () {
                      errorSnackBar(
                          message: "يرجى اختيار المستودع",
                          // ignore: use_build_context_synchronously
                          context: context);
                    },
                    onChange: () {
                      // Force UI refresh when products change
                      setState(() {});
                    },
                    onAddProduct: (ProductDTO product) {
                      provider.addProductToSelectedList(product);
                      // Force UI refresh
                      setState(() {});
                    },
                    onRemoveProduct: (int id) {
                      provider.deleteProductFromSelectedList(id);
                      // Force UI refresh
                      setState(() {});
                    },
                    onSearchByBarcode: (String barcode) async {
                      // Get the controller
                      final inventoryIncomingController = Provider.of<
                          InventoryOperationTransfareToSectionController>(
                        context,
                        listen: false,
                      );

                      // Process the barcode
                      var result =
                          await inventoryIncomingController.getItemByBarcode(
                        barcode: barcode,
                      );

                      // If result is a ProductDTO object rather than a boolean,
                      // it means we need to show the attribute selection dialog
                      if (result is ProductDTO) {
                        // Show attribute dialog for the product
                        await showDialog(
                          // ignore: use_build_context_synchronously
                          context: context,
                          barrierDismissible: false,
                          builder: (BuildContext context) {
                            return _buildAttributeSelectionDialog(result);
                          },
                        );

                        // Return true to indicate success (dialog was shown)
                        return true;
                      }

                      // Force UI refresh immediately after barcode scan
                      setState(() {
                        // This will trigger a rebuild with the updated product list
                      });

                      // Add a small delay and refresh again to ensure UI updates
                      await Future.delayed(const Duration(milliseconds: 100));
                      setState(() {});

                      // Show error if product not found
                      if (result == false) {
                        errorSnackBar(
                          message: T(
                              "There is no product associated with the barcode"),
                          // ignore: use_build_context_synchronously
                          context: context,
                        );
                      }

                      return result;
                    },
                  ),

                  // Products List
                  if (provider.selectedSectionProduct.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.zero,
                      child: InvoiceProductListHeaderWidget(
                        backgroundColor: context.primaryColor,
                        textColor: Colors.white,
                      ),
                    ),
                    Consumer<InventoryOperationTransfareToSectionController>(
                      builder: (context, inventoryOpertationOutgoingController,
                          child) {
                        return ListView.separated(
                          itemCount: provider.selectedSectionProduct.length,
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          separatorBuilder: (context, index) => Divider(
                            color: Colors.grey.withOpacity(0.2),
                            height: 1,
                            indent: 0,
                            endIndent: 0,
                          ),
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return InvoiceListItemsWidget(
                              id: provider.selectedSectionProduct[index].id ??
                                  0,
                              barcode: provider
                                  .selectedSectionProduct[index].barcode,
                              virtualProductId: provider
                                  .selectedSectionProduct[index]
                                  .virtualProductId,
                              selectedInvoiceProduct:
                                  inventoryOpertationOutgoingController
                                      .selectedSectionProduct,
                              onChangeWarehouse: (int productId,
                                  int warehouseId, String warehouseName,
                                  [String? virtualProductId]) {
                                errorSnackBar(
                                    message: "لا يمكن تغير المستودع هنا");
                              },
                              onDeleteProduct: (int productId,
                                  [String? virtualProductId]) {
                                if (virtualProductId != null) {
                                  // If we have a virtual ID, use it when deleting
                                  provider.deleteProductFromSelectedList(
                                      productId,
                                      virtualProductId: virtualProductId);
                                } else {
                                  // Otherwise use the regular product ID
                                  provider
                                      .deleteProductFromSelectedList(productId);
                                }
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdatePrice: (int productId, double price,
                                  [String? virtualProductId]) {
                                if (virtualProductId != null) {
                                  provider.updateProductPrice(
                                      productId, price, virtualProductId);
                                } else {
                                  provider.updateProductPrice(productId, price);
                                }
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdateQuantity: (int productId, double quantity,
                                  [String? virtualProductId]) {
                                if (virtualProductId != null) {
                                  provider.updateProductQuantity(
                                      productId, quantity, virtualProductId);
                                } else {
                                  provider.updateProductQuantity(
                                      productId, quantity);
                                }
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                              onUpdateUnit: (int productId, ItemPriceDTO unit,
                                  [String? virtualProductId]) {
                                provider.updateProductUnit(
                                    productId, unit, virtualProductId);
                                provider.calculateInvoiceTotal();
                                setState(() {});
                              },
                            );
                          },
                        );
                      },
                    ),
                  ] else ...[
                    // Empty State
                    const InventoryOperationNoProductsWidget(),
                  ],

                  // Summary Section
                ],
              ),
            ],
          ),
        ),
        bottomNavigationBar: InventoryOpertaionBottomBarWidget(
            model: provider.inventorySectionTransfare,
            products: provider.selectedSectionProduct,
            onclear: () {
              provider.selectedSectionProduct.clear();
              provider.calculateInvoiceTotal();
            },
            onSave: () async {
              if (provider.inventorySectionTransfare.toStoreID == null) {
                errorSnackBar(message: "يرجى اختيار المستودع ");
                return;
              }

              if (provider.inventorySectionTransfare.fromStoreID == null) {
                errorSnackBar(message: "يرجى اختيار المستودع ");
                return;
              }
              pleaseWaitDialog(context: context, isShown: true);

              var result = await provider.saveInventoryTransfareToSection();
              if (result.isSuccess) {
                // ignore: use_build_context_synchronously
                pleaseWaitDialog(context: context, isShown: false);
                successSnackBar(message: T("The operation has been saved"));

                // Reset the invoice and set default values

                provider.selectedSectionProduct.clear();
                provider.inventorySectionTransfare = InventoryOperationModel();
                // _setDefaultValues();
                setState(() {});
                return;
              }
              // ignore: use_build_context_synchronously
              pleaseWaitDialog(context: context, isShown: false);
              errorSnackBar(
                  message: result.message != null
                      ? result.message!.first.toString()
                      : T("Not saved"));
            }),
      ),
    );
  }

  Widget _buildAttributeSelectionDialog(ProductDTO product) {
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      title: Text(T('Select Product Options')),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              product.title ?? T('Unknown Product'),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 16),
            // Add attribute selection widgets here
            // This would be similar to the POS screen implementation
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(T('Cancel')),
        ),
        ElevatedButton(
          onPressed: () {
            // Add the product with selected attributes
            Provider.of<InventoryOperationTransfareToSectionController>(context,
                    listen: false)
                .addProductToSelectedList(product);
            Navigator.of(context).pop();
          },
          child: Text(T('Add to Cart')),
        ),
      ],
    );
  }
}
