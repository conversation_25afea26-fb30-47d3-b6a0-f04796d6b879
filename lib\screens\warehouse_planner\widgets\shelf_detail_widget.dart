import 'package:flutter/material.dart';
import '../../../models/warehouse_planner/warehouse_layout.dart';

class ShelfDetailWidget extends StatelessWidget {
  final Shelf shelf;

  const ShelfDetailWidget({
    Key? key,
    required this.shelf,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان ومعلومات أساسية
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: shelf.color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getShelfIcon(shelf.type),
                  color: shelf.color,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      shelf.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      _getShelfTypeText(shelf.type),
                      style: const TextStyle(
                        color: Color(0xFF6C757D),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              _buildOccupancyBadge(),
            ],
          ),

          const SizedBox(height: 16),

          // إحصائيات سريعة
          Row(
            children: [
              Expanded(
                  child: _buildStatCard(
                      'المستويات', '${shelf.levels}', Icons.layers)),
              const SizedBox(width: 8),
              Expanded(
                  child: _buildStatCard(
                      'الخانات', '${shelf.slotsPerLevel}', Icons.grid_view)),
              const SizedBox(width: 8),
              Expanded(
                  child: _buildStatCard(
                      'الإجمالي', '${shelf.bins.length}', Icons.inventory)),
            ],
          ),

          const SizedBox(height: 16),

          // مخطط الخزانة المصغر
          Text(
            'خريطة الخزانة',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          _buildShelfMiniMap(),

          const SizedBox(height: 16),

          // قائمة الأدوية الموجودة
          Text(
            'الأدوية المخزنة (${_getOccupiedBinsCount()})',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          _buildMedicinesList(),
        ],
      ),
    );
  }

  Widget _buildOccupancyBadge() {
    final occupiedBins = _getOccupiedBinsCount();
    final totalBins = shelf.bins.length;
    final occupancyRate = totalBins > 0 ? (occupiedBins / totalBins) : 0.0;

    Color badgeColor;
    String badgeText;

    if (occupancyRate == 0) {
      badgeColor = Colors.grey;
      badgeText = 'فارغ';
    } else if (occupancyRate < 0.5) {
      badgeColor = Colors.green;
      badgeText = 'متاح';
    } else if (occupancyRate < 0.8) {
      badgeColor = Colors.orange;
      badgeText = 'مشغول';
    } else {
      badgeColor = Colors.red;
      badgeText = 'ممتلئ';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: badgeColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: badgeColor.withOpacity(0.3)),
      ),
      child: Text(
        badgeText,
        style: TextStyle(
          color: badgeColor,
          fontSize: 11,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 16, color: const Color(0xFF6C757D)),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 10,
              color: Color(0xFF6C757D),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShelfMiniMap() {
    return Container(
      height: 80,
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE9ECEF)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: GridView.builder(
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: shelf.slotsPerLevel,
            crossAxisSpacing: 1,
            mainAxisSpacing: 1,
            childAspectRatio: 1,
          ),
          itemCount: shelf.bins.length,
          itemBuilder: (context, index) {
            final bin = shelf.bins[index];
            return Container(
              decoration: BoxDecoration(
                color: bin.statusColor,
                border: Border.all(
                  color: Colors.white,
                  width: 0.5,
                ),
              ),
              child: bin.productName != null
                  ? Center(
                      child: Text(
                        bin.productCode?.substring(0, 2) ?? 'XX',
                        style: const TextStyle(
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    )
                  : null,
            );
          },
        ),
      ),
    );
  }

  Widget _buildMedicinesList() {
    final medicinesInShelf =
        shelf.bins.where((bin) => bin.productName != null).toList();

    if (medicinesInShelf.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: const Color(0xFFF8F9FA),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: const [
            Icon(Icons.info_outline, color: Color(0xFF6C757D)),
            SizedBox(width: 8),
            Text(
              'لا توجد أدوية مخزنة في هذه الخزانة',
              style: TextStyle(color: Color(0xFF6C757D)),
            ),
          ],
        ),
      );
    }

    return Column(
      children: medicinesInShelf.take(3).map((bin) {
        final level = bin.level + 1;
        final slot = bin.slot + 1;

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: const Color(0xFFE9ECEF)),
          ),
          child: Row(
            children: [
              Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: bin.statusColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      bin.productName!,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      'L$level-S$slot • ${bin.quantity} ${bin.unitName ?? 'وحدة'}',
                      style: const TextStyle(
                        fontSize: 10,
                        color: Color(0xFF6C757D),
                      ),
                    ),
                  ],
                ),
              ),
              if (bin.expiryDate != null && _isExpiringSoon(bin.expiryDate!))
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: const Text(
                    'قريب الانتهاء',
                    style: TextStyle(
                      fontSize: 8,
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
        );
      }).toList(),
    );
  }

  int _getOccupiedBinsCount() {
    return shelf.bins.where((bin) => bin.status != BinStatus.empty).length;
  }

  bool _isExpiringSoon(DateTime expiryDate) {
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate.difference(now).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }

  IconData _getShelfIcon(ShelfType type) {
    switch (type) {
      case ShelfType.standard:
        return Icons.shelves;
      case ShelfType.refrigerated:
        return Icons.ac_unit;
      case ShelfType.controlled:
        return Icons.thermostat;
      case ShelfType.hazardous:
        return Icons.warning;
      case ShelfType.narcotics:
        return Icons.security;
    }
  }

  String _getShelfTypeText(ShelfType type) {
    switch (type) {
      case ShelfType.standard:
        return 'خزانة عادية';
      case ShelfType.refrigerated:
        return 'خزانة مبردة';
      case ShelfType.controlled:
        return 'خزانة محكمة';
      case ShelfType.hazardous:
        return 'مواد خطرة';
      case ShelfType.narcotics:
        return 'خزانة مخدرات';
    }
  }
}
