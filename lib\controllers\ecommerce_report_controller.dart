import 'package:flutter/material.dart';
import 'package:inventory_application/models/ecommerceDTO/reports/ecommerce_report_dto.dart';
import 'package:inventory_application/models/ecommerceDTO/ecommerce_order_dto.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/controllers/branch_controller.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';

class EcommerceReportController with ChangeNotifier {
  final ReportController _reportController = ReportController();
  final ProductController _productController = ProductController();
  final WarehouseController _warehouseController = WarehouseController();
  final BranchController _branchController = BranchController();

  // Current report data
  EcommerceItemTransactionsReport? _currentReport;
  EcommerceItemQuantityReport? _currentQuantityReport;

  // Loading states
  bool _isLoading = false;
  bool _isLoadingProducts = false;

  // Selected items
  ProductDTO? _selectedProduct;
  final List<ProductDTO> _selectedProducts =
      []; // For compatibility with InvoiceSelectProductWidget
  ComboBoxDataModel? _selectedBranch;
  ComboBoxDataModel? _selectedWarehouse;
  DateTime? _fromDate;
  DateTime? _toDate;
  List<int>? _selectedOptionIds;

  // Getters
  EcommerceItemTransactionsReport? get currentReport => _currentReport;
  EcommerceItemQuantityReport? get currentQuantityReport =>
      _currentQuantityReport;
  bool get isLoading => _isLoading;
  bool get isLoadingProducts => _isLoadingProducts;
  ProductDTO? get selectedProduct => _selectedProduct;
  List<ProductDTO> get selectedProducts => _selectedProducts;
  ComboBoxDataModel? get selectedBranch => _selectedBranch;
  ComboBoxDataModel? get selectedWarehouse => _selectedWarehouse;
  DateTime? get fromDate => _fromDate;
  DateTime? get toDate => _toDate;
  List<int>? get selectedOptionIds => _selectedOptionIds;

  // Get data lists
  List<ProductDTO> get products => _productController.realProductList;
  List<ComboBoxDataModel> get warehouses => _warehouseController.warehouses;
  List<ComboBoxDataModel> get branches => _branchController.branches;

  // Setters

  void setSelectedBranch(ComboBoxDataModel? branch) {
    _selectedBranch = branch;
    notifyListeners();
  }

  void setSelectedWarehouse(ComboBoxDataModel? warehouse) {
    _selectedWarehouse = warehouse;
    notifyListeners();
  }

  void setFromDate(DateTime? fromDate) {
    _fromDate = fromDate;
    notifyListeners();
  }

  void setToDate(DateTime? toDate) {
    _toDate = toDate;
    notifyListeners();
  }

  void setSelectedOptionIds(List<int>? optionIds) {
    _selectedOptionIds = optionIds;
    notifyListeners();
  }

  // Add product to selected list
  void addProductToSelectedList(ProductDTO product) {
    _selectedProducts.clear(); // For report, we only want one product
    _selectedProducts.add(product);
    _selectedProduct = product;
    notifyListeners();
  }

  // Remove product from selected list
  void removeProductFromSelectedList(int productId) {
    _selectedProducts.removeWhere((product) => product.id == productId);
    if (_selectedProducts.isEmpty) {
      _selectedProduct = null;
    }
    notifyListeners();
  }

  // Search product by barcode
  Future<dynamic> getItemByBarcode({required String barcode}) async {
    try {
      // Use the existing ProductController method
      var result =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);
      return result; // Returns ProductDTO if found, null if not
    } catch (e) {
      debugPrint('Error searching by barcode: $e');
      return null;
    }
  }

  // Get product transaction details report
  Future<bool> getProductTransactionDetails() async {
    if (_selectedProduct == null) {
      return false;
    }

    try {
      _isLoading = true;
      notifyListeners();

      final request = EcommerceReportRequest(
        id: _selectedProduct!.id!,
        fromDate: _fromDate,
        toDate: _toDate,
        branchId: _selectedBranch?.id,
        storeId: _selectedWarehouse?.id,
        optionIds: _selectedOptionIds,
        postId: true,
      );

      final result =
          await _reportController.getProductTranactionDeatilsReport(request);

      debugPrint(
          '📊 EcommerceReportController - Received report: ${result.productId}');
      debugPrint(
          '📊 EcommerceReportController - Transactions count: ${result.transactions?.length ?? 0}');

      _currentReport = result;

      return true;
    } catch (e) {
      debugPrint('Error getting product transaction details: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Get product quantity report
  Future<bool> getProductQuantitiesReport() async {
    if (_selectedProduct == null) {
      return false;
    }

    try {
      _isLoading = true;
      notifyListeners();

      final request = EcommerceReportRequest(
        id: _selectedProduct!.id!,
        fromDate: _fromDate,
        toDate: _toDate,
        branchId: _selectedBranch?.id,
        storeId: _selectedWarehouse?.id,
        optionIds: _selectedOptionIds,
        postId: true,
      );

      final result = await _reportController.getProductQuantitesReport(request);

      debugPrint(
          '📊 EcommerceReportController - Received quantity report: ${result.productId}');
      debugPrint(
          '📊 EcommerceReportController - Combinations count: ${result.combinationQuantities?.length ?? 0}');

      _currentQuantityReport = result;

      return true;
    } catch (e) {
      debugPrint('Error getting product quantities report: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear all data
  void clearData() {
    _currentReport = null;
    _currentQuantityReport = null;
    _selectedProduct = null;
    _selectedProducts.clear();
    _selectedBranch = null;
    _selectedWarehouse = null;
    _fromDate = null;
    _toDate = null;
    _selectedOptionIds = null;
    _isLoading = false;
    notifyListeners();
  }

  // Load initial data
  Future<void> loadData() async {
    _isLoadingProducts = true;
    notifyListeners();

    try {
      await _productController.getItems(resetAndRefresh: true);
      await _warehouseController.fetchWarehouses();
      await _branchController.fetchBranches();
    } catch (e) {
      debugPrint('Error loading data: $e');
    } finally {
      _isLoadingProducts = false;
      notifyListeners();
    }
  }

  // Helper methods
  String getTransactionTypeDisplayName(TransactionTypes type) {
    switch (type) {
      case TransactionTypes.Purchase:
        return 'مشتريات';
      case TransactionTypes.Sale:
        return 'مبيعات';
      case TransactionTypes.Return:
        return 'مرتجع مبيعات';
      case TransactionTypes.PurchaseReturn:
        return 'مرتجع مشتريات';
      case TransactionTypes.ItemsTransferOutgoing:
        return 'تحويل صادر';
      case TransactionTypes.ItemsTransferIncoming:
        return 'تحويل وارد';
      case TransactionTypes.ItemsTransToAnotherWharehouse:
        return 'تحويل لمخزن آخر';
      case TransactionTypes.AdjustmentIncrease:
        return 'تسوية زيادة';
      case TransactionTypes.AdjustmentDecrease:
        return 'تسوية نقص';
      case TransactionTypes.Damaged:
        return 'تالف';
      case TransactionTypes.StockTaking:
        return 'جرد';
      case TransactionTypes.Reservation:
        return 'حجز';
      case TransactionTypes.ReleaseReservation:
        return 'إلغاء حجز';
      case TransactionTypes.Incoming:
        return 'وارد';
      case TransactionTypes.Outgoing:
        return 'صادر';
      case TransactionTypes.TransferToSection:
        return 'تحويل للقسم';
      case TransactionTypes.TransferToSectionRequset:
        return 'طلب تحويل للقسم';
      default:
        return 'غير محدد';
    }
  }

  Color getTransactionTypeColor(TransactionTypes type) {
    switch (type) {
      case TransactionTypes.Purchase:
      case TransactionTypes.ItemsTransferIncoming:
      case TransactionTypes.AdjustmentIncrease:
      case TransactionTypes.Incoming:
        return Colors.green;
      case TransactionTypes.Sale:
      case TransactionTypes.ItemsTransferOutgoing:
      case TransactionTypes.AdjustmentDecrease:
      case TransactionTypes.Outgoing:
        return Colors.red;
      case TransactionTypes.Return:
      case TransactionTypes.PurchaseReturn:
        return Colors.orange;
      case TransactionTypes.Damaged:
        return Colors.purple;
      case TransactionTypes.StockTaking:
        return Colors.blue;
      case TransactionTypes.Reservation:
      case TransactionTypes.ReleaseReservation:
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  IconData getTransactionTypeIcon(TransactionTypes type) {
    switch (type) {
      case TransactionTypes.Purchase:
        return Icons.shopping_cart;
      case TransactionTypes.Sale:
        return Icons.point_of_sale;
      case TransactionTypes.Return:
      case TransactionTypes.PurchaseReturn:
        return Icons.keyboard_return;
      case TransactionTypes.ItemsTransferOutgoing:
      case TransactionTypes.ItemsTransferIncoming:
        return Icons.transfer_within_a_station;
      case TransactionTypes.AdjustmentIncrease:
        return Icons.add_circle;
      case TransactionTypes.AdjustmentDecrease:
        return Icons.remove_circle;
      case TransactionTypes.Damaged:
        return Icons.warning;
      case TransactionTypes.StockTaking:
        return Icons.inventory;
      case TransactionTypes.Reservation:
        return Icons.book_online;
      case TransactionTypes.ReleaseReservation:
        return Icons.cancel_presentation;
      default:
        return Icons.swap_horiz;
    }
  }
}
