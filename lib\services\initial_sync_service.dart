import 'package:flutter/material.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/branch_controller.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/cost_center_controller.dart';
import 'package:inventory_application/controllers/device_setup_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/payment_type_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/supplier_controller.dart';
import 'package:inventory_application/controllers/unit_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/helpers/accounts_voucher_counter.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/purchase_invoice_counter.dart';
import 'package:inventory_application/services/authentication_service.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:provider/provider.dart';

class InitialSyncService with ChangeNotifier {
  bool _isFirstTimeSync = false;
  bool _isSyncInProgress = false;
  double _syncProgress = 0.0;
  String _currentSyncStep = '';
  List<String> _syncSteps = [];
  int _currentStepIndex = 0;
  bool _syncCompleted = false;
  String? _syncError;

  // Getters
  bool get isFirstTimeSync => _isFirstTimeSync;
  bool get isSyncInProgress => _isSyncInProgress;
  double get syncProgress => _syncProgress;
  String get currentSyncStep => _currentSyncStep;
  List<String> get syncSteps => _syncSteps;
  int get currentStepIndex => _currentStepIndex;
  bool get syncCompleted => _syncCompleted;
  String? get syncError => _syncError;

  /// التحقق من أول مرة تسجيل دخول للمستخدم في الفرع الحالي
  Future<bool> checkIfFirstTimeUserInBranch(
      String username, int branchId) async {
    try {
      final db = await DatabaseHelper().database;

      // التحقق من وجود المستخدم في هذا الفرع
      var userResult = await db.query(
        'User',
        where: 'usernameOrEmail = ? AND BranchId = ?',
        whereArgs: [username, branchId],
      );

      // التحقق من وجود بيانات أساسية في هذا الفرع
      var dataChecks = await Future.wait([
        _checkTableHasData('Customer', branchId),
        _checkTableHasData('ProductModel', branchId),
        _checkTableHasData('Category', branchId),
        _checkTableHasData('Warehouse', branchId),
        _checkTableHasData('Salesmen', branchId),
        _checkTableHasData('PaymentType', branchId),
        _checkTableHasData('Units', branchId),
      ]);

      // إذا لم يكن المستخدم موجود في هذا الفرع أو لا توجد بيانات أساسية
      bool isFirstTime =
          userResult.isEmpty || dataChecks.any((hasData) => !hasData);

      _isFirstTimeSync = isFirstTime;
      notifyListeners();

      return isFirstTime;
    } catch (e) {
      print('Error checking first time user: $e');
      return true; // في حالة الخطأ، نعتبرها أول مرة للأمان
    }
  }

  /// التحقق من وجود بيانات في جدول معين للفرع المحدد
  Future<bool> _checkTableHasData(String tableName, int branchId) async {
    try {
      final db = await DatabaseHelper().database;

      // بعض الجداول مثل Units لا تحتوي على BranchId
      String query;
      List<dynamic> args;

      if (tableName == 'Units') {
        query = 'SELECT COUNT(*) as count FROM $tableName';
        args = [];
      } else {
        query = 'SELECT COUNT(*) as count FROM $tableName WHERE BranchId = ?';
        args = [branchId];
      }

      var result = await db.rawQuery(query, args);
      int count = result.first['count'] as int;
      return count > 0;
    } catch (e) {
      print('Error checking table $tableName: $e');
      return false;
    }
  }

  /// تشغيل المزامنة الأولية الكاملة
  Future<bool> performInitialSync(
    BuildContext context,
  ) async {
    try {
      _isSyncInProgress = true;
      _syncProgress = 0.0;
      _syncCompleted = false;
      _syncError = null;
      _currentStepIndex = 0;

      // تحديد خطوات المزامنة
      _syncSteps = [
        'جاري إعداد معلومات الجهاز...',
        'جاري تحديث معلومات الفروع...',
        'جاري تحديث الصلاحيات والأدوار...',
        'جاري تحديث عدادات الفواتير...',
        'جاري تحديث عدادات العمليات...',
        'جاري إعداد بيانات المستخدم...',
        'جاري تحديث الفئات...',
        'جاري تحديث المخازن...',
        'جاري تحديث المندوبين...',
        'جاري تحديث أنواع الدفع...',
        'جاري تحديث الوحدات...',
        'جاري تحديث العملاء...',
        'جاري تحديث المنتجات...',
        'جاري تحديث إعدادات الفواتير...',
        'جاري الانتهاء من المزامنة...'
      ];

      notifyListeners();
      var tokenBody = decodeJWTToken(AuthController.getToken());

      // خطوة 1: إعداد معلومات الجهاز
      await _updateSyncStep(0, 'جاري إعداد معلومات الجهاز...');
      await context.read<AppController>().getDeviceDetails();
      // ignore: use_build_context_synchronously
      await context.read<DeviceSetupController>().deviceIdSetup();
      await _updateProgress(9.0);

      // خطوة 2: مزامنة الفروع
      await _updateSyncStep(1, 'جاري تحديث معلومات الفروع...');
      await context.read<BranchController>().fetchBranchesFromServer();
      await _updateProgress(18.0);

      // خطوة 3: مزامنة الصلاحيات والأدوار
      await _updateSyncStep(2, 'جاري تحديث الصلاحيات والأدوار...');
      await context
          .read<AuthenticationService>()
          .fetchRolesWithPermissionsFromServer();
      await _updateProgress(20.0);

      // خطوة 4: مزامنة عدادات الفواتير
      await _updateSyncStep(3, 'جاري تحديث عدادات الفواتير...');
      await CounterGenerator.getInvoicesCounterFromServer();
      AppController.getIsSharedProductsFromShared();
      AppController.getisPharmacyFromShared();
      await _updateProgress(27.0);

      // خطوة 5: مزامنة عدادات العمليات
      await _updateSyncStep(4, 'جاري تحديث عدادات العمليات...');
      await InventoryOperationCounterGenerator
          .getInventoryOperationCounterFromServer();
      await PurchaseCounterGenerator.getPurchaseInvoiceCounterFromServer();
      await AccountsVoucherCounterGenerator
          .getAccountsVoucherCounterFromServer();
      await _updateProgress(34.0);

      // خطوة 6: إعداد بيانات المستخدم
      await _updateSyncStep(5, 'جاري إعداد بيانات المستخدم...');
      AppController.currentBranchId =
          int.parse(tokenBody["BranchID"].toString());
      AppController.setBranchName();
      // ignore: use_build_context_synchronously
      context
          .read<AuthenticationService>()
          .setUserBranches(tokenBody["UserBranchs"]);
      // ignore: use_build_context_synchronously
      context
          .read<AuthenticationService>()
          .setUserModules(tokenBody["UserModule"]);
      // ignore: use_build_context_synchronously
      context
          .read<AuthenticationService>()
          .setUserRoles(tokenBody["UserRoles"]);
      if (tokenBody["IsMainAdmin"] != null) {
        // ignore: use_build_context_synchronously
        context
            .read<AuthenticationService>()
            .setIsMainAdmin(tokenBody["IsMainAdmin"] == "True" ? true : false);
      }
      await _updateProgress(41.0);

      // خطوة 7: مزامنة الفئات
      await _updateSyncStep(6, 'جاري تحديث الفئات...');
      await context.read<CategoryController>().fetchCategoriesFromServer();
      await _updateProgress(48.0);

      // خطوة 8: مزامنة المخازن
      await _updateSyncStep(7, 'جاري تحديث المخازن...');
      await context.read<WarehouseController>().fetchWarehousesFromServer();
      await _updateProgress(55.0);

      // خطوة 9: مزامنة المندوبين
      await _updateSyncStep(8, 'جاري تحديث المندوبين...');
      await context.read<SalesmenController>().fetchSalesmenFromServer();
      await _updateProgress(62.0);

      // خطوة 10: مزامنة أنواع الدفع
      await _updateSyncStep(9, 'جاري تحديث أنواع الدفع...');
      await context.read<PaymentTypeController>().fetchPayemntTypesFromServer();
      await _updateProgress(69.0);

      // خطوة 11: مزامنة الوحدات
      await _updateSyncStep(10, 'جاري تحديث الوحدات...');
      await context.read<UnitController>().fetchUnitsFromServer();
      await context.read<CostCenterController>().fetchSuppliersFromServer();
      await _updateProgress(76.0);

      // خطوة 12: مزامنة العملاء
      await _updateSyncStep(11, 'جاري تحديث العملاء...');
      await context.read<CustomerController>().getCustomers();
      await context.read<SupplierController>().fetchSuppliersFromServer();
      await _updateProgress(83.0);

      // خطوة 13: مزامنة المنتجات
      await _updateSyncStep(12, 'جاري تحديث المنتجات...');
      await context.read<ProductController>().fetchProductAfterCertineId();
      await _updateProgress(90.0);

      // خطوة 14: تحديث إعدادات الفواتير
      await _updateSyncStep(13, 'جاري تحديث إعدادات الفواتير...');
      await context.read<InvoiceSettingsController>().loadSettings();
      await _updateProgress(97.0);

      // خطوة 15: إنهاء المزامنة
      await _updateSyncStep(14, 'جاري الانتهاء من المزامنة...');

      // حفظ حالة اكتمال المزامنة الأولية للمستخدم والفرع
      await _markInitialSyncCompleted();
      await _updateProgress(100.0);

      _syncCompleted = true;
      _isSyncInProgress = false;

      notifyListeners();
      return true;
    } catch (e) {
      _syncError = 'حدث خطأ أثناء المزامنة: ${e.toString()}';
      _isSyncInProgress = false;
      notifyListeners();
      print('Error during initial sync: $e');
      return false;
    }
  }

  /// تحديث خطوة المزامنة الحالية
  Future<void> _updateSyncStep(int stepIndex, String stepMessage) async {
    _currentStepIndex = stepIndex;
    _currentSyncStep = stepMessage;
    notifyListeners();

    // إضافة تأخير صغير لإظهار التحديث للمستخدم
    await Future.delayed(const Duration(milliseconds: 500));
  }

  /// تحديث تقدم المزامنة
  Future<void> _updateProgress(double progress) async {
    _syncProgress = progress;
    notifyListeners();

    // إضافة تأخير صغير لإظهار التحديث للمستخدم
    await Future.delayed(const Duration(milliseconds: 300));
  }

  /// حفظ حالة اكتمال المزامنة الأولية
  Future<void> _markInitialSyncCompleted() async {
    try {
      String keyName =
          'initial_sync_completed_${AppController.currentBranchId}';
      await LocaleManager.instance
          .setStringValueByStringKey(keyName, DateTime.now().toIso8601String());
    } catch (e) {
      print('Error marking sync completed: $e');
    }
  }

  /// التحقق من إتمام المزامنة الأولية مسبقاً
  Future<bool> isInitialSyncCompleted() async {
    try {
      String keyName =
          'initial_sync_completed_${AppController.currentBranchId}';
      String syncDate =
          LocaleManager.instance.getStringValueByStringKey(keyName);
      return syncDate.isNotEmpty;
    } catch (e) {
      print('Error checking sync completion: $e');
      return false;
    }
  }

  /// مزامنة سريعة بدون UI - للاستخدام في الخلفية
  Future<bool> performQuickSync(BuildContext context) async {
    try {
      // إعداد معلومات الجهاز أولاً
      await context.read<AppController>().getDeviceDetails();
      await context.read<DeviceSetupController>().deviceIdSetup();

      // مزامنة العدادات أولاً
      await CounterGenerator.getInvoicesCounterFromServer();
      await InventoryOperationCounterGenerator
          .getInventoryOperationCounterFromServer();
      await PurchaseCounterGenerator.getPurchaseInvoiceCounterFromServer();
      await AccountsVoucherCounterGenerator
          .getAccountsVoucherCounterFromServer();
      AppController.getIsSharedProductsFromShared();
      AppController.getisPharmacyFromShared();
      // تشغيل المزامنة بصمت
      await Future.wait([
        context.read<BranchController>().fetchBranchesFromServer(),
        context
            .read<AuthenticationService>()
            .fetchRolesWithPermissionsFromServer(),
        context.read<CategoryController>().fetchCategoriesFromServer(),
        context.read<CostCenterController>().fetchSuppliersFromServer(),
        context.read<SupplierController>().fetchSuppliersFromServer(),
        context.read<WarehouseController>().fetchWarehousesFromServer(),
        context.read<SalesmenController>().fetchSalesmenFromServer(),
        context.read<PaymentTypeController>().fetchPayemntTypesFromServer(),
        context.read<UnitController>().fetchUnitsFromServer(),
      ]);

      // مزامنة العملاء والمنتجات قد تستغرق وقتًا أطول
      await context.read<CustomerController>().getCustomers();
      await context.read<ProductController>().fetchProductAfterCertineId();

      // تحديث إعدادات الفواتير
      await context.read<InvoiceSettingsController>().loadSettings();

      await _markInitialSyncCompleted();
      return true;
    } catch (e) {
      print('Error during quick sync: $e');
      return false;
    }
  }

  /// إعادة تعيين حالة المزامنة
  void resetSyncState() {
    _isFirstTimeSync = false;
    _isSyncInProgress = false;
    _syncProgress = 0.0;
    _currentSyncStep = '';
    _syncSteps.clear();
    _currentStepIndex = 0;
    _syncCompleted = false;
    _syncError = null;
    notifyListeners();
  }

  /// إجبار المزامنة الأولية (لإعادة المزامنة)
  Future<void> forceInitialSync() async {
    String keyName = 'initial_sync_completed_${AppController.currentBranchId}';
    LocaleManager.instance.removeKeyByStringKey(keyName);
    resetSyncState();
  }
}
