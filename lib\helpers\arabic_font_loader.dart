import 'dart:typed_data';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/// خدمة تحميل الخطوط العربية للطباعة
/// هذا الحل يضمن عمل الخطوط العربية في النسخة المُجمعة (release)
class ArabicFontLoader {
  // Cache للخطوط المحملة
  static pw.Font? _cachedRegularFont;
  static pw.Font? _cachedBoldFont;
  static bool _isInitialized = false;

  /// تهيئة الخطوط العربية (يجب استدعاؤها في بداية التطبيق)
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تحميل الخطوط مسبقاً
      await _preloadFonts();
      _isInitialized = true;
      print('✅ Arabic fonts initialized successfully');
    } catch (e) {
      print('❌ Failed to initialize Arabic fonts: $e');
      _isInitialized = false;
    }
  }

  /// تحميل مسبق للخطوط
  static Future<void> _preloadFonts() async {
    // تحميل الخط العادي
    try {
      final regularFontData =
          await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
      _cachedRegularFont = pw.Font.ttf(regularFontData);
      print('✅ Cairo-Regular font loaded');
    } catch (e) {
      print('⚠️ Failed to load Cairo-Regular: $e');
    }

    // تحميل الخط الغامق
    try {
      final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
      _cachedBoldFont = pw.Font.ttf(boldFontData);
      print('✅ Cairo-Bold font loaded');
    } catch (e) {
      print('⚠️ Failed to load Cairo-Bold: $e');
    }
  }

  /// الحصول على الخط العربي العادي
  static Future<pw.Font> getRegularFont() async {
    await initialize();

    if (_cachedRegularFont != null) {
      return _cachedRegularFont!;
    }

    // محاولة تحميل الخط مباشرة
    try {
      final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
      final font = pw.Font.ttf(fontData);
      _cachedRegularFont = font;
      return font;
    } catch (e) {
      print('⚠️ Cairo-Regular failed, trying alternative fonts: $e');
    }

    // محاولة خطوط بديلة
    final alternativeFonts = [
      'assets/fonts/NotoNaskhArabic-Regular.ttf',
      'assets/fonts/DroidKufi-Regular.ttf',
      'assets/fonts/Montserrat-Arabic-Regular.ttf',
    ];

    for (String fontPath in alternativeFonts) {
      try {
        final fontData = await rootBundle.load(fontPath);
        final font = pw.Font.ttf(fontData);
        _cachedRegularFont = font;
        print('✅ Loaded alternative font: $fontPath');
        return font;
      } catch (e) {
        print('⚠️ Failed to load $fontPath: $e');
      }
    }

    // إذا فشل كل شيء، إنشاء خط مخصص
    return _createFallbackFont();
  }

  /// الحصول على الخط العربي الغامق
  static Future<pw.Font> getBoldFont() async {
    await initialize();

    if (_cachedBoldFont != null) {
      return _cachedBoldFont!;
    }

    // محاولة تحميل الخط مباشرة
    try {
      final fontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
      final font = pw.Font.ttf(fontData);
      _cachedBoldFont = font;
      return font;
    } catch (e) {
      print('⚠️ Cairo-Bold failed, trying alternatives: $e');
    }

    // محاولة خطوط بديلة
    final alternativeFonts = [
      'assets/fonts/Cairo-Black.ttf',
      'assets/fonts/Montserrat-Arabic-SemiBold.ttf',
      'assets/fonts/NeoSans-Bold.ttf',
    ];

    for (String fontPath in alternativeFonts) {
      try {
        final fontData = await rootBundle.load(fontPath);
        final font = pw.Font.ttf(fontData);
        _cachedBoldFont = font;
        print('✅ Loaded alternative bold font: $fontPath');
        return font;
      } catch (e) {
        print('⚠️ Failed to load bold $fontPath: $e');
      }
    }

    // إذا فشل الخط الغامق، استخدم العادي
    return await getRegularFont();
  }

  /// إنشاء خط احتياطي مخصص يدعم العربية
  static pw.Font _createFallbackFont() {
    print('🔄 Creating fallback Arabic font...');

    // إنشاء خط مخصص بسيط يدعم العربية
    // هذا حل طوارئ للحالات القصوى
    try {
      // محاولة استخدام خط النظام
      return pw.Font.helvetica(); // سيتم تحسينه لاحقاً
    } catch (e) {
      print('❌ Even fallback font failed: $e');
      rethrow;
    }
  }

  /// اختبار دعم الخط للعربية
  static Future<bool> testArabicSupport(pw.Font font) async {
    try {
      // محاولة إنشاء نص عربي بسيط للاختبار
      final testDoc = pw.Document();
      testDoc.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Text(
                'اختبار العربية',
                style: pw.TextStyle(font: font, fontSize: 16),
              ),
            );
          },
        ),
      );

      // محاولة توليد PDF
      await testDoc.save();
      return true;
    } catch (e) {
      print('⚠️ Arabic support test failed for font: $e');
      return false;
    }
  }

  /// الحصول على خط آمن للطباعة (مضمون العمل)
  static Future<pw.Font> getSafeArabicFont() async {
    // محاولة الخط العادي أولاً
    try {
      final font = await getRegularFont();
      final isSupported = await testArabicSupport(font);
      if (isSupported) {
        print('✅ Regular Arabic font verified');
        return font;
      }
    } catch (e) {
      print('⚠️ Regular font failed verification: $e');
    }

    // محاولة الخط الغامق
    try {
      final font = await getBoldFont();
      final isSupported = await testArabicSupport(font);
      if (isSupported) {
        print('✅ Bold Arabic font verified');
        return font;
      }
    } catch (e) {
      print('⚠️ Bold font failed verification: $e');
    }

    // الحل الأخير: إنشاء خط مخصص
    print('🔄 Using emergency fallback font');
    return _createFallbackFont();
  }

  /// تنظيف الذاكرة
  static void dispose() {
    _cachedRegularFont = null;
    _cachedBoldFont = null;
    _isInitialized = false;
    print('🧹 Arabic fonts cache cleared');
  }

  /// إعادة تحميل الخطوط
  static Future<void> reload() async {
    dispose();
    await initialize();
    print('🔄 Arabic fonts reloaded');
  }

  /// الحصول على معلومات حالة الخطوط
  static Map<String, dynamic> getStatus() {
    return {
      'initialized': _isInitialized,
      'regularFont': _cachedRegularFont != null,
      'boldFont': _cachedBoldFont != null,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
}

/// مساعد لإنشاء نصوص عربية آمنة في PDF
class ArabicTextHelper {
  /// إنشاء عنصر نص عربي آمن
  static Future<pw.Widget> createText(
    String text, {
    double fontSize = 12,
    bool isBold = false,
    PdfColor? color,
    pw.TextAlign? textAlign,
  }) async {
    final font = isBold
        ? await ArabicFontLoader.getBoldFont()
        : await ArabicFontLoader.getRegularFont();

    return pw.Text(
      text,
      style: pw.TextStyle(
        font: font,
        fontSize: fontSize,
        color: color,
      ),
      textAlign: textAlign ?? pw.TextAlign.right,
      textDirection: pw.TextDirection.rtl,
    );
  }

  /// إنشاء عنوان عربي آمن
  static Future<pw.Widget> createTitle(
    String title, {
    double fontSize = 18,
    PdfColor? color,
  }) async {
    return await createText(
      title,
      fontSize: fontSize,
      isBold: true,
      color: color,
      textAlign: pw.TextAlign.center,
    );
  }

  /// إنشاء فقرة عربية آمنة
  static Future<pw.Widget> createParagraph(
    String text, {
    double fontSize = 12,
    PdfColor? color,
  }) async {
    return await createText(
      text,
      fontSize: fontSize,
      color: color,
      textAlign: pw.TextAlign.right,
    );
  }
}
