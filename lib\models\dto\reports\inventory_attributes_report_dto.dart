class InventoryAttributesReportDTO {
  final String attribute;
  final int branchId;
  final double opening;
  final double purchases;
  final double sales;
  final double deliveryNote;
  final double salesReturns;
  final double outgoingItems;
  final double itemsTransfer;
  final double purchasesReturns;
  final double damaged;
  final double incoming;
  final double outgoing;
  final double shortage;
  final double surplus;

  InventoryAttributesReportDTO({
    required this.attribute,
    required this.branchId,
    required this.opening,
    required this.purchases,
    required this.sales,
    required this.deliveryNote,
    required this.salesReturns,
    required this.outgoingItems,
    required this.itemsTransfer,
    required this.purchasesReturns,
    required this.damaged,
    required this.incoming,
    required this.outgoing,
    required this.shortage,
    required this.surplus,
  });

  // Calculate balance using the provided formula
  double get balance {
    return opening +
        purchases +
        salesReturns +
        purchasesReturns +
        incoming +
        surplus -
        sales -
        deliveryNote -
        outgoingItems -
        itemsTransfer -
        damaged -
        outgoing -
        shortage;
  }

  factory InventoryAttributesReportDTO.fromJson(Map<String, dynamic> json) {
    return InventoryAttributesReportDTO(
      attribute: json['Attribute'] ?? '',
      branchId: json['Branch_ID'] ?? 0,
      opening: (json['Opening'] ?? 0).toDouble(),
      purchases: (json['Purchases'] ?? 0).toDouble(),
      sales: (json['Sales'] ?? 0).toDouble(),
      deliveryNote: (json['DeliveryNote'] ?? 0).toDouble(),
      salesReturns: (json['SalesReturns'] ?? 0).toDouble(),
      outgoingItems: (json['Outgoing_Items'] ?? 0).toDouble(),
      itemsTransfer: (json['Items_Transfer'] ?? 0).toDouble(),
      purchasesReturns: (json['PurchasesReturns'] ?? 0).toDouble(),
      damaged: (json['Damaged'] ?? 0).toDouble(),
      incoming: (json['Incoming'] ?? 0).toDouble(),
      outgoing: (json['Outgoing'] ?? 0).toDouble(),
      shortage: (json['Shortage'] ?? 0).toDouble(),
      surplus: (json['Surplus'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Attribute': attribute,
      'Branch_ID': branchId,
      'Opening': opening,
      'Purchases': purchases,
      'Sales': sales,
      'DeliveryNote': deliveryNote,
      'SalesReturns': salesReturns,
      'Outgoing_Items': outgoingItems,
      'Items_Transfer': itemsTransfer,
      'PurchasesReturns': purchasesReturns,
      'Damaged': damaged,
      'Incoming': incoming,
      'Outgoing': outgoing,
      'Shortage': shortage,
      'Surplus': surplus,
    };
  }
}
