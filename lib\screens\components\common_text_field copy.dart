import 'package:flutter/material.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';

class MyPhoneField extends StatefulWidget {
  const MyPhoneField({
    Key? key,
    this.hintText,
    this.labelText,
    this.labelColor = const Color(0xff42474d),
    this.borderColor,
    this.borderRadiusBottomLeft = 10,
    this.borderRadiusBottomRight = 10,
    this.borderRadiusTopRight = 10,
    this.borderRadiusTopLeft = 10,
    this.borderRadiusAll = 10,
    this.asideByAside = false,
    this.textInputAction = TextInputAction.done,
    required this.onChanged,
    required this.onCountryChanged,
  }) : super(key: key);
  final String? hintText;
  final String? labelText;
  final Color? labelColor;
  final Color? borderColor;
  final double? borderRadiusBottomLeft;
  final double? borderRadiusBottomRight;
  final double? borderRadiusTopRight;
  final double? borderRadiusTopLeft;
  final double? borderRadiusAll;

  final bool? asideByAside;
  final TextInputAction? textInputAction;
  final Function(PhoneNumber val) onChanged;
  final Function(Country val) onCountryChanged;
  @override
  State<MyPhoneField> createState() => _MyPhoneFieldState();
}

class _MyPhoneFieldState extends State<MyPhoneField> {
  @override
  Widget build(BuildContext context) {
    return IntlPhoneField(
      textInputAction: widget.textInputAction,
      initialCountryCode: 'IQ',
      showDropdownIcon: true,
      textAlign: TextAlign.right,
      decoration: InputDecoration(
        counterText: "",
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        labelText: widget.labelText,
        hintText: widget.hintText,
        hintStyle: TextStyle(
          fontSize: 13,
          color: widget.labelColor,
          height: 1,
        ),
      ),
      onChanged: widget.onChanged,
      onCountryChanged: widget.onCountryChanged,
    );
  }
}
//=======================================================
