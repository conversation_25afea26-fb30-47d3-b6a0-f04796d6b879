import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/components/common_datetime_picker.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:provider/provider.dart';

class InvoiceListFilterDialogWidget extends StatefulWidget {
  const InvoiceListFilterDialogWidget({super.key, required this.onSearch});

  final Function onSearch;
  @override
  State<InvoiceListFilterDialogWidget> createState() =>
      _InvoiceListFilterDialogWidgetState();
}

class _InvoiceListFilterDialogWidgetState
    extends State<InvoiceListFilterDialogWidget> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var filterData = Provider.of<InvoiceController>(context).filterModel;

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 10),
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            constraints: BoxConstraints(
              maxWidth: context.width,
              maxHeight: context.height * 0.9,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context),
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        _buildDateRangeSection(context, filterData),
                   //     const SizedBox(height: 20),
                       // _buildInvoiceTypeSection(context, filterData),
                        const SizedBox(height: 20),
                        _buildInvoiceCodeSection(context, filterData),
                        const SizedBox(height: 20),
                        _buildCustomerSection(context, filterData),
                        const SizedBox(height: 30),
                        _buildActionButtons(context, filterData),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.newPrimaryColor,
            context.newPrimaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.filter_list,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  T("Filter Invoices"),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  T("Refine your search results"),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeSection(BuildContext context, dynamic filterData) {
    return _buildSection(
      title: T("Date Range"),
      icon: Icons.date_range,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade50,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T("From Date"),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: context.newTextColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: MyDatePicker(
                          width: double.infinity,
                          onSave: (date) {
                            filterData.fromDate = date;
                            setState(() {});
                          },
                          initialVal: filterData.fromDate ?? DateTime.now(),
                          caption: "".myDateFormatter(filterData.fromDate),
                          backColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 15),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T("To Date"),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: context.newTextColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: MyDatePicker(
                          width: double.infinity,
                          onSave: (date) {
                            filterData.toDate = date;
                            setState(() {});
                          },
                          initialVal: filterData.toDate ?? DateTime.now(),
                          caption: "".myDateFormatter(filterData.toDate),
                          backColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildInvoiceTypeSection(BuildContext context, dynamic filterData) {
  //   return _buildSection(
  //     title: T("Invoice Type"),
  //     icon: Icons.receipt_long,
  //     child: Container(
  //       decoration: BoxDecoration(
  //         color: Colors.white,
  //         borderRadius: BorderRadius.circular(12),
  //         border: Border.all(color: Colors.grey.shade300),
  //       ),
  //       child: MyComboBox(
  //         selectedValue: filterData.invoiceType?.index,
  //         caption: filterData.invoiceTypeName ?? T('Select Invoice Type'),
  //         height: 55,
  //         width: double.infinity,
  //         onSelect: (int id, String name) {
  //           filterData.invoiceType = SalesType.values[id];
  //           filterData.invoiceTypeName = name;
  //           setState(() {});
  //         },
  //         modalTitle: T('Invoice Type'),
  //         data: [
  //           ComboBoxDataModel(id: 3, name: T("Sales Invoices")),
  //           ComboBoxDataModel(id: 4, name: T("Return Invoices")),
  //           ComboBoxDataModel(id: 0, name: T("Sales Orders")),
  //         ],
  //         isShowLabel: false,
  //         labelText: "",
  //       ),
  //     ),
  //   );
  // }

  Widget _buildInvoiceCodeSection(BuildContext context, dynamic filterData) {
    return _buildSection(
      title: T("Invoice Number"),
      icon: Icons.numbers,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: CommonTextField(
          initialValue: filterData.invoiceCode,
          floatingLabelBehavior: FloatingLabelBehavior.never,
          label: T('Enter invoice number'),
          onChanged: (value) {
            filterData.invoiceCode = value;
          },
        ),
      ),
    );
  }

  Widget _buildCustomerSection(BuildContext context, dynamic filterData) {
    return _buildSection(
      title: T("Customer"),
      icon: Icons.person,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: MyComboBox(
          selectedValue: filterData.customerId,
          caption: filterData.customerName ?? T('Select Customer'),
          height: 55,
          width: double.infinity,
          onSelect: (int id, String name) {
            filterData.customerId = id;
            filterData.customerName = name;
            setState(() {});
          },
          modalTitle: T('Select Customer'),
          data: Provider.of<CustomerController>(context, listen: false)
              .customers
              .map(
                (e) => ComboBoxDataModel(id: e.iD ?? 0, name: e.name ?? ""),
              )
              .toList(),
          isShowLabel: false,
          labelText: "",
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.newPrimaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: context.newPrimaryColor,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: context.newTextColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        child,
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, dynamic filterData) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _clearFilters(filterData),
            icon: const Icon(Icons.clear_all),
            label: Text(T("Clear All")),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: Colors.grey.shade400),
              foregroundColor: Colors.grey.shade700,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop();
              widget.onSearch();
            },
            icon: const Icon(Icons.search),
            label: Text(T("Apply Filters")),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.newPrimaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _clearFilters(dynamic filterData) {
    setState(() {
      filterData.fromDate = null;
      filterData.toDate = null;
      filterData.invoiceType = null;
      filterData.invoiceTypeName = null;
      filterData.invoiceCode = null;
      filterData.customerId = null;
      filterData.customerName = null;
    });
  }
}
