import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';

class Warehouse3DToolbarWidget extends StatelessWidget {
  final VoidCallback? onAddCabinet;
  final VoidCallback? onAddShelf;
  final VoidCallback? onToggleMode;
  final bool isAddingCabinet;
  final bool isAddingShelf;

  const Warehouse3DToolbarWidget({
    Key? key,
    this.onAddCabinet,
    this.onAddShelf,
    this.onToggleMode,
    this.isAddingCabinet = false,
    this.isAddingShelf = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 80,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          const SizedBox(width: 16),

          // أيقونة المستودع
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF3498DB).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.warehouse,
              color: Color(0xFF3498DB),
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // عنوان الشريط
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  T('أدوات التصميم'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
                Text(
                  T('اختر أداة لإضافة عناصر للمستودع'),
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // أدوات التحكم
          Row(
            children: [
              // زر إضافة خزانة
              _buildToolButton(
                icon: Icons.inbox,
                label: T('خزانة'),
                isActive: isAddingCabinet,
                color: const Color(0xFF3498DB),
                onTap: onAddCabinet,
              ),

              const SizedBox(width: 8),

              // زر إضافة رف
              _buildToolButton(
                icon: Icons.shelves,
                label: T('رف'),
                isActive: isAddingShelf,
                color: const Color(0xFF27AE60),
                onTap: onAddShelf,
              ),

              const SizedBox(width: 8),

              // زر إلغاء التحديد
              _buildToolButton(
                icon: Icons.mouse,
                label: T('تحديد'),
                isActive: !isAddingCabinet && !isAddingShelf,
                color: const Color(0xFF95A5A6),
                onTap: onToggleMode,
              ),
            ],
          ),

          const SizedBox(width: 16),
        ],
      ),
    );
  }

  Widget _buildToolButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required Color color,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive ? color : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isActive ? color : color.withOpacity(0.3),
            width: 1.5,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isActive ? Colors.white : color,
              size: 20,
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                color: isActive ? Colors.white : color,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
