import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/branch_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/setting/change_password_screen.dart';
import 'package:provider/provider.dart';

class MyProfileScreen extends StatefulWidget {
  const MyProfileScreen({super.key});

  @override
  State<MyProfileScreen> createState() => _MyProfileScreenState();
}

class _MyProfileScreenState extends State<MyProfileScreen> {
  String currentBranchName = "";
  String deviceCode = "";

  @override
  void initState() {
    super.initState();
    _loadProfileData();
  }

  Future<void> _loadProfileData() async {
    // Get current branch name
    try {
      final branchController =
          Provider.of<BranchController>(context, listen: false);
      final branch =
          await branchController.getBranchById(AppController.currentBranchId);
      if (branch != null) {
        setState(() {
          currentBranchName = branch['Name'] ?? '';
        });
      }
    } catch (e) {
      print('Error loading branch: $e');
    }

    // Get device code
    try {
      final deviceCodeValue =
          LocaleManager.instance.getStringValue(PreferencesKeys.DeviceCode);
      setState(() {
        deviceCode = deviceCodeValue.toString();
      });
    } catch (e) {
      print('Error loading device code: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              CommonHeader(icon: Icons.person, title: T("الملف الشخصي")),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                child: Column(
                  children: [
                    _buildProfileHeader(),
                    const SizedBox(height: 20),
                    _buildUserInfo(),
                    const SizedBox(height: 20),
                    _buildSystemInfo(),
                    const SizedBox(height: 20),
                    _buildActionButtons(),
                    const SizedBox(height: 60),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.newPrimaryColor,
            context.newPrimaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: context.newPrimaryColor.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Profile Avatar
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(40),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 2,
              ),
            ),
            child: const Icon(
              Icons.person,
              size: 40,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),
          // User Name
          Text(
            AuthController.getUserName().isNotEmpty
                ? AuthController.getUserName()
                : "مستخدم",
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          // User Type Badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              AuthController.getIsMainAdmin() ? "المدير الرئيسي" : "مستخدم",
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "معلومات المستخدم",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.newSecondaryColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            icon: Icons.person_outline,
            label: "اسم المستخدم",
            value: AuthController.getUserName().isNotEmpty
                ? AuthController.getUserName()
                : "غير متوفر",
          ),
          const Divider(height: 24),
          _buildInfoRow(
            icon: Icons.badge_outlined,
            label: "رقم المستخدم",
            value: AuthController.getUserId().toString(),
          ),
          const Divider(height: 24),
          _buildInfoRow(
            icon: Icons.business_outlined,
            label: "الفرع الحالي",
            value: currentBranchName.isNotEmpty
                ? currentBranchName
                : "جاري التحميل...",
          ),
          const Divider(height: 24),
          _buildInfoRow(
            icon: Icons.admin_panel_settings_outlined,
            label: "نوع الحساب",
            value: AuthController.getIsMainAdmin() ? "مدير" : "مستخدم عادي",
          ),
        ],
      ),
    );
  }

  Widget _buildSystemInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "معلومات النظام",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.newSecondaryColor,
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow(
            icon: Icons.devices_outlined,
            label: "رمز الجهاز",
            value: deviceCode.isNotEmpty ? deviceCode : "غير متوفر",
          ),
          const Divider(height: 24),
          _buildInfoRow(
            icon: Icons.access_time_outlined,
            label: "آخر تسجيل دخول",
            value: DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now()),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Change Password Button
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.blue.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const ChangePasswordScreen(),
                ),
              );
            },
            icon: const Icon(Icons.lock_outline),
            label: Text("تغيير كلمة المرور"),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 54),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
          ),
        ),
        const SizedBox(height: 12),
        // Logout Button
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.red.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ElevatedButton.icon(
            onPressed: () async {
              await _showLogoutConfirmationDialog();
            },
            icon: const Icon(Icons.logout),
            label: Text("تسجيل الخروج"),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              minimumSize: const Size(double.infinity, 54),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: context.newPrimaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 20,
            color: context.newPrimaryColor,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  color: context.newTextColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _showLogoutConfirmationDialog() async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.logout,
                color: Colors.red,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                "تسجيل الخروج",
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Text(
            "هل أنت متأكد من أنك تريد تسجيل الخروج؟",
            style: const TextStyle(fontSize: 16),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                "إلغاء",
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                pleaseWaitDialog(context: context, isShown: true);

                try {
                  // Logout logic here
                  await Future.delayed(const Duration(seconds: 1));
                  AppController.isAuth = false;

                  // Navigate to login screen
                  Navigator.of(context).pushNamedAndRemoveUntil(
                    '/signin',
                    (Route<dynamic> route) => false,
                  );
                } catch (e) {
                  pleaseWaitDialog(context: context, isShown: false);
                  errorSnackBar(message: T("Logout failed"));
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(
                "تسجيل الخروج",
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
