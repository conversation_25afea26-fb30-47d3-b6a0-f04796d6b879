import 'package:flutter/material.dart';
import 'package:inventory_application/services/report_printer_service.dart';
import 'package:inventory_application/models/dto/reports/server/sales_summary_report_dto.dart';

/// مساعد لإضافة وظائف الطباعة للتقارير
class ReportPrintHelper {
  /// إضافة زر طباعة لتقرير ملخص المبيعات
  static Widget buildSalesSummaryPrintButton({
    required BuildContext context,
    required SalesSummaryReportDTO? reportData,
    String? reportTitle,
    DateTime? fromDate,
    DateTime? toDate,
    bool isLoading = false,
  }) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      child: ElevatedButton.icon(
        onPressed: (reportData != null && !isLoading)
            ? () => _printSalesSummaryReport(
                context, reportData, reportTitle, fromDate, toDate)
            : null,
        icon: isLoading
            ? const Sized<PERSON><PERSON>(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.print),
        label: Text(
          isLoading ? 'جاري الطباعة...' : 'طباعة التقرير',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  /// إضافة زر طباعة عام لأي تقرير
  static Widget buildGenericPrintButton({
    required BuildContext context,
    required Map<String, dynamic>? reportData,
    required String reportTitle,
    DateTime? fromDate,
    DateTime? toDate,
    List<Map<String, String>>? customFields,
    bool isLoading = false,
  }) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.all(16),
      child: ElevatedButton.icon(
        onPressed: (reportData != null && !isLoading)
            ? () => _printGenericReport(context, reportData, reportTitle,
                fromDate, toDate, customFields)
            : null,
        icon: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            : const Icon(Icons.print),
        label: Text(
          isLoading ? 'جاري الطباعة...' : 'طباعة التقرير',
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  /// إضافة FloatingActionButton للطباعة
  static Widget buildFloatingPrintButton({
    required BuildContext context,
    required bool hasData,
    required VoidCallback onPressed,
  }) {
    return FloatingActionButton.extended(
      onPressed: hasData ? onPressed : null,
      backgroundColor: hasData ? Colors.green : Colors.grey,
      icon: const Icon(Icons.print, color: Colors.white),
      label: const Text(
        'طباعة',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// إضافة عنصر قائمة للطباعة في AppBar
  static PopupMenuItem<String> buildPrintMenuItem() {
    return const PopupMenuItem<String>(
      value: 'print',
      child: Row(
        children: [
          Icon(Icons.print, color: Colors.green),
          SizedBox(width: 8),
          Text('طباعة التقرير'),
        ],
      ),
    );
  }

  /// تنفيذ طباعة تقرير ملخص المبيعات
  static Future<void> _printSalesSummaryReport(
    BuildContext context,
    SalesSummaryReportDTO reportData,
    String? reportTitle,
    DateTime? fromDate,
    DateTime? toDate,
  ) async {
    try {
      await ReportPrinterService.printSalesSummaryReport(
        reportData,
        context,
        reportTitle: reportTitle ?? 'تقرير ملخص المبيعات',
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الطباعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تنفيذ طباعة تقرير عام
  static Future<void> _printGenericReport(
    BuildContext context,
    Map<String, dynamic> reportData,
    String reportTitle,
    DateTime? fromDate,
    DateTime? toDate,
    List<Map<String, String>>? customFields,
  ) async {
    try {
      await ReportPrinterService.printGenericReport(
        reportData,
        reportTitle,
        context,
        fromDate: fromDate,
        toDate: toDate,
        customFields: customFields,
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الطباعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// مربع حوار تأكيد الطباعة
  static Future<bool?> showPrintConfirmationDialog(
    BuildContext context, {
    String? reportTitle,
  }) async {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text(
            'تأكيد الطباعة',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          content: Text(
            'هل تريد طباعة ${reportTitle ?? "التقرير"}؟',
            style: const TextStyle(fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text(
                'إلغاء',
                style: TextStyle(color: Colors.grey),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: const Text('طباعة'),
            ),
          ],
        );
      },
    );
  }

  /// إنشاء بيانات مخصصة للتقرير العام
  static List<Map<String, String>> createCustomFields({
    String? totalSales,
    String? totalReturns,
    String? totalReceived,
    String? totalRemaining,
    String? invoicesCount,
    String? customersCount,
    String? productsCount,
  }) {
    final List<Map<String, String>> fields = [];

    if (totalSales != null) {
      fields.add({'label': 'إجمالي المبيعات', 'value': totalSales});
    }
    if (totalReturns != null) {
      fields.add({'label': 'إجمالي المرتجعات', 'value': totalReturns});
    }
    if (totalReceived != null) {
      fields.add({'label': 'إجمالي المستلم', 'value': totalReceived});
    }
    if (totalRemaining != null) {
      fields.add({'label': 'إجمالي المتبقي', 'value': totalRemaining});
    }
    if (invoicesCount != null) {
      fields.add({'label': 'عدد الفواتير', 'value': invoicesCount});
    }
    if (customersCount != null) {
      fields.add({'label': 'عدد العملاء', 'value': customersCount});
    }
    if (productsCount != null) {
      fields.add({'label': 'عدد المنتجات', 'value': productsCount});
    }

    return fields;
  }
}
