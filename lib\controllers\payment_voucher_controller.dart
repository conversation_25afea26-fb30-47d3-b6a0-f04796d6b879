import 'dart:convert';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/accounts_vouchers_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/accounts_voucher_counter.dart';
import 'package:inventory_application/models/model/vouchar_model.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';

class PaymentVoucherController with ChangeNotifier {
  AccountsVoucharsModel paymentVoucher = AccountsVoucharsModel();
  List<AccountsVoucharsModel> paymentVouchers = [];

  //---------------------------------------------------------------------------
  Future<String> getPaymentVoucherNumber() async {
    try {
      var number = await AccountsVoucherCounterGenerator.getNextCounterByType(
          AccountsVoucharType.PaymentVoucher);
      notifyListeners();
      return number;
    } catch (e) {
      print(e);
      return "";
    }
  }

  //---------------------------------------------------------------------------
  void clearPaymentVoucher() {
    paymentVoucher = AccountsVoucharsModel();
    notifyListeners();
  }

  //---------------------------------------------------------------------------
  void updatePaymentVoucherField({
    String? note,
    double? voucharAmount,
    int? accountIdDebit,
    int? accountIdCredit,
    int? currencyId,
    double? exchangeRate,
    String? voucharDateFormated,
    String? customerName,
    String? invoiceNo,
    int? paymentTypeId,
  }) {
    if (note != null) paymentVoucher.note = note;
    if (voucharAmount != null) paymentVoucher.voucharAmount = voucharAmount;
    if (accountIdDebit != null) paymentVoucher.accountIdDebit = accountIdDebit;
    if (accountIdCredit != null)
      paymentVoucher.accountIdCredit = accountIdCredit;
    if (currencyId != null) paymentVoucher.currencyId = currencyId;
    if (exchangeRate != null) paymentVoucher.exchangeRate = exchangeRate;
    if (voucharDateFormated != null)
      paymentVoucher.voucharDateFormated = voucharDateFormated;
    if (customerName != null) paymentVoucher.customerName = customerName;
    if (invoiceNo != null) paymentVoucher.invoiceNo = invoiceNo;
    if (paymentTypeId != null) paymentVoucher.paymentTypeId = paymentTypeId;

    notifyListeners();
  }

  //---------------------------------------------------------------------------
  Future<ResponseResultModel> savePaymentVoucher() async {
    try {
      var url = '/AccountsVouchers/Manage?voucher_type=PaymentVoucher';
      final db = AccountsVouchersController();

      // Set payment type
      paymentVoucher.receiptTypeInt = AccountsVoucharType.PaymentVoucher.value;
      paymentVoucher.receiptType =
          AccountsVoucherCounterGenerator.getVoucherTypeDisplayName(
              AccountsVoucharType.PaymentVoucher);

      // Set date if not provided
      paymentVoucher.voucharDateFormated ??=
          DateFormat('dd/MM/yyyy', 'en').format(DateTime.now());

      // Generate code if not provided
      if (paymentVoucher.code == null || paymentVoucher.code == "") {
        paymentVoucher.code = await getPaymentVoucherNumber();
      } else {
        if (await AccountsVoucherCounterGenerator.checkIfCounterUsed(
            AccountsVoucharType.PaymentVoucher, paymentVoucher.code ?? "")) {
          paymentVoucher.code = await getPaymentVoucherNumber();
        }
      }

      // Check if offline
      if (await isThereNetworkConnection() == false) {
        var localId = await db.insertOrUpdateAccountsVoucher(
            SqlLiteInvoiceModel(
                    data: jsonEncode(paymentVoucher.toJson()),
                    status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
                    id: null,
                    localCode: paymentVoucher.code,
                    type: AccountsVoucharType.PaymentVoucher.name)
                .toJson());

        clearPaymentVoucher();
        await AccountsVoucherCounterGenerator.setCounterByTypeAuto(
            type: AccountsVoucharType.PaymentVoucher);
        notifyListeners();

        return ResponseResultModel(isSuccess: true, data: {"localId": localId});
      }

      // Save to server
      var result = await Api.post(
        action: url,
        body: paymentVoucher.toJson(),
      );

      if (result != null && result.isSuccess) {
        await AccountsVoucherCounterGenerator
            .setAccountsVoucherCounterInServer();
        await AccountsVoucherCounterGenerator.setCounterByTypeAuto(
            type: AccountsVoucharType.PaymentVoucher);

        var localId = await db.insertOrUpdateAccountsVoucher(
            SqlLiteInvoiceModel(
                    data: jsonEncode(paymentVoucher.toJson()),
                    status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                    id: null,
                    localCode: paymentVoucher.code,
                    type: AccountsVoucharType.PaymentVoucher.name)
                .toJson());

        clearPaymentVoucher();
        notifyListeners();

        return ResponseResultModel(data: {
          "id": result.data["ID"],
          "code": result.data["Code"],
          "localId": localId
        }, isSuccess: true);
      }

      return ResponseResultModel(
          isSuccess: false, message: [result?.message ?? "Unknown error"]);
    } catch (e) {
      print("Error in savePaymentVoucher: $e");
      return ResponseResultModel(isSuccess: false, message: [e.toString()]);
    }
  }

  //---------------------------------------------------------------------------
  Future<bool> validatePaymentVoucher() async {
    if (paymentVoucher.voucharAmount == null ||
        paymentVoucher.voucharAmount! <= 0) {
      return false;
    }
    if (paymentVoucher.accountIdDebit == null) {
      return false;
    }
    if (paymentVoucher.accountIdCredit == null) {
      return false;
    }
    return true;
  }

  //---------------------------------------------------------------------------
  Future<List<AccountsVoucherDtoWithLiteId>> getPaymentVouchers() async {
    final db = AccountsVouchersController();
    var allVouchers = await db.fetchLocalVouchers();

    // Filter only payment vouchers
    return allVouchers
        .where((voucher) =>
            voucher.data?.receiptTypeInt ==
            AccountsVoucharType.PaymentVoucher.value)
        .toList();
  }
}
