import 'package:flutter/material.dart';
import 'package:virtual_keyboard_multi_language/virtual_keyboard_multi_language.dart';

class CommonVirtualKeyboardWidget extends StatefulWidget {
  const CommonVirtualKeyboardWidget({
    super.key,
    required this.textEditingController,
    required this.type,
    this.onKeyPress,
    this.height,
  });

  final TextEditingController textEditingController;
  final VirtualKeyboardType type;
  final Function(VirtualKeyboardKey)? onKeyPress;
  final double? height;

  @override
  State<CommonVirtualKeyboardWidget> createState() =>
      _CommonVirtualKeyboardWidgetState();
}

class _CommonVirtualKeyboardWidgetState
    extends State<CommonVirtualKeyboardWidget> {
  @override
  Widget build(BuildContext context) {
    return VirtualKeyboard(
      height: widget.height ?? 300,
      textColor: Colors.black,
      textController: widget.textEditingController,
      type: widget.type,
      postKeyPress: widget.onKeyPress, // This is the correct usage
      defaultLayouts: const [
        VirtualKeyboardDefaultLayouts.English,
        VirtualKeyboardDefaultLayouts.Arabic,
      ],
    );
  }
}
