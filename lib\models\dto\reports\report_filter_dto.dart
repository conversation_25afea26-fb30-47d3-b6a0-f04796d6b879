import 'package:flutter/material.dart';

enum ReportType {
  dailySales,
  monthlySales,
  productSales,
  customerSales,
}

class ReportFilterDTO {
  DateTime? fromDate;
  DateTime? toDate;
  int? customerId;
  String? customerName;
  int? productId;
  String? productName;
  int? warehouseId;
  String? warehouseName;
  ReportType reportType;

  ReportFilterDTO({
    this.fromDate,
    this.toDate,
    this.customerId,
    this.customerName,
    this.productId,
    this.productName,
    this.warehouseId,
    this.warehouseName,
    required this.reportType,
  });

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
      'customerId': customerId,
      'customerName': customerName,
      'productId': productId,
      'productName': productName,
      'warehouseId': warehouseId,
      'warehouseName': warehouseName,
      'reportType': reportType.toString(),
    };
  }

  // Create from JSON
  factory ReportFilterDTO.fromJson(Map<String, dynamic> json) {
    return ReportFilterDTO(
      fromDate: json['fromDate'] != null ? DateTime.parse(json['fromDate']) : null,
      toDate: json['toDate'] != null ? DateTime.parse(json['toDate']) : null,
      customerId: json['customerId'],
      customerName: json['customerName'],
      productId: json['productId'],
      productName: json['productName'],
      warehouseId: json['warehouseId'],
      warehouseName: json['warehouseName'],
      reportType: _parseReportType(json['reportType']),
    );
  }

  // Helper method to parse report type from string
  static ReportType _parseReportType(String? typeString) {
    if (typeString == null) return ReportType.dailySales;
    
    switch (typeString) {
      case 'ReportType.dailySales':
        return ReportType.dailySales;
      case 'ReportType.monthlySales':
        return ReportType.monthlySales;
      case 'ReportType.productSales':
        return ReportType.productSales;
      case 'ReportType.customerSales':
        return ReportType.customerSales;
      default:
        return ReportType.dailySales;
    }
  }

  // Create a copy with updated values
  ReportFilterDTO copyWith({
    DateTime? fromDate,
    DateTime? toDate,
    int? customerId,
    String? customerName,
    int? productId,
    String? productName,
    int? warehouseId,
    String? warehouseName,
    ReportType? reportType,
  }) {
    return ReportFilterDTO(
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      customerId: customerId ?? this.customerId,
      customerName: customerName ?? this.customerName,
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      warehouseId: warehouseId ?? this.warehouseId,
      warehouseName: warehouseName ?? this.warehouseName,
      reportType: reportType ?? this.reportType,
    );
  }
}
