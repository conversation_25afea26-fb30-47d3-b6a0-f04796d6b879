import 'package:dotted_decoration/dotted_decoration.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class CommonCollapseWidget extends StatefulWidget {
  const CommonCollapseWidget(
      {super.key,
      required this.header,
      required this.body,
      required this.isDottedDecoration,
      required this.headerColor,
      this.isOpen,
      this.headerMargin});

  final Widget header;
  final Widget body;
  final bool isDottedDecoration;
  final Color headerColor;
  final bool? isOpen;
  final EdgeInsetsGeometry? headerMargin;

  @override
  State<CommonCollapseWidget> createState() => _CommonCollapseWidgetState();
}

class _CommonCollapseWidgetState extends State<CommonCollapseWidget>
    with SingleTickerProviderStateMixin {
  bool isOped = false;
  late AnimationController _animationController;
  late Animation<double> _animation;
  @override
  void initState() {
    setState(() {
      // isOped = false;
      isOped = widget.isOpen ?? false;
    });
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    _animation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut,
      ),
    );
    isOped == true ? _openCollapse() : true;
  }

  void _toggle() {
    setState(() {
      isOped = !isOped;
      if (isOped) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  void _openCollapse() async {
    await Future.delayed(const Duration(milliseconds: 200));
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        InkWell(
          onTap: () {
            _toggle();
          },
          child: Container(
            margin: widget.headerMargin,
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            decoration: BoxDecoration(
              color: widget.headerColor,
              borderRadius: BorderRadius.circular(15),
            ),
            child: widget.header,
          ),
        ),
        const SizedBox(height: 5),

        // Use AnimatedBuilder for the animated content
        AnimatedBuilder(
          animation: _animation,
          builder: (context, child) {
            return ClipRect(
              child: Align(
                heightFactor: _animation.value,
                alignment: Alignment.topCenter,
                child: Container(
                  padding: const EdgeInsets.all(15),
                  decoration: widget.isDottedDecoration
                      ? DottedDecoration(
                          shape: Shape.box,
                          borderRadius: BorderRadius.circular(15),
                          color: context.onSecondary,
                          strokeWidth: 2,
                        )
                      : const BoxDecoration(
                          color: Colors.white,
                        ),
                  child: widget.body,
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
