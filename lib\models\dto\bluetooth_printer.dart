import 'package:flutter_blue_plus/flutter_blue_plus.dart';

class BluetoothPrinter {
  final String name;
  final String address;
  final bool isConnected;
  final BluetoothDevice? device; // Reference to the actual BluetoothDevice

  BluetoothPrinter({
    required this.name,
    required this.address,
    this.isConnected = false,
    this.device,
  });

  // Create a BluetoothPrinter from a BluetoothDevice
  factory BluetoothPrinter.fromDevice(BluetoothDevice device) {
    return BluetoothPrinter(
      name: device.platformName.isNotEmpty
          ? device.platformName
          : 'Unknown Device',
      address: device.remoteId.str,
      isConnected: device.isConnected,
      device: device,
    );
  }

  @override
  String toString() {
    return 'BluetoothPrinter{name: $name, address: $address, isConnected: $isConnected}';
  }
}
