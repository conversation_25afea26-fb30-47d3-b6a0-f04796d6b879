import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/return_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';

import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/invoices/invoices/returns%20invoice/widgets/create_return_invoice_dialog_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/returns%20invoice/widgets/return_invoice_bottom_navbar_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:inventory_application/screens/shared/add_product_with_selected_attributes_widget.dart';
import 'package:inventory_application/screens/shared/product_attribute_selection_dialog_widget.dart';
import 'package:provider/provider.dart';

class CreateReturnInvoiceScreen extends StatefulWidget {
  const CreateReturnInvoiceScreen({super.key});

  @override
  State<CreateReturnInvoiceScreen> createState() =>
      _CreateReturnInvoiceScreenState();
}

class _CreateReturnInvoiceScreenState extends State<CreateReturnInvoiceScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Navigator.of(context).push(DialogRoute(
        context: context,
        builder: (context) => const CreateReturnInvoiceDialogWidget(),
      ));
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use Consumer to ensure we get the latest data from the controller
    var products = Provider.of<ReturnInvoiceController>(context)
        .selectedReturnInvoiceProduct;
    var provider = Provider.of<ReturnInvoiceController>(context, listen: false);
    return WillPopScope(
      onWillPop: () async {
        var result = await showConfirmDialog(
          title: T('Exit'),
          content: T("Are you sure?"),
          backText: T("back"),
          confirmText: T("exit"),
        );
        if (result == true) {
          if (provider.invoice.id != null) {
            provider.invoice = InvoiceDto();
            provider.selectedReturnInvoiceProduct = [];
          }
        }
        return result ?? false;
      },
      child: Scaffold(
        backgroundColor: context.newBackgroundColor,
        appBar: AppBar(
          elevation: 0,
          toolbarHeight: 60,
          shadowColor: Colors.transparent,
          backgroundColor: Colors.white,
          automaticallyImplyLeading: false,
          title: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      InkWell(
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: context.colors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.arrow_back,
                            color: context.colors.primary,
                            size: 24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        T("Return Invoice"),
                        style: TextStyle(
                          color: context.newTextColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      // _buildCompactButton(
                      //   label: T("Save & View"),
                      //   icon: Icons.visibility_outlined,
                      //   onPressed: () async {
                      //     pleaseWaitDialog(context: context, isShown: true);
                      //     var result = await provider.saveReturnInvoice();

                      //     if (result.isSuccess) {
                      //       var response = result.data;

                      //       // ignore: use_build_context_synchronously
                      //       pleaseWaitDialog(
                      //           context: context, isShown: false);
                      //       successSnackBar(
                      //           message: T("The operation has been saved"));

                      //       // ignore: use_build_context_synchronously
                      //       Navigator.of(context).push(
                      //         MaterialPageRoute(
                      //           builder: (context) =>
                      //               InoviceDetailsForLocalPage(
                      //             id: response.localId ?? 0,
                      //           ),
                      //         ),
                      //       );
                      //       return;
                      //     }

                      //     // ignore: use_build_context_synchronously
                      //     pleaseWaitDialog(
                      //         context: context, isShown: false);

                      //     errorSnackBar(
                      //       message: result.message != null
                      //           ? result.message!.first.toString()
                      //           : T("Not saved"),
                      //     );
                      //   },
                      // ),
                      const SizedBox(width: 8),
                      _buildCompactButton(
                        label: T("Save & New"),
                        icon: Icons.save_outlined,
                        isMain: true,
                        onPressed: () async {
                          pleaseWaitDialog(context: context, isShown: true);
                          var result = await provider.saveReturnInvoice();
                          if (result.isSuccess) {
                            // ignore: use_build_context_synchronously
                            pleaseWaitDialog(context: context, isShown: false);
                            successSnackBar(
                                message: T("The operation has been saved"));

                            // Reset the invoice and set default values
                            provider.selectedReturnInvoiceProduct.clear();
                            provider.invoice = InvoiceDto();

                            // Show dialog to create a new return invoice
                            WidgetsBinding.instance.addPostFrameCallback((_) {
                              Navigator.of(context).push(DialogRoute(
                                context: context,
                                builder: (context) =>
                                    const CreateReturnInvoiceDialogWidget(),
                              ));
                            });

                            return;
                          }
                          // ignore: use_build_context_synchronously
                          pleaseWaitDialog(context: context, isShown: false);
                          errorSnackBar(
                              message: result.message != null
                                  ? result.message!.first.toString()
                                  : T("Not saved"));
                        },
                      ),
                    ],
                  )
                ],
              ),
            ],
          ),
          toolbarOpacity: 1,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(1.0),
            child: Container(
              color: Colors.grey.withOpacity(0.2),
              height: 1.0,
            ),
          ),
        ),
        bottomNavigationBar: const ReturnInvoiceBottomNavbarWidget(),
        body: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SingleChildScrollView(
            padding: EdgeInsets.zero,
            physics: const BouncingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InvoiceSelectProductWidget(
                  selectedProducts: products,
                  onChange: () {
                    // Force UI refresh when products change
                    setState(() {});
                  },
                  onAddProduct: (ProductDTO product) {
                    provider.addProductToSelectedList(product);
                    // Force UI refresh
                    setState(() {});
                  },
                  onRemoveProduct: (int id) {
                    provider.deleteProductFromSelectedList(id);
                    // Force UI refresh
                    setState(() {});
                  },
                  onSearchByBarcode: (String barcode) async {
                    // Get the controller
                    final returnInvoiceController =
                        Provider.of<ReturnInvoiceController>(
                      context,
                      listen: false,
                    );

                    // Process the barcode
                    var result = await returnInvoiceController.getItemByBarcode(
                      barcode: barcode,
                    );

                    // If result is a ProductDTO object rather than a boolean,
                    // it means we need to show the attribute selection dialog
                    if (result is ProductDTO) {
                      final selectedOptions =
                          await showDialog<Map<int, ItemAttributeOption>>(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return AttributeSelectionDialog(product: result);
                        },
                      );

                      if (selectedOptions != null) {
                        addProductWithSelectedAttributes(
                          product: result,
                          selectedOptions: selectedOptions,
                          currentProducts:
                              provider.selectedReturnInvoiceProduct,
                          onUpdateExisting: (updatedProduct, index) {
                            provider.selectedReturnInvoiceProduct[index] =
                                updatedProduct;
                          },
                          onAddNew: (newProduct) {
                            provider.addProductToSelectedList(newProduct);
                          },
                          onAfterChange: () {
                            provider.calculateInvoiceTotal();
                            // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
                            provider.notifyListeners();
                          },
                        );
                      }

                      return true;
                    }

                    // Force UI refresh immediately after barcode scan
                    setState(() {
                      // This will trigger a rebuild with the updated product list
                    });

                    // Add a small delay and refresh again to ensure UI updates
                    await Future.delayed(const Duration(milliseconds: 100));
                    setState(() {});

                    // Show error if product not found
                    if (result == false) {
                      errorSnackBar(
                        message: T(
                            "There is no product associated with the barcode"),
                        context: context,
                      );
                    }

                    return result;
                  },
                ),
                if (products.isNotEmpty) ...[
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.zero,
                    child: InvoiceProductListHeaderWidget(
                      backgroundColor: context.newPrimaryColor,
                      textColor: Colors.white,
                    ),
                  ),
                  Consumer<ReturnInvoiceController>(
                    builder: (context, returnInvoiceControllerUpdate, child) {
                      return ListView.separated(
                        itemCount: products.length,
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        separatorBuilder: (context, index) => Divider(
                          color: Colors.grey.withOpacity(0.2),
                          height: 1,
                          indent: 0,
                          endIndent: 0,
                        ),
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          return InvoiceListItemsWidget(
                            id: products[index].id ?? 0,
                            barcode: products[index].barcode,
                            selectedInvoiceProduct:
                                returnInvoiceControllerUpdate
                                    .selectedReturnInvoiceProduct,
                            onChangeWarehouse: (int productId, int warehouseId,
                                String warehouseName,
                                [String? virtualProductId]) {
                              provider.setProductWarehouse(productId,
                                  warehouseId, warehouseName, virtualProductId);
                              setState(() {});
                            },
                            onDeleteProduct: (int productId,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                provider.deleteProductFromSelectedList(
                                    productId, virtualProductId);
                              } else {
                                provider
                                    .deleteProductFromSelectedList(productId);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdatePrice: (int productId, double price,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                provider.updateProductPrice(
                                    productId, price, virtualProductId);
                              } else {
                                provider.updateProductPrice(productId, price);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdateQuantity: (int productId, double quantity,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                provider.updateProductQuantity(
                                    productId, quantity, virtualProductId);
                              } else {
                                provider.updateProductQuantity(
                                    productId, quantity);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdateUnit: (int productId, ItemPriceDTO unit,
                                [String? virtualProductId]) {
                              provider.updateProductUnit(
                                  productId, unit, virtualProductId);
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                          );
                        },
                      );
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    bool isMain = false,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isMain ? context.newPrimaryColor : Colors.white,
        foregroundColor: isMain ? Colors.white : context.newPrimaryColor,
        elevation: isMain ? 2 : 0,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: isMain
                ? Colors.transparent
                : context.newPrimaryColor.withOpacity(0.5),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
