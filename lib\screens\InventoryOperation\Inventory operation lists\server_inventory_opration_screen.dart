import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';

import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/screens/InventoryOperation/Inventory%20operation%20lists/widget/Inventory_operationList_item_widget.dart';

import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart' as ptr;

class OperationFilter {
  final String label;
  final IconData icon;
  final InventoryOperationType? type; // null means "All"

  OperationFilter({
    required this.label,
    required this.icon,
    this.type,
  });
}

class ServerInventoryOperationsScreen extends StatefulWidget {
  const ServerInventoryOperationsScreen({super.key});

  @override
  _ServerInventoryOperationsScreenState createState() =>
      _ServerInventoryOperationsScreenState();
}

class _ServerInventoryOperationsScreenState
    extends State<ServerInventoryOperationsScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final ptr.RefreshController _refreshController =
      ptr.RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  InventoryOperationType _selectedFilter = InventoryOperationType.values.first;

  String _getOperationLabel(InventoryOperationType type) {
    switch (type) {
      case InventoryOperationType.unknown:
        return 'All';
      case InventoryOperationType.DamagedExpired:
        return 'Damaged/Expired';
      case InventoryOperationType.Incoming:
        return 'Incoming';
      case InventoryOperationType.Outgoing:
        return 'Outgoing';
      case InventoryOperationType.OpeningBalance:
        return 'Opening Balance';
      case InventoryOperationType.ItemsTransfer:
        return 'نقل اصناف';
      case InventoryOperationType.Stocktaking:
        return 'الجرد';
      case InventoryOperationType.Shortage:
        return 'عجز';
      case InventoryOperationType.Surplus:
        return 'فائض';
      case InventoryOperationType.TransferToSection:
        return 'اذن صرف لقسم';
      case InventoryOperationType.TransferToSectionRequset:
        return 'طلب اذن صرف لقسم';
    }
  }

  IconData _getOperationIcon(InventoryOperationType type) {
    switch (type) {
      case InventoryOperationType.unknown:
        return Icons.article_outlined;
      case InventoryOperationType.DamagedExpired:
        return Icons.delete_forever_outlined;
      case InventoryOperationType.Incoming:
        return Icons.download_outlined;
      case InventoryOperationType.Outgoing:
        return Icons.upload_outlined;
      case InventoryOperationType.OpeningBalance:
        return Icons.account_balance_wallet_outlined;
      case InventoryOperationType.ItemsTransfer:
        return Icons.compare_arrows_outlined;
      case InventoryOperationType.Stocktaking:
        return Icons.assignment_outlined;
      case InventoryOperationType.Shortage:
        return Icons.remove_circle_outline;
      case InventoryOperationType.Surplus:
        return Icons.add_circle_outline;
      case InventoryOperationType.TransferToSection:
        return Icons.arrow_forward_ios;
      case InventoryOperationType.TransferToSectionRequset:
        return Icons.arrow_forward_ios;
    }
  }

  final List<InventoryOperationType> filterTypes =
      InventoryOperationType.values;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    Provider.of<InventoryOperationController>(context, listen: false)
        .getInventories();
  }

  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    final controller =
        Provider.of<InventoryOperationController>(context, listen: false);
    await controller.getInventories(
      resetAndRefresh: true,
      transactionsType: _selectedFilter.name,
    );
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    await Provider.of<InventoryOperationController>(context, listen: false)
        .getInventories(transactionsType: _selectedFilter.name);
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    var data =
        Provider.of<InventoryOperationController>(context).inventoryOpration;

    return ApplicationLayout(
        child: Column(
      children: [
        // Header
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                offset: const Offset(0, 2),
                blurRadius: 6,
              )
            ],
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(16),
              bottomRight: Radius.circular(16),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonHeader(
                icon: Icons.inventory_2_outlined,
                title: T("Inventory Operations"),
              ),
            ],
          ),
        ),
        _buildSearchHeader(context),

        // Sync Button

        Container(
          height: 45,
          margin: const EdgeInsets.symmetric(vertical: 12),
          child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: filterTypes.length,
              separatorBuilder: (_, __) => const SizedBox(width: 8),
              itemBuilder: (context, index) {
                final type = filterTypes[index];
                final isSelected = _selectedFilter == type;
                final label = T(_getOperationLabel(type));
                final icon = _getOperationIcon(type);
                if (type == InventoryOperationType.Shortage) {
                  return const SizedBox.shrink();
                }
                if (type == InventoryOperationType.Surplus) {
                  return const SizedBox.shrink();
                }

                return _buildFilterButton(
                  label: label,
                  icon: icon,
                  isSelected: isSelected,
                  onTap: () {
                    setState(() {
                      _selectedFilter = type;
                    });

                    Provider.of<InventoryOperationController>(context,
                            listen: false)
                        .getInventories(
                      transactionsType: type.name,
                      resetAndRefresh: true,
                    );
                  },
                );
              }),
        ),

        // Hybrid SmartRefresher with Mouse Scroll Support
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: _buildSmartRefresherWithMouseSupport(data),
          ),
        ),
      ],
    ));
  }

  Widget _buildSmartRefresherWithMouseSupport(
      List<InventoryOperationModel> data) {
    // For desktop platforms (Windows, macOS, Linux), use a hybrid approach
    if (kIsWeb ||
        defaultTargetPlatform == TargetPlatform.windows ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.linux) {
      // For Windows specifically, we'll wrap SmartRefresher with enhanced scrolling
      return Scrollbar(
        controller: _scrollController,
        thumbVisibility: true,
        trackVisibility: true,
        child: ptr.SmartRefresher(
          enablePullDown: true,
          enablePullUp: true,
          physics: const BouncingScrollPhysics(), // This enables mouse wheel
          header: ptr.WaterDropHeader(
            waterDropColor: context.newPrimaryColor,
          ),
          footer: ptr.CustomFooter(
            builder: (context, mode) {
              Widget body;
              if (mode == ptr.LoadStatus.loading) {
                body = const CupertinoActivityIndicator();
              } else if (mode == ptr.LoadStatus.idle) {
                body = Text(
                  T("Pull up to load more"),
                  style: TextStyle(color: context.newTextColor),
                );
              } else if (mode == ptr.LoadStatus.failed) {
                body = Text(
                  T("Load Failed! Click to retry!"),
                  style: TextStyle(color: context.newTextColor),
                );
              } else if (mode == ptr.LoadStatus.canLoading) {
                body = Text(
                  T("Release to load more"),
                  style: TextStyle(color: context.newTextColor),
                );
              } else {
                body = Text(
                  T("No more operations"),
                  style: TextStyle(color: context.newTextColor),
                );
              }
              return SizedBox(
                height: 55.0,
                child: Center(child: body),
              );
            },
          ),
          controller: _refreshController,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          child: data.isEmpty
              ? Center(child: Text(T("No Inventory found")))
              : ListView.builder(
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: data.length + 1, // +1 for Load More button
                  itemBuilder: (context, index) {
                    // If it's the last item, show Load More button
                    if (index == data.length) {
                      return Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        child: ElevatedButton.icon(
                          onPressed: _onLoading,
                          icon: const Icon(Icons.refresh, size: 20),
                          label: Text(T("Load More")),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.newPrimaryColor,
                            foregroundColor: Colors.white,
                            elevation: 2,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      );
                    }

                    // Regular inventory item
                    final inventory = data[index];
                    return InventoryOperationListItemWidget(
                      transactionsType: _selectedFilter,
                      model: inventory,
                      onDelete: () {},
                    );
                  },
                ),
        ),
      );
    }

    // For mobile platforms, use SmartRefresher as usual
    return Scrollbar(
      controller: _scrollController,
      thumbVisibility: true,
      trackVisibility: true,
      child: ptr.SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        header: ptr.WaterDropHeader(
          waterDropColor: context.newPrimaryColor,
        ),
        footer: ptr.CustomFooter(
          builder: (context, mode) {
            Widget body;
            if (mode == ptr.LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == ptr.LoadStatus.idle) {
              body = Text(
                T("Pull up to load more"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              );
            } else if (mode == ptr.LoadStatus.failed) {
              body = Text(
                T("Load Failed! Click to retry!"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              );
            } else if (mode == ptr.LoadStatus.canLoading) {
              body = Text(
                T("Release to load more"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              );
            } else {
              body = Text(
                T("No more operations"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              );
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: data.isEmpty
            ? Center(child: Text(T("No Inventory found")))
            : SingleChildScrollView(
                child: ListView.builder(
                  itemCount: data.length,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    final inventory = data[index];
                    return InventoryOperationListItemWidget(
                      transactionsType: _selectedFilter,
                      model: inventory,
                      onDelete: () {},
                    );
                  },
                ),
              ),
      ),
    );
  }

  Widget _buildFilterButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? context.newPrimaryColor
              : context.newPrimaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? context.newPrimaryColor
                : context.newPrimaryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : context.newPrimaryColor,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : context.newPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperationInfoRow(
      BuildContext context, String label, String value,
      {bool isTotal = false,
      Color? valueColor,
      bool showCopyButton = false,
      String? valueToCopy}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: context.newTextColor.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  color: valueColor ??
                      (isTotal
                          ? context.newSecondaryColor
                          : context.newTextColor),
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                  fontSize: isTotal ? 16 : 14,
                ),
              ),
              if (showCopyButton &&
                  valueToCopy != null &&
                  valueToCopy.isNotEmpty)
                IconButton(
                  constraints: BoxConstraints.tight(const Size(32, 32)),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: valueToCopy));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(T("$label copied to clipboard")),
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.copy,
                    size: 16,
                    color: context.newPrimaryColor.withOpacity(0.7),
                  ),
                  tooltip: T("Copy to clipboard"),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchHeader(BuildContext context) {
    return Container(
      width: context.width - 20,
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: context.newPrimaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.filter_list,
              color: context.newPrimaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  T("Filter & Search"),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: context.newTextColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  T("Find invoices by date, type, customer or number"),
                  style: TextStyle(
                    fontSize: 12,
                    color: context.newTextColor.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: context.newPrimaryColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) =>
                        InventoryListFilterDialogWidget(
                      onSearch: (
                          {required String fromDate, required String toDate}) {
                        final controller =
                            Provider.of<InventoryOperationController>(context,
                                listen: false);

                        // 🟡 Store the selected dates in the filterModel
                        controller.filterModel.fromDate = fromDate;
                        controller.filterModel.toDate = toDate;

                        // ✅ Also pass the selected transaction type when calling getInvoices
                        controller.getInventories(
                          resetAndRefresh: true,
                          transactionsType: _selectedFilter.name,
                        );
                      },
                    ),
                  );
                },
                borderRadius: BorderRadius.circular(10),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.search,
                        color: Colors.white,
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        T("Filter"),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class InventoryListFilterDialogWidget extends StatefulWidget {
  final Function({
    required String fromDate,
    required String toDate,
  }) onSearch;

  const InventoryListFilterDialogWidget({
    super.key,
    required this.onSearch,
  });

  @override
  State<InventoryListFilterDialogWidget> createState() =>
      _InventoryListFilterDialogWidgetState();
}

class _InventoryListFilterDialogWidgetState
    extends State<InventoryListFilterDialogWidget> {
  DateTime selectedFromDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime selectedToDate = DateTime.now();

  Future<void> _pickFromDate() async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedFromDate,
      firstDate: DateTime(2000),
      lastDate: selectedToDate,
    );
    if (picked != null) {
      setState(() {
        selectedFromDate = picked;
      });
    }
  }

  Future<void> _pickToDate() async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedToDate,
      firstDate: selectedFromDate,
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        selectedToDate = picked;
      });
    }
  }

  void _applyFilter() {
    widget.onSearch(
      fromDate: DateFormat('dd.MM.yyyy', 'en_US').format(selectedFromDate),
      toDate: DateFormat('dd.MM.yyyy', 'en_US').format(selectedToDate),
    );
    Navigator.of(context).pop(); // Close dialog
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text("Filter by Date"),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text("From Date"),
            subtitle: Text(DateFormat('dd.MM.yyyy').format(selectedFromDate)),
            trailing: const Icon(Icons.calendar_today_outlined),
            onTap: _pickFromDate,
          ),
          ListTile(
            title: const Text("To Date"),
            subtitle: Text(DateFormat('dd.MM.yyyy').format(selectedToDate)),
            trailing: Icon(Icons.calendar_today_outlined),
            onTap: _pickToDate,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(), // Cancel
          child: const Text("Cancel"),
        ),
        ElevatedButton(
          onPressed: _applyFilter,
          child: const Text("Apply"),
        ),
      ],
    );
  }
}
