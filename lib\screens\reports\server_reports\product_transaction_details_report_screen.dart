import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/models/dto/reports/product_transaction_details_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/base/database/database_helper .dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/services/server_reports_service.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/controllers/branch_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';

class ProductTransactionDetailsReportScreen extends StatefulWidget {
  const ProductTransactionDetailsReportScreen({Key? key}) : super(key: key);

  @override
  State<ProductTransactionDetailsReportScreen> createState() =>
      _ProductTransactionDetailsReportScreenState();
}

class _ProductTransactionDetailsReportScreenState
    extends State<ProductTransactionDetailsReportScreen>
    with TickerProviderStateMixin {
  // Animation Controllers
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // State Variables
  bool _isLoading = false;
  ProductTransactionReportDTO? _reportData;
  ProductDTO? _selectedProduct;
  List<ProductDTO> _products = [];
  List<ProductDTO> _filteredProducts = [];
  final TextEditingController _productSearchController =
      TextEditingController();

  // Filter Variables
  DateTime? _fromDate;
  DateTime? _toDate;
  int? _selectedStoreId;
  int? _selectedBranchId;
  String? _selectedCombination;
  List<ComboBoxDataModel> _branchesComboData = [];
  List<ComboBoxDataModel> _storesComboData = [];
  List<ComboBoxDataModel> _combinationsComboData = [];

  // Sorting variables
  int _sortColumnIndex = 0;
  bool _sortAscending = true;
  List<ProductTransactionDetailDTO> _sortedTransactions = [];

  // Pagination variables
  int _currentPage = 0;
  int _itemsPerPage = 20;
  final List<int> _itemsPerPageOptions = [
    10,
    20,
    50,
    100,
    -1
  ]; // -1 means show all
  List<ProductTransactionDetailDTO> _paginatedTransactions = [];

  // Services
  final ServerReportsService _reportsService = ServerReportsService();

  // Responsive Design Properties
  bool get isDesktop => MediaQuery.of(context).size.width > 1200;
  bool get isTablet => MediaQuery.of(context).size.width > 600 && !isDesktop;
  bool get isMobile => MediaQuery.of(context).size.width <= 600;

  double get contentPadding => isDesktop ? 24 : (isTablet ? 20 : 16);
  double get cardPadding => isDesktop ? 20 : (isTablet ? 16 : 12);
  double get fontSize => isDesktop ? 1.1 : (isTablet ? 1.05 : 1.0);

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuart,
    ));

    // Start animations
    _animationController.forward();

    // Load initial data
    _loadAllProductsFromDatabase();
    _loadBranchesAndStores();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _productSearchController.dispose();
    super.dispose();
  }

  Future<void> _loadBranchesAndStores() async {
    try {
      // Load branches
      final branchController =
          Provider.of<BranchController>(context, listen: false);
      await branchController.getBranches();

      // Convert branches to ComboBoxDataModel
      setState(() {
        _branchesComboData = branchController.branches
            .map((branch) => ComboBoxDataModel(
                  id: branch.id ?? 0,
                  name: branch.name ?? '',
                ))
            .toList();
      });

      // Load warehouses
      final warehouseController =
          Provider.of<WarehouseController>(context, listen: false);
      await warehouseController.getWarehouses();

      // Convert warehouses to ComboBoxDataModel
      setState(() {
        _storesComboData = warehouseController.warehouses
            .map((warehouse) => ComboBoxDataModel(
                  id: warehouse.id ?? 0,
                  name: warehouse.name ?? '',
                ))
            .toList();
      });
    } catch (e) {
      print('Error loading branches and stores: $e');
    }
  }

  Future<void> _loadAllProductsFromDatabase() async {
    try {
      final db = await DatabaseHelper().database;

      // Build a query for the local database without pagination
      List<String> whereClauses = [];
      List<dynamic> whereArgs = [];

      if (!AppController.isSharedProducts) {
        whereClauses.add('BranchId = ?');
        whereArgs.add(AppController.currentBranchId);
      }

      String? whereClause =
          whereClauses.isNotEmpty ? whereClauses.join(' AND ') : null;
      List<dynamic>? queryArgs = whereArgs.isNotEmpty ? whereArgs : null;

      // Query for ALL products without limit
      List<Map<String, dynamic>> products = await db.query(
        'ProductModel',
        where: whereClause,
        whereArgs: queryArgs,
        orderBy: 'Name ASC', // Order by name for better UX
      );

      List<ProductDTO> loadedProducts = [];

      if (products.isNotEmpty) {
        for (var productData in products) {
          var productModel = ProductModel.fromJson(productData);

          // Simplified loading - just basic product info for selection
          var productDto = ProductDTO(
            id: productModel.iD,
            title: productModel.name,
            code: productModel.code,
            mainImageUrl: productModel.mainImageUrl,
          );

          loadedProducts.add(productDto);
        }
      }

      setState(() {
        _products = loadedProducts;
        _filteredProducts = List.from(_products);
      });
    } catch (e) {
      print('Error loading products from database: $e');
    }
  }

  bool _hasActiveFilters() {
    return _fromDate != null ||
        _toDate != null ||
        _selectedBranchId != null ||
        _selectedStoreId != null ||
        (_selectedCombination != null && _selectedCombination!.isNotEmpty);
  }

  void _filterProducts(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredProducts = List.from(_products);
      } else {
        _filteredProducts = _products.where((product) {
          // Search in title
          bool titleMatch = product.title != null &&
              product.title!.toLowerCase().contains(query.toLowerCase());

          // Search in code (if available)
          bool codeMatch = product.code != null &&
              (product.code!.toLowerCase().contains(query.toLowerCase()) ||
                  product.code!.startsWith(query));

          // Also search for partial word matches
          bool partialMatch = product.title != null &&
              product.title!.split(' ').any(
                  (word) => word.toLowerCase().startsWith(query.toLowerCase()));

          return titleMatch || codeMatch || partialMatch;
        }).toList();
      }
    });
  }

  Future<void> _loadReport() async {
    if (_selectedProduct == null) {
      _showSnackBar(T('يرجى اختيار منتج'), isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final data = await _reportsService.getProductTransactionDetails(
        itemId: _selectedProduct!.id!,
        fromDate: _fromDate,
        toDate: _toDate,
        storeId: _selectedStoreId,
        branchId: _selectedBranchId,
      );

      if (data != null && data.details != null && data.details!.isNotEmpty) {
        setState(() {
          _reportData = data;
          // Extract combinations from loaded data
          _extractCombinationsFromData(data.details!);
          // Sort transactions after loading
          _sortTransactions(data.details!);
        });
        _showSnackBar(T('تم تحميل التقرير بنجاح'));
      } else {
        setState(() {
          _reportData = null;
          _combinationsComboData.clear();
          _selectedCombination = null;
          _sortedTransactions.clear();
          _paginatedTransactions.clear();
        });
        _showSnackBar(T('لا توجد بيانات للمنتج المحدد'), isError: false);
      }
    } catch (e) {
      _showSnackBar(T('حدث خطأ أثناء تحميل التقرير'), isError: true);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _extractCombinationsFromData(List<ProductTransactionDetailDTO> data) {
    // Get unique combinations from the data
    Set<String> uniqueCombinations = {};
    for (var item in data) {
      if (item.attributes != null && item.attributes!.isNotEmpty) {
        uniqueCombinations.add(item.attributes!);
      }
    }

    // Convert to ComboBoxDataModel list
    _combinationsComboData = uniqueCombinations
        .map((combination) => ComboBoxDataModel(
              id: combination.hashCode, // Use hashCode as unique id
              name: combination,
            ))
        .toList();

    // Sort alphabetically
    _combinationsComboData.sort((a, b) => a.name.compareTo(b.name));
  }

  void _sortTransactions(List<ProductTransactionDetailDTO>? transactions) {
    if (transactions == null) {
      _sortedTransactions = [];
      _updatePagination();
      return;
    }

    // Filter by combination if selected
    List<ProductTransactionDetailDTO> filteredData = transactions;

    if (_selectedCombination != null && _selectedCombination!.isNotEmpty) {
      filteredData = transactions
          .where((item) => item.attributes == _selectedCombination)
          .toList();
    }

    _sortedTransactions = List.from(filteredData);

    switch (_sortColumnIndex) {
      case 0: // Date
        _sortedTransactions.sort((a, b) {
          final dateA = DateTime.tryParse(a.date ?? '') ?? DateTime(1900);
          final dateB = DateTime.tryParse(b.date ?? '') ?? DateTime(1900);
          return _sortAscending
              ? dateA.compareTo(dateB)
              : dateB.compareTo(dateA);
        });
        break;
      case 1: // Transaction Type
        _sortedTransactions.sort((a, b) {
          final typeA = a.transactionTypeDisplayName;
          final typeB = b.transactionTypeDisplayName;
          return _sortAscending
              ? typeA.compareTo(typeB)
              : typeB.compareTo(typeA);
        });
        break;
      case 2: // Store
        _sortedTransactions.sort((a, b) {
          final storeA = a.store ?? '';
          final storeB = b.store ?? '';
          return _sortAscending
              ? storeA.compareTo(storeB)
              : storeB.compareTo(storeA);
        });
        break;
      case 3: // Branch ID
        _sortedTransactions.sort((a, b) {
          final branchA = a.branchId ?? 0;
          final branchB = b.branchId ?? 0;
          return _sortAscending
              ? branchA.compareTo(branchB)
              : branchB.compareTo(branchA);
        });
        break;
      case 4: // Quantity
        _sortedTransactions.sort((a, b) {
          final quantityA = a.quantity ?? 0;
          final quantityB = b.quantity ?? 0;
          return _sortAscending
              ? quantityA.compareTo(quantityB)
              : quantityB.compareTo(quantityA);
        });
        break;
      case 5: // Attributes
        _sortedTransactions.sort((a, b) {
          final attrA = a.attributes ?? '';
          final attrB = b.attributes ?? '';
          return _sortAscending
              ? attrA.compareTo(attrB)
              : attrB.compareTo(attrA);
        });
        break;
    }

    _updatePagination();
  }

  // Update pagination after sorting or filtering
  void _updatePagination() {
    if (_itemsPerPage == -1) {
      // Show all items
      _paginatedTransactions = List.from(_sortedTransactions);
    } else {
      // Calculate pagination
      final totalItems = _sortedTransactions.length;
      final totalPages = (totalItems / _itemsPerPage).ceil();

      // Reset current page if it's out of bounds
      if (_currentPage >= totalPages && totalPages > 0) {
        _currentPage = totalPages - 1;
      } else if (_currentPage < 0) {
        _currentPage = 0;
      }

      final startIndex = _currentPage * _itemsPerPage;
      final endIndex = (startIndex + _itemsPerPage).clamp(0, totalItems);

      _paginatedTransactions =
          _sortedTransactions.sublist(startIndex, endIndex);
    }
  }

  // Change items per page
  void _changeItemsPerPage(int newItemsPerPage) {
    setState(() {
      _itemsPerPage = newItemsPerPage;
      _currentPage = 0; // Reset to first page
      _updatePagination();
    });
  }

  // Go to next page
  void _nextPage() {
    if (_itemsPerPage == -1) return; // No pagination when showing all

    final totalPages = (_sortedTransactions.length / _itemsPerPage).ceil();
    if (_currentPage < totalPages - 1) {
      setState(() {
        _currentPage++;
        _updatePagination();
      });
    }
  }

  // Go to previous page
  void _previousPage() {
    if (_itemsPerPage == -1) return; // No pagination when showing all

    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
        _updatePagination();
      });
    }
  }

  // Go to specific page
  void _goToPage(int page) {
    if (_itemsPerPage == -1) return; // No pagination when showing all

    final totalPages = (_sortedTransactions.length / _itemsPerPage).ceil();
    if (page >= 0 && page < totalPages) {
      setState(() {
        _currentPage = page;
        _updatePagination();
      });
    }
  }

  // Get total pages count
  int get _totalPages {
    if (_itemsPerPage == -1) return 1;
    return (_sortedTransactions.length / _itemsPerPage).ceil();
  }

  void _onSort(int columnIndex, bool ascending) {
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
      _sortTransactions(_reportData?.details);
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: TextStyle(
            fontSize: 14 * fontSize,
            fontWeight: FontWeight.w500,
          ),
        ),
        backgroundColor: isError ? Colors.red : Colors.green,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.all(contentPadding),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: Duration(seconds: isError ? 4 : 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: _buildModernAppBar(),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: _buildMainContent(),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.analytics_outlined,
              color: Colors.white,
              size: isDesktop ? 24 : 20,
            ),
          ),
          SizedBox(width: cardPadding * 0.5),
          Expanded(
            child: Text(
              T('تقرير تفاصيل معاملات المنتج'),
              style: TextStyle(
                fontSize: (20 * fontSize).clamp(18, 24),
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
      backgroundColor: const Color(0xFF3B82F6),
      elevation: 0,
      centerTitle: false,
      actions: [
        if (_hasActiveFilters())
          Container(
            margin: EdgeInsets.symmetric(horizontal: cardPadding * 0.5),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.filter_alt,
                  color: Colors.white,
                  size: isDesktop ? 16 : 14,
                ),
                const SizedBox(width: 4),
                Text(
                  T('فلاتر مطبقة'),
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: (12 * fontSize).clamp(10, 14),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        IconButton(
          onPressed: () {
            _showSnackBar(T(
                'تقرير شامل لتفاصيل معاملات المنتج مع إمكانية الفلترة والترتيب'));
          },
          icon: Icon(
            Icons.info_outline,
            color: Colors.white,
            size: isDesktop ? 22 : 20,
          ),
        ),
        SizedBox(width: cardPadding * 0.5),
      ],
    );
  }

  Widget _buildMainContent() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(contentPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product Selection Section
          _buildProductDropdown(),

          if (_selectedProduct != null) ...[
            _buildFiltersSection(context),
          ],

          if (_isLoading) ...[
            SizedBox(height: contentPadding),
            _buildLoadingWidget(),
          ] else if (_reportData != null) ...[
            SizedBox(height: contentPadding),

            // Summary Cards
            _buildSummaryCards(),

            SizedBox(height: contentPadding),

            // Totals Section (Detailed breakdown by attributes and branches)
            _buildTotalsSection(),

            SizedBox(height: contentPadding),

            // Transaction Details Section
            _buildTransactionDetailsSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildProductDropdown() {
    final selectedProductName = _selectedProduct?.title ?? T('اختيار المنتج');

    return Container(
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: const Color(0xFF3B82F6).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.inventory_2,
                  color: const Color(0xFF3B82F6),
                  size: isDesktop ? 20 : 18,
                ),
              ),
              SizedBox(width: cardPadding * 0.6),
              Text(
                T('اختيار المنتج'),
                style: TextStyle(
                  fontSize: (18 * fontSize).clamp(16, 20),
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF1F2937),
                ),
              ),
              const Spacer(),
              if (_products.isNotEmpty)
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: const Color(0xFF10B981).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_products.length}',
                    style: TextStyle(
                      fontSize: (10 * fontSize).clamp(8, 12),
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF10B981),
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: cardPadding * 0.8),
          InkWell(
            onTap: _showProductSelectionDialog,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: EdgeInsets.all(cardPadding * 0.8),
              decoration: BoxDecoration(
                border: Border.all(
                  color: _selectedProduct != null
                      ? const Color(0xFF3B82F6)
                      : const Color(0xFFD1D5DB),
                ),
                borderRadius: BorderRadius.circular(12),
                color: _selectedProduct != null
                    ? const Color(0xFF3B82F6).withOpacity(0.05)
                    : Colors.white,
              ),
              child: Row(
                children: [
                  Icon(
                    _selectedProduct != null
                        ? Icons.check_circle
                        : Icons.inventory,
                    color: _selectedProduct != null
                        ? const Color(0xFF3B82F6)
                        : const Color(0xFF9CA3AF),
                    size: isDesktop ? 20 : 18,
                  ),
                  SizedBox(width: cardPadding * 0.6),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          selectedProductName,
                          style: TextStyle(
                            fontSize: (16 * fontSize).clamp(14, 18),
                            fontWeight: FontWeight.w600,
                            color: _selectedProduct != null
                                ? const Color(0xFF3B82F6)
                                : const Color(0xFF6B7280),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (_selectedProduct?.code != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            '${T('الكود')}: ${_selectedProduct!.code}',
                            style: TextStyle(
                              fontSize: (12 * fontSize).clamp(10, 14),
                              color: const Color(0xFF9CA3AF),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_drop_down,
                    color: const Color(0xFF9CA3AF),
                    size: isDesktop ? 24 : 20,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showProductSelectionDialog() {
    _productSearchController.clear();
    _filteredProducts = List.from(_products);

    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => StatefulBuilder(
        builder: (BuildContext context, StateSetter setModalState) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.8,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Header
                Container(
                  padding: EdgeInsets.all(cardPadding),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF8FAFC),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                    ),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.inventory_2, color: Color(0xFF3B82F6)),
                      SizedBox(width: cardPadding * 0.6),
                      Expanded(
                        child: Text(
                          T('اختيار المنتج'),
                          style: TextStyle(
                            fontSize: (18 * fontSize).clamp(16, 20),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (_products.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: const Color(0xFF10B981).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            '${_products.length}',
                            style: TextStyle(
                              fontSize: (10 * fontSize).clamp(8, 12),
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF10B981),
                            ),
                          ),
                        ),
                      SizedBox(width: cardPadding * 0.3),
                      IconButton(
                        onPressed: () {
                          _productSearchController.clear();
                          Navigator.pop(context);
                        },
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),

                // Search Field
                Padding(
                  padding: EdgeInsets.all(cardPadding),
                  child: TextField(
                    controller: _productSearchController,
                    onChanged: (query) {
                      setModalState(() {
                        if (query.isEmpty) {
                          _filteredProducts = List.from(_products);
                        } else {
                          _filteredProducts = _products.where((product) {
                            // Search in title
                            bool titleMatch = product.title != null &&
                                product.title!
                                    .toLowerCase()
                                    .contains(query.toLowerCase());

                            // Search in code (if available)
                            bool codeMatch = product.code != null &&
                                (product.code!
                                        .toLowerCase()
                                        .contains(query.toLowerCase()) ||
                                    product.code!.startsWith(query));

                            // Also search for partial word matches
                            bool partialMatch = product.title != null &&
                                product.title!.split(' ').any((word) => word
                                    .toLowerCase()
                                    .startsWith(query.toLowerCase()));

                            return titleMatch || codeMatch || partialMatch;
                          }).toList();
                        }
                      });
                    },
                    decoration: InputDecoration(
                      hintText: T('البحث عن منتج...'),
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: const Color(0xFFF8FAFC),
                    ),
                  ),
                ),

                // Products List
                Expanded(
                  child: _filteredProducts.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                _productSearchController.text.isNotEmpty
                                    ? Icons.search_off
                                    : Icons.inventory_2_outlined,
                                size: isDesktop ? 64 : 48,
                                color: const Color(0xFF9CA3AF),
                              ),
                              SizedBox(height: cardPadding),
                              Text(
                                _productSearchController.text.isNotEmpty
                                    ? T('لم يتم العثور على منتجات')
                                    : T('لا توجد منتجات متاحة'),
                                style: TextStyle(
                                  fontSize: (16 * fontSize).clamp(14, 18),
                                  color: const Color(0xFF6B7280),
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding:
                              EdgeInsets.symmetric(horizontal: cardPadding),
                          itemCount: _filteredProducts.length,
                          itemBuilder: (context, index) {
                            final product = _filteredProducts[index];
                            final isSelected =
                                _selectedProduct?.id == product.id;

                            return Container(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                onTap: () {
                                  setState(() {
                                    _selectedProduct = product;
                                    // Clear previous report data when selecting new product
                                    _reportData = null;
                                    _sortedTransactions.clear();
                                    _paginatedTransactions.clear();
                                    _combinationsComboData.clear();
                                    _selectedCombination = null;
                                  });
                                  Navigator.pop(context);
                                },
                                selected: isSelected,
                                selectedTileColor:
                                    const Color(0xFF3B82F6).withOpacity(0.1),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(12),
                                  side: BorderSide(
                                    color: isSelected
                                        ? const Color(0xFF3B82F6)
                                        : const Color(0xFFE5E7EB),
                                  ),
                                ),
                                leading: CircleAvatar(
                                  backgroundColor: isSelected
                                      ? const Color(0xFF3B82F6)
                                      : const Color(0xFFF3F4F6),
                                  child: Icon(
                                    isSelected
                                        ? Icons.check
                                        : Icons.inventory_2,
                                    color: isSelected
                                        ? Colors.white
                                        : const Color(0xFF9CA3AF),
                                    size: isDesktop ? 20 : 18,
                                  ),
                                ),
                                title: Text(
                                  product.title ?? T('منتج غير محدد'),
                                  style: TextStyle(
                                    fontWeight: FontWeight.w600,
                                    fontSize: (14 * fontSize).clamp(12, 16),
                                    color: isSelected
                                        ? const Color(0xFF3B82F6)
                                        : const Color(0xFF1F2937),
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                subtitle: product.code != null
                                    ? Text(
                                        '${T('الكود')}: ${product.code}',
                                        style: TextStyle(
                                          fontSize:
                                              (12 * fontSize).clamp(10, 14),
                                          color: const Color(0xFF6B7280),
                                        ),
                                      )
                                    : null,
                                trailing: isSelected
                                    ? const Icon(
                                        Icons.check_circle,
                                        color: Color(0xFF3B82F6),
                                      )
                                    : null,
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFiltersSection(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(cardPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date Range Section
          _buildDateRangeSection(),

          SizedBox(height: cardPadding * 0.8),

          // Branch and Store Selection
          _buildBranchStoreSection(),

          SizedBox(height: cardPadding * 0.8),

          // Combination Filter (only show if data is loaded)
          if (_combinationsComboData.isNotEmpty) ...[
            Row(
              children: [
                Icon(
                  Icons.tune,
                  color: const Color(0xFF10B981),
                  size: isDesktop ? 18 : 16,
                ),
                SizedBox(width: cardPadding * 0.3),
                Text(
                  T('فلترة حسب التركيبة'),
                  style: TextStyle(
                    fontSize: (14 * fontSize).clamp(12, 16),
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF374151),
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: const Color(0xFF10B981).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_combinationsComboData.length}',
                    style: TextStyle(
                      fontSize: (10 * fontSize).clamp(8, 12),
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF10B981),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: cardPadding * 0.5),
            _buildCombinationDropdown(),
            SizedBox(height: cardPadding * 0.8),
          ],

          const SizedBox(height: 15),
          SizedBox(
            width: MediaQuery.of(context).size.width -
                40, // set your desired width
            child: ElevatedButton.icon(
              onPressed: _isLoading ? null : _loadReport,
              icon: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Icon(
                      Icons.analytics,
                      size: isDesktop ? 18 : 16,
                    ),
              label: Text(
                _isLoading ? T('جاري التحميل...') : T('إنشاء التقرير'),
                style: TextStyle(
                  fontSize: (14 * fontSize).clamp(12, 16),
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3B82F6),
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(
                  horizontal: cardPadding * 0.8,
                  vertical: cardPadding * 0.6,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDateRangeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: T('من تاريخ'),
                date: _fromDate,
                onTap: () => _selectDate(context, true),
                icon: Icons.date_range,
              ),
            ),
            SizedBox(width: cardPadding * 0.5),
            Expanded(
              child: _buildDateField(
                label: T('إلى تاريخ'),
                date: _toDate,
                onTap: () => _selectDate(context, false),
                icon: Icons.date_range,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: (12 * fontSize).clamp(10, 14),
            fontWeight: FontWeight.w500,
            color: const Color(0xFF6B7280),
          ),
        ),
        const SizedBox(height: 6),
        InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: EdgeInsets.all(cardPadding * 0.6),
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFD1D5DB)),
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  size: isDesktop ? 16 : 14,
                  color: const Color(0xFF6B7280),
                ),
                SizedBox(width: cardPadding * 0.4),
                Expanded(
                  child: Text(
                    date != null
                        ? DateFormat('yyyy/MM/dd').format(date)
                        : label,
                    style: TextStyle(
                      fontSize: (14 * fontSize).clamp(12, 16),
                      color: date != null
                          ? const Color(0xFF1F2937)
                          : const Color(0xFF9CA3AF),
                    ),
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: const Color(0xFF9CA3AF),
                  size: isDesktop ? 20 : 18,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: isFromDate
          ? (_fromDate ?? DateTime.now())
          : (_toDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
          // Validate date range
          if (_toDate != null && _fromDate!.isAfter(_toDate!)) {
            _toDate = _fromDate;
          }
        } else {
          _toDate = picked;
          // Validate date range
          if (_fromDate != null && _toDate!.isBefore(_fromDate!)) {
            _fromDate = _toDate;
          }
        }
      });
    }
  }

  Widget _buildBranchStoreSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isMobile) ...[
          // Mobile: Stack vertically
          _buildBranchDropdown(),
          SizedBox(height: cardPadding * 0.5),
          _buildStoreDropdown(),
        ] else ...[
          // Desktop/Tablet: Side by side
          Row(
            children: [
              Expanded(
                child: _buildBranchDropdown(),
              ),
              SizedBox(width: cardPadding * 0.5),
              Expanded(
                child: _buildStoreDropdown(),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildBranchDropdown() {
    final selectedBranchName = _branchesComboData.isNotEmpty
        ? _branchesComboData
            .firstWhere(
              (branch) => branch.id == _selectedBranchId,
              orElse: () => ComboBoxDataModel(id: 0, name: T('كافة الفروع')),
            )
            .name
        : T('كافة الفروع');

    return MyComboBox(
      caption: selectedBranchName,
      labelText: T('الفرع'),
      modalTitle: T('اختيار الفرع'),
      selectedValue: _selectedBranchId,
      data: [
        ComboBoxDataModel(id: 0, name: T('كافة الفروع')),
        ..._branchesComboData,
      ],
      onSelect: (id, name) {
        setState(() {
          _selectedBranchId = id == 0 ? null : id;
        });
      },
      onRefresh: () {
        _loadBranchesAndStores();
      },
      height: cardPadding * 2.5,
      fontSize: (14 * fontSize).clamp(12, 16),
    );
  }

  Widget _buildStoreDropdown() {
    final selectedStoreName = _storesComboData.isNotEmpty
        ? _storesComboData
            .firstWhere(
              (store) => store.id == _selectedStoreId,
              orElse: () => ComboBoxDataModel(id: 0, name: T('كافة المخازن')),
            )
            .name
        : T('كافة المخازن');

    return MyComboBox(
      caption: selectedStoreName,
      labelText: T('المخزن'),
      modalTitle: T('اختيار المخزن'),
      selectedValue: _selectedStoreId,
      data: [
        ComboBoxDataModel(id: 0, name: T('كافة المخازن')),
        ..._storesComboData,
      ],
      onSelect: (id, name) {
        setState(() {
          _selectedStoreId = id == 0 ? null : id;
        });
      },
      onRefresh: () {
        _loadBranchesAndStores();
      },
      height: cardPadding * 2.5,
      fontSize: (14 * fontSize).clamp(12, 16),
    );
  }

  Widget _buildCombinationDropdown() {
    final selectedCombinationName =
        _selectedCombination != null && _selectedCombination!.isNotEmpty
            ? _selectedCombination!
            : T('كافة التركيبات');

    return MyComboBox(
      caption: selectedCombinationName,
      labelText: T('التركيبة'),
      modalTitle: T('اختيار التركيبة'),
      selectedValue: _selectedCombination?.hashCode,
      data: [
        ComboBoxDataModel(id: 0, name: T('كافة التركيبات')),
        ..._combinationsComboData,
      ],
      onSelect: (id, name) {
        setState(() {
          _selectedCombination = id == 0 ? null : name;
          _sortTransactions(_reportData?.details); // Re-sort with new filter
        });
      },
      onRefresh: () {
        // Re-extract combinations from current data
        if (_reportData?.details != null && _reportData!.details!.isNotEmpty) {
          _extractCombinationsFromData(_reportData!.details!);
        }
      },
      height: cardPadding * 2.5,
      fontSize: (14 * fontSize).clamp(12, 16),
    );
  }

  Widget _buildLoadingWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(cardPadding),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  spreadRadius: 0,
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                SizedBox(
                  width: isDesktop ? 40 : 32,
                  height: isDesktop ? 40 : 32,
                  child: const CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(Color(0xFF3B82F6)),
                  ),
                ),
                SizedBox(height: cardPadding * 0.8),
                Text(
                  T('جاري تحميل التقرير...'),
                  style: TextStyle(
                    fontSize: (16 * fontSize).clamp(14, 18),
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF6B7280),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTotalsSection() {
    if (_reportData?.totals == null || _reportData!.totals!.isEmpty) {
      return const SizedBox.shrink();
    }

    // Filter totals based on selected combination
    List<ProductTransactionTotalDTO> filteredTotals = _reportData!.totals!;
    if (_selectedCombination != null && _selectedCombination!.isNotEmpty) {
      filteredTotals = _reportData!.totals!
          .where((total) => total.attributes == _selectedCombination)
          .toList();
    }

    if (filteredTotals.isEmpty) {
      return const SizedBox.shrink();
    }

    // Group by attributes
    Map<String, List<ProductTransactionTotalDTO>> groupedTotals = {};
    for (var total in filteredTotals) {
      final attribute = total.attributes ?? 'غير محدد';
      if (!groupedTotals.containsKey(attribute)) {
        groupedTotals[attribute] = [];
      }
      groupedTotals[attribute]!.add(total);
    }

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.pie_chart,
                  color: Colors.indigo,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  T('المجاميع حسب التركيبة والفروع'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo,
                  ),
                ),
                const Spacer(),
                if (_selectedCombination != null &&
                    _selectedCombination!.isNotEmpty)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      T('مفلتر: $_selectedCombination'),
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Build sections for each attribute
            ...groupedTotals.entries.map((entry) {
              final attribute = entry.key;
              final totalsForAttribute = entry.value;

              return _buildAttributeTotalSection(attribute, totalsForAttribute);
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildAttributeTotalSection(
      String attribute, List<ProductTransactionTotalDTO> totals) {
    // Calculate totals for this attribute across all branches
    double totalIncoming =
        totals.fold(0.0, (sum, item) => sum + (item.incomingTotal ?? 0));
    double totalOutgoing =
        totals.fold(0.0, (sum, item) => sum + (item.outgoingTotal ?? 0));
    double totalAvailable =
        totals.fold(0.0, (sum, item) => sum + (item.available ?? 0));

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Attribute header with totals
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.category, color: Colors.blue, size: 18),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    attribute,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'إجمالي الوارد: ${NumberFormat('#,##0.##').format(totalIncoming)}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                    Text(
                      'إجمالي الصادر: ${NumberFormat('#,##0.##').format(totalOutgoing)}',
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.red,
                      ),
                    ),
                    Text(
                      'الرصيد: ${NumberFormat('#,##0.##').format(totalAvailable)}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: totalAvailable >= 0 ? Colors.green : Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),

          // Branch details
          ...totals.map((total) => _buildBranchTotalRow(total)).toList(),
        ],
      ),
    );
  }

  Widget _buildBranchTotalRow(ProductTransactionTotalDTO total) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Icon(Icons.business, color: Colors.grey[600], size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'فرع ${total.branchId ?? 'غير محدد'}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'وارد: ${NumberFormat('#,##0.##').format(total.incomingTotal ?? 0)}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'صادر: ${NumberFormat('#,##0.##').format(total.outgoingTotal ?? 0)}',
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: (total.available ?? 0) >= 0
                      ? Colors.blue.withOpacity(0.1)
                      : Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'رصيد: ${NumberFormat('#,##0.##').format(total.available ?? 0)}',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: (total.available ?? 0) >= 0
                        ? Colors.blue
                        : Colors.orange,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    if (_reportData?.totals == null || _reportData!.totals!.isEmpty) {
      return const SizedBox.shrink();
    }

    final totals = _reportData!.totals!;
    final totalIncoming =
        totals.fold(0.0, (sum, item) => sum + (item.incomingTotal ?? 0));
    final totalOutgoing =
        totals.fold(0.0, (sum, item) => sum + (item.outgoingTotal ?? 0));
    final totalAvailable =
        totals.fold(0.0, (sum, item) => sum + (item.available ?? 0));
    final totalAttributes =
        totals.map((item) => item.attributes).toSet().length;

    List<Widget> summaryCards = [
      _buildSummaryCard(
        context: context,
        title: T('إجمالي الوارد'),
        value: NumberFormat('#,##0.##').format(totalIncoming),
        icon: Icons.arrow_downward,
        gradient: const LinearGradient(
          colors: [Color(0xFF10B981), Color(0xFF047857)],
        ),
      ),
      _buildSummaryCard(
        context: context,
        title: T('إجمالي الصادر'),
        value: NumberFormat('#,##0.##').format(totalOutgoing),
        icon: Icons.arrow_upward,
        gradient: const LinearGradient(
          colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
        ),
      ),
      _buildSummaryCard(
        context: context,
        title: T('الرصيد المتاح'),
        value: NumberFormat('#,##0.##').format(totalAvailable),
        icon: Icons.inventory,
        gradient: const LinearGradient(
          colors: [Color(0xFF3B82F6), Color(0xFF1E40AF)],
        ),
      ),
      _buildSummaryCard(
        context: context,
        title: T('عدد التركيبات'),
        value: totalAttributes.toString(),
        icon: Icons.category,
        gradient: const LinearGradient(
          colors: [Color(0xFF8B5CF6), Color(0xFF7C3AED)],
        ),
      ),
    ];

    // Add combination filter card if active
    if (_selectedCombination != null && _selectedCombination!.isNotEmpty) {
      summaryCards.add(
        _buildSummaryCard(
          context: context,
          title: T('فلتر التركيبة'),
          value: _selectedCombination!,
          icon: Icons.tune,
          gradient: const LinearGradient(
            colors: [Color(0xFFEF4444), Color(0xFFDC2626)],
          ),
        ),
      );
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: summaryCards,
    );
  }

  Widget _buildSummaryCard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required LinearGradient gradient,
  }) {
    return Expanded(
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: cardPadding * 0.3),
        padding: EdgeInsets.all(cardPadding),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withOpacity(0.3),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: isMobile
            ?
            // Mobile: Horizontal layout
            Row(
                children: [
                  Icon(icon, color: Colors.white, size: 24),
                  SizedBox(width: cardPadding * 0.8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          value,
                          style: TextStyle(
                            fontSize: (20 * fontSize).clamp(18, 24),
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: (12 * fontSize).clamp(10, 14),
                            color: Colors.white.withOpacity(0.9),
                            fontWeight: FontWeight.w500,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              )
            :
            // Desktop/Tablet: Vertical layout
            Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(icon, color: Colors.white, size: isDesktop ? 28 : 24),
                  SizedBox(height: cardPadding * 0.6),
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: (24 * fontSize).clamp(20, 28),
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: cardPadding * 0.2),
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: (12 * fontSize).clamp(10, 14),
                      color: Colors.white.withOpacity(0.9),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
      ),
    );
  }

  Widget _buildTransactionDetailsSection() {
    if (_sortedTransactions.isEmpty) {
      return Container(
        padding: EdgeInsets.all(cardPadding * 2),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              spreadRadius: 0,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.receipt_long,
                size: isDesktop ? 64 : 48,
                color: const Color(0xFF9CA3AF),
              ),
              SizedBox(height: cardPadding),
              Text(
                T('لا توجد معاملات للعرض'),
                style: TextStyle(
                  fontSize: (18 * fontSize).clamp(16, 20),
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF6B7280),
                ),
              ),
              SizedBox(height: cardPadding * 0.5),
              Text(
                T('جرب تغيير الفلاتر أو اختيار منتج آخر'),
                style: TextStyle(
                  fontSize: (14 * fontSize).clamp(12, 16),
                  color: const Color(0xFF9CA3AF),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل المعاملات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'معاملات المنتج:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _itemsPerPage == -1
                          ? 'عرض جميع المعاملات (${_sortedTransactions.length})'
                          : 'عرض ${_paginatedTransactions.length} من ${_sortedTransactions.length} معاملة • الصفحة ${_currentPage + 1} من ${_totalPages}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.withOpacity(0.3)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: _buildTransactionDataTable(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionDataTable() {
    return Column(
      children: [
        // Pagination Controls - Top
        _buildPaginationControls(),
        const SizedBox(height: 8),

        // Legend for sorting
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            'انقر على رأس العمود للترتيب',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),

        // Data Table
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Container(
            width: MediaQuery.of(context).size.width -
                64, // Adjust for container padding
            child: DataTable(
              sortColumnIndex: _sortColumnIndex,
              sortAscending: _sortAscending,
              columnSpacing: 8,
              horizontalMargin: 4,
              headingRowColor:
                  MaterialStateProperty.all(Colors.blue.withOpacity(0.1)),
              columns: [
                DataColumn(
                  label: const Text(
                    'التاريخ',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'نوع المعاملة',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'المخزن',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'الفرع',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'الكمية',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  numeric: true,
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'التركيبة',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'الاتجاه',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
              rows: _paginatedTransactions.map((transaction) {
                return DataRow(
                  cells: [
                    DataCell(
                      Text(
                        transaction.date != null
                            ? DateFormat('yyyy/MM/dd')
                                .format(DateTime.parse(transaction.date!))
                            : 'غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    DataCell(
                      Text(
                        transaction.transactionTypeDisplayName,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    DataCell(
                      Text(
                        transaction.store ?? 'غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    DataCell(
                      Text(
                        transaction.branchId?.toString() ?? 'غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    DataCell(
                      Text(
                        NumberFormat('#,##0.##')
                            .format(transaction.quantity ?? 0),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ),
                    DataCell(
                      Text(
                        transaction.attributes ?? 'غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    DataCell(
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: (transaction.isIncoming ?? false)
                              ? Colors.green.withOpacity(0.1)
                              : Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          (transaction.isIncoming ?? false) ? 'وارد' : 'صادر',
                          style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.bold,
                            color: (transaction.isIncoming ?? false)
                                ? Colors.green
                                : Colors.red,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ),

        // Pagination Controls - Bottom
        const SizedBox(height: 8),
        _buildPaginationControls(),
      ],
    );
  }

  // Build pagination controls widget
  Widget _buildPaginationControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Items per page selection and info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Items per page dropdown
              Row(
                children: [
                  const Text(
                    'عرض:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(6),
                      color: Colors.white,
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<int>(
                        value: _itemsPerPage,
                        items: _itemsPerPageOptions.map((int value) {
                          return DropdownMenuItem<int>(
                            value: value,
                            child: Text(
                              value == -1 ? 'الكل' : value.toString(),
                              style: const TextStyle(fontSize: 13),
                            ),
                          );
                        }).toList(),
                        onChanged: (int? newValue) {
                          if (newValue != null) {
                            _changeItemsPerPage(newValue);
                          }
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'عنصر لكل صفحة',
                    style: TextStyle(fontSize: 13, color: Colors.grey),
                  ),
                ],
              ),

              // Pagination info
              Text(
                _itemsPerPage == -1
                    ? 'عرض جميع العناصر (${_sortedTransactions.length})'
                    : 'عرض ${(_currentPage * _itemsPerPage) + 1} - ${((_currentPage + 1) * _itemsPerPage).clamp(0, _sortedTransactions.length)} من ${_sortedTransactions.length}',
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Navigation controls (only show if pagination is enabled)
          if (_itemsPerPage != -1 && _totalPages > 1) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Previous button
                IconButton(
                  onPressed: _currentPage > 0 ? _previousPage : null,
                  icon: const Icon(Icons.chevron_right), // Right arrow for RTL
                  tooltip: 'الصفحة السابقة',
                  style: IconButton.styleFrom(
                    backgroundColor: _currentPage > 0
                        ? Colors.blue.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    foregroundColor:
                        _currentPage > 0 ? Colors.blue : Colors.grey,
                  ),
                ),

                const SizedBox(width: 16),

                // Page numbers
                ..._buildPageNumbers(),

                const SizedBox(width: 16),

                // Next button
                IconButton(
                  onPressed: _currentPage < _totalPages - 1 ? _nextPage : null,
                  icon: const Icon(Icons.chevron_left), // Left arrow for RTL
                  tooltip: 'الصفحة التالية',
                  style: IconButton.styleFrom(
                    backgroundColor: _currentPage < _totalPages - 1
                        ? Colors.blue.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    foregroundColor: _currentPage < _totalPages - 1
                        ? Colors.blue
                        : Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // Build page number buttons
  List<Widget> _buildPageNumbers() {
    List<Widget> pageButtons = [];

    // Show max 5 page numbers centered around current page
    int startPage =
        (_currentPage - 2).clamp(0, _totalPages - 5).clamp(0, _currentPage);
    int endPage = (startPage + 4).clamp(_currentPage, _totalPages - 1);

    // Adjust start if we're near the end
    if (endPage - startPage < 4) {
      startPage = (endPage - 4).clamp(0, startPage);
    }

    // First page + ellipsis if needed
    if (startPage > 0) {
      pageButtons.add(_buildPageButton(0));
      if (startPage > 1) {
        pageButtons.add(const Padding(
          padding: EdgeInsets.symmetric(horizontal: 4),
          child: Text('...', style: TextStyle(color: Colors.grey)),
        ));
      }
    }

    // Page numbers
    for (int i = startPage; i <= endPage; i++) {
      pageButtons.add(_buildPageButton(i));
    }

    // Ellipsis + last page if needed
    if (endPage < _totalPages - 1) {
      if (endPage < _totalPages - 2) {
        pageButtons.add(const Padding(
          padding: EdgeInsets.symmetric(horizontal: 4),
          child: Text('...', style: TextStyle(color: Colors.grey)),
        ));
      }
      pageButtons.add(_buildPageButton(_totalPages - 1));
    }

    return pageButtons;
  }

  // Build individual page button
  Widget _buildPageButton(int pageIndex) {
    final isCurrentPage = pageIndex == _currentPage;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: InkWell(
        onTap: () => _goToPage(pageIndex),
        borderRadius: BorderRadius.circular(6),
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: isCurrentPage ? Colors.blue : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isCurrentPage ? Colors.blue : Colors.grey.shade300,
            ),
          ),
          child: Center(
            child: Text(
              '${pageIndex + 1}',
              style: TextStyle(
                color: isCurrentPage ? Colors.white : Colors.grey.shade700,
                fontWeight: isCurrentPage ? FontWeight.bold : FontWeight.normal,
                fontSize: 13,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
