import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

enum PaperSize {
  mm58, // 58mm receipt paper
  mm80, // 80mm receipt paper
  a4, // A4 paper
  letter // Letter paper
}

class PrinterSettingsController with ChangeNotifier {
  String? _selectedPrinterName;
  String? _printerType; // 'usb' or 'network'
  String? _printerIP;
  int? _printerPort;
  String? _printerModel;
  PaperSize _paperSize = PaperSize.mm80;
  String _receiptHeader = '';
  String _receiptFooter = '';
  bool _printLogo = false;
  String? _logoPath;
  int _copies = 1;

  // Getters
  String? get selectedPrinterName => _selectedPrinterName;
  String? get printerName => _selectedPrinterName;
  String? get printerType => _printerType;
  String? get printerIP => _printerIP;
  int? get printerPort => _printerPort;
  String? get printerModel => _printerModel;
  PaperSize get paperSize => _paperSize;
  String get receiptHeader => _receiptHeader;
  String get receiptFooter => _receiptFooter;
  bool get printLogo => _printLogo;
  String? get logoPath => _logoPath;
  int get copies => _copies;

  // Constructor
  PrinterSettingsController() {
    loadSettings();
  }

  // Load settings from SharedPreferences
  Future<void> loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      _selectedPrinterName = prefs.getString('selectedPrinterName');
      _printerType = prefs.getString('printerType');
      _printerIP = prefs.getString('printerIP');
      _printerPort = prefs.getInt('printerPort');
      _printerModel = prefs.getString('printerModel');

      // Load paper size
      final paperSizeIndex = prefs.getInt('paperSize');
      if (paperSizeIndex != null && paperSizeIndex < PaperSize.values.length) {
        _paperSize = PaperSize.values[paperSizeIndex];
      }

      // Load receipt customization
      _receiptHeader = prefs.getString('receiptHeader') ?? '';
      _receiptFooter = prefs.getString('receiptFooter') ?? '';
      _printLogo = prefs.getBool('printLogo') ?? false;
      _logoPath = prefs.getString('logoPath');
      _copies = prefs.getInt('copies') ?? 1;

      notifyListeners();
    } catch (e) {
      debugPrint("Error loading printer settings: $e");
    }
  }

  // Save printer settings
  Future<void> savePrinterSettings({
    required String printerName,
    required String type,
    String? ip,
    int? port,
    String? model,
    PaperSize? paperSize,
    String? receiptHeader,
    String? receiptFooter,
    bool? printLogo,
    String? logoPath,
    int? copies,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.setString('selectedPrinterName', printerName);
      await prefs.setString('printerType', type);
      if (ip != null) await prefs.setString('printerIP', ip);
      if (port != null) await prefs.setInt('printerPort', port);
      if (model != null) await prefs.setString('printerModel', model);

      // Save paper size
      if (paperSize != null) {
        await prefs.setInt('paperSize', paperSize.index);
        _paperSize = paperSize;
      }

      // Save receipt customization
      if (receiptHeader != null) {
        await prefs.setString('receiptHeader', receiptHeader);
        _receiptHeader = receiptHeader;
      }

      if (receiptFooter != null) {
        await prefs.setString('receiptFooter', receiptFooter);
        _receiptFooter = receiptFooter;
      }

      if (printLogo != null) {
        await prefs.setBool('printLogo', printLogo);
        _printLogo = printLogo;
      }

      if (logoPath != null) {
        await prefs.setString('logoPath', logoPath);
        _logoPath = logoPath;
      }

      if (copies != null) {
        await prefs.setInt('copies', copies);
        _copies = copies;
      }

      _selectedPrinterName = printerName;
      _printerType = type;
      _printerIP = ip;
      _printerPort = port;
      _printerModel = model;

      notifyListeners();
    } catch (e) {
      debugPrint("Error saving printer settings: $e");
      rethrow;
    }
  }

  // Clear printer settings
  Future<void> clearPrinterSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      await prefs.remove('selectedPrinterName');
      await prefs.remove('printerType');
      await prefs.remove('printerIP');
      await prefs.remove('printerPort');
      await prefs.remove('printerModel');
      await prefs.remove('paperSize');
      await prefs.remove('receiptHeader');
      await prefs.remove('receiptFooter');
      await prefs.remove('printLogo');
      await prefs.remove('logoPath');
      await prefs.remove('copies');

      _selectedPrinterName = null;
      _printerType = null;
      _printerIP = null;
      _printerPort = null;
      _printerModel = null;
      _paperSize = PaperSize.mm80;
      _receiptHeader = '';
      _receiptFooter = '';
      _printLogo = false;
      _logoPath = null;
      _copies = 1;

      notifyListeners();
    } catch (e) {
      debugPrint("Error clearing printer settings: $e");
      rethrow;
    }
  }

  // Test printer connection
  Future<bool> testPrinterConnection() async {
    try {
      if (_printerType == 'usb') {
        // Test USB printer connection
        final printers = await Printing.listPrinters();
        return printers.any((printer) => printer.name == _selectedPrinterName);
      } else if (_printerType == 'network') {
        // Test network printer connection
        // This is a simplified test - in a real app, you would try to establish
        // a socket connection to the printer IP and port
        return _printerIP != null && _printerIP!.isNotEmpty;
        // Port is optional for network printers
      } else if (_printerType == 'bluetooth') {
        // Test Bluetooth printer connection
        if (_selectedPrinterName == null || _selectedPrinterName!.isEmpty) {
          return false;
        }

        // On mobile platforms, we could try to connect to the Bluetooth device
        if (!Platform.isWindows && !Platform.isLinux && !Platform.isMacOS) {
          try {
            // This is a simplified test - in a real app, you would:
            // 1. Find the device by address
            // 2. Connect to it
            // 3. Check if it supports printer services

            // For now, we'll just return true if the printer name is set
            debugPrint(
                "Testing connection to Bluetooth printer: $_selectedPrinterName");
            return true;
          } catch (e) {
            debugPrint("Error connecting to Bluetooth printer: $e");
            return false;
          }
        } else {
          // On desktop platforms, just return true if the printer name is set
          return true;
        }
      }
      return false;
    } catch (e) {
      debugPrint("Error testing printer connection: $e");
      return false;
    }
  }

  // Get PdfPageFormat based on selected paper size
  PdfPageFormat getPdfPageFormat() {
    switch (_paperSize) {
      case PaperSize.mm58:
        // 58mm receipt paper (typically 58mm x 210mm)
        return const PdfPageFormat(
            58 * PdfPageFormat.mm, 210 * PdfPageFormat.mm);
      case PaperSize.mm80:
        // 80mm receipt paper (typically 80mm x 297mm)
        return const PdfPageFormat(
            80 * PdfPageFormat.mm, 297 * PdfPageFormat.mm);
      case PaperSize.a4:
        return PdfPageFormat.a4;
      case PaperSize.letter:
        return PdfPageFormat.letter;
    }
  }

  // Generate a test receipt
  Future<Uint8List> generateTestReceipt() async {
    final pdf = pw.Document();

    pdf.addPage(
      pw.Page(
        pageFormat: getPdfPageFormat(),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              if (_receiptHeader.isNotEmpty)
                pw.Text(_receiptHeader,
                    style: pw.TextStyle(
                        fontSize: 12, fontWeight: pw.FontWeight.bold)),
              pw.SizedBox(height: 10),
              pw.Text('TEST RECEIPT',
                  style: pw.TextStyle(
                      fontSize: 14, fontWeight: pw.FontWeight.bold)),
              pw.SizedBox(height: 5),
              pw.Text('Date: ${DateTime.now().toString().substring(0, 19)}'),
              pw.SizedBox(height: 10),
              pw.Divider(),
              pw.SizedBox(height: 10),
              pw.Text('This is a test receipt to verify printer settings.'),
              pw.SizedBox(height: 10),
              pw.Text('Printer: $_selectedPrinterName'),
              pw.Text('Type: $_printerType'),
              if (_printerType == 'network') ...[
                pw.Text('IP: $_printerIP'),
                pw.Text('Port: $_printerPort'),
              ],
              pw.Text('Model: $_printerModel'),
              pw.Text('Paper Size: $_paperSize'),
              pw.SizedBox(height: 10),
              pw.Divider(),
              pw.SizedBox(height: 10),
              if (_receiptFooter.isNotEmpty)
                pw.Text(_receiptFooter,
                    style: const pw.TextStyle(fontSize: 10)),
            ],
          );
        },
      ),
    );

    return pdf.save();
  }

  // Print test receipt
  Future<bool> printTestReceipt() async {
    try {
      final pdfData = await generateTestReceipt();

      if (_printerType == 'usb') {
        // Find the selected printer
        final printers = await Printing.listPrinters();
        final selectedPrinter = printers.firstWhere(
          (printer) => printer.name == _selectedPrinterName,
          orElse: () => throw Exception('Selected printer not found'),
        );

        // Print to the selected printer
        final result = await Printing.directPrintPdf(
          printer: selectedPrinter,
          onLayout: (_) => pdfData,
        );

        return result;
      } else if (_printerType == 'network') {
        // For network printers, we'll use the generic print dialog
        // In a real app, you would implement direct network printing
        try {
          await Printing.layoutPdf(
            onLayout: (_) => pdfData,
            name: 'Test Receipt',
            format: getPdfPageFormat(),
          );
          return true;
        } catch (e) {
          debugPrint("Error in network printing: $e");
          return false;
        }
      } else if (_printerType == 'bluetooth') {
        // For Bluetooth printers, we'll use the generic print dialog for now
        // In a real app, you would implement direct Bluetooth printing using ESC/POS commands
        try {
          debugPrint(
              "Printing test receipt to Bluetooth printer: $_selectedPrinterName");

          // For a real implementation, you would:
          // 1. Find the Bluetooth device by address/name
          // 2. Connect to it
          // 3. Convert the PDF to ESC/POS commands or bitmap
          // 4. Send the commands to the printer

          // For now, we'll use the generic print dialog
          await Printing.layoutPdf(
            onLayout: (_) => pdfData,
            name: 'Test Receipt',
            format: getPdfPageFormat(),
          );
          return true;
        } catch (e) {
          debugPrint("Error in Bluetooth printing: $e");
          return false;
        }
      }

      return false;
    } catch (e) {
      debugPrint("Error printing test receipt: $e");
      return false;
    }
  }
}
