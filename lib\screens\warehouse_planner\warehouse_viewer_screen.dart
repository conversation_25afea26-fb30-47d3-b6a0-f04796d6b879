import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/warehouse_planner_provider.dart';
import '../../controllers/product_controller.dart';
import '../../models/dto/products/product_dto.dart';
import '../../models/warehouse_planner/warehouse_layout.dart';
import 'widgets/simple_warehouse_viewer_canvas.dart';

/// شاشة عرض المخطط للعامل - للقراءة فقط مع البحث
class WarehouseViewerScreen extends StatefulWidget {
  final String layoutId;
  final String layoutName;

  const WarehouseViewerScreen({
    Key? key,
    required this.layoutId,
    required this.layoutName,
  }) : super(key: key);

  @override
  State<WarehouseViewerScreen> createState() => _WarehouseViewerScreenState();
}

class _WarehouseViewerScreenState extends State<WarehouseViewerScreen> {
  final TextEditingController _searchController = TextEditingController();
  final ProductController _productController = ProductController();

  List<ProductDTO> _searchResults = [];
  bool _isSearching = false;
  String? _highlightedShelfId;
  ProductDTO? _selectedProduct;
  List<ProductLocation> _productLocations = [];

  @override
  void initState() {
    super.initState();
    _loadLayout();
  }

  void _loadLayout() {
    final provider =
        Provider.of<WarehousePlannerProvider>(context, listen: false);
    provider.loadWarehouse(widget.layoutId);
  }

  Future<void> _searchProducts(String query) async {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults.clear();
        _highlightedShelfId = null;
        _selectedProduct = null;
        _productLocations.clear();
      });
      return;
    }

    setState(() => _isSearching = true);

    try {
      await _productController.getItems(
        resetAndRefresh: true,
        search: query,
      );
      final results = _productController.realProductList;

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() => _isSearching = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في البحث: $e')),
      );
    }
  }

  void _selectProduct(ProductDTO product) {
    setState(() {
      _selectedProduct = product;
      _productLocations.clear();
      _highlightedShelfId = null;
    });

    // البحث عن مواقع المنتج في المخطط
    final provider =
        Provider.of<WarehousePlannerProvider>(context, listen: false);
    final layout = provider.currentLayout;

    if (layout != null) {
      final locations = <ProductLocation>[];

      for (final shelf in layout.shelves) {
        for (final bin in shelf.bins) {
          if (bin.productId == product.id) {
            locations.add(ProductLocation(
              shelfId: shelf.id,
              binId: bin.id,
              shelfName: shelf.name,
              levelIndex: _getBinLevel(shelf, bin.id),
              slotIndex: _getBinSlot(shelf, bin.id),
              quantity: bin.quantity.toDouble(),
            ));
          }
        }
      }

      setState(() {
        _productLocations = locations;
      });

      if (locations.isNotEmpty) {
        _highlightLocation(locations.first);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('المنتج "${product.title}" غير موجود في هذا المستودع'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _highlightLocation(ProductLocation location) {
    setState(() {
      _highlightedShelfId = location.shelfId;
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'المنتج موجود في ${location.shelfName} - المستوى ${location.levelIndex + 1} - الخانة ${location.slotIndex + 1}',
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  int _getBinLevel(Shelf shelf, String binId) {
    final totalSlots = shelf.slotsPerLevel;
    final binIndex = shelf.bins.indexWhere((b) => b.id == binId);
    return binIndex ~/ totalSlots;
  }

  int _getBinSlot(Shelf shelf, String binId) {
    final totalSlots = shelf.slotsPerLevel;
    final binIndex = shelf.bins.indexWhere((b) => b.id == binId);
    return binIndex % totalSlots;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text('مخطط المستودع - ${widget.layoutName}'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showLayoutInfo,
          ),
        ],
      ),
      body: Consumer<WarehousePlannerProvider>(
        builder: (context, provider, child) {
          if (provider.currentLayout == null) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Row(
            children: [
              // لوحة البحث
              Container(
                width: 350,
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(2, 0),
                    ),
                  ],
                ),
                child: _buildSearchPanel(),
              ),

              // منطقة عرض المخطط
              Expanded(
                child: _buildCanvasArea(provider.currentLayout!),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSearchPanel() {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border(
              bottom: BorderSide(color: Colors.grey.shade200),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '🔍 البحث عن الأصناف',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade800,
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'ابحث بالاسم أو الباركود...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _searchProducts('');
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                onChanged: _searchProducts,
              ),
            ],
          ),
        ),

        // نتائج البحث
        Expanded(
          child: _buildSearchResults(),
        ),

        // معلومات المنتج المحدد
        if (_selectedProduct != null) _buildSelectedProductInfo(),
      ],
    );
  }

  Widget _buildSearchResults() {
    if (_isSearching) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_searchController.text.isNotEmpty && _searchResults.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(20),
          child: Text(
            'لا توجد نتائج للبحث',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.search,
                size: 64,
                color: Colors.grey.shade300,
              ),
              const SizedBox(height: 16),
              Text(
                'ابدأ بكتابة اسم المنتج\nأو الباركود للبحث',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontSize: 16,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final product = _searchResults[index];
        final isSelected = _selectedProduct?.id == product.id;

        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4),
          elevation: isSelected ? 4 : 1,
          color: isSelected ? Colors.blue.shade50 : null,
          child: ListTile(
            leading: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.blue.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.medication,
                color: Colors.blue.shade700,
              ),
            ),
            title: Text(
              product.title ?? 'منتج غير محدد',
              style: TextStyle(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Colors.blue.shade800 : null,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (product.code != null) Text('الكود: ${product.code}'),
                Text(
                  'الرصيد: ${product.stock?.toStringAsFixed(0) ?? '0'}',
                  style: TextStyle(
                    color: (product.stock ?? 0) > 0 ? Colors.green : Colors.red,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            trailing: isSelected
                ? Icon(Icons.check_circle, color: Colors.blue.shade700)
                : const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => _selectProduct(product),
          ),
        );
      },
    );
  }

  Widget _buildSelectedProductInfo() {
    if (_selectedProduct == null) return const SizedBox();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        border: Border(
          top: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.green.shade700),
              const SizedBox(width: 8),
              Text(
                'مواقع المنتج',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.green.shade800,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          if (_productLocations.isEmpty)
            Text(
              'المنتج غير موجود في هذا المستودع',
              style: TextStyle(color: Colors.red.shade600),
            )
          else
            Column(
              children: _productLocations.map((location) {
                final isHighlighted = _highlightedShelfId == location.shelfId;

                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isHighlighted ? Colors.green.shade100 : Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isHighlighted
                          ? Colors.green.shade300
                          : Colors.grey.shade200,
                      width: isHighlighted ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              location.shelfName,
                              style:
                                  const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                                'المستوى ${location.levelIndex + 1} - الخانة ${location.slotIndex + 1}'),
                            Text('الكمية: ${location.quantity}'),
                          ],
                        ),
                      ),
                      IconButton(
                        icon: Icon(
                          isHighlighted
                              ? Icons.visibility
                              : Icons.visibility_outlined,
                          color: Colors.green.shade700,
                        ),
                        onPressed: () => _highlightLocation(location),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
        ],
      ),
    );
  }

  Widget _buildCanvasArea(WarehouseLayout layout) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: SimpleWarehouseViewerCanvas(
        layout: layout,
        highlightedShelfId: _highlightedShelfId,
        onShelfTap: _onShelfTap,
      ),
    );
  }

  void _onShelfTap(String shelfId) {
    final provider =
        Provider.of<WarehousePlannerProvider>(context, listen: false);
    final layout = provider.currentLayout;

    if (layout != null) {
      final shelf = layout.shelves.firstWhere(
        (s) => s.id == shelfId,
        orElse: () => layout.shelves.first,
      );

      _showShelfDetails(shelf);
    }
  }

  void _showShelfDetails(Shelf shelf) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 500,
          height: 600,
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'تفاصيل ${shelf.name}',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),

              // معلومات الخزانة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                        'الأبعاد: ${shelf.width.toInt()} × ${shelf.depth.toInt()} سم'),
                    Text('عدد المستويات: ${shelf.levels}'),
                    Text('الخانات بكل مستوى: ${shelf.slotsPerLevel}'),
                    Text('إجمالي الخانات: ${shelf.bins.length}'),
                  ],
                ),
              ),

              const SizedBox(height: 16),
              const Text(
                'محتويات الخزانة:',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),

              // قائمة المحتويات
              Expanded(
                child: ListView.builder(
                  itemCount: shelf.bins.length,
                  itemBuilder: (context, index) {
                    final bin = shelf.bins[index];
                    final level = index ~/ shelf.slotsPerLevel;
                    final slot = index % shelf.slotsPerLevel;

                    return ListTile(
                      leading: Container(
                        width: 30,
                        height: 30,
                        decoration: BoxDecoration(
                          color: bin.productId != null
                              ? Colors.green
                              : Colors.grey.shade200,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Center(
                          child: Text(
                            '${level + 1}.${slot + 1}',
                            style: TextStyle(
                              fontSize: 10,
                              color: bin.productId != null
                                  ? Colors.white
                                  : Colors.grey,
                            ),
                          ),
                        ),
                      ),
                      title: Text(
                        bin.productName ?? 'فارغ',
                        style: TextStyle(
                          color: bin.productId != null
                              ? Colors.black
                              : Colors.grey,
                        ),
                      ),
                      subtitle: bin.productId != null
                          ? Text('الكمية: ${bin.quantity}')
                          : null,
                    );
                  },
                ),
              ),

              // أزرار الإجراءات
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('إغلاق'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showLayoutInfo() {
    final provider =
        Provider.of<WarehousePlannerProvider>(context, listen: false);
    final layout = provider.currentLayout;

    if (layout == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات المخطط'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الاسم: ${layout.name}'),
            Text(
                'الأبعاد: ${layout.width.toInt()} × ${layout.height.toInt()} سم'),
            Text('عدد الجدران: ${layout.walls.length}'),
            Text('عدد المداخل: ${layout.entrances.length}'),
            Text('عدد الخزائن: ${layout.shelves.length}'),
            if (layout.linkedWarehouseName != null)
              Text('المستودع المربوط: ${layout.linkedWarehouseName}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

/// كلاس لتخزين موقع المنتج
class ProductLocation {
  final String shelfId;
  final String binId;
  final String shelfName;
  final int levelIndex;
  final int slotIndex;
  final double quantity;

  ProductLocation({
    required this.shelfId,
    required this.binId,
    required this.shelfName,
    required this.levelIndex,
    required this.slotIndex,
    required this.quantity,
  });
}
