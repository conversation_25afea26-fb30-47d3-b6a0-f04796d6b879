class CustomerSalesServerDTO {
  final int? customerId;
  final String? customerName;
  final double? totalSales;
  final double? totalQuantity;

  CustomerSalesServerDTO({
    this.customerId,
    this.customerName,
    this.totalSales,
    this.totalQuantity,
  });

  factory CustomerSalesServerDTO.fromJson(Map<String, dynamic> json) {
    return CustomerSalesServerDTO(
      customerId: json['CustomerId'],
      customerName: json['CustomerName'],
      totalSales: json['TotalSales']?.toDouble(),
      totalQuantity: json['TotalQuantity']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CustomerId': customerId,
      'CustomerName': customerName,
      'TotalSales': totalSales,
      'TotalQuantity': totalQuantity,
    };
  }
}
