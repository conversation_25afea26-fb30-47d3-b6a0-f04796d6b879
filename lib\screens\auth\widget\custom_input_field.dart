import 'package:flutter/material.dart';

class CustomInputField extends StatefulWidget {
  final IconData icon;
  final String hintText;
  final bool isPassword;
  final TextEditingController controller;
  final Function(String) onChange;

  const CustomInputField({
    super.key,
    required this.icon,
    required this.hintText,
    required this.controller,
    this.isPassword = false,
    required this.onChange,
  });

  @override
  _CustomInputFieldState createState() => _CustomInputFieldState();
}

class _CustomInputFieldState extends State<CustomInputField> {
  bool _isObscured = true;

  @override
  void initState() {
    super.initState();
    _isObscured = widget.isPassword; // Set initial state for password field
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2), // Light background with opacity
          borderRadius: BorderRadius.circular(12), // Rounded corners
        ),
        child: TextField(
          controller: widget.controller, // Assign controller
          obscureText: _isObscured, // Use the toggle variable
          style: const TextStyle(color: Colors.white), // Text color
          decoration: InputDecoration(
            prefixIcon: Icon(
              widget.icon,
              color: Colors.white, // Icon color
            ),
            suffixIcon: widget.isPassword
                ? IconButton(
                    icon: Icon(
                      _isObscured ? Icons.visibility_off : Icons.visibility,
                      color: Colors.white70, // Toggle icon color
                    ),
                    onPressed: () {
                      setState(() {
                        _isObscured =
                            !_isObscured; // Toggle password visibility
                      });
                    },
                  )
                : null,
            hintText: widget.hintText,
            hintStyle:
                const TextStyle(color: Colors.white70), // Hint text color
            border: InputBorder.none, // No border
            contentPadding: const EdgeInsets.symmetric(
                vertical: 15), // Padding inside the input field
          ),
          onChanged: (value) {
            widget.onChange(value);
          },
        ),
      ),
    );
  }
}
