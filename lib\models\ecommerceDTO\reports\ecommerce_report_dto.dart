import 'package:flutter/foundation.dart';
import 'package:inventory_application/models/ecommerceDTO/ecommerce_order_dto.dart';

class EcommerceReportRequest {
  int? id;
  DateTime? fromDate;
  DateTime? toDate;
  int? storeId;
  int? branchId;
  bool? postId;
  List<int>? optionIds;

  EcommerceReportRequest({
    required this.id,
    this.fromDate,
    this.toDate,
    this.storeId,
    this.branchId,
    this.postId,
    this.optionIds,
  });

  // fromJson method
  factory EcommerceReportRequest.fromJson(Map<String, dynamic> json) {
    return EcommerceReportRequest(
      id: json['id'],
      fromDate:
          json['fromDate'] != null ? DateTime.parse(json['fromDate']) : null,
      toDate: json['toDate'] != null ? DateTime.parse(json['toDate']) : null,
      storeId: json['storeId'],
      branchId: json['branchId'],
      postId: json['postId'],
      optionIds:
          json['optionIds'] != null ? List<int>.from(json['optionIds']) : null,
    );
  }

  // toJson method
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'fromDate': fromDate?.toIso8601String(),
      'toDate': toDate?.toIso8601String(),
      'storeId': storeId,
      'branchId': branchId,
      'postId': postId,
      'optionIds': optionIds,
    };
  }
}

//--------------------------------------------------------------------------------------------------------------
class EcommerceItemTransactionDTO {
  TransactionTypes? transactionType;
  int? productId;
  double? price;
  int? orderId;
  bool? isHaveCombination;
  String? erpReferenceId;
  String? erpReferenceCode;
  int? erpStoreId;
  int? erpBranchId;
  String? note;
  int? combinationId;
  String? combinationAsString;
  double? quantity;
  String? branch;
  String? store;
  bool? isPosted;
  DateTime? date;
  String? postStatusString;

  EcommerceItemTransactionDTO({
    this.transactionType,
    this.productId,
    this.price,
    this.orderId,
    this.isHaveCombination,
    this.erpReferenceId,
    this.erpReferenceCode,
    this.erpStoreId,
    this.erpBranchId,
    this.note,
    this.combinationId,
    this.combinationAsString,
    this.quantity,
    this.branch,
    this.store,
    this.isPosted,
    this.date,
    this.postStatusString,
  });

  EcommerceItemTransactionDTO.fromJson(Map<String, dynamic> json) {
    // debugPrint('💾 Parsing transaction JSON: $json');

    transactionType = TransactionTypes.values[json['transactionType']];
    productId = json['productId'];
    price = (json['price'] ?? 0).toDouble();
    orderId = json['orderId'];
    isHaveCombination = json['isHaveCombination'] ?? false;
    erpReferenceId = json['erpReferenceId'] ?? '';
    erpReferenceCode = json['erpReferenceCode'] ?? '';
    erpStoreId = json['erpStoreId'];
    erpBranchId = json['erpBranchId'];
    note = json['note'] ?? '';
    combinationId = json['combinationId'];
    combinationAsString = json['combinationAsString'] ?? '';
    quantity = (json['quantity'] ?? 0).toDouble();
    branch = json['branch'] ?? '';
    store = json['store'] ?? '';
    isPosted = json['isPosted'] ?? false;
    date = json['date'] != null ? DateTime.parse(json['date']) : null;
    postStatusString = json['postStatusString'] ?? '';

    debugPrint(
        '💾 Parsed transaction - Type: $transactionType, Quantity: $quantity, Date: $date');
  }

  Map<String, dynamic> toJson() {
    return {
      'transactionType': transactionType.toString().split('.').last,
      'productId': productId,
      'price': price,
      'orderId': orderId,
      'isHaveCombination': isHaveCombination,
      'erpReferenceId': erpReferenceId,
      'erpReferenceCode': erpReferenceCode,
      'erpStoreId': erpStoreId,
      'erpBranchId': erpBranchId,
      'note': note,
      'combinationId': combinationId,
      'combinationAsString': combinationAsString,
      'quantity': quantity,
      'branch': branch,
      'store': store,
      'isPosted': isPosted,
      'date': date?.toIso8601String(),
      'postStatusString': postStatusString,
    };
  }
}

//-----------------------------------------------------------
class EcommerceItemTransactionsReport {
  int? productId;
  String? productName;
  double? totalIncomeQuantity;
  double? totalOuputQuantity;
  double? totalExistQuantity;
  List<EcommerceItemTransactionDTO>? transactions;

  EcommerceItemTransactionsReport({
    this.productId,
    this.productName,
    this.totalIncomeQuantity,
    this.totalOuputQuantity,
    this.totalExistQuantity,
    this.transactions,
  });

  EcommerceItemTransactionsReport.fromJson(Map<String, dynamic> json) {
    debugPrint('📈 Parsing EcommerceItemTransactionsReport: $json');

    productId = json['productId'];
    productName = json['productName'] ?? '';
    totalIncomeQuantity = (json['totalIncomeQuantity'] ?? 0).toDouble();
    totalOuputQuantity = (json['totalOuputQuantity'] ?? 0).toDouble();
    totalExistQuantity = (json['totalExistQuantity'] ?? 0).toDouble();

    final transactionList = json['transactions'] ?? json['transations'];

    if (transactionList != null) {
      transactions = <EcommerceItemTransactionDTO>[];
      for (var v in transactionList) {
        transactions!.add(EcommerceItemTransactionDTO.fromJson(v));
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['productId'] = productId;
    data['productName'] = productName;
    data['totalIncomeQuantity'] = totalIncomeQuantity;
    data['totalOuputQuantity'] = totalOuputQuantity;
    data['totalExistQuantity'] = totalExistQuantity;
    if (transactions != null) {
      data['transactions'] = transactions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

//-----------------------------------------------------------
class EcommerceItemQuantityReport {
  int? productId;
  String? productName;

  List<EcommerceItemQuantitiesItemDTO>? combinationQuantities;

  EcommerceItemQuantityReport({
    this.productId,
    this.productName,
    this.combinationQuantities,
  });

  EcommerceItemQuantityReport.fromJson(Map<String, dynamic> json) {
    debugPrint('📈 Parsing EcommerceItemTransactionsReport: $json');

    productId = json['productId'];
    productName = json['productName'] ?? '';

    final transactionList =
        json['combinationQuantities'] ?? json['transations'];

    if (transactionList != null) {
      combinationQuantities = <EcommerceItemQuantitiesItemDTO>[];
      for (var v in transactionList) {
        combinationQuantities!.add(EcommerceItemQuantitiesItemDTO.fromJson(v));
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['productId'] = productId;
    data['productName'] = productName;

    if (combinationQuantities != null) {
      data['combinationQuantities'] =
          combinationQuantities!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

//--------------------------------------------------------------------------------------------------------------
class EcommerceItemQuantitiesItemDTO {
  int? combinationId;
  String? combinationName;
  double? incomingQuantity;
  double? outgoingQuantity;
  String? branch;
  String? store;
  String? postStatusString;

  EcommerceItemQuantitiesItemDTO({
    this.combinationName,
    this.combinationId,
    this.incomingQuantity,
    this.outgoingQuantity,
    this.branch,
    this.store,
    this.postStatusString,
  });

  EcommerceItemQuantitiesItemDTO.fromJson(Map<String, dynamic> json) {
    // debugPrint('💾 Parsing transaction JSON: $json');

    combinationId = json['combinationId'];
    combinationName = json['combinationName'] ?? '';
    incomingQuantity = (json['incomingQuantity'] ?? 0).toDouble();
    outgoingQuantity = (json['outgoingQuantity'] ?? 0).toDouble();
    branch = json['branch'] ?? '';
    store = json['store'] ?? '';
    postStatusString = json['postStatusString'] ?? '';
  }

  Map<String, dynamic> toJson() {
    return {
      'combinationId': combinationId,
      'combinationName': combinationName,
      'incomingQuantity': incomingQuantity,
      'outgoingQuantity': outgoingQuantity,
      'branch': branch,
      'store': store,
      'postStatusString': postStatusString,
    };
  }
}
