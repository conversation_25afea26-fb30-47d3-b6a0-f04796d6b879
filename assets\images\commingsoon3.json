{"metadata": {"lottielabInfoHTML": "<!DOCTYPE html><html><meta http-equiv='refresh' content='0, URL=?info'></html><!-- "}, "v": "5.7.5", "fr": 100, "ip": 0, "op": 400, "w": 1300, "h": 1000, "nm": "Comp 1", "ddd": 0, "assets": [{"id": "0", "layers": [{"ddd": 0, "ind": 1, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "1", "w": 848, "h": 317, "ind": 2, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [-14, 301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 1}, {"ddd": 0, "ind": 3, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[370.71, -10.9], [370.71, 33.67], [-370.71, 33.67], [-370.71, -10.9], [-347.86, -33.67], [347.94, -33.67], [370.71, -10.9]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [-12.652, 0], [0, 0], [0, -12.568]], "o": [[0, 0], [0, 0], [0, 0], [0, -12.568], [0, 0], [12.569, 0], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-419.02, 612.98], [480.98, 612.98], [480.98, -287.02], [-419.02, -287.02], [-419.02, 612.98]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [30.98, 162.98], "ix": 2}, "a": {"a": 0, "k": [30.98, 162.98], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-419.02, -287.02], [480.98, -287.02], [480.98, 612.98], [-419.02, 612.98], [-419.02, -287.02]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [30.98, 162.98], "ix": 2}, "a": {"a": 0, "k": [30.98, 162.98], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.68, 0.76, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [450, 450], "ix": 2}, "a": {"a": 0, "k": [30.98, 162.97], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 4, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[370.71, -154.75], [370.71, 154.75], [347.94, 177.52], [-347.86, 177.52], [-370.71, 154.75], [-370.71, -154.75], [-347.86, -177.52], [347.94, -177.52], [370.71, -154.75]], "i": [[0, 0], [0, 0], [12.569, 0], [0, 0], [0, 12.57], [0, 0], [-12.652, 0], [0, 0], [0, -12.568]], "o": [[0, 0], [0, 12.57], [0, 0], [-12.652, 0], [0, 0], [0, -12.568], [0, 0], [12.569, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [419.02, 430.88], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 5, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[367.9, 177.52], [-367.91, 177.52], [-370.7, 174.73], [-370.7, -174.73], [-367.91, -177.52], [367.9, -177.52], [370.7, -174.73], [370.7, 174.73], [367.9, 177.52]], "i": [[0, 0], [0, 0], [0, 1.543], [0, 0], [-1.542, 0], [0, 0], [0, -1.543], [0, 0], [1.543, 0]], "o": [[0, 0], [-1.542, 0], [0, 0], [0, -1.543], [0, 0], [1.543, 0], [0, 0], [0, 1.543], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.373, 0.69, 0.906], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [435.36, 447.2], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "2", "layers": [{"ddd": 0, "ind": 6, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [14, -301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-346, 30], [318, 30]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.683, 0.76, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 20, "ix": 2}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [420, 353], "ix": 2}, "a": {"a": 0, "k": [-14, 30], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 7, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [14, -301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-346, 30], [318, 30]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.683, 0.76, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 20, "ix": 2}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [420, 396], "ix": 2}, "a": {"a": 0, "k": [-14, 30], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 8, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [14, -301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-346, 30], [318, 30]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.683, 0.76, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 20, "ix": 2}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [420, 439], "ix": 2}, "a": {"a": 0, "k": [-14, 30], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 9, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [14, -301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-346, 30], [318, 30]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.683, 0.76, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 20, "ix": 2}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [420, 482], "ix": 2}, "a": {"a": 0, "k": [-14, 30], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 10, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [14, -301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-346, 30], [318, 30]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.683, 0.76, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 20, "ix": 2}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [420, 525], "ix": 2}, "a": {"a": 0, "k": [-14, 30], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 11, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [14, -301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-346, 30], [318, 30]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.683, 0.76, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 20, "ix": 2}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [420, 568], "ix": 2}, "a": {"a": 0, "k": [-14, 30], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 12, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [14, -301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [848, 317], "ix": 2}, "p": {"a": 0, "k": [410, 459.5], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "1", "layers": [{"ddd": 0, "ind": 13, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [14, -301], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "td": 1, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-412, -97], [333, -98], [-413, -83], [-409, -53], [327, -53], [-410, -37], [-407, -12], [326, -11], [-408, 6], [-409, 34], [325, 33], [-412, 49], [-411, 77], [330, 76], [-412, 97], [-414, 117], [326, 117]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [0.12], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [0.47], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 12, "s": [1.04], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 16, "s": [1.82], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 20, "s": [2.8], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 24, "s": [3.97], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 28, "s": [5.33], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 32, "s": [6.86], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 36, "s": [8.55], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 40, "s": [10.4], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 44, "s": [12.39], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 48, "s": [14.52], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 52, "s": [16.77], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 56, "s": [19.13], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 60, "s": [21.6], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 64, "s": [24.17], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 68, "s": [26.82], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 72, "s": [29.55], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 76, "s": [32.35], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 80, "s": [35.2], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 84, "s": [38.1], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 88, "s": [41.04], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 92, "s": [44.01], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 96, "s": [47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 100, "s": [50], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 104, "s": [53], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 108, "s": [55.99], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 112, "s": [58.96], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 116, "s": [61.9], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 120, "s": [64.8], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 124, "s": [67.65], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 128, "s": [70.45], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 132, "s": [73.18], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 136, "s": [75.83], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 140, "s": [78.4], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 144, "s": [80.87], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 148, "s": [83.23], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 152, "s": [85.48], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 156, "s": [87.61], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 160, "s": [89.6], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 164, "s": [91.45], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 168, "s": [93.14], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 172, "s": [94.67], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 176, "s": [96.03], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 180, "s": [97.2], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 184, "s": [98.18], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 188, "s": [98.96], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 192, "s": [99.53], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 196, "s": [99.88], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.329]}}, {"t": 200, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.01]}}, {"t": 204, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 208, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 212, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 216, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 220, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 224, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 228, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 232, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 236, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 240, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 244, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 248, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 252, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 256, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 260, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 264, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 268, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 304, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 308, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 312, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 316, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 320, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 324, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 328, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 332, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 336, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 340, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 344, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 348, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 352, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 356, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 360, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 364, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 368, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 372, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 376, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 380, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 384, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 388, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 392, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 396, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 25, "ix": 2}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [450, 450], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "2", "w": 848, "h": 317, "ind": 2, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [410, 459.5], "ix": 2}, "a": {"a": 0, "k": [410, 459.5], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "3", "layers": [{"ddd": 0, "ind": 14, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[15.16, -80.19], [-15.16, 80.19]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0, 0.75, 0.844], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 0.53, "ix": 2}, "lc": 2, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [998.22, 747.45], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 15, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-108.15, -78.14], [-62.95, 92.88], [-54.58, 99.33], [54.58, 99.33], [62.95, 92.88], [108.15, -78.14], [91.84, -99.33], [-91.84, -99.33], [-108.15, -78.14]], "i": [[0, 0], [0, 0], [-3.933, 0], [0, 0], [-1.004, 3.802], [0, 0], [11.069, 0], [0, 0], [-2.828, -10.702]], "o": [[0, 0], [1.006, 3.802], [0, 0], [3.934, 0], [0, 0], [2.828, -10.702], [0, 0], [-11.069, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0, 0.75, 0.844], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 0.53, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [949.49, 745.78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 16, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-134.27, 131.5], [-101.64, 18.16], [-4.8, -80.11], [63.22, -129.1], [115.14, -74.47], [164.86, -25.06], [-99.55, 131.5]], "i": [[0, 0], [-63.224, 15.205], [-93.637, -33.39], [-54.422, -2.401], [0, 0], [0, -41.616], [20.92, -225.187]], "o": [[0, 0], [0, 0], [0, 0], [54.421, 2.401], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.38, 0.773, 0.946], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [1122.51, 516.61], "ix": 2}, "a": {"a": 0, "k": [11.6, 1.16], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 17, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[25.61, 2.37], [-7.33, -16.63], [-24.89, -1.66], [-9.92, 15.9], [25.61, 2.37]], "i": [[0, 0], [17.382, 1.388], [0.717, -8.985], [-8.983, -0.717], [0, 0]], "o": [[0, 0], [-8.985, -0.717], [-0.717, 8.984], [18.096, 1.445], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-420.9, 391.08], [479.1, 391.08], [479.1, -508.92], [-420.9, -508.92], [-420.9, 391.08]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [29.1, -58.92], "ix": 2}, "a": {"a": 0, "k": [29.1, -58.92], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-420.9, -508.92], [479.1, -508.92], [479.1, 391.08], [-420.9, 391.08], [-420.9, -508.92]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [29.1, -58.92], "ix": 2}, "a": {"a": 0, "k": [29.1, -58.92], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [29.1, -58.92], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 18, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-25.72, -4.47], [5.78, 16.82], [24.37, 3.14], [10.68, -15.45], [-25.72, -4.47]], "i": [[0, 0], [-17.24, -2.618], [-1.353, 8.912], [8.911, 1.353], [0, 0]], "o": [[0, 0], [8.912, 1.354], [1.354, -8.911], [-17.945, -2.726], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-471.23, 384.23], [428.77, 384.23], [428.77, -515.77], [-471.23, -515.77], [-471.23, 384.23]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-21.23, -65.77], "ix": 2}, "a": {"a": 0, "k": [-21.23, -65.77], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-471.23, -515.77], [428.77, -515.77], [428.77, 384.23], [-471.23, 384.23], [-471.23, -515.77]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-21.23, -65.77], "ix": 2}, "a": {"a": 0, "k": [-21.23, -65.77], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-21.23, -65.77], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 19, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[25.61, 2.37], [-7.33, -16.63], [-24.89, -1.66], [-9.92, 15.9], [25.61, 2.37]], "i": [[0, 0], [17.384, 1.388], [0.717, -8.984], [-8.984, -0.717], [0, 0]], "o": [[0, 0], [-8.983, -0.716], [-0.716, 8.984], [18.095, 1.445], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-423.22, 456.05], [476.78, 456.05], [476.78, -443.95], [-423.22, -443.95], [-423.22, 456.05]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [26.78, 6.05], "ix": 2}, "a": {"a": 0, "k": [26.78, 6.05], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-423.22, -443.95], [476.78, -443.95], [476.78, 456.05], [-423.22, 456.05], [-423.22, -443.95]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [26.78, 6.05], "ix": 2}, "a": {"a": 0, "k": [26.78, 6.05], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [26.78, 6.05], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 20, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-25.72, -4.47], [5.78, 16.82], [24.37, 3.14], [10.69, -15.45], [-25.72, -4.47]], "i": [[0, 0], [-17.24, -2.62], [-1.354, 8.911], [8.91, 1.353], [0, 0]], "o": [[0, 0], [8.913, 1.353], [1.353, -8.91], [-17.947, -2.727], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-473.55, 449.21], [426.45, 449.21], [426.45, -450.79], [-473.55, -450.79], [-473.55, 449.21]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-23.55, -0.79], "ix": 2}, "a": {"a": 0, "k": [-23.55, -0.79], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-473.55, -450.79], [426.45, -450.79], [426.45, 449.21], [-473.55, 449.21], [-473.55, -450.79]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-23.55, -0.79], "ix": 2}, "a": {"a": 0, "k": [-23.55, -0.79], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-23.55, -0.79], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 21, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[25.73, 5.17], [-5.26, -16.86], [-24.16, -3.63], [-10.93, 15.28], [25.73, 5.17]], "i": [[0, 0], [17.174, 3.026], [1.563, -8.876], [-8.876, -1.565], [0, 0]], "o": [[0, 0], [-8.876, -1.563], [-1.565, 8.877], [17.877, 3.149], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-430.31, 512.52], [469.69, 512.52], [469.69, -387.48], [-430.31, -387.48], [-430.31, 512.52]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [19.68, 62.52], "ix": 2}, "a": {"a": 0, "k": [19.69, 62.52], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-430.31, -387.48], [469.69, -387.48], [469.69, 512.52], [-430.31, 512.52], [-430.31, -387.48]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [19.68, 62.52], "ix": 2}, "a": {"a": 0, "k": [19.69, 62.52], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [19.68, 62.52], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 22, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-25.67, -7.24], [3.68, 16.94], [23.48, 5.08], [11.61, -14.72], [-25.67, -7.24]], "i": [[0, 0], [-16.916, -4.238], [-2.191, 8.743], [8.743, 2.19], [0, 0]], "o": [[0, 0], [8.742, 2.191], [2.19, -8.743], [-17.607, -4.412], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-480.72, 500.21], [419.28, 500.21], [419.28, -399.79], [-480.72, -399.79], [-480.72, 500.21]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-30.72, 50.21], "ix": 2}, "a": {"a": 0, "k": [-30.72, 50.21], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-480.72, -399.79], [419.28, -399.79], [419.28, 500.21], [-480.72, 500.21], [-480.72, -399.79]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-30.72, 50.21], "ix": 2}, "a": {"a": 0, "k": [-30.72, 50.21], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-30.72, 50.21], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 23, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[25.38, 9.98], [-1.51, -16.91], [-22.35, -7.01], [-12.45, 13.84], [25.38, 9.98]], "i": [[0, 0], [16.428, 5.847], [3.022, -8.49], [-8.49, -3.023], [0, 0]], "o": [[0, 0], [-8.491, -3.023], [-3.023, 8.493], [17.102, 6.088], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-445.26, 569.15], [454.74, 569.15], [454.74, -330.85], [-445.26, -330.85], [-445.26, 569.15]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [4.74, 119.15], "ix": 2}, "a": {"a": 0, "k": [4.74, 119.15], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-445.26, -330.85], [454.74, -330.85], [454.74, 569.15], [-445.26, 569.15], [-445.26, -330.85]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [4.74, 119.15], "ix": 2}, "a": {"a": 0, "k": [4.74, 119.15], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [4.74, 119.15], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 24, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-25.01, -11.95], [-0.11, 16.78], [21.39, 8.38], [13, -13.11], [-25.01, -11.95]], "i": [[0, 0], [-15.971, -7], [-3.618, 8.254], [8.255, 3.617], [0, 0]], "o": [[0, 0], [8.256, 3.618], [3.618, -8.254], [-16.627, -7.286], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-494.68, 547.47], [405.32, 547.47], [405.32, -352.54], [-494.68, -352.54], [-494.68, 547.47]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.68, 97.46], "ix": 2}, "a": {"a": 0, "k": [-44.68, 97.46], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-494.68, -352.54], [405.32, -352.54], [405.32, 547.47], [-494.68, 547.47], [-494.68, -352.54]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-44.68, 97.46], "ix": 2}, "a": {"a": 0, "k": [-44.68, 97.46], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-44.68, 97.47], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 25, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[24.89, 12.47], [0.54, -16.73], [-21.12, -8.75], [-13.13, 12.91], [24.89, 12.47]], "i": [[0, 0], [15.835, 7.304], [3.774, -8.184], [-8.184, -3.775], [0, 0]], "o": [[0, 0], [-8.183, -3.774], [-3.773, 8.185], [16.486, 7.601], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-466.88, 625.04], [433.12, 625.04], [433.12, -274.96], [-466.88, -274.96], [-466.88, 625.04]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-16.88, 175.04], "ix": 2}, "a": {"a": 0, "k": [-16.88, 175.04], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-466.88, -274.96], [433.12, -274.96], [433.12, 625.04], [-466.88, 625.04], [-466.88, -274.96]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-16.88, 175.04], "ix": 2}, "a": {"a": 0, "k": [-16.88, 175.04], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-16.88, 175.04], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 26, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-24.36, -14.36], [-2.15, 16.5], [20.02, 10.07], [13.59, -12.09], [-24.36, -14.36]], "i": [[0, 0], [-15.278, -8.408], [-4.346, 7.896], [7.897, 4.346], [0, 0]], "o": [[0, 0], [7.895, 4.347], [4.346, -7.895], [-15.902, -8.752], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-515.19, 598.56], [384.81, 598.56], [384.81, -301.44], [-515.19, -301.44], [-515.19, 598.56]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-65.19, 148.56], "ix": 2}, "a": {"a": 0, "k": [-65.19, 148.56], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-515.19, -301.44], [384.81, -301.44], [384.81, 598.56], [-515.19, 598.56], [-515.19, -301.44]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-65.19, 148.56], "ix": 2}, "a": {"a": 0, "k": [-65.19, 148.56], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-65.19, 148.56], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 27, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[23.95, 15.55], [3.19, -16.3], [-19.25, -10.91], [-13.86, 11.53], [23.95, 15.55]], "i": [[0, 0], [14.871, 9.107], [4.707, -7.687], [-7.685, -4.707], [0, 0]], "o": [[0, 0], [-7.685, -4.707], [-4.707, 7.685], [15.481, 9.479], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-496.88, 675.82], [403.12, 675.82], [403.12, -224.18], [-496.88, -224.18], [-496.88, 675.82]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-46.88, 225.82], "ix": 2}, "a": {"a": 0, "k": [-46.88, 225.82], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-496.88, -224.18], [403.12, -224.18], [403.12, 675.82], [-496.88, 675.82], [-496.88, -224.18]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-46.88, 225.82], "ix": 2}, "a": {"a": 0, "k": [-46.88, 225.82], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-46.89, 225.82], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 28, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-23.23, -17.31], [-4.78, 15.94], [17.99, 12.15], [14.2, -10.62], [-23.23, -17.31]], "i": [[0, 0], [-14.186, -10.142], [-5.241, 7.332], [7.333, 5.241], [0, 0]], "o": [[0, 0], [7.333, 5.24], [5.24, -7.332], [-14.769, -10.556], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-543.18, 643.4], [356.82, 643.4], [356.82, -256.6], [-543.18, -256.6], [-543.18, 643.4]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-93.18, 193.4], "ix": 2}, "a": {"a": 0, "k": [-93.18, 193.4], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-543.18, -256.6], [356.82, -256.6], [356.82, 643.4], [-543.18, 643.4], [-543.18, -256.6]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-93.18, 193.4], "ix": 2}, "a": {"a": 0, "k": [-93.18, 193.4], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-93.18, 193.41], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 29, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-21.47, 20.75], [14.67, 8.93], [15.27, -14.14], [-7.8, -14.73], [-21.47, 20.75]], "i": [[0, 0], [-12.647, 12.007], [6.206, 6.536], [6.537, -6.206], [0, 0]], "o": [[0, 0], [6.537, -6.206], [-6.206, -6.536], [-13.163, 12.499], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-549.64, 691.23], [350.36, 691.23], [350.36, -208.77], [-549.64, -208.77], [-549.64, 691.23]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-99.64, 241.23], "ix": 2}, "a": {"a": 0, "k": [-99.64, 241.23], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-549.64, -208.77], [350.36, -208.77], [350.36, 691.23], [-549.64, 691.23], [-549.64, -208.77]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-99.64, 241.23], "ix": 2}, "a": {"a": 0, "k": [-99.64, 241.23], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-99.64, 241.23], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 30, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-29.43, 166.23], [53.94, -163.12], [50.25, -166.67], [-34.53, 166.67], [-29.43, 166.23]], "i": [[0, 0], [-1.03, 1.075], [0, 0], [-19.406, -222.644], [0, 0]], "o": [[-19.204, -220.167], [0, 0], [-1.043, 1.074], [0, 0], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-480.32, 510.21], [419.68, 510.21], [419.68, -389.79], [-480.32, -389.79], [-480.32, 510.21]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-30.32, 60.21], "ix": 2}, "a": {"a": 0, "k": [-30.32, 60.21], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-480.32, -389.79], [419.68, -389.79], [419.68, 510.21], [-480.32, 510.21], [-480.32, -389.79]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [-30.32, 60.21], "ix": 2}, "a": {"a": 0, "k": [-30.32, 60.21], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.142, 0.542, 0.683], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [-30.32, 60.21], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 31, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-18.16, 11.18], [-1.96, -12.33], [14.78, -7.84], [10.3, 8.89], [-18.16, 11.18]], "i": [[0, 0], [-11.337, 6.549], [-3.384, -5.859], [5.859, -3.383], [0, 0]], "o": [[0, 0], [5.86, -3.384], [3.384, 5.859], [-11.802, 6.817], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-392.86, 552.72], [507.14, 552.72], [507.14, -347.28], [-392.86, -347.28], [-392.86, 552.72]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [57.14, 102.72], "ix": 2}, "a": {"a": 0, "k": [57.14, 102.72], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-392.86, -347.28], [507.14, -347.28], [507.14, 552.72], [-392.86, 552.72], [-392.86, -347.28]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [57.14, 102.72], "ix": 2}, "a": {"a": 0, "k": [57.14, 102.72], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.224, 0.655, 0.844], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [57.14, 102.72], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 32, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[17.65, -12.53], [3.16, 12.07], [-13.86, 8.79], [-10.57, -8.22], [17.65, -12.53]], "i": [[0, 0], [10.844, -7.336], [3.792, 5.605], [-5.604, 3.791], [0, 0]], "o": [[0, 0], [-5.605, 3.793], [-3.791, -5.604], [11.288, -7.638], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-357.73, 529.33], [542.27, 529.33], [542.27, -370.67], [-357.73, -370.67], [-357.73, 529.33]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [92.27, 79.33], "ix": 2}, "a": {"a": 0, "k": [92.27, 79.33], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-357.73, -370.67], [542.27, -370.67], [542.27, 529.33], [-357.73, 529.33], [-357.73, -370.67]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [92.27, 79.33], "ix": 2}, "a": {"a": 0, "k": [92.27, 79.33], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.224, 0.655, 0.844], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [92.27, 79.33], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 33, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-17.26, 13.37], [-3.93, -11.87], [13.22, -9.38], [10.73, 7.77], [-17.26, 13.37]], "i": [[0, 0], [-10.492, 7.831], [-4.048, -5.423], [5.423, -4.048], [0, 0]], "o": [[0, 0], [5.423, -4.048], [4.047, 5.422], [-10.922, 8.151], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-366.95, 588.57], [533.05, 588.57], [533.05, -311.43], [-366.95, -311.43], [-366.95, 588.57]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [83.05, 138.57], "ix": 2}, "a": {"a": 0, "k": [83.05, 138.57], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-366.95, -311.43], [533.05, -311.43], [533.05, 588.57], [-366.95, 588.57], [-366.95, -311.43]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [83.05, 138.57], "ix": 2}, "a": {"a": 0, "k": [83.05, 138.57], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.224, 0.655, 0.844], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [83.05, 138.57], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 34, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[16.61, -14.61], [5.1, 11.52], [-12.19, 10.25], [-10.92, -7.03], [16.61, -14.61]], "i": [[0, 0], [9.91, -8.557], [4.423, 5.122], [-5.122, 4.421], [0, 0]], "o": [[0, 0], [-5.122, 4.422], [-4.421, -5.121], [10.315, -8.909], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-333.71, 560.99], [566.28, 560.99], [566.28, -339.01], [-333.71, -339.01], [-333.71, 560.99]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [116.29, 110.99], "ix": 2}, "a": {"a": 0, "k": [116.29, 110.99], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-333.71, -339.01], [566.28, -339.01], [566.28, 560.99], [-333.71, 560.99], [-333.71, -339.01]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [116.29, 110.99], "ix": 2}, "a": {"a": 0, "k": [116.29, 110.99], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.224, 0.655, 0.844], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [116.28, 110.99], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 35, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[17.03, 14.01], [-10.8, 7.66], [-12.84, -9.55], [4.37, -11.58], [17.03, 14.01]], "i": [[0, 0], [10.28, 8.108], [-4.19, 5.313], [-5.312, -4.19], [0, 0]], "o": [[0, 0], [-5.313, -4.19], [4.19, -5.313], [10.701, 8.44], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-326.47, 596.34], [573.53, 596.34], [573.53, -303.66], [-326.47, -303.66], [-326.47, 596.34]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [123.53, 146.34], "ix": 2}, "a": {"a": 0, "k": [123.53, 146.34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-326.47, -303.66], [573.53, -303.66], [573.53, 596.34], [-326.47, 596.34], [-326.47, -303.66]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [123.53, 146.34], "ix": 2}, "a": {"a": 0, "k": [123.53, 146.34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.224, 0.655, 0.844], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [123.53, 146.34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 36, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[40.56, 121.71], [-44.41, -118.8], [-41.9, -121.71], [44.41, 121.69], [40.56, 121.71]], "i": [[0, 0], [0.845, 0.731], [0, 0], [-0.797, -167.792], [0, 0]], "o": [[-0.778, -165.927], [0, 0], [0.854, 0.733], [0, 0], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-383.16, 465.25], [516.84, 465.25], [516.84, -434.75], [-383.16, -434.75], [-383.16, 465.25]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [66.84, 15.25], "ix": 2}, "a": {"a": 0, "k": [66.84, 15.25], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-383.16, -434.75], [516.84, -434.75], [516.84, 465.25], [-383.16, 465.25], [-383.16, -434.75]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tr", "p": {"a": 0, "k": [66.84, 15.25], "ix": 2}, "a": {"a": 0, "k": [66.84, 15.25], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0.224, 0.655, 0.844], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "a": {"a": 0, "k": [66.84, 15.25], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 37, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[82.53, 69.18], [7.61, -10.03], [-111.79, -5.14], [-49.73, -66.27], [16.67, -27.76], [72.84, -35.83], [91.12, 21.44], [108.79, 56.9], [106.96, 68.24]], "i": [[0, 0], [69.817, 26.071], [0, 0], [-55.215, -2.907], [0, 0], [-26.234, -23.104], [0, 0], [2.993, -32.906], [0.824, -3.454]], "o": [[0, 0], [-69.817, -26.073], [0, 0], [55.214, 2.906], [0, 0], [21.773, 19.178], [0, 0], [-0.375, 4.112], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.38, 0.773, 0.946], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [811, 579.18], "ix": 2}, "a": {"a": 0, "k": [-1.35, 1.41], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 38, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[148.47, -0.72], [0, 15.89], [-148.47, -0.72], [-70.11, -15.32], [-67.71, -6.29], [-59.35, 0.14], [49.75, 0.14], [58.2, -6.29], [60.7, -15.89], [148.47, -0.72]], "i": [[0, 0], [82.016, 0], [0, 9.219], [-46.673, 2.786], [0, 0], [-3.937, 0], [0, 0], [-1.057, 3.744], [0, 0], [0, -6.724]], "o": [[0, 9.219], [-82.016, 0], [0, -6.339], [0, 0], [0.96, 3.744], [0, 0], [3.938, 0], [0, 0], [51.765, 2.593], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.64, 0.836], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [954.27, 844.98], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "4", "layers": [{"ddd": 0, "ind": 39, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "5", "w": 741, "h": 273, "ind": 40, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [588, 369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 39}, {"ddd": 0, "ind": 41, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[311.79, -160.43], [-311.79, -160.43], [-342.82, -129.4], [-342.82, 67.5], [-311.79, 98.52], [205.24, 98.52], [297.58, 160.43], [260.22, 98.52], [311.79, 98.52], [342.82, 67.5], [342.82, -129.4], [311.79, -160.43]], "i": [[0, 0], [0, 0], [0, -17.136], [0, 0], [-17.136, 0], [0, 0], [-75.129, 0], [6.451, 56.246], [0, 0], [0, 17.136], [0, 0], [17.138, 0]], "o": [[0, 0], [-17.136, 0], [0, 0], [0, 17.136], [0, 0], [2.593, 20.809], [0, 0], [0, 0], [17.138, 0], [0, 0], [0, -17.136], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.922, 0.981, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 42, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[51.55, -26.53], [-51.55, -26.53], [-56.68, -21.39], [-56.68, 11.16], [-51.55, 16.29], [33.94, 16.29], [49.2, 26.53], [43.03, 16.29], [51.55, 16.29], [56.68, 11.16], [56.68, -21.39], [51.55, -26.53]], "i": [[0, 0], [0, 0], [0, -2.834], [0, 0], [-2.833, 0], [0, 0], [-12.422, 0], [1.067, 9.3], [0, 0], [0, 2.833], [0, 0], [2.833, 0]], "o": [[0, 0], [-2.833, 0], [0, 0], [0, 2.833], [0, 0], [0.429, 3.44], [0, 0], [0, 0], [2.833, 0], [0, 0], [0, -2.834], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.373, 0.691, 0.906], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [976, 550], "ix": 2}, "s": {"a": 0, "k": [596, 596], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "6", "layers": [{"ddd": 0, "ind": 43, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-588, -369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-306, -122], [308, -122]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.7, 0.922, 0.98], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 16, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [961, 412], "ix": 2}, "a": {"a": 0, "k": [1, -122], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 44, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-588, -369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-306, -122], [308, -122]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.7, 0.922, 0.98], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 16, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [961, 450], "ix": 2}, "a": {"a": 0, "k": [1, -122], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 45, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-588, -369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-306, -122], [308, -122]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.7, 0.922, 0.98], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 16, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [961, 488], "ix": 2}, "a": {"a": 0, "k": [1, -122], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 46, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-588, -369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-306, -122], [308, -122]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.7, 0.922, 0.98], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 16, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [961, 526], "ix": 2}, "a": {"a": 0, "k": [1, -122], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 47, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-588, -369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-306, -122], [308, -122]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.7, 0.922, 0.98], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 16, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [961, 564], "ix": 2}, "a": {"a": 0, "k": [1, -122], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 48, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-588, -369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-306, -122], [308, -122]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [0.7, 0.922, 0.98], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 16, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [961, 602], "ix": 2}, "a": {"a": 0, "k": [1, -122], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 49, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-588, -369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [741, 273], "ix": 2}, "p": {"a": 0, "k": [958.5, 505.5], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "5", "layers": [{"ddd": 0, "ind": 50, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-588, -369], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "td": 1, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-326, -130], [323, -128], [-329, -119], [-331, -91], [321, -92], [-332, -78], [-329, -53], [324, -51], [-329, -42], [-327, -13], [321, -15], [-330, 0], [-329, 25], [321, 24], [-328, 36], [-329, 61], [329, 60]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [0.47], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [1.82], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [3.97], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [6.86], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [10.4], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [14.52], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [19.13], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [24.17], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [29.55], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [35.2], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [41.04], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [47], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [53], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [58.96], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [64.8], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [70.45], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [75.83], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [80.87], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [85.48], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [89.6], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [93.14], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [96.03], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [98.18], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [99.53], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.039]}}, {"t": 104, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 108, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 112, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 116, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 120, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 124, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 128, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 132, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 136, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 140, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 144, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 148, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 152, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 156, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 160, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [100], "i": {"x": [0.833], "y": [9.294]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [100], "i": {"x": [0.833], "y": [0.918]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 204, "s": [0.47], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [-6.059]}}, {"t": 208, "s": [1.82], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 212, "s": [3.97], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 216, "s": [6.86], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 220, "s": [10.4], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 224, "s": [14.52], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 228, "s": [19.13], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 232, "s": [24.17], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 236, "s": [29.55], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 240, "s": [35.2], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 244, "s": [41.04], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 248, "s": [47], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 252, "s": [53], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [58.96], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 260, "s": [64.8], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 264, "s": [70.45], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 268, "s": [75.83], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 272, "s": [80.87], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 276, "s": [85.48], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 280, "s": [89.6], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 284, "s": [93.14], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 288, "s": [96.03], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 292, "s": [98.18], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 296, "s": [99.53], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 300, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.039]}}, {"t": 304, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 308, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 312, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 316, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 320, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 324, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 328, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 332, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 336, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 340, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 344, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 348, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 352, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 356, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 360, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 364, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 368, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 372, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 376, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 380, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 384, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 388, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 392, "s": [100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 396, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 20, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [960, 540], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "6", "w": 741, "h": 273, "ind": 40, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [958.5, 505.5], "ix": 2}, "a": {"a": 0, "k": [958.5, 505.5], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "7", "layers": [{"ddd": 0, "ind": 51, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-94.48, -27.2], [-13.06, 1.78], [38.74, -2.64], [61.23, 70.87], [73.77, 30.54], [72.94, 80.83], [96.69, 73.14], [82.09, -46.4], [47.62, -67.12], [-19.04, -70.17], [-90.36, -72.4], [-94.48, -27.2]], "i": [[0, 0], [-79.366, 19.32], [-10.801, -5.83], [-5.743, 2.009], [-24.819, -4.578], [0, 0], [-0.668, 10.716], [15.714, 21.709], [0, 0], [46.799, -13.694], [-0.121, -1.348], [-3.322, -30.308]], "o": [[0, 0], [21.779, -5.303], [16.224, 51.039], [7.19, -2.515], [18.989, 3.502], [0, 0], [0.398, -6.403], [-15.712, -21.708], [0, 0], [-46.796, 13.693], [0.119, 1.35], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.12, 0.34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [-47.76, -130.95], "ix": 2}, "a": {"a": 0, "k": [0.76, 2.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[74.57, -49.3], [73.75, -25.96], [73.33, -23.38], [-0.97, 48.67], [-9.24, 49.41], [-74.33, -9.07], [-75.47, -12.08], [-54.58, 10.84], [-37.39, -4.55], [1.36, -7.77], [28.28, -2], [31.16, -2.25], [56.36, -45.44], [74.57, -49.3]], "i": [[0, 0], [0.691, -6.078], [0.211, -0.889], [37.034, -3.309], [2.602, -0.087], [11.079, 29.889], [0.377, 0.982], [-6.948, -0.684], [-18.87, 7.367], [-8.502, -1.053], [-4.992, -0.715], [-0.693, 0.352], [-2.343, 14.345], [-5.227, -0.909]], "o": [[0.907, 9.342], [-0.073, 0.804], [-4.338, 26.917], [-2.863, 0.402], [-40.084, 1.624], [-0.384, -1.053], [0, 0], [6.947, 0.685], [12.037, -4.699], [7.848, 1.038], [1.242, 0.106], [6.653, -3.421], [0.951, -5.592], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.12, 0.34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [-26.78, -26.24], "ix": 2}, "a": {"a": 0, "k": [-0.28, -0.24], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[77.17, -9.31], [77.7, 14.49], [77.28, 17.07], [76.71, 19.66], [11.89, 87.64], [2.63, 87.81], [-61.47, 29.9], [-62.61, 26.88], [-76.64, -37.61], [-73.54, -72.02], [-0.95, -87.86], [63.74, -83.42], [77.17, -9.31]], "i": [[0, 0], [0.69, -6.079], [0.21, -0.889], [0.283, -0.894], [23.936, -3.079], [2.986, 0.169], [11.296, 29.071], [0.377, 0.982], [0.866, 9.709], [-4.848, 7.345], [-1.366, 0.123], [0, 0], [-3.187, -31.608]], "o": [[0.905, 9.341], [-0.073, 0.804], [-0.145, 0.809], [-6.801, 26.921], [-3.163, 0.283], [-33.333, -1.517], [-0.384, -1.053], [-9.952, -27.018], [-0.205, -2.299], [5.329, -8.072], [1.367, -0.122], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [-38.28, -63.06], "ix": 2}, "a": {"a": 0, "k": [0.52, 0.02], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-26.04, -27.74], [0, 34.21], [0, -35.11], [-26.04, -27.74]], "i": [[0, 0], [-28.118, 0.906], [0, 0], [0, 0]], "o": [[0, 0], [28.119, -0.907], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.12, 0.34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [-122.79, -131.39], "ix": 2}, "a": {"a": 0, "k": [-6.79, -0.45], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [458.36, 384.1], "ix": 2}, "a": {"a": 0, "k": [-1.64, 48.1], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [0.01], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [0.04], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [0.08], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [0.14], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [0.21], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [0.29], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [0.38], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [0.48], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [0.59], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [0.7], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [0.82], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [0.94], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [1.06], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [1.18], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [1.3], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [1.41], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [1.52], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [1.62], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [1.71], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [1.79], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [1.86], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [1.92], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [1.96], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [1.99], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.001]}}, {"t": 104, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 108, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 112, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 116, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 120, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 124, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 128, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 132, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 136, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 140, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 144, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 148, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 152, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 156, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 160, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 208, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 212, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 216, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 220, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 224, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 228, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 232, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 236, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 240, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 244, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 248, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 252, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 256, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 260, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 264, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 268, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [2], "i": {"x": [0.833], "y": [1.001]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [2], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 304, "s": [1.99], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [1.96], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [1.92], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [1.86], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [1.79], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [1.71], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [1.62], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [1.52], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [1.41], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [1.3], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [1.18], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [1.06], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [0.94], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [0.82], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [0.7], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [0.59], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [0.48], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [0.38], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [0.29], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [0.21], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [0.14], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [0.08], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [0.04], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [0.01]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 52, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-38.8, 13.97], [-30.88, 33.4], [-11.28, 48.07], [35.47, 13.97], [18.88, -48.59]], "i": [[0, 0], [-1.559, -6.639], [-9.203, 0.518], [0, 0], [0, 0]], "o": [[3.876, 5.26], [2.107, 8.973], [50.082, -2.814], [-10.321, -15.786], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [458.36, 384.1], "ix": 2}, "a": {"a": 0, "k": [-1.64, 48.1], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [0.01], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [0.04], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [0.08], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [0.14], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [0.21], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [0.29], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [0.38], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [0.48], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [0.59], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [0.7], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [0.82], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [0.94], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [1.06], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [1.18], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [1.3], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [1.41], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [1.52], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [1.62], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [1.71], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [1.79], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [1.86], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [1.92], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [1.96], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [1.99], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.001]}}, {"t": 104, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 108, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 112, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 116, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 120, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 124, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 128, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 132, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 136, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 140, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 144, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 148, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 152, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 156, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 160, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 208, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 212, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 216, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 220, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 224, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 228, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 232, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 236, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 240, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 244, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 248, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 252, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 256, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 260, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 264, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 268, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [2], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [2], "i": {"x": [0.833], "y": [1.001]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [2], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 304, "s": [1.99], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [1.96], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [1.92], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [1.86], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [1.79], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [1.71], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [1.62], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [1.52], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [1.41], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [1.3], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [1.18], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [1.06], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [0.94], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [0.82], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [0.7], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [0.59], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [0.48], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [0.38], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [0.29], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [0.21], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [0.14], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [0.08], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [0.04], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [0.01]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 53, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[43.23, 18.07], [42, 17.73], [44.58, 20.75], [45.02, 22.99], [39.77, 24.44], [35.85, 22.54], [30.93, 23.66], [23.21, 18.18], [23.21, 19.74], [17.84, 21.2], [9.56, 15.16], [5.2, 10.12], [0.95, 7.21], [-5.54, 5.09], [-5.87, 5.09], [-5.87, 4.98], [-49.27, -2.4], [25.34, -8.89], [37.19, -7.66], [40.44, -2.07], [34.96, -1.06], [35.4, -0.61], [38.76, 3.3], [48.05, 14.26], [48.49, 16.61], [43.23, 18.07]], "i": [[0, 0], [0.448, 0.112], [-0.336, -0.447], [0, 0], [2.126, 0.111], [1.454, 1.119], [2.013, 0.112], [3.245, 3.244], [0, 0], [2.125, 0.112], [3.467, 3.692], [1.679, 2.013], [1.23, 0.783], [1.006, 0], [0, 0], [0, 0], [19.912, 0], [-28.077, -15.884], [-3.467, 0], [2.573, -1.342], [2.349, -0.336], [-0.224, -0.224], [-1.006, -1.23], [-0.895, -0.894], [0, 0], [2.014, 0.112]], "o": [[-0.448, -0.111], [1.343, 1.454], [1.231, 1.342], [-1.677, 1.789], [-1.119, -0.112], [-1.566, 1.343], [-2.014, -0.224], [0.448, 0.894], [-1.789, 1.678], [-2.014, -0.224], [-1.119, -1.119], [-1.566, -1.119], [-2.685, -1.901], [-0.224, 0], [0, 0], [-16.22, -0.336], [0, 0], [2.909, 0.448], [8.166, 0.111], [-0.895, 0.335], [0.111, 0.112], [1.23, 1.342], [3.579, 4.363], [1.23, 1.454], [-1.678, 1.678], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [290.73, 463.46], "ix": 2}, "a": {"a": 0, "k": [-0.27, 4.96], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 54, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[114.6, 7.33], [-114.6, 7.33], [-111.25, -4.64], [-84.06, -4.87], [-70.08, -1.18], [79.03, -1.18], [114.6, 7.33]], "i": [[0, 0], [0, 0], [-3.132, 7.271], [-8.166, -2.125], [0, 0], [-28.972, 0], [0, 0]], "o": [[0, 0], [0, 0], [8.39, -2.685], [8.39, 2.236], [0, 0], [29.084, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.95, 0.57, 0.153], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [392, 447.38], "ix": 2}, "a": {"a": 0, "k": [0, 0.38], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 55, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[169.97, 52.24], [-172.88, 52.24], [-169.52, 40.27], [-14.6, -40.49], [41.89, -52.13], [169.97, 52.24]], "i": [[0, 0], [0, 0], [-3.132, 7.271], [-114.434, 18.569], [0, 0], [2.909, -104.477]], "o": [[0, 0], [0, 0], [8.724, -20.582], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.98, 0.7, 0.326], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [448.57, 402.06], "ix": 2}, "a": {"a": 0, "k": [-1.43, 0.06], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 56, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[88.2, -21.91], [88.2, -21.97], [133.68, -157.14], [90.33, -186.67], [68.16, -176.03], [30.44, -191.1], [20.02, -157.14], [29.21, -148.3], [-4.12, -18.86], [-7.76, -13.99], [-133.68, 164], [-102.85, 191.1], [-16.63, 112.97], [86.44, -19.26], [87.31, -20.56], [88.2, -21.91]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.665, -6.868], [30.476, -61.534], [1.22, -1.758], [0, 0], [0, 0], [-40.083, 36.638], [-10.183, 15.261], [-0.288, 0.436], [0, 0]], "o": [[0, 0], [33.959, -52.567], [0, 0], [0, 0], [-13.908, -6.085], [0, 0], [0, 0], [0.222, 19.112], [-1.216, 1.651], [-53.852, 77.553], [0, 0], [0, 0], [36.372, -33.247], [0.291, -0.432], [0.555, -0.835], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.98, 0.7, 0.326], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [548, 408.06], "ix": 2}, "a": {"a": 0, "k": [60, -197.94], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [0.02], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [0.07], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [0.16], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [0.27], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [0.42], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [0.58], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [0.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [0.97], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [1.18], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [1.41], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [1.64], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [1.88], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [2.12], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [2.36], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [2.59], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [2.82], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [3.03], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [3.23], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [3.42], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [3.58], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [3.73], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [3.84], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [3.93], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [3.98], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [4], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0]}}, {"t": 104, "s": [3.98], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 108, "s": [3.93], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 112, "s": [3.84], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 116, "s": [3.73], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 120, "s": [3.58], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 124, "s": [3.42], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 128, "s": [3.23], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 132, "s": [3.03], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 136, "s": [2.82], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 140, "s": [2.59], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 144, "s": [2.36], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 148, "s": [2.12], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 152, "s": [1.88], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [1.64], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 160, "s": [1.41], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 164, "s": [1.18], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 168, "s": [0.97], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 172, "s": [0.77], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 176, "s": [0.58], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 180, "s": [0.42], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 184, "s": [0.27], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 188, "s": [0.16], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 192, "s": [0.07], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 196, "s": [0.02], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 200, "s": [0], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [0.02], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 208, "s": [0.07], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 212, "s": [0.16], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 216, "s": [0.27], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 220, "s": [0.42], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 224, "s": [0.58], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 228, "s": [0.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 232, "s": [0.97], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 236, "s": [1.18], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 240, "s": [1.41], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 244, "s": [1.64], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 248, "s": [1.88], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 252, "s": [2.12], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [2.36], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 260, "s": [2.59], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 264, "s": [2.82], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 268, "s": [3.03], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 272, "s": [3.23], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 276, "s": [3.42], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 280, "s": [3.58], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 284, "s": [3.73], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 288, "s": [3.84], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 292, "s": [3.93], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 296, "s": [3.98], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 300, "s": [4], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0]}}, {"t": 304, "s": [3.98], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [3.93], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [3.84], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [3.73], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [3.58], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [3.42], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [3.23], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [3.03], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [2.82], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [2.59], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [2.36], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [2.12], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [1.88], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [1.64], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [1.41], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [1.18], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [0.97], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [0.77], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [0.58], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [0.42], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [0.27], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [0.16], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [0.07], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [0.02]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 57, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[21.06, -50.16], [1.83, -24.3], [-19.84, -15.56], [-43.08, 2.27], [-36.44, 6.46], [-18.44, -0.17], [-18.44, 9.39], [-33.47, 29.19], [-52, 43.87], [-45.7, 48.06], [-23.33, 36.18], [-14.89, 29.62], [3.67, 31.52], [12.38, 27.7], [15.12, 27.06], [27.35, -1.57], [53.4, -27.97], [21.06, -50.16]], "i": [[0, 0], [7.69, -5.943], [10.837, -2.797], [3.147, -3.845], [-6.642, 1.399], [-2.797, 0], [2.797, -6.163], [4.544, -3.495], [1.399, -4.194], [-6.642, 2.098], [-8.04, 4.194], [-2.119, 1.775], [-6.565, 0.995], [-2.33, 1.667], [-0.817, 0.332], [-3.147, 5.593], [0, 0], [0, 0]], "o": [[0, 0], [-7.691, 5.942], [-10.836, 2.797], [-3.146, 3.845], [6.642, -1.398], [2.797, 0], [-2.797, 6.164], [-4.544, 3.496], [-1.398, 4.195], [6.641, -2.098], [4.323, -2.255], [2.619, 0.674], [3.388, -0.513], [0.924, -0.201], [5.943, -2.414], [3.147, -5.593], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [-137.57, 197.36], "ix": 2}, "a": {"a": 0, "k": [0.43, -0.64], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [548, 408.06], "ix": 2}, "a": {"a": 0, "k": [60, -197.94], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [0.02], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [0.07], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [0.16], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [0.27], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [0.42], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [0.58], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [0.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [0.97], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [1.18], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [1.41], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [1.64], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [1.88], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [2.12], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [2.36], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [2.59], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [2.82], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [3.03], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [3.23], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [3.42], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [3.58], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [3.73], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [3.84], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [3.93], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [3.98], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [4], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0]}}, {"t": 104, "s": [3.98], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 108, "s": [3.93], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 112, "s": [3.84], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 116, "s": [3.73], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 120, "s": [3.58], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 124, "s": [3.42], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 128, "s": [3.23], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 132, "s": [3.03], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 136, "s": [2.82], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 140, "s": [2.59], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 144, "s": [2.36], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 148, "s": [2.12], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 152, "s": [1.88], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [1.64], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 160, "s": [1.41], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 164, "s": [1.18], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 168, "s": [0.97], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 172, "s": [0.77], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 176, "s": [0.58], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 180, "s": [0.42], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 184, "s": [0.27], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 188, "s": [0.16], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 192, "s": [0.07], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 196, "s": [0.02], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 200, "s": [0], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [0.02], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 208, "s": [0.07], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 212, "s": [0.16], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 216, "s": [0.27], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 220, "s": [0.42], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 224, "s": [0.58], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 228, "s": [0.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 232, "s": [0.97], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 236, "s": [1.18], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 240, "s": [1.41], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 244, "s": [1.64], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 248, "s": [1.88], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 252, "s": [2.12], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [2.36], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 260, "s": [2.59], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 264, "s": [2.82], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 268, "s": [3.03], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 272, "s": [3.23], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 276, "s": [3.42], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 280, "s": [3.58], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 284, "s": [3.73], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 288, "s": [3.84], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 292, "s": [3.93], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 296, "s": [3.98], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 300, "s": [4], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0]}}, {"t": 304, "s": [3.98], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [3.93], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [3.84], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [3.73], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [3.58], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [3.42], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [3.23], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [3.03], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [2.82], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [2.59], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [2.36], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [2.12], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [1.88], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [1.64], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [1.41], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [1.18], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [0.97], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [0.77], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [0.58], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [0.42], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [0.27], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [0.16], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [0.07], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [0.02]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "8", "layers": [{"ddd": 0, "ind": 58, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[56.01, -57.19], [33.26, -21.02], [20.39, 22.53], [5.82, 34.05], [-75.64, 36.19], [-76.17, 52.41], [-22.32, 59], [33.06, 60.89], [75.12, -47.09], [56.01, -57.19]], "i": [[0, 0], [11.15, -34.805], [2.464, -9.285], [6.884, -0.181], [0, 0], [0, 0], [-22.048, -3.144], [-11.747, 1.439], [1.048, 9.76], [0, 0]], "o": [[0, 0], [-6.231, 19.452], [-1.767, 6.656], [0, 0], [0, 0], [0, 0], [22.048, 3.144], [21.762, -2.666], [-1.637, -15.245], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.98, 0.7, 0.326], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [439.49, 311.87], "ix": 2}, "a": {"a": 0, "k": [-0.51, 1.87], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 59, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-4.76, 62.84], [-36.56, 63.22], [-33.24, 36.02], [-32.01, 35.97], [-17.46, 24.45], [-4.57, -19.1], [18.17, -55.26], [35.9, -45.49], [-4.76, 62.84]], "i": [[0, 0], [12.985, 0.758], [-1.327, 9.289], [0, 0], [-1.754, 6.682], [-6.255, 19.477], [0, 0], [-0.711, -18.482], [21.752, -2.653]], "o": [[-7.44, 0.901], [0.853, -8.767], [0, 0], [6.872, -0.19], [2.464, -9.289], [11.137, -34.784], [0, 0], [0.664, 18.198], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.95, 0.57, 0.153], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [488.18, 324.18], "ix": 2}, "a": {"a": 0, "k": [-0.32, 3.18], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 60, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[18.77, 5.94], [17.63, -3.3], [5.4, -2.7], [-8.91, -6.35], [-14.98, -3.74], [-7.45, -1.33], [-10.05, -0.52], [-13.68, -0.52], [-17.97, -2.45], [-19.98, -4.5], [-25.71, -7.04], [-22.35, -0.52], [-14.46, 5.87], [-1.84, 9.39], [3.13, 9.27], [13.91, 7.99], [19.05, 8.14], [18.77, 5.94]], "i": [[0, 0], [0, 0], [3.933, 0.475], [5.687, 1.09], [-0.9, -1.374], [0, 0], [3.507, 0], [0, 0], [0.142, 0.091], [0.736, 0.78], [-0.395, -2.692], [0, 0], [-5.08, -2.633], [-3.641, -0.743], [-1.638, 0.395], [-4.824, 0.062], [0, 0], [3.6, 1.204]], "o": [[0, 0], [0, 0], [-3.934, -0.474], [-5.734, -1.09], [1.09, 1.611], [0, 0], [0, 0], [-1.7, -0.275], [-0.634, -0.629], [-3.752, -3.977], [0.395, 2.692], [2.37, 2.737], [2.626, 1.361], [1.651, 0.336], [2.219, -0.535], [3.164, 0.139], [7.06, 0.569], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [352.36, 351.69], "ix": 2}, "a": {"a": 0, "k": [-1.36, 0.57], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 61, "ty": 3, "sr": 1, "ks": {"p": {"a": 0, "k": [368.5, 304.41], "ix": 2}, "a": {"a": 0, "k": [448.67, 450.04], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "9", "w": 145, "h": 100, "ind": 62, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [376, 400], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 61}, {"ddd": 0, "ind": 63, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[57.37, 100.33], [-12.44, 109.94], [-57.37, 110.7], [-52.25, 34.12], [-48.93, 6.92], [-14.86, -98.67], [20.45, -119.04], [48.74, -49.52], [41.63, 30.52], [57.37, 100.33]], "i": [[0, 0], [37.012, -9.051], [0, 0], [-4.123, 40.803], [-1.327, 9.289], [-18.577, 27.676], [0, 0], [0.047, -31.325], [-1.185, -18.055], [0, 0]], "o": [[-13.127, -5.355], [-37.059, 9.098], [1.327, -2.796], [0.853, -8.767], [5.545, -37.77], [9.193, -13.695], [14.264, 17.44], [-0.048, 31.325], [1.28, 18.103], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.98, 0.7, 0.326], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [506.16, 311.87], "ix": 2}, "a": {"a": 0, "k": [0, -2.42], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 64, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[17.3, -9.77], [21.55, 9.94], [-6.25, 21.82], [-11.71, -3.52], [17.3, -9.77]], "i": [[0, 0], [-5.561, -4.698], [0, 0], [9.836, 18.297], [0, 0]], "o": [[0, 0], [-8.555, 10.333], [0, 0], [-9.836, -18.298], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.74], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [510.28, 201.08], "ix": 2}, "a": {"a": 0, "k": [4.12, 4.08], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 65, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[20.45, 23.57], [11.16, 10.01], [2.2, 4.51], [-56.88, -17.32], [-25.48, -24.43], [28.08, -29.72], [53.45, -2.28], [41.88, 50.57], [33.08, 37.83], [31.65, 9.84], [20.45, 23.57]], "i": [[0, 0], [0.374, 11.594], [7.095, -6.481], [4.112, 53.595], [-12.61, 6.059], [-18.718, -22.855], [-3.427, -20.842], [7.412, -3.115], [1.774, 9.878], [14.095, 12.885], [-6.871, -10.548]], "o": [[0, 0], [-0.374, -11.594], [-7.095, 6.48], [5.316, 1.685], [6.553, -3.149], [9.201, -1.628], [3.427, 20.842], [-4.768, 2.004], [-0.754, -5.144], [-5.825, -4.052], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.12, 0.34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [-9.04, -31.87], "ix": 2}, "a": {"a": 0, "k": [-1.29, 5.88], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [523.46, 187.23], "ix": 2}, "a": {"a": 0, "k": [24.96, 27.73], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.685]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [-0.07], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 8, "s": [-0.26], "i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 12, "s": [-0.54], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.149]}}, {"t": 16, "s": [-0.9], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 20, "s": [-1.32], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 24, "s": [-1.77], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 28, "s": [-2.23], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 32, "s": [-2.68], "i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 36, "s": [-3.1], "i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.179]}}, {"t": 40, "s": [-3.46], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.189]}}, {"t": 44, "s": [-3.75], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.21]}}, {"t": 48, "s": [-3.93], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.315]}}, {"t": 52, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.006]}}, {"t": 56, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 60, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 64, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 68, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 72, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 76, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 80, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 84, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 88, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 92, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 96, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 100, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 104, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 108, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 112, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 116, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 120, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 124, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 128, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 132, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 136, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 140, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 144, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 148, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 152, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 156, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 160, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 208, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 212, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 216, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 220, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 224, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 228, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 232, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 236, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 240, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 244, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 248, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 252, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 256, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 260, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 264, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 268, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 304, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 308, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 312, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 316, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 320, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 324, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 328, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 332, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 336, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 340, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 344, "s": [-4], "i": {"x": [0.833], "y": [0.994]}, "o": {"x": [0.167], "y": [0]}}, {"t": 348, "s": [-4], "i": {"x": [0.833], "y": [0.685]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 352, "s": [-3.93], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 356, "s": [-3.75], "i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 360, "s": [-3.46], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.149]}}, {"t": 364, "s": [-3.1], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 368, "s": [-2.68], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 372, "s": [-2.23], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 376, "s": [-1.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 380, "s": [-1.32], "i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 384, "s": [-0.9], "i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.179]}}, {"t": 388, "s": [-0.54], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.189]}}, {"t": 392, "s": [-0.26], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.21]}}, {"t": 396, "s": [-0.07], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.315]}}, {"t": 400, "s": [0], "i": {"x": [0.833], "y": [0.685]}, "o": {"x": [0.167], "y": [0]}}, {"t": 404, "s": [-0.07], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 408, "s": [-0.26], "i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 412, "s": [-0.54], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.149]}}, {"t": 416, "s": [-0.9], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 420, "s": [-1.32], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 424, "s": [-1.77], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 428, "s": [-2.23]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 66, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-36.81, -15.6], [-34.28, -4.26], [-31.84, -1.81], [-31.92, 6.81], [-15.5, 38.86], [20.77, 30.02], [36.68, 0.41], [16.25, -41.89], [-19.95, -35.86], [-36.81, -15.6]], "i": [[0, 0], [-4.624, -7.201], [-0.212, -0.819], [0.137, -2.51], [-7.675, -8.09], [-13.005, 6.859], [2.22, 21.809], [1.16, 0.458], [1.353, -0.291], [0, 0]], "o": [[0, 0], [0.433, 0.675], [0.781, 3.01], [3.55, 12.423], [7.882, 3.482], [2.777, -0.244], [-2.22, -21.809], [-1.16, -0.458], [-1.353, 0.291], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.74], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [523.46, 187.23], "ix": 2}, "a": {"a": 0, "k": [24.96, 27.73], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.685]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [-0.07], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 8, "s": [-0.26], "i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 12, "s": [-0.54], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.149]}}, {"t": 16, "s": [-0.9], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 20, "s": [-1.32], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 24, "s": [-1.77], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 28, "s": [-2.23], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 32, "s": [-2.68], "i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 36, "s": [-3.1], "i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.179]}}, {"t": 40, "s": [-3.46], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.189]}}, {"t": 44, "s": [-3.75], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.21]}}, {"t": 48, "s": [-3.93], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.315]}}, {"t": 52, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.006]}}, {"t": 56, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 60, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 64, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 68, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 72, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 76, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 80, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 84, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 88, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 92, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 96, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 100, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 104, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 108, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 112, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 116, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 120, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 124, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 128, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 132, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 136, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 140, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 144, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 148, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 152, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 156, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 160, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 208, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 212, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 216, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 220, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 224, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 228, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 232, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 236, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 240, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 244, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 248, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 252, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 256, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 260, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 264, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 268, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 304, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 308, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 312, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 316, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 320, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 324, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 328, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 332, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 336, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 340, "s": [-4], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 344, "s": [-4], "i": {"x": [0.833], "y": [0.994]}, "o": {"x": [0.167], "y": [0]}}, {"t": 348, "s": [-4], "i": {"x": [0.833], "y": [0.685]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 352, "s": [-3.93], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 356, "s": [-3.75], "i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 360, "s": [-3.46], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.149]}}, {"t": 364, "s": [-3.1], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 368, "s": [-2.68], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 372, "s": [-2.23], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 376, "s": [-1.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 380, "s": [-1.32], "i": {"x": [0.833], "y": [0.844]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 384, "s": [-0.9], "i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.179]}}, {"t": 388, "s": [-0.54], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.189]}}, {"t": 392, "s": [-0.26], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.21]}}, {"t": 396, "s": [-0.07], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.315]}}, {"t": 400, "s": [0], "i": {"x": [0.833], "y": [0.685]}, "o": {"x": [0.167], "y": [0]}}, {"t": 404, "s": [-0.07], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 408, "s": [-0.26], "i": {"x": [0.833], "y": [0.811]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 412, "s": [-0.54], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.149]}}, {"t": 416, "s": [-0.9], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 420, "s": [-1.32], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 424, "s": [-1.77], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 428, "s": [-2.23]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 67, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[39.7, -14.81], [36.62, 11.06], [36.29, 13.67], [33.77, 13.77], [21.83, 14.15], [-37.93, 14.53], [-39.7, 3.02], [29.23, -13.06], [39.7, -14.81]], "i": [[0, 0], [0, 0], [0, 0], [0.854, -0.048], [4.028, -0.142], [0, 0], [0, 0], [-14.644, 2.606], [0, 0]], "o": [[0, 0], [0, 0], [-0.853, 0.047], [-3.886, 0.142], [-28.955, 0.664], [0, 0], [27.391, -8.009], [6.587, -1.137], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.98, 0.7, 0.326], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [466.95, 309.37], "ix": 2}, "a": {"a": 0, "k": [37.95, -0.63], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 4, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 8, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 12, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 16, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 20, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 24, "s": [-3.63], "i": {"x": [0.833], "y": [91.944]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 28, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 32, "s": [-4.67], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 36, "s": [-5.71], "i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 40, "s": [-6.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.203]}}, {"t": 44, "s": [-7.47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 48, "s": [-8.1], "i": {"x": [0.833], "y": [1.142]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 52, "s": [-8.72], "i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.053]}}, {"t": 56, "s": [-7.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 60, "s": [-3.52], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 64, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.293]}}, {"t": 68, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 72, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 76, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 80, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 84, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 88, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 92, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 96, "s": [0], "i": {"x": [0.833], "y": [1.208]}, "o": {"x": [0.167], "y": [0]}}, {"t": 100, "s": [0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 104, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 108, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 112, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 116, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 120, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 124, "s": [-3.63], "i": {"x": [0.833], "y": [91.944]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 128, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 132, "s": [-4.67], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 136, "s": [-5.71], "i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 140, "s": [-6.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.203]}}, {"t": 144, "s": [-7.47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 148, "s": [-8.1], "i": {"x": [0.833], "y": [1.142]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 152, "s": [-8.72], "i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.053]}}, {"t": 156, "s": [-7.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 160, "s": [-3.52], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.293]}}, {"t": 168, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [0], "i": {"x": [0.833], "y": [1.208]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 204, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 208, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 212, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 216, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 220, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 224, "s": [-3.63], "i": {"x": [0.833], "y": [91.944]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 228, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 232, "s": [-4.67], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 236, "s": [-5.71], "i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 240, "s": [-6.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.203]}}, {"t": 244, "s": [-7.47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 248, "s": [-8.1], "i": {"x": [0.833], "y": [1.142]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 252, "s": [-8.72], "i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.053]}}, {"t": 256, "s": [-7.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 260, "s": [-3.52], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.293]}}, {"t": 268, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [0], "i": {"x": [0.833], "y": [1.208]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 304, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 308, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 312, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 316, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 320, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 324, "s": [-3.63], "i": {"x": [0.833], "y": [91.944]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 328, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 332, "s": [-4.67], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 336, "s": [-5.71], "i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 340, "s": [-6.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.203]}}, {"t": 344, "s": [-7.47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 348, "s": [-8.1], "i": {"x": [0.833], "y": [1.142]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 352, "s": [-8.72], "i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.053]}}, {"t": 356, "s": [-7.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 360, "s": [-3.52], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 364, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.293]}}, {"t": 368, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 372, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 376, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 380, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 384, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 388, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 392, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 396, "s": [0], "i": {"x": [0.833], "y": [1.208]}, "o": {"x": [0.167], "y": [0]}}, {"t": 400, "s": [0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 404, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 408, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 412, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 416, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 420, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 424, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 428, "s": [-3.63]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 68, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[21.73, -10.92], [8.88, -7.2], [-7.11, -7.2], [-18.74, 1.14], [-24.68, 9.65], [-13.25, 1.39], [-5.9, 0.21], [-6.32, 3.5], [-10.6, 5.42], [-6.55, 6.97], [1.9, 3.5], [7.65, 1.7], [25.67, -1.29]], "i": [[0, 0], [3.042, -0.563], [4.506, -0.451], [4.547, -3.659], [-0.991, -0.615], [-4.227, 3.033], [-1.464, -1.014], [1.239, -0.788], [0.225, -0.788], [-4.055, 0.765], [-1.352, 1.014], [-3.042, 0.338], [0, 0]], "o": [[0, 0], [-3.042, 0.563], [-2.317, 0.232], [-3.645, 4.144], [2.041, 1.266], [3.204, -2.298], [1.464, 1.014], [-1.239, 0.789], [-0.225, 0.789], [4.056, -0.764], [1.352, -1.014], [3.042, -0.338], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [-44.79, 10.97], "ix": 2}, "a": {"a": 0, "k": [0.41, -0.55], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [466.95, 309.37], "ix": 2}, "a": {"a": 0, "k": [37.95, -0.63], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 4, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 8, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 12, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 16, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 20, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 24, "s": [-3.63], "i": {"x": [0.833], "y": [91.944]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 28, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 32, "s": [-4.67], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 36, "s": [-5.71], "i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 40, "s": [-6.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.203]}}, {"t": 44, "s": [-7.47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 48, "s": [-8.1], "i": {"x": [0.833], "y": [1.142]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 52, "s": [-8.72], "i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.053]}}, {"t": 56, "s": [-7.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 60, "s": [-3.52], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 64, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.293]}}, {"t": 68, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 72, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 76, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 80, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 84, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 88, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 92, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 96, "s": [0], "i": {"x": [0.833], "y": [1.208]}, "o": {"x": [0.167], "y": [0]}}, {"t": 100, "s": [0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 104, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 108, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 112, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 116, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 120, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 124, "s": [-3.63], "i": {"x": [0.833], "y": [91.944]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 128, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 132, "s": [-4.67], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 136, "s": [-5.71], "i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 140, "s": [-6.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.203]}}, {"t": 144, "s": [-7.47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 148, "s": [-8.1], "i": {"x": [0.833], "y": [1.142]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 152, "s": [-8.72], "i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.053]}}, {"t": 156, "s": [-7.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 160, "s": [-3.52], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.293]}}, {"t": 168, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [0], "i": {"x": [0.833], "y": [1.208]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 204, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 208, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 212, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 216, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 220, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 224, "s": [-3.63], "i": {"x": [0.833], "y": [91.944]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 228, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 232, "s": [-4.67], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 236, "s": [-5.71], "i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 240, "s": [-6.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.203]}}, {"t": 244, "s": [-7.47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 248, "s": [-8.1], "i": {"x": [0.833], "y": [1.142]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 252, "s": [-8.72], "i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.053]}}, {"t": 256, "s": [-7.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 260, "s": [-3.52], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.293]}}, {"t": 268, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [0], "i": {"x": [0.833], "y": [1.208]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 304, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 308, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 312, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 316, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 320, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 324, "s": [-3.63], "i": {"x": [0.833], "y": [91.944]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 328, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 332, "s": [-4.67], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 336, "s": [-5.71], "i": {"x": [0.833], "y": [0.859]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 340, "s": [-6.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.203]}}, {"t": 344, "s": [-7.47], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 348, "s": [-8.1], "i": {"x": [0.833], "y": [1.142]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 352, "s": [-8.72], "i": {"x": [0.833], "y": [0.744]}, "o": {"x": [0.167], "y": [0.053]}}, {"t": 356, "s": [-7.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 360, "s": [-3.52], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 364, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.293]}}, {"t": 368, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 372, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 376, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 380, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 384, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 388, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 392, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 396, "s": [0], "i": {"x": [0.833], "y": [1.208]}, "o": {"x": [0.167], "y": [0]}}, {"t": 400, "s": [0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 404, "s": [-2.5], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 408, "s": [-5], "i": {"x": [0.833], "y": [0.942]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 412, "s": [-7.5], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.195]}}, {"t": 416, "s": [-6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 420, "s": [-5.19], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 424, "s": [-3.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [136.611]}}, {"t": 428, "s": [-3.63]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 69, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[51.06, 141.06], [37.32, 117.79], [37.27, 117.74], [37.18, 117.55], [-34.71, 6.61], [-34.85, 6.37], [-37.18, 1.82], [-37.51, 1.02], [-38.27, -1.4], [-38.55, -2.87], [-38.6, -3.1], [-51.06, -50.68], [-17.37, -141.06], [-5.09, -141.06], [-24.57, -80.49], [-32.26, -21.57], [3.97, 56.77], [51.06, 141.06]], "i": [[0, 0], [4.881, 8.056], [0.048, 0.047], [0.047, 0.047], [11.942, 19.145], [0, 0.095], [0.569, 1.422], [0.095, 0.237], [0.095, 0.616], [0.094, 0.522], [0, 0.095], [5.734, 20.188], [0, 0], [0, 0], [5.639, -18.813], [0.494, -10.766], [-8.293, -14.612], [-3.034, -5.971]], "o": [[-4.218, -7.393], [0, 0], [0, -0.047], [-26.349, -43.172], [-0.047, -0.095], [-0.996, -1.611], [-0.095, -0.284], [-0.38, -0.948], [-0.095, -0.474], [0, -0.047], [-1.99, -10.094], [0, 0], [0, 0], [0, 0], [-5.64, 18.767], [-1.086, 23.646], [8.921, 15.719], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.251, 0.32], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [553, 584], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 70, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[40, 90.75], [24.79, 113.59], [20.57, 112.03], [6.78, 99.76], [6.68, 99.52], [4.83, 96.11], [4.5, 95.3], [3.74, 92.88], [3.46, 91.42], [3.41, 91.18], [-9.05, 43.6], [-19.43, 7.44], [-40.14, -77.15], [27.01, -69.95], [38.48, -42.41], [39.9, 87.01], [39.95, 88.86], [40, 90.75]], "i": [[0, 0], [8.199, 2.037], [1.422, 0.663], [2.986, 4.597], [-0.048, 0.095], [0.569, 1.232], [0.095, 0.237], [0.095, 0.616], [0.094, 0.522], [0, 0.095], [5.734, 20.188], [3.555, 12.416], [-0.284, 10.71], [-21.42, -15.07], [-0.142, -10.142], [-0.379, -26.112], [0, -0.569], [0, -0.569]], "o": [[0.427, 20.235], [-1.374, -0.332], [-5.497, -2.654], [-0.047, -0.095], [-0.616, -0.901], [-0.095, -0.284], [-0.38, -0.948], [-0.095, -0.474], [0, -0.047], [-1.99, -10.094], [-3.269, -11.421], [-10.805, -37.296], [1.043, -38.48], [9.004, 6.398], [0.189, 9.525], [0.047, 0.664], [0.047, 0.711], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.35, 0.291, 0.373], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [515.93, 493.12], "ix": 2}, "a": {"a": 0, "k": [-0.07, 9.12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 71, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[38.97, -36.89], [-9.03, 84.7], [-9.03, 84.74], [-18.59, 91.37], [-46.1, 68.21], [-45.83, 60.5], [-36.79, -61.89], [-28.89, -86.18], [38.97, -36.89]], "i": [[0, 0], [7.826, -21.592], [0, 0], [4.702, -1.127], [0.841, 21.421], [-0.284, 2.781], [-1.001, 9.238], [-4.928, 1.593], [7.247, -34.558]], "o": [[-0.099, 0.555], [0, 0], [-1.147, 3.016], [-10.787, 2.649], [-0.112, -2.362], [1.845, -17.323], [0.959, -9.239], [24.597, -7.838], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.35, 0.291, 0.373], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [493.72, 490.61], "ix": 2}, "a": {"a": 0, "k": [-3.24, 2.16], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 72, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[42.52, 386.78], [40.76, 386.81], [21.39, 385.37], [21.41, 381.81], [15.21, 209.94], [15.21, 189.4], [47.82, 205.76], [52.79, 220.36], [55.97, 240.96], [42.52, 386.78]], "i": [[0, 0], [0.558, 0.003], [0, 0], [0, 0], [1.121, 28.175], [0.035, 1.643], [-8.849, -19.002], [-1.256, -5.281], [-1.58, -7.188], [3.85, -35.134]], "o": [[-0.601, 0.039], [-12.348, 0.057], [0, 0], [1.005, -54.529], [-0.074, -1.972], [-0.524, -24.269], [2.075, 4.428], [1.549, 6.355], [9.345, 42.504], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.35, 0.291, 0.373], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [474.86, 634.71], "ix": 2}, "a": {"a": 0, "k": [36.86, 282.71], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 73, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[57.51, 75.37], [38.46, 84.61], [36.7, 81.53], [32.87, 74.57], [32.82, 74.52], [32.63, 74.14], [18.89, 50.87], [18.84, 50.83], [18.74, 50.64], [-53.15, -60.3], [-53.29, -60.54], [-55.61, -65.09], [-55.94, -65.9], [-56.7, -68.31], [-56.99, -69.78], [-57.03, -70.02], [-54.62, -78.6], [-53.1, -79.97], [-23.05, -76.04], [-21.4, -74.81], [-20.54, -74.19], [5.05, -48.5], [57.51, 75.37]], "i": [[0, 0], [0, 0], [0, 0], [1.327, 2.369], [0, 0], [0.094, 0.19], [4.881, 8.056], [0.048, 0.047], [0.047, 0.047], [11.942, 19.145], [0, 0.095], [0.569, 1.422], [0.095, 0.237], [0.095, 0.616], [0.094, 0.522], [0, 0.095], [-1.943, 2.085], [-0.569, 0.426], [-10.663, -7.535], [-0.569, -0.426], [-0.284, -0.189], [-7.156, -10.852], [-13.648, -31.514]], "o": [[-11.563, 6.872], [0, 0], [-1.232, -2.275], [0, 0], [0, -0.047], [-4.218, -7.393], [0, 0], [0, -0.047], [-26.349, -43.172], [-0.047, -0.095], [-0.996, -1.611], [-0.095, -0.284], [-0.38, -0.948], [-0.095, -0.474], [0, -0.047], [-0.474, -3.649], [0.427, -0.521], [6.445, -4.644], [0.521, 0.379], [0.284, 0.19], [8.862, 6.493], [24.359, 36.774], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.35, 0.291, 0.373], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [570.73, 648.61], "ix": 2}, "a": {"a": 0, "k": [0.18, 0.93], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 74, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[41.58, 31.1], [43.67, 18.84], [26.18, -51.6], [-27.13, -36.75], [-50.09, -36.77], [-43.77, 51.6], [41.58, 31.1]], "i": [[0, 0], [-0.938, 4.837], [25.121, 24.29], [23.709, -1.6], [0, 0], [-7.532, -63.17], [0, 0]], "o": [[0, 0], [2.977, -15.348], [0, 0], [-18.67, 1.259], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.35, 0.291, 0.373], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [510.96, 445], "ix": 2}, "a": {"a": 0, "k": [-2.04, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 75, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[26.36, -0.58], [25.2, 10.35], [10.56, 10.35], [9.84, 7.91], [1.36, 10.35], [-10.99, 10.35], [-25.4, 7.61], [-12.74, 1.26], [6, -6.62], [8.44, -9.8], [10.95, -7.63], [18.58, -3.47], [23.53, -5.85], [26.36, -0.58]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [3.729, 0], [0, 0], [0.229, 1.592], [-2.814, 0], [-1.906, 4.102], [-1.672, -0.557], [-1.004, -1.842], [-3.171, 0.558], [-1.315, 1.375], [0, -4.159]], "o": [[0, 4.159], [0, 0], [0, 0], [0, 0], [0, 0], [-9.046, 0], [-0.955, -6.653], [8.08, 0], [1.384, -2.976], [0, 0], [1.541, 2.828], [1.813, -0.319], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.23, 0.23, 0.23], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [454.4, 754.64], "ix": 2}, "a": {"a": 0, "k": [0.45, 0.19], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 76, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-8.64, -15.43], [-8.35, 11.14], [6.12, 13.36], [9.48, -13.8]], "i": [[0, 0], [-1.127, -4.951], [-1.477, 2.068], [0, 0]], "o": [[0, 0], [0.888, 3.9], [1.477, -2.068], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [470.52, 742.58], "ix": 2}, "a": {"a": 0, "k": [0.27, -0.42], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 77, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[22.29, -12.71], [26.11, -1.36], [15.02, 5.25], [12.84, 3.61], [7, 9.71], [-3.99, 14.26], [-22.76, 14.06], [-10.06, 3.99], [1.13, -10.79], [2.23, -14.64], [7.68, -10.11], [17.71, -16.57], [22.29, -12.71]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [3.391, -2.128], [2.898, -0.661], [0.802, 1.393], [-2.614, 1.042], [-0.252, 4.516], [-1.76, 0.102], [-2.031, -5.103], [-2.027, 5.019], [-1.54, -3.863]], "o": [[1.54, 3.863], [0, 0], [0, 0], [0, 0], [-2.519, 1.581], [-10.139, 2.315], [-3.352, -5.826], [7.505, -2.993], [0.183, -3.277], [0, 0], [0.133, -0.032], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.23, 0.23, 0.23], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [612.91, 746.52], "ix": 2}, "a": {"a": 0, "k": [1.41, -0.48], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 78, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-10.31, -4.02], [-4.56, 10.35], [9.71, 7.04], [0.65, -13.94]], "i": [[0, 0], [-1.752, -3.595], [-0.606, 2.469], [0, 0]], "o": [[0, 0], [1.751, 3.596], [0.607, -2.468], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [620.76, 728.05], "ix": 2}, "a": {"a": 0, "k": [-0.29, -1.12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 79, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0.01, -8.21], [-152.84, 0], [0.01, 8.21], [152.84, 0], [0.01, -8.21]], "i": [[0, 0], [0, -4.532], [-84.414, 0], [0, 4.53], [84.404, 0]], "o": [[-84.414, 0], [0, 4.53], [84.404, 0], [0, -4.532], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.644, 0.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [539.73, 762], "ix": 2}, "o": {"a": 0, "k": 41, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "a", "layers": [{"ddd": 0, "ind": 80, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-376, -400], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [900, 900], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0.11, 0.27, 0.71], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "a": {"a": 0, "k": [-450, -450], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 81, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-376, -400], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [145, 100], "ix": 2}, "p": {"a": 0, "k": [448.5, 450], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "9", "layers": [{"ddd": 0, "ind": 82, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-376, -400], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "td": 1, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[417.09, 494.86], [411.34, 496.93], [410.53, 496.55], [377.07, 403.72], [377.45, 402.92], [383.2, 400.85], [384.01, 401.23], [417.47, 494.05], [417.09, 494.86]], "i": [[0, 0], [0, 0], [0.118, 0.328], [0, 0], [-0.328, 0.118], [0, 0], [-0.118, -0.328], [0, 0], [0.328, -0.118]], "o": [[0, 0], [-0.328, 0.118], [0, 0], [-0.118, -0.328], [0, 0], [0.328, -0.118], [0, 0], [0.118, 0.329], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[519.56, 499.27], [413.65, 499.27], [412.92, 498.54], [412.92, 492.39], [413.65, 491.66], [519.56, 491.66], [520.3, 492.39], [520.3, 498.54], [519.56, 499.27]], "i": [[0, 0], [0, 0], [0, 0.405], [0, 0], [-0.405, 0], [0, 0], [0, -0.405], [0, 0], [0.405, 0]], "o": [[0, 0], [-0.405, 0], [0, 0], [0, -0.405], [0, 0], [0.405, 0], [0, 0], [0, 0.405], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "a", "w": 145, "h": 100, "ind": 62, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [448.5, 450], "ix": 2}, "a": {"a": 0, "k": [448.5, 450], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "b", "layers": [{"ddd": 0, "ind": 83, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[29.59, 5.16], [29.16, 0.03], [19.19, -3.71], [13.17, -4.27], [9.73, 0.16], [-12.72, -3.92], [-24.09, -13.85], [-29.54, -2.96], [-29.5, 4.82], [-27.54, 11.92], [-21.8, 12.89], [-15.92, 11.98], [-15.97, 7.3], [-11.21, 6.95], [-3.51, 9.59], [0.66, 12.52], [12.89, 13.32], [29.59, 5.16]], "i": [[0, 0], [1.097, 0.481], [3.047, 0.777], [-1.72, -0.556], [2.694, -0.294], [5.446, 0.371], [0.547, 1.336], [0.343, -2.286], [-0.893, -1.948], [-0.004, -3.063], [-4.077, -0.115], [0, 0], [0, 0], [-2.706, -0.076], [-1.644, -1.213], [-1.514, -0.365], [-4.736, 0.531], [-1.371, 3.314]], "o": [[0.803, -1.94], [-1.521, -0.667], [-3.046, -0.777], [1.721, 0.557], [-9.305, 1.016], [-7.86, -0.536], [-1.3, 1.115], [-0.31, 2.066], [0.62, 1.344], [0, 0], [2.581, 0.073], [0, 0], [0, 0], [2.705, 0.076], [1.288, 0.951], [4.368, 1.051], [9.972, -1.118], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.35, 0.291, 0.373], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [485.06, 786.17], "ix": 2}, "a": {"a": 0, "k": [0.06, -0.13], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 84, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[27.75, 10.38], [27.96, 5.24], [18.95, 0.1], [13.3, -1.33], [9.5, 2.57], [-11.34, -4.71], [-22.38, -16.39], [-27.43, -6.18], [-28.34, 1.53], [-27.34, 8.85], [-22.01, 10.63], [-16.31, 10.58], [-15.79, 5.94], [-11.23, 6.28], [-4.2, 9.36], [-0.91, 13.41], [10.9, 16.06], [27.75, 10.38]], "i": [[0, 0], [0.984, 0.635], [2.799, 1.208], [-1.568, -0.799], [2.595, 0.097], [5.128, 1.153], [0.357, 1.402], [0.603, -2.215], [-0.613, -2.059], [0.368, -3.035], [-3.858, -0.702], [0, 0], [0, 0], [-2.561, -0.465], [-0.254, -0.289], [-1.368, -0.649], [-4.563, -0.156], [-1.706, 3.086]], "o": [[0.998, -1.806], [-1.365, -0.881], [-2.799, -1.209], [1.567, 0.799], [-8.963, -0.335], [-7.401, -1.663], [-1.37, 0.917], [-0.546, 2.002], [0.425, 1.421], [0, 0], [2.443, 0.444], [0, 0], [0, 0], [2.56, 0.465], [1.05, 1.195], [3.62, 1.717], [9.609, 0.33], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.35, 0.291, 0.373], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [418.63, 780.89], "ix": 2}, "a": {"a": 0, "k": [0.04, -0.15], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 85, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[41.11, 16.75], [32.19, 8.78], [29.11, -2.88], [31.69, -9.47], [29.98, -32.66], [24.51, -40.91], [25.46, -48.54], [30.43, -48.49], [35.1, -44.7], [32.67, -62.76], [29.6, -70.1], [41.3, -62.81], [8.69, -89.96], [-11.15, -86.28], [-23.51, -72.38], [-35.1, -57.23], [-35.67, -41.5], [-30.26, -18.76], [-31.14, 0.27], [-42.01, 14.61], [-41.69, 37.45], [-34.55, 57.73], [-25.45, 84.07], [-4.92, 89.64], [11.92, 84.32], [37.4, 60.87], [38.97, 43.12], [45.23, 29.28], [41.41, 17.12], [41.11, 16.75]], "i": [[0, 0], [2.309, 3.329], [-0.509, 3.828], [-1.053, 2.123], [6.689, 7.634], [1.117, 3.128], [-2.746, 1.718], [-1.52, -1.37], [-0.541, 6.186], [5.196, 10.632], [-0.427, 1.131], [-2.732, -4.265], [26.87, -0.669], [6.04, -3.064], [-0.404, -5.424], [0.74, -3.614], [-0.683, -5.238], [-1.526, -7.644], [2.459, -5.927], [2.864, -5.551], [-3.723, -7.055], [1.214, -8.636], [-11.995, -10.552], [-6.387, 0.989], [-5.672, 3.079], [-6.49, 14.483], [-3.275, 8.405], [-0.503, 5.228], [2.643, 3.405], [0.102, 0.12]], "o": [[-2.611, -3.094], [-2.18, -3.141], [0.314, -2.362], [4.685, -9.443], [-2.186, -2.496], [-1.011, -2.831], [1.911, -1.195], [1.063, 0.958], [0.517, -5.927], [-0.616, -1.259], [6.679, 1.272], [2.99, -3.346], [-6.788, 0.169], [-6.04, 3.063], [-9.516, 1.833], [-1.057, 5.151], [1.008, 7.723], [1.242, 6.22], [-2.362, 5.693], [-3.638, 7.051], [3.709, 7.029], [-0.395, 2.809], [4.907, 4.316], [6.387, -0.99], [10.037, -5.45], [3.674, -8.201], [1.836, -4.711], [0.416, -4.321], [-0.097, -0.125], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.13, 0.36], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [412.73, 226.95], "ix": 2}, "a": {"a": 0, "k": [0.34, -0.05], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 86, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[20.02, -62.61], [35.43, -59.97], [66.56, -50.51], [65.58, -17.24], [37.86, -16.09], [-12.17, 0.43]], "i": [[0, 0], [-7.548, -4.457], [0, 0], [2.596, -9.227], [20.73, -2.25], [0, 0]], "o": [[0, 0], [10.083, 5.955], [0, 0], [0, 0], [-21.429, 2.325], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.98, 0.7, 0.326], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [434, 300], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.686]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [-0.1], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 8, "s": [-0.36], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 12, "s": [-0.75], "i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 16, "s": [-1.25], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 20, "s": [-1.82], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 24, "s": [-2.43], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 28, "s": [-3.04], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 32, "s": [-3.62], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 36, "s": [-4.14], "i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.185]}}, {"t": 40, "s": [-4.56], "i": {"x": [0.833], "y": [0.878]}, "o": {"x": [0.167], "y": [0.202]}}, {"t": 44, "s": [-4.86], "i": {"x": [0.833], "y": [0.94]}, "o": {"x": [0.167], "y": [0.264]}}, {"t": 48, "s": [-5], "i": {"x": [0.833], "y": [0.531]}, "o": {"x": [0.167], "y": [-0.209]}}, {"t": 52, "s": [-4.96], "i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.101]}}, {"t": 56, "s": [-4.78], "i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 60, "s": [-4.48], "i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.147]}}, {"t": 64, "s": [-4.08], "i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.154]}}, {"t": 68, "s": [-3.62], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 72, "s": [-3.1], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 76, "s": [-2.56], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 80, "s": [-2.02], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 84, "s": [-1.5], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 88, "s": [-1.02], "i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 92, "s": [-0.61], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.19]}}, {"t": 96, "s": [-0.29], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.211]}}, {"t": 100, "s": [-0.07], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.316]}}, {"t": 104, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.006]}}, {"t": 108, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 112, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 116, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 120, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 124, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 128, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 132, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 136, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 140, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 144, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 148, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 152, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 156, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 160, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [0], "i": {"x": [0.833], "y": [1.008]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [0], "i": {"x": [0.833], "y": [0.686]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 204, "s": [-0.1], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 208, "s": [-0.36], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 212, "s": [-0.75], "i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 216, "s": [-1.25], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 220, "s": [-1.82], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 224, "s": [-2.43], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 228, "s": [-3.04], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 232, "s": [-3.62], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 236, "s": [-4.14], "i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.185]}}, {"t": 240, "s": [-4.56], "i": {"x": [0.833], "y": [0.878]}, "o": {"x": [0.167], "y": [0.202]}}, {"t": 244, "s": [-4.86], "i": {"x": [0.833], "y": [0.94]}, "o": {"x": [0.167], "y": [0.264]}}, {"t": 248, "s": [-5], "i": {"x": [0.833], "y": [0.531]}, "o": {"x": [0.167], "y": [-0.209]}}, {"t": 252, "s": [-4.96], "i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.101]}}, {"t": 256, "s": [-4.78], "i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 260, "s": [-4.48], "i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.147]}}, {"t": 264, "s": [-4.08], "i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.154]}}, {"t": 268, "s": [-3.62], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 272, "s": [-3.1], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 276, "s": [-2.56], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 280, "s": [-2.02], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 284, "s": [-1.5], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 288, "s": [-1.02], "i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 292, "s": [-0.61], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.19]}}, {"t": 296, "s": [-0.29], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.211]}}, {"t": 300, "s": [-0.07], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.316]}}, {"t": 304, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.006]}}, {"t": 308, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 312, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 316, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 320, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 324, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 328, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 332, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 336, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 340, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 344, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 348, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 352, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 356, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 360, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 364, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 368, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 372, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 376, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 380, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 384, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 388, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 392, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 396, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 87, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[36.79, 26.02], [37.93, 60.62], [39.06, 69.74], [37.26, 73.3], [24.73, 78.15], [-27.1, 80.67], [-41.64, 75.31], [-38.6, 64.58], [-40.87, 8.38], [-43.8, -67.27], [8.48, -82.12], [14.77, -68.5], [38.96, -33.75], [44.99, -21.68], [44.94, -5.18], [36.79, 26.02]], "i": [[0, 0], [-1.134, -13.923], [0.206, -2.372], [1.083, -0.98], [5.259, -0.928], [7.632, -1.495], [0, 4.383], [-0.258, 5.982], [2.424, 13.304], [-3.506, 31.198], [0, 0], [-4.022, -0.67], [-4.692, -18.564], [-1.186, -3.352], [2.114, -5.672], [1.289, -12.324]], "o": [[-0.877, 8.147], [0.206, 2.372], [-0.103, 1.393], [-2.475, 2.424], [-17.119, 3.094], [-7.168, 1.444], [0, -3.042], [0.567, -13.097], [-5.053, -27.897], [0, 0], [4.022, 0.67], [2.939, 0.465], [2.836, 4.795], [2.32, 6.24], [-4.022, 10.984], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.98, 0.7, 0.326], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [434.44, 299.49], "ix": 2}, "a": {"a": 0, "k": [0.44, -0.51], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 88, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-33.15, -197.32], [-53.69, -44.78], [-44.58, 31.83], [-37.27, 164.38], [54.8, 170.83], [55.05, 9.95], [31.56, -203.22]], "i": [[0, 0], [-3.496, -60.967], [-1.095, -16.129], [0.3, -41.851], [-21.426, 28.991], [1.686, 70.105], [0.414, 8.807]], "o": [[-17.371, 34.439], [0.926, 16.138], [2.489, 36.695], [23.311, 38.836], [2.381, -50.572], [-2.546, -105.753], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.35, 0.291, 0.373], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [437.4, 565.43], "ix": 2}, "a": {"a": 0, "k": [0.93, -5.08], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 89, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-71.66, 30.52], [-1.09, 25.15], [79.03, -18.75], [71.35, -30.52], [-2.51, 0.59], [-80.13, 0.34]], "i": [[0, 0], [-5.72, 1.122], [0, 0], [8.779, 2.55], [6.964, -4.236], [0, 0]], "o": [[23.239, -0.268], [22.538, -4.422], [0, 0], [-14.605, 11.85], [-4.514, 2.746], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [101.47, -49], "ix": 2}, "a": {"a": 0, "k": [-0.53, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-7.64, 16.42], [8.19, 9.63], [9.37, 5.14], [8.96, -1.69], [16.14, -14.98], [12.31, -13.76], [-1.34, -4.64], [0.57, -8.03], [-0.09, -12.34], [-3.25, -9.48], [-10.16, -3.61], [-15.17, 3.21], [-14.29, 8.3]], "i": [[0, 0], [-2.613, 4.179], [0.182, 1.581], [4.57, -1.323], [-1.276, 5.358], [2.174, -3.561], [5.944, -1.329], [-0.982, 2.087], [1.036, 0.384], [0.605, -0.514], [2.302, -1.955], [1.154, -3.134], [-2.275, 0.741]], "o": [[3.094, 0.898], [0.839, -1.342], [1.526, -1.796], [4.708, -3.151], [0.427, -1.795], [-3.021, 4.951], [-0.382, -0.984], [1.089, -2.313], [-0.782, -0.29], [-2.302, 1.955], [-2.532, 2.15], [-0.193, 0.522], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [188.6, -84.77], "ix": 2}, "a": {"a": 0, "k": [0.27, 0.09], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [434, 300], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.686]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [-0.1], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 8, "s": [-0.36], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 12, "s": [-0.75], "i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 16, "s": [-1.25], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 20, "s": [-1.82], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 24, "s": [-2.43], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 28, "s": [-3.04], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 32, "s": [-3.62], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 36, "s": [-4.14], "i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.185]}}, {"t": 40, "s": [-4.56], "i": {"x": [0.833], "y": [0.878]}, "o": {"x": [0.167], "y": [0.202]}}, {"t": 44, "s": [-4.86], "i": {"x": [0.833], "y": [0.94]}, "o": {"x": [0.167], "y": [0.264]}}, {"t": 48, "s": [-5], "i": {"x": [0.833], "y": [0.531]}, "o": {"x": [0.167], "y": [-0.209]}}, {"t": 52, "s": [-4.96], "i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.101]}}, {"t": 56, "s": [-4.78], "i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 60, "s": [-4.48], "i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.147]}}, {"t": 64, "s": [-4.08], "i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.154]}}, {"t": 68, "s": [-3.62], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 72, "s": [-3.1], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 76, "s": [-2.56], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 80, "s": [-2.02], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 84, "s": [-1.5], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 88, "s": [-1.02], "i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 92, "s": [-0.61], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.19]}}, {"t": 96, "s": [-0.29], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.211]}}, {"t": 100, "s": [-0.07], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.316]}}, {"t": 104, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.006]}}, {"t": 108, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 112, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 116, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 120, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 124, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 128, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 132, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 136, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 140, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 144, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 148, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 152, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 156, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 160, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [0], "i": {"x": [0.833], "y": [1.008]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [0], "i": {"x": [0.833], "y": [0.686]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 204, "s": [-0.1], "i": {"x": [0.833], "y": [0.79]}, "o": {"x": [0.167], "y": [0.113]}}, {"t": 208, "s": [-0.36], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.138]}}, {"t": 212, "s": [-0.75], "i": {"x": [0.833], "y": [0.822]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 216, "s": [-1.25], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 220, "s": [-1.82], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 224, "s": [-2.43], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 228, "s": [-3.04], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 232, "s": [-3.62], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 236, "s": [-4.14], "i": {"x": [0.833], "y": [0.858]}, "o": {"x": [0.167], "y": [0.185]}}, {"t": 240, "s": [-4.56], "i": {"x": [0.833], "y": [0.878]}, "o": {"x": [0.167], "y": [0.202]}}, {"t": 244, "s": [-4.86], "i": {"x": [0.833], "y": [0.94]}, "o": {"x": [0.167], "y": [0.264]}}, {"t": 248, "s": [-5], "i": {"x": [0.833], "y": [0.531]}, "o": {"x": [0.167], "y": [-0.209]}}, {"t": 252, "s": [-4.96], "i": {"x": [0.833], "y": [0.779]}, "o": {"x": [0.167], "y": [0.101]}}, {"t": 256, "s": [-4.78], "i": {"x": [0.833], "y": [0.807]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 260, "s": [-4.48], "i": {"x": [0.833], "y": [0.818]}, "o": {"x": [0.167], "y": [0.147]}}, {"t": 264, "s": [-4.08], "i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.154]}}, {"t": 268, "s": [-3.62], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 272, "s": [-3.1], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 276, "s": [-2.56], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 280, "s": [-2.02], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 284, "s": [-1.5], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 288, "s": [-1.02], "i": {"x": [0.833], "y": [0.851]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 292, "s": [-0.61], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.19]}}, {"t": 296, "s": [-0.29], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.211]}}, {"t": 300, "s": [-0.07], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.316]}}, {"t": 304, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.006]}}, {"t": 308, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 312, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 316, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 320, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 324, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 328, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 332, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 336, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 340, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 344, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 348, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 352, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 356, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 360, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 364, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 368, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 372, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 376, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 380, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 384, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 388, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 392, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 396, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 90, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[28.69, -25.25], [-3.99, -47.02], [-39.82, -18.28], [-25.93, 23.89], [-23.74, 48.94], [19.33, 47.39], [20.84, 29.25], [34.77, 15.85], [38.31, 0.87], [33.07, -19.05], [28.69, -25.25]], "i": [[0, 0], [19.431, -1.923], [-0.236, -14.56], [-3.799, -10.073], [0, 0], [0, 0], [-2.628, 4.976], [-6.689, 5.405], [1.75, 7.688], [1.677, 3.372], [1.116, 2.137]], "o": [[-5.123, -9.814], [-19.431, 1.923], [0.236, 14.559], [3.798, 10.073], [0, 0], [0, 0], [1.47, -2.784], [2.923, -2.362], [-2.616, -11.495], [-1.204, -2.421], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.74], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [421.37, 190.75], "ix": 2}, "a": {"a": 0, "k": [-0.46, 0.87], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 91, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [338, 344], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 92, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [356, 340.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 93, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [374, 337], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 94, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [392, 333.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 95, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [410, 330], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 96, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [428, 326.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 97, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [446, 323], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 98, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [464, 319.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 99, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [482, 316], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 100, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.55, 41.17], [5.53, 41.68], [2.24, 39.34], [-10.88, -37.88], [-8.55, -41.17], [-5.53, -41.68], [-2.24, -39.34], [10.88, 37.88], [8.55, 41.17]], "i": [[0, 0], [0, 0], [0.264, 1.554], [0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264]], "o": [[0, 0], [-1.554, 0.264], [0, 0], [-0.264, -1.554], [0, 0], [1.554, -0.264], [0, 0], [0.264, 1.554], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.3, 0.255, 0.6], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [500, 312.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 101, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[98.34, 32.4], [-82, 63.79], [-85.82, 61.08], [-101.06, -28.58], [-98.34, -32.4], [82, -63.79], [85.82, -61.08], [101.06, 28.58], [98.34, 32.4]], "i": [[0, 0], [0, 0], [0.307, 1.804], [0, 0], [-1.804, 0.306], [0, 0], [-0.307, -1.804], [0, 0], [1.804, -0.307]], "o": [[0, 0], [-1.804, 0.307], [0, 0], [-0.307, -1.804], [0, 0], [1.804, -0.307], [0, 0], [0.307, 1.804], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.224, 0.161, 0.5], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [420.5, 328.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 102, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[107.82, 38.44], [-89.07, 71.9], [-90, 71.24], [-108.48, -37.52], [-107.82, -38.44], [89.07, -71.9], [90, -71.24], [108.48, 37.52], [107.82, 38.44]], "i": [[0, 0], [0, 0], [0.074, 0.436], [0, 0], [-0.437, 0.074], [0, 0], [-0.074, -0.436], [0, 0], [0.437, -0.074]], "o": [[0, 0], [-0.437, 0.074], [0, 0], [-0.074, -0.437], [0, 0], [0.436, -0.074], [0, 0], [0.074, 0.436], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.173, 0.12, 0.34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [420.5, 328.5], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 103, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[1.16, -25.71], [-11.95, 21.96], [11.95, 25.71], [11.95, -8.12]], "i": [[0, 0], [3.227, -21.056], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.98, 0.7, 0.326], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [372, 262], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 104, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[8.29, -65.45], [-11.9, -15.93], [4.22, 35.09], [4.69, 63.48], [5.03, 65.53], [10.07, 32.29], [9.34, -65.53], [8.29, -65.45]], "i": [[0, 0], [0, 0], [-1.124, -18.188], [-0.487, -9.455], [-0.188, -0.662], [-0.656, 7.546], [1.773, 43.212], [0.353, -0.065]], "o": [[0, 0], [9.367, 14.999], [0.583, 9.447], [0.035, 0.67], [2.528, -14.088], [1.835, -21.122], [-0.352, 0.023], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.99, 0.797, 0.585], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [438.61, 548], "ix": 2}, "a": {"a": 0, "k": [-0.39, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 105, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[27.97, -153.26], [-22.14, -193.52], [-24.57, -135.49], [-22.38, -103.58], [-4.03, 1.59], [-15.01, 47.36], [-17.94, 169.79], [-0.26, 186.52], [31.29, 3.25], [27.97, -153.26]], "i": [[0, 0], [0, 0], [-9.324, -29.702], [-2.474, -6.923], [0.942, -6.773], [2.556, -19.925], [2.333, -20.694], [-0.882, 7.005], [-2.597, 29.897], [3.277, 72.042]], "o": [[0, 0], [0, 0], [4.586, 14.606], [2.311, 6.467], [-0.942, 6.773], [-3.074, 23.963], [-2.332, 20.694], [7.529, -59.858], [2.597, -29.898], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [420.09, 573.69], "ix": 2}, "a": {"a": 0, "k": [2.09, -1.81], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 106, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[2.36, -25.34], [-0.4, -0.86], [10.11, 12.57], [26.43, 20.16], [25.93, 24.6], [11.01, 28.76], [-13.29, 19.65], [-21.37, 19.76], [-27.14, 10.73], [-21.19, -4.7], [-17.72, -29.38]], "i": [[0, 0], [-1.296, -2.729], [-4.6, -2.79], [-0.874, -1.298], [1.474, -1.216], [4.421, 0.621], [8.342, 0.811], [4.021, 1.938], [-0.259, 3.464], [-2.278, 4.466], [0, 0]], "o": [[0, 0], [1.297, 2.728], [4.827, 2.927], [0.873, 1.298], [-3.806, 3.139], [-14.333, -2.016], [-1.44, -0.14], [-4.021, -1.938], [0.446, -5.976], [2.278, -4.466], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [419.09, 765.41], "ix": 2}, "a": {"a": 0, "k": [-0.05, -0.25], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 107, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[92.71, -128.68], [74.88, -133.74], [62.32, -145.47], [61.31, -169.22], [69.63, -340.47], [47.78, -497.44], [-14.61, -544.32], [-25.5, -481.16], [-4.52, -453.79], [36.64, -335.21], [29.45, -284.38], [42.45, -167.49], [42.26, -146.35], [36.14, -129.96], [43.26, -121.88], [51.59, -123.2], [77.86, -117.83], [92.75, -124.2], [92.71, -128.68]], "i": [[0, 0], [5.361, 2.175], [1.228, 2.886], [-0.092, 6.168], [-0.885, 24.871], [17.326, 69.198], [0, 0], [-6.116, -30.758], [-2.579, -6.923], [0, 0], [0.154, -26.541], [-2.169, -26.013], [1.459, -3.891], [-0.295, -5.989], [-4.401, -1.316], [-1.506, 0.077], [-15.068, 0.157], [-3.536, 3.684], [1.067, 1.156]], "o": [[-1.067, -1.155], [-5.107, -2.073], [-1.227, -2.887], [0.635, -42.491], [1.538, -43.231], [0, 0], [0, 0], [3.548, 17.845], [2.578, 6.922], [0, 0], [-0.132, 22.655], [0.275, 5.084], [-1.789, 4.769], [0.172, 3.473], [4.4, 1.316], [8.723, -0.449], [4.647, -0.049], [1.369, -1.426], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.883, 0.742], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [454.34, 583.92], "ix": 2}, "a": {"a": 0, "k": [33.41, -331.08], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 108, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0, -11.78], [-105.23, 0], [0, 11.78], [105.23, 0], [0, -11.78]], "i": [[0, 0], [0, -6.507], [-58.114, 0], [0, 6.504], [58.122, 0]], "o": [[-58.114, 0], [0, 6.504], [58.122, 0], [0, -6.507], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.644, 0.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [450, 793], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "c", "layers": [{"ddd": 0, "ind": 109, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "d", "w": 387, "h": 44, "ind": 110, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [407, 552], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 109}, {"ddd": 0, "ind": 111, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "e", "w": 331, "h": 44, "ind": 112, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [462, 503], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 111}, {"ddd": 0, "ind": 113, "ty": 3, "sr": 1, "ks": {"p": {"a": 0, "k": [439.75, 552], "ix": 2}, "a": {"a": 0, "k": [115, 65], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "f", "w": 125, "h": 108, "ind": 114, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [120, 14], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 113}, {"ddd": 0, "ind": 115, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [47, 47], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 1, "k": [{"t": 0, "s": [25], "i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}}, {"t": 100, "s": [11], "i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}}, {"t": 200, "s": [68], "i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}}, {"t": 300, "s": [60], "i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}}, {"t": 396, "s": [25]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [0.612, 0.844, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 47, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [390, 551], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 116, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [95, 95], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0.612, 0.844, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [390, 551], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "g", "w": 500, "h": 350, "ind": 117, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [566, 398], "ix": 2}, "a": {"a": 0, "k": [250, 175], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "h", "w": 230, "h": 130, "ind": 118, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [212.75, 552], "ix": 2}, "a": {"a": 0, "k": [115, 65], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 119, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[108.08, -46.9], [-108.08, -46.9], [-111.36, -43.61], [-111.36, 43.61], [-108.08, 46.9], [108.08, 46.9], [111.36, 43.61], [111.36, -43.61], [108.08, -46.9]], "i": [[0, 0], [0, 0], [0, -1.812], [0, 0], [-1.813, 0], [0, 0], [0, 1.811], [0, 0], [1.813, 0]], "o": [[0, 0], [-1.813, 0], [0, 0], [0, 1.811], [0, 0], [1.813, 0], [0, 0], [0, -1.812], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [212.75, 552], "ix": 2}, "o": {"a": 0, "k": 20, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "i", "w": 250, "h": 100, "ind": 120, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [210, 451], "ix": 2}, "a": {"a": 0, "k": [125, 50], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 121, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[108.08, -46.9], [-108.08, -46.9], [-111.36, -43.61], [-111.36, 43.61], [-108.08, 46.9], [108.08, 46.9], [111.36, 43.61], [111.36, -43.61], [108.08, -46.9]], "i": [[0, 0], [0, 0], [0, -1.813], [0, 0], [-1.813, 0], [0, 0], [0, 1.813], [0, 0], [1.813, 0]], "o": [[0, 0], [-1.813, 0], [0, 0], [0, 1.813], [0, 0], [1.813, 0], [0, 0], [0, -1.813], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [212.75, 449], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 122, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [6], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [6.19], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [6.75], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [7.63], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [8.81], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [10.26], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [11.95], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [13.84], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [15.91], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [18.11], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [20.43], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [22.83], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [25.27], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [27.73], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [30.17], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [32.57], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [34.88], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [37.09], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [39.16], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [41.05], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [42.74], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [44.19], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [45.37], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [46.26], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [46.81], "i": {"x": [0.833], "y": [1.096]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [47], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.045]}}, {"t": 104, "s": [46.59], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [45.73], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [44.64], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [43.42], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [42.11], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [40.73], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [39.3], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [37.83], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [36.34], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [34.83], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [33.3], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [31.77], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [30.23], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [28.7], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [27.17], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [25.66], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [24.17], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [22.7], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [21.27], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [19.89], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [18.58], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [17.36], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [16.27], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [15.41], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [15], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [15.41], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [16.27], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [17.36], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [18.58], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [19.89], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [21.27], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [22.7], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [24.17], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [25.66], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [27.17], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [28.7], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [30.23], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [31.77], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [33.3], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [34.83], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [36.34], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [37.83], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [39.3], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [40.73], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [42.11], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [43.42], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [44.64], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [45.73], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [46.59], "i": {"x": [0.833], "y": [0.955]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [47], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [-0.096]}}, {"t": 304, "s": [46.81], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [46.26], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [45.37], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [44.19], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [42.74], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [41.05], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [39.16], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [37.09], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [34.88], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [32.57], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [30.17], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [27.73], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [25.27], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [22.83], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [20.43], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [18.11], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [15.91], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [13.84], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [11.95], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [10.26], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [8.81], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [7.63], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [6.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [6.19]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [306, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 123, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [77], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [76.81], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [76.27], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [75.41], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [74.26], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [72.84], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [71.19], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [69.35], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [67.33], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [65.18], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [62.92], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [60.58], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [58.2], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [55.8], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [53.42], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [51.08], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [48.82], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [46.67], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [44.65], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [42.81], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [41.16], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [39.74], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [38.59], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [37.73], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [37.19], "i": {"x": [0.833], "y": [1.009]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [37], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.008]}}, {"t": 104, "s": [37.21], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [37.64], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [38.18], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [38.79], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [39.45], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [40.14], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [40.85], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [41.58], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [42.33], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [43.09], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [43.85], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [44.62], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [45.38], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [46.15], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [46.91], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [47.67], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [48.42], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [49.15], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [49.86], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [50.55], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [51.21], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [51.82], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [52.36], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [52.79], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [53], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [52.79], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [52.36], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [51.82], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [51.21], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [50.55], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [49.86], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [49.15], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [48.42], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [47.67], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [46.91], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [46.15], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [45.38], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [44.62], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [43.85], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [43.09], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [42.33], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [41.58], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [40.85], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [40.14], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [39.45], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [38.79], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [38.18], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [37.64], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [37.21], "i": {"x": [0.833], "y": [0.992]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [37], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [-0.009]}}, {"t": 304, "s": [37.19], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [37.73], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [38.59], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [39.74], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [41.16], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [42.81], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [44.65], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [46.67], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [48.82], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [51.08], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [53.42], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [55.8], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [58.2], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [60.58], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [62.92], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [65.18], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [67.33], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [69.35], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [71.19], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [72.84], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [74.26], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [75.41], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [76.27], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [76.81]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [285, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 124, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [4], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [4.24], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [4.93], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [6.03], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [7.5], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [9.3], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [11.4], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [13.76], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [16.32], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [19.07], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [21.95], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [24.93], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [27.97], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [31.03], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [34.07], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [37.05], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [39.93], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [42.67], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [45.24], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [47.6], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [49.7], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [51.5], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [52.97], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [54.07], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [54.76], "i": {"x": [0.833], "y": [1.16]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [55], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.055]}}, {"t": 104, "s": [54.3], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [52.85], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [51.02], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [48.96], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [46.74], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [44.41], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [42], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [39.53], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [37.01], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [34.46], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [31.88], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [29.3], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [26.7], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [24.12], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [21.54], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [18.99], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [16.47], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [14], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [11.59], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [9.26], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [7.04], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [4.98], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [3.15], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [1.7], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [1], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [1.7], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [3.15], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [4.98], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [7.04], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [9.26], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [11.59], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [14], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [16.47], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [18.99], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [21.54], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [24.12], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [26.7], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [29.3], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [31.88], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [34.46], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [37.01], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [39.53], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [42], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [44.41], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [46.74], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [48.96], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [51.02], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [52.85], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [54.3], "i": {"x": [0.833], "y": [0.945]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [55], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [-0.16]}}, {"t": 304, "s": [54.76], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [54.07], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [52.97], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [51.5], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [49.7], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [47.6], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [45.24], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [42.67], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [39.93], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [37.05], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [34.07], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [31.03], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [27.97], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [24.93], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [21.95], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [19.07], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [16.32], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [13.76], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [11.4], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [9.3], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [7.5], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [6.03], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [4.93], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [4.24]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [264.5, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 125, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [43], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [42.86], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [42.45], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [41.81], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [40.94], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [39.88], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [38.65], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [37.26], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [35.75], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [34.13], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [32.44], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [30.69], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [28.9], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [27.1], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [25.31], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [23.56], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [21.86], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [20.25], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [18.74], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [17.36], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [16.12], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [15.06], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [14.19], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [13.54], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [13.14], "i": {"x": [0.833], "y": [1.055]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [13], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.033]}}, {"t": 104, "s": [13.23], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [13.72], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [14.33], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [15.01], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [15.75], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [16.53], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [17.33], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [18.16], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [19], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [19.85], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [20.71], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [21.57], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [22.43], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [23.29], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [24.15], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [25], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [25.84], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [26.67], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [27.47], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [28.25], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [28.99], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [29.67], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [30.28], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [30.77], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [31], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [30.77], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [30.28], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [29.67], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [28.99], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [28.25], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [27.47], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [26.67], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [25.84], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [25], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [24.15], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [23.29], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [22.43], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [21.57], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [20.71], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [19.85], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [19], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [18.16], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [17.33], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [16.53], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [15.75], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [15.01], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [14.33], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [13.72], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [13.23], "i": {"x": [0.833], "y": [0.967]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [13], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [-0.055]}}, {"t": 304, "s": [13.14], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [13.54], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [14.19], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [15.06], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [16.12], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [17.36], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [18.74], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [20.25], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [21.86], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [23.56], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [25.31], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [27.1], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [28.9], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [30.69], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [32.44], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [34.13], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [35.75], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [37.26], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [38.65], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [39.88], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [40.94], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [41.81], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [42.45], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [42.86]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [244, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 126, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [13], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [13.1], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [13.4], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [13.87], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [14.51], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [15.29], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [16.19], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [17.21], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [18.32], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [19.5], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [20.74], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [22.03], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [23.34], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [24.66], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [25.97], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [27.26], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [28.5], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [29.68], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [30.79], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [31.81], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [32.71], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [33.49], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [34.13], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [34.6], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [34.9], "i": {"x": [0.833], "y": [0.467]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [35], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.099]}}, {"t": 104, "s": [35.55], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [36.71], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [38.17], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [39.81], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [41.58], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [43.43], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [45.35], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [47.32], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [49.33], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [51.36], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [53.41], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [55.47], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [57.53], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [59.59], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [61.64], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [63.67], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [65.68], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [67.65], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [69.57], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [71.42], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [73.19], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [74.83], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [76.29], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [77.44], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [78], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [77.44], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [76.29], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [74.83], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [73.19], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [71.42], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [69.57], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [67.65], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [65.68], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [63.67], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [61.64], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [59.59], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [57.53], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [55.47], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [53.41], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [51.36], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [49.33], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [47.32], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [45.35], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [43.43], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [41.58], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [39.81], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [38.17], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [36.71], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [35.55], "i": {"x": [0.833], "y": [0.901]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [35], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.533]}}, {"t": 304, "s": [34.9], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [34.6], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [34.13], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [33.49], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [32.71], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [31.81], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [30.79], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [29.68], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [28.5], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [27.26], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [25.97], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [24.66], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [23.34], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [22.03], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [20.74], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [19.5], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [18.32], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [17.21], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [16.19], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [15.29], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [14.51], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [13.87], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [13.4], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [13.1]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [224, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 127, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [61], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [60.82], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [60.31], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [59.49], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [58.39], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [57.05], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [55.48], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [53.73], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [51.82], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [49.77], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [47.62], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [45.4], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [43.14], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [40.86], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [38.6], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [36.38], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [34.23], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [32.18], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [30.27], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [28.52], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [26.95], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [25.61], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [24.51], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [23.69], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [23.18], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [23], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 104, "s": [22.79], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [22.36], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [21.82], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [21.21], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [20.55], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [19.86], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [19.15], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [18.42], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [17.67], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [16.91], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [16.15], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [15.38], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [14.62], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [13.85], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [13.09], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [12.33], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [11.58], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [10.85], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [10.14], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [9.45], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [8.79], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [8.18], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [7.64], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [7.21], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [7], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [7.21], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [7.64], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [8.18], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [8.79], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [9.45], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [10.14], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [10.85], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [11.58], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [12.33], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [13.09], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [13.85], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [14.62], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [15.38], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [16.15], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [16.91], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [17.67], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [18.42], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [19.15], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [19.86], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [20.55], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [21.21], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [21.82], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [22.36], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [22.79], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [23], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 304, "s": [23.18], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [23.69], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [24.51], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [25.61], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [26.95], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [28.52], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [30.27], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [32.18], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [34.23], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [36.38], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [38.6], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [40.86], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [43.14], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [45.4], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [47.62], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [49.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [51.82], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [53.73], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [55.48], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [57.05], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [58.39], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [59.49], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [60.31], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [60.82]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [203, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 128, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [22], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [22.09], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [22.34], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [22.75], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [23.3], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [23.98], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [24.76], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [25.64], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [26.59], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [27.61], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [28.69], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [29.8], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [30.93], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [32.07], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [33.2], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [34.31], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [35.39], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [36.41], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [37.37], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [38.24], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [39.02], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [39.7], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [40.24], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [40.66], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [40.91], "i": {"x": [0.833], "y": [0.674]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [41], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 104, "s": [41.26], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [41.8], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [42.48], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [43.24], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [44.06], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [44.92], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [45.81], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [46.73], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [47.66], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [48.61], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [49.56], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [50.52], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [51.48], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [52.44], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [53.39], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [54.34], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [55.27], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [56.19], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [57.08], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [57.94], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [58.76], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [59.52], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [60.2], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [60.74], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [61], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [60.74], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [60.2], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [59.52], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [58.76], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [57.94], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [57.08], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [56.19], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [55.27], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [54.34], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [53.39], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [52.44], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [51.48], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [50.52], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [49.56], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [48.61], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [47.66], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [46.73], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [45.81], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [44.92], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [44.06], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [43.24], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [42.48], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [41.8], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [41.26], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [41], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.326]}}, {"t": 304, "s": [40.91], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [40.66], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [40.24], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [39.7], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [39.02], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [38.24], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [37.37], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [36.41], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [35.39], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [34.31], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [33.2], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [32.07], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [30.93], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [29.8], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [28.69], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [27.61], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [26.59], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [25.64], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [24.76], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [23.98], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [23.3], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [22.75], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [22.34], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [22.09]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [182, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 129, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [45], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [44.88], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [44.53], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [43.97], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [43.22], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [42.3], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [41.23], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [40.03], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [38.72], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [37.32], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [35.85], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [34.33], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [32.78], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [31.22], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [29.67], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [28.15], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [26.68], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [25.28], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [23.97], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [22.77], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [21.7], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [20.78], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [20.03], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [19.47], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [19.12], "i": {"x": [0.833], "y": [0.926]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [19], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.701]}}, {"t": 104, "s": [19.01], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [19.04], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [19.07], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [19.11], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [19.15], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [19.2], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [19.24], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [19.29], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [19.33], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [19.38], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [19.43], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [19.48], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [19.52], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [19.57], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [19.62], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [19.67], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [19.71], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [19.76], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [19.8], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [19.85], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [19.89], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [19.93], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [19.96], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [19.99], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [20], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [19.99], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [19.96], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [19.93], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [19.89], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [19.85], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [19.8], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [19.76], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [19.71], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [19.67], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [19.62], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [19.57], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [19.52], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [19.48], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [19.43], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [19.38], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [19.33], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [19.29], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [19.24], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [19.2], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [19.15], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [19.11], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [19.07], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [19.04], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [19.01], "i": {"x": [0.833], "y": [1.701]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [19], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.074]}}, {"t": 304, "s": [19.12], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [19.47], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [20.03], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [20.78], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [21.7], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [22.77], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [23.97], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [25.28], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [26.68], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [28.15], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [29.67], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [31.22], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [32.78], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [34.33], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [35.85], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [37.32], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [38.72], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [40.03], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [41.23], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [42.3], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [43.22], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [43.97], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [44.53], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [44.88]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [161.5, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 130, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [18], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [18.16], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [18.64], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [19.39], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [20.4], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [21.64], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [23.08], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [24.7], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [26.46], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [28.34], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [30.32], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [32.37], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [34.45], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [36.55], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [38.63], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [40.68], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [42.66], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [44.54], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [46.3], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [47.92], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [49.36], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [50.6], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [51.61], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [52.36], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [52.84], "i": {"x": [0.833], "y": [0.996]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [53], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [-0.005]}}, {"t": 104, "s": [52.84], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 108, "s": [52.52], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 112, "s": [52.12], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 116, "s": [51.66], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 120, "s": [51.16], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 124, "s": [50.65], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 128, "s": [50.11], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 132, "s": [49.56], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 136, "s": [49], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 140, "s": [48.44], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 144, "s": [47.86], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 148, "s": [47.29], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 152, "s": [46.71], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 156, "s": [46.14], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 160, "s": [45.56], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 164, "s": [45], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 168, "s": [44.44], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 172, "s": [43.89], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 176, "s": [43.35], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 180, "s": [42.84], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 184, "s": [42.34], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 188, "s": [41.88], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 192, "s": [41.48], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 196, "s": [41.16], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 200, "s": [41], "i": {"x": [0.833], "y": [0.743]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [41.16], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.123]}}, {"t": 208, "s": [41.48], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 212, "s": [41.88], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 216, "s": [42.34], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 220, "s": [42.84], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 224, "s": [43.35], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 228, "s": [43.89], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 232, "s": [44.44], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 236, "s": [45], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 240, "s": [45.56], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 244, "s": [46.14], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 248, "s": [46.71], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 252, "s": [47.29], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 256, "s": [47.86], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 260, "s": [48.44], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 264, "s": [49], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 268, "s": [49.56], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 272, "s": [50.11], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 276, "s": [50.65], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 280, "s": [51.16], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 284, "s": [51.66], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 288, "s": [52.12], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 292, "s": [52.52], "i": {"x": [0.833], "y": [0.877]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 296, "s": [52.84], "i": {"x": [0.833], "y": [1.005]}, "o": {"x": [0.167], "y": [0.257]}}, {"t": 300, "s": [53], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.004]}}, {"t": 304, "s": [52.84], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [52.36], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [51.61], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [50.6], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [49.36], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [47.92], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [46.3], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [44.54], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [42.66], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [40.68], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [38.63], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [36.55], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [34.45], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [32.37], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [30.32], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [28.34], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [26.46], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [24.7], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [23.08], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [21.64], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [20.4], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [19.39], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [18.64], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [18.16]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [141.5, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 131, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-335, -141.5], [-335, -63]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 1, "k": [{"t": 0, "s": [31], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [30.86], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [30.44], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 12, "s": [29.77], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 16, "s": [28.87], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 20, "s": [27.78], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 24, "s": [26.5], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 28, "s": [25.07], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 32, "s": [23.51], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 36, "s": [21.84], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 40, "s": [20.09], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 44, "s": [18.28], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 48, "s": [16.43], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 52, "s": [14.57], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 56, "s": [12.72], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 60, "s": [10.91], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 64, "s": [9.16], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 68, "s": [7.49], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 72, "s": [5.93], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 76, "s": [4.5], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 80, "s": [3.22], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 84, "s": [2.13], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 88, "s": [1.23], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 92, "s": [0.56], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 96, "s": [0.14], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.324]}}, {"t": 100, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.012]}}, {"t": 104, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 108, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 112, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 116, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 120, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 124, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 128, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 132, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 136, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 140, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 144, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 148, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 152, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 156, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 160, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 208, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 212, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 216, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 220, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 224, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 228, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 232, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 236, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 240, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 244, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 248, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 252, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 256, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 260, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 264, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 268, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [0], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [0], "i": {"x": [0.833], "y": [0.988]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [0], "i": {"x": [0.833], "y": [0.676]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 304, "s": [0.14], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 308, "s": [0.56], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}}, {"t": 312, "s": [1.23], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 316, "s": [2.13], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}}, {"t": 320, "s": [3.22], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 324, "s": [4.5], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}}, {"t": 328, "s": [5.93], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 332, "s": [7.49], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 336, "s": [9.16], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 340, "s": [10.91], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 344, "s": [12.72], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 348, "s": [14.57], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 352, "s": [16.43], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 356, "s": [18.28], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 360, "s": [20.09], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 364, "s": [21.84], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 368, "s": [23.51], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 372, "s": [25.07], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 376, "s": [26.5], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 380, "s": [27.78], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 384, "s": [28.87], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}}, {"t": 388, "s": [29.77], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}}, {"t": 392, "s": [30.44], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.216]}}, {"t": 396, "s": [30.86]}], "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 15, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [120.5, 347.75], "ix": 2}, "a": {"a": 0, "k": [-335, -102.25], "ix": 2}, "o": {"a": 0, "k": 30, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 132, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[108.08, -46.9], [-108.08, -46.9], [-111.36, -43.61], [-111.36, 43.61], [-108.08, 46.9], [108.08, 46.9], [111.36, 43.61], [111.36, -43.61], [108.08, -46.9]], "i": [[0, 0], [0, 0], [0, -1.813], [0, 0], [-1.812, 0], [0, 0], [0, 1.813], [0, 0], [1.812, 0]], "o": [[0, 0], [-1.812, 0], [0, 0], [0, 1.813], [0, 0], [1.812, 0], [0, 0], [0, -1.813], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.38, 0.96, 0.875], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [213, 348], "ix": 2}, "o": {"a": 0, "k": 20, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 133, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[359.15, -156.69], [-359.15, -156.69], [-359.15, 156.69], [359.15, 156.69], [359.15, -156.69]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.38, 0.96, 0.875], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [450, 450], "ix": 2}, "o": {"a": 0, "k": 20, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 134, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0, 16.53], [0, 16.53], [-16.53, 0], [-16.53, 0], [0, -16.53], [0, -16.53], [16.53, 0], [16.53, 0], [0, 16.53]], "i": [[0, 0], [0, 0], [0, 9.091], [0, 0], [-9.091, 0], [0, 0], [0, -9.091], [0, 0], [9.091, 0]], "o": [[0, 0], [-9.091, 0], [0, 0], [0, -9.091], [0, 0], [9.091, 0], [0, 0], [0, 9.091], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.232, 0.36, 0.82], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [450, 644], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 135, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[129, -84.94], [-129, -84.94], [-135.74, -78.19], [-135.74, 78.2], [-129, 84.94], [129, 84.94], [135.74, 78.2], [135.74, -78.19], [129, -84.94]], "i": [[0, 0], [0, 0], [0, -3.72], [0, 0], [-3.73, 0], [0, 0], [0, 3.72], [0, 0], [3.72, 0]], "o": [[0, 0], [-3.73, 0], [0, 0], [0, 3.72], [0, 0], [3.72, 0], [0, 0], [0, -3.72], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.291, 0.39, 0.997], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [450, 450], "ix": 2}, "s": {"a": 0, "k": [264, 264], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 136, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[358.48, -206.51], [358.48, 206.51], [340.68, 224.31], [-340.68, 224.31], [-358.48, 206.51], [-358.48, -206.51], [-340.68, -224.31], [340.68, -224.31], [358.48, -206.51]], "i": [[0, 0], [0, 0], [9.824, 0], [0, 0], [0, 9.824], [0, 0], [-9.851, 0], [0, 0], [0, -9.824]], "o": [[0, 0], [0, 9.824], [0, 0], [-9.851, 0], [0, 0], [0, -9.824], [0, 0], [9.824, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.644, 0.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [450, 450], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 137, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[72.94, 51.89], [-72.94, 51.89], [-57.1, -10.4], [-53.51, -24.53], [-46.53, -51.89], [46.53, -51.89], [53.51, -24.53], [57.1, -10.4], [72.94, 51.89]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.291, 0.39, 0.997], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [450, 681], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 138, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[128.93, 11.2], [-128.93, 11.2], [-140.13, 0], [-140.13, 0], [-128.93, -11.2], [128.93, -11.2], [140.13, 0], [140.13, 0], [128.93, 11.2]], "i": [[0, 0], [0, 0], [0, 6.187], [0, 0], [-6.187, 0], [0, 0], [0, -6.187], [0, 0], [6.187, 0]], "o": [[0, 0], [-6.187, 0], [0, 0], [0, -6.187], [0, 0], [6.187, 0], [0, 0], [0, 6.187], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.26, 0.365, 0.89], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [450, 741], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "j", "layers": [{"ddd": 0, "ind": 139, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-407, -552], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[117.18, -21.23], [-117.19, -21.23], [-119.98, -18.44], [-119.98, 18.44], [-117.19, 21.23], [117.18, 21.23], [119.98, 18.44], [119.98, -18.44], [117.18, -21.23]], "i": [[0, 0], [0, 0], [0, -1.542], [0, 0], [-1.541, 0], [0, 0], [0, 1.541], [0, 0], [1.541, 0]], "o": [[0, 0], [-1.541, 0], [0, 0], [0, 1.541], [0, 0], [1.541, 0], [0, 0], [0, -1.542], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.46, 0.675, 0.99], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [673, 574], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 140, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-407, -552], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [387, 44], "ix": 2}, "p": {"a": 0, "k": [600.5, 574], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "d", "layers": [{"ddd": 0, "refId": "k", "w": 387, "h": 44, "ind": 110, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [600.5, 574], "ix": 2}, "a": {"a": 0, "k": [600.5, 574], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "td": 1}, {"ddd": 0, "refId": "j", "w": 387, "h": 44, "ind": 110, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [600.5, 574], "ix": 2}, "a": {"a": 0, "k": [600.5, 574], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "k", "layers": [{"ddd": 0, "ind": 141, "ty": 3, "sr": 1, "ks": {"p": {"a": 0, "k": [147.75, 22], "ix": 2}, "a": {"a": 0, "k": [-118.25, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "l", "w": 387, "h": 44, "ind": 142, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [-266, -22], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 141}]}, {"id": "l", "layers": [{"ddd": 0, "ind": 143, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [266, 22], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[117.18, -21.23], [-117.19, -21.23], [-119.98, -18.44], [-119.98, 18.44], [-117.19, 21.23], [117.18, 21.23], [119.98, 18.44], [119.98, -18.44], [117.18, -21.23]], "i": [[0, 0], [0, 0], [0, -1.542], [0, 0], [-1.541, 0], [0, 0], [0, 1.541], [0, 0], [1.541, 0]], "o": [[0, 0], [-1.541, 0], [0, 0], [0, 1.541], [0, 0], [1.541, 0], [0, 0], [0, -1.542], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.46, 0.675, 0.99], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 0, "s": [-267, 0], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [-0.442, 0], "to": [0.112, 0]}, {"t": 4, "s": [-266.33, 0], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}, "ti": [-0.856, 0], "to": [0.442, 0]}, {"t": 8, "s": [-264.35, 0], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.136]}, "ti": [-1.229, 0], "to": [0.856, 0]}, {"t": 12, "s": [-261.19, 0], "i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.146]}, "ti": [-1.562, 0], "to": [1.229, 0]}, {"t": 16, "s": [-256.97, 0], "i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.151]}, "ti": [-1.862, 0], "to": [1.562, 0]}, {"t": 20, "s": [-251.81, 0], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}, "ti": [-2.126, 0], "to": [1.862, 0]}, {"t": 24, "s": [-245.8, 0], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "ti": [-2.348, 0], "to": [2.126, 0]}, {"t": 28, "s": [-239.06, 0], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "ti": [-2.534, 0], "to": [2.348, 0]}, {"t": 32, "s": [-231.72, 0], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}, "ti": [-2.685, 0], "to": [2.534, 0]}, {"t": 36, "s": [-223.86, 0], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "ti": [-2.797, 0], "to": [2.685, 0]}, {"t": 40, "s": [-215.61, 0], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "ti": [-2.871, 0], "to": [2.797, 0]}, {"t": 44, "s": [-207.08, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "ti": [-2.909, 0], "to": [2.871, 0]}, {"t": 48, "s": [-198.38, 0], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}, "ti": [-2.909, 0], "to": [2.909, 0]}, {"t": 52, "s": [-189.62, 0], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [-2.871, 0], "to": [2.909, 0]}, {"t": 56, "s": [-180.92, 0], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}, "ti": [-2.797, 0], "to": [2.871, 0]}, {"t": 60, "s": [-172.39, 0], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}, "ti": [-2.685, 0], "to": [2.797, 0]}, {"t": 64, "s": [-164.14, 0], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}, "ti": [-2.535, 0], "to": [2.685, 0]}, {"t": 68, "s": [-156.28, 0], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "ti": [-2.348, 0], "to": [2.535, 0]}, {"t": 72, "s": [-148.93, 0], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "ti": [-2.124, 0], "to": [2.348, 0]}, {"t": 76, "s": [-142.19, 0], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}, "ti": [-1.863, 0], "to": [2.124, 0]}, {"t": 80, "s": [-136.19, 0], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}, "ti": [-1.564, 0], "to": [1.863, 0]}, {"t": 84, "s": [-131.02, 0], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}, "ti": [-1.227, 0], "to": [1.564, 0]}, {"t": 88, "s": [-126.8, 0], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}, "ti": [-0.853, 0], "to": [1.227, 0]}, {"t": 92, "s": [-123.65, 0], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}, "ti": [-0.442, 0], "to": [0.853, 0]}, {"t": 96, "s": [-121.68, 0], "i": {"x": [0.833], "y": [0.861]}, "o": {"x": [0.167], "y": [0.324]}, "ti": [-0.186, 0], "to": [0.442, 0]}, {"t": 100, "s": [-121, 0], "i": {"x": [0.833], "y": [0.755]}, "o": {"x": [0.167], "y": [0.207]}, "ti": [0.076, 0], "to": [0.186, 0]}, {"t": 104, "s": [-120.56, 0], "i": {"x": [0.833], "y": [0.728]}, "o": {"x": [0.167], "y": [0.126]}, "ti": [0.484, 0], "to": [-0.076, 0]}, {"t": 108, "s": [-121.45, 0], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.12]}, "ti": [0.785, 0], "to": [-0.484, 0]}, {"t": 112, "s": [-123.47, 0], "i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.145]}, "ti": [0.999, 0], "to": [-0.785, 0]}, {"t": 116, "s": [-126.17, 0], "i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.152]}, "ti": [1.189, 0], "to": [-0.999, 0]}, {"t": 120, "s": [-129.46, 0], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}, "ti": [1.358, 0], "to": [-1.189, 0]}, {"t": 124, "s": [-133.3, 0], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "ti": [1.499, 0], "to": [-1.358, 0]}, {"t": 128, "s": [-137.61, 0], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "ti": [1.618, 0], "to": [-1.499, 0]}, {"t": 132, "s": [-142.29, 0], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}, "ti": [1.715, 0], "to": [-1.618, 0]}, {"t": 136, "s": [-147.31, 0], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "ti": [1.786, 0], "to": [-1.715, 0]}, {"t": 140, "s": [-152.58, 0], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "ti": [1.833, 0], "to": [-1.786, 0]}, {"t": 144, "s": [-158.03, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "ti": [1.857, 0], "to": [-1.833, 0]}, {"t": 148, "s": [-163.58, 0], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}, "ti": [1.857, 0], "to": [-1.857, 0]}, {"t": 152, "s": [-169.17, 0], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [1.834, 0], "to": [-1.857, 0]}, {"t": 156, "s": [-174.73, 0], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}, "ti": [1.787, 0], "to": [-1.834, 0]}, {"t": 160, "s": [-180.18, 0], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "ti": [1.714, 0], "to": [-1.787, 0]}, {"t": 164, "s": [-185.45, 0], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}, "ti": [1.619, 0], "to": [-1.714, 0]}, {"t": 168, "s": [-190.47, 0], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "ti": [1.5, 0], "to": [-1.619, 0]}, {"t": 172, "s": [-195.16, 0], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "ti": [1.357, 0], "to": [-1.5, 0]}, {"t": 176, "s": [-199.46, 0], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}, "ti": [1.19, 0], "to": [-1.357, 0]}, {"t": 180, "s": [-203.3, 0], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.18]}, "ti": [0.999, 0], "to": [-1.19, 0]}, {"t": 184, "s": [-206.6, 0], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.185]}, "ti": [0.784, 0], "to": [-0.999, 0]}, {"t": 188, "s": [-209.29, 0], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.195]}, "ti": [0.546, 0], "to": [-0.784, 0]}, {"t": 192, "s": [-211.31, 0], "i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.215]}, "ti": [0.283, 0], "to": [-0.546, 0]}, {"t": 196, "s": [-212.57, 0], "i": {"x": [0.833], "y": [0.878]}, "o": {"x": [0.167], "y": [0.331]}, "ti": [0.104, 0], "to": [-0.283, 0]}, {"t": 200, "s": [-213, 0], "i": {"x": [0.833], "y": [0.687]}, "o": {"x": [0.167], "y": [0.261]}, "ti": [0.125, 0], "to": [-0.104, 0]}, {"t": 204, "s": [-213.2, 0], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.114]}, "ti": [0.038, 0], "to": [-0.125, 0]}, {"t": 208, "s": [-213.75, 0], "i": {"x": [0.833], "y": [0.624]}, "o": {"x": [0.167], "y": [0.219]}, "ti": [-0.251, 0], "to": [-0.038, 0]}, {"t": 212, "s": [-213.43, 0], "i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.107]}, "ti": [-0.439, 0], "to": [0.251, 0]}, {"t": 216, "s": [-212.24, 0], "i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.151]}, "ti": [-0.524, 0], "to": [0.439, 0]}, {"t": 220, "s": [-210.79, 0], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.155]}, "ti": [-0.597, 0], "to": [0.524, 0]}, {"t": 224, "s": [-209.1, 0], "i": {"x": [0.833], "y": [0.825]}, "o": {"x": [0.167], "y": [0.158]}, "ti": [-0.66, 0], "to": [0.597, 0]}, {"t": 228, "s": [-207.21, 0], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.159]}, "ti": [-0.713, 0], "to": [0.66, 0]}, {"t": 232, "s": [-205.14, 0], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}, "ti": [-0.755, 0], "to": [0.713, 0]}, {"t": 236, "s": [-202.93, 0], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "ti": [-0.787, 0], "to": [0.755, 0]}, {"t": 240, "s": [-200.61, 0], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "ti": [-0.808, 0], "to": [0.787, 0]}, {"t": 244, "s": [-198.21, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "ti": [-0.817, 0], "to": [0.808, 0]}, {"t": 248, "s": [-195.76, 0], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}, "ti": [-0.817, 0], "to": [0.817, 0]}, {"t": 252, "s": [-193.3, 0], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [-0.807, 0], "to": [0.817, 0]}, {"t": 256, "s": [-190.86, 0], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}, "ti": [-0.786, 0], "to": [0.807, 0]}, {"t": 260, "s": [-188.46, 0], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "ti": [-0.755, 0], "to": [0.786, 0]}, {"t": 264, "s": [-186.14, 0], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}, "ti": [-0.713, 0], "to": [0.755, 0]}, {"t": 268, "s": [-183.93, 0], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "ti": [-0.66, 0], "to": [0.713, 0]}, {"t": 272, "s": [-181.86, 0], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "ti": [-0.597, 0], "to": [0.66, 0]}, {"t": 276, "s": [-179.97, 0], "i": {"x": [0.833], "y": [0.846]}, "o": {"x": [0.167], "y": [0.176]}, "ti": [-0.523, 0], "to": [0.597, 0]}, {"t": 280, "s": [-178.28, 0], "i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.181]}, "ti": [-0.439, 0], "to": [0.523, 0]}, {"t": 284, "s": [-176.84, 0], "i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.184]}, "ti": [-0.264, 0], "to": [0.439, 0]}, {"t": 288, "s": [-175.65, 0], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.331]}, "ti": [0.026, 0], "to": [0.264, 0]}, {"t": 292, "s": [-175.25, 0], "i": {"x": [0.833], "y": [0.886]}, "o": {"x": [0.167], "y": [0.144]}, "ti": [0.125, 0], "to": [-0.026, 0]}, {"t": 296, "s": [-175.8, 0], "i": {"x": [0.833], "y": [0.728]}, "o": {"x": [0.167], "y": [0.313]}, "ti": [0.109, 0], "to": [-0.125, 0]}, {"t": 300, "s": [-176, 0], "i": {"x": [0.833], "y": [0.67]}, "o": {"x": [0.167], "y": [0.12]}, "ti": [0.299, 0], "to": [-0.109, 0]}, {"t": 304, "s": [-176.45, 0], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}, "ti": [0.577, 0], "to": [-0.299, 0]}, {"t": 308, "s": [-177.79, 0], "i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.136]}, "ti": [0.825, 0], "to": [-0.577, 0]}, {"t": 312, "s": [-179.92, 0], "i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.146]}, "ti": [1.048, 0], "to": [-0.825, 0]}, {"t": 316, "s": [-182.74, 0], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}, "ti": [1.246, 0], "to": [-1.048, 0]}, {"t": 320, "s": [-186.2, 0], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}, "ti": [1.417, 0], "to": [-1.246, 0]}, {"t": 324, "s": [-190.22, 0], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "ti": [1.562, 0], "to": [-1.417, 0]}, {"t": 328, "s": [-194.71, 0], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}, "ti": [1.681, 0], "to": [-1.562, 0]}, {"t": 332, "s": [-199.59, 0], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}, "ti": [1.775, 0], "to": [-1.681, 0]}, {"t": 336, "s": [-204.8, 0], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "ti": [1.84, 0], "to": [-1.775, 0]}, {"t": 340, "s": [-210.24, 0], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "ti": [1.877, 0], "to": [-1.84, 0]}, {"t": 344, "s": [-215.84, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "ti": [1.888, 0], "to": [-1.877, 0]}, {"t": 348, "s": [-221.5, 0], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [1.876, 0], "to": [-1.888, 0]}, {"t": 352, "s": [-227.16, 0], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}, "ti": [1.839, 0], "to": [-1.876, 0]}, {"t": 356, "s": [-232.76, 0], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}, "ti": [1.775, 0], "to": [-1.839, 0]}, {"t": 360, "s": [-238.2, 0], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.17]}, "ti": [1.681, 0], "to": [-1.775, 0]}, {"t": 364, "s": [-243.41, 0], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "ti": [1.561, 0], "to": [-1.681, 0]}, {"t": 368, "s": [-248.29, 0], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "ti": [1.418, 0], "to": [-1.561, 0]}, {"t": 372, "s": [-252.78, 0], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.176]}, "ti": [1.247, 0], "to": [-1.418, 0]}, {"t": 376, "s": [-256.8, 0], "i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.18]}, "ti": [1.048, 0], "to": [-1.247, 0]}, {"t": 380, "s": [-260.25, 0], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.185]}, "ti": [0.826, 0], "to": [-1.048, 0]}, {"t": 384, "s": [-263.09, 0], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.194]}, "ti": [0.577, 0], "to": [-0.826, 0]}, {"t": 388, "s": [-265.21, 0], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}, "ti": [0.298, 0], "to": [-0.577, 0]}, {"t": 392, "s": [-266.55, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.329]}, "ti": [0.076, 0], "to": [-0.298, 0]}, {"t": 396, "s": [-267, 0]}], "ix": 2}, "a": {"a": 0, "k": [-121, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "m", "layers": [{"ddd": 0, "ind": 144, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-462, -503], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[117.18, -21.23], [-117.19, -21.23], [-119.98, -18.44], [-119.98, 18.44], [-117.19, 21.23], [117.18, 21.23], [119.98, 18.44], [119.98, -18.44], [117.18, -21.23]], "i": [[0, 0], [0, 0], [0, -1.542], [0, 0], [-1.541, 0], [0, 0], [0, 1.541], [0, 0], [1.541, 0]], "o": [[0, 0], [-1.541, 0], [0, 0], [0, 1.541], [0, 0], [1.541, 0], [0, 0], [0, -1.542], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.612, 0.844, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [673, 525], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 145, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-462, -503], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [331, 44], "ix": 2}, "p": {"a": 0, "k": [627.5, 525], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "e", "layers": [{"ddd": 0, "refId": "n", "w": 331, "h": 44, "ind": 112, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [627.5, 525], "ix": 2}, "a": {"a": 0, "k": [627.5, 525], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "td": 1}, {"ddd": 0, "refId": "m", "w": 331, "h": 44, "ind": 112, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [627.5, 525], "ix": 2}, "a": {"a": 0, "k": [627.5, 525], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "n", "layers": [{"ddd": 0, "ind": 146, "ty": 3, "sr": 1, "ks": {"p": {"a": 0, "k": [211, 22], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "o", "w": 331, "h": 44, "ind": 147, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [-211, -22], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 146}]}, {"id": "o", "layers": [{"ddd": 0, "ind": 148, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [211, 22], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[117.18, -21.23], [-117.19, -21.23], [-119.98, -18.44], [-119.98, 18.44], [-117.19, 21.23], [117.18, 21.23], [119.98, 18.44], [119.98, -18.44], [117.18, -21.23]], "i": [[0, 0], [0, 0], [0, -1.542], [0, 0], [-1.541, 0], [0, 0], [0, 1.541], [0, 0], [1.541, 0]], "o": [[0, 0], [-1.541, 0], [0, 0], [0, 1.541], [0, 0], [1.541, 0], [0, 0], [0, -1.542], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.612, 0.844, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 1, "k": [{"t": 0, "s": [0, 0], "i": {"x": [0.833], "y": [0.673]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [0.138, 0], "to": [-0.035, 0]}, {"t": 4, "s": [-0.21, 0], "i": {"x": [0.833], "y": [0.782]}, "o": {"x": [0.167], "y": [0.112]}, "ti": [0.268, 0], "to": [-0.138, 0]}, {"t": 8, "s": [-0.83, 0], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.135]}, "ti": [0.388, 0], "to": [-0.268, 0]}, {"t": 12, "s": [-1.82, 0], "i": {"x": [0.833], "y": [0.815]}, "o": {"x": [0.167], "y": [0.146]}, "ti": [0.494, 0], "to": [-0.388, 0]}, {"t": 16, "s": [-3.15, 0], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.152]}, "ti": [0.587, 0], "to": [-0.494, 0]}, {"t": 20, "s": [-4.78, 0], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}, "ti": [0.67, 0], "to": [-0.587, 0]}, {"t": 24, "s": [-6.68, 0], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "ti": [0.74, 0], "to": [-0.67, 0]}, {"t": 28, "s": [-8.8, 0], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "ti": [0.799, 0], "to": [-0.74, 0]}, {"t": 32, "s": [-11.12, 0], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}, "ti": [0.847, 0], "to": [-0.799, 0]}, {"t": 36, "s": [-13.6, 0], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "ti": [0.882, 0], "to": [-0.847, 0]}, {"t": 40, "s": [-16.2, 0], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "ti": [0.904, 0], "to": [-0.882, 0]}, {"t": 44, "s": [-18.89, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "ti": [0.915, 0], "to": [-0.904, 0]}, {"t": 48, "s": [-21.62, 0], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}, "ti": [0.915, 0], "to": [-0.915, 0]}, {"t": 52, "s": [-24.38, 0], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [0.905, 0], "to": [-0.915, 0]}, {"t": 56, "s": [-27.11, 0], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}, "ti": [0.882, 0], "to": [-0.905, 0]}, {"t": 60, "s": [-29.8, 0], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.169]}, "ti": [0.846, 0], "to": [-0.882, 0]}, {"t": 64, "s": [-32.41, 0], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}, "ti": [0.797, 0], "to": [-0.846, 0]}, {"t": 68, "s": [-34.88, 0], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}, "ti": [0.739, 0], "to": [-0.797, 0]}, {"t": 72, "s": [-37.19, 0], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "ti": [0.671, 0], "to": [-0.739, 0]}, {"t": 76, "s": [-39.31, 0], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.176]}, "ti": [0.588, 0], "to": [-0.671, 0]}, {"t": 80, "s": [-41.22, 0], "i": {"x": [0.833], "y": [0.849]}, "o": {"x": [0.167], "y": [0.181]}, "ti": [0.492, 0], "to": [-0.588, 0]}, {"t": 84, "s": [-42.84, 0], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.186]}, "ti": [0.387, 0], "to": [-0.492, 0]}, {"t": 88, "s": [-44.17, 0], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.194]}, "ti": [0.27, 0], "to": [-0.387, 0]}, {"t": 92, "s": [-45.16, 0], "i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.216]}, "ti": [0.139, 0], "to": [-0.27, 0]}, {"t": 96, "s": [-45.79, 0], "i": {"x": [0.833], "y": [0.869]}, "o": {"x": [0.167], "y": [0.331]}, "ti": [0.055, 0], "to": [-0.139, 0]}, {"t": 100, "s": [-46, 0], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.229]}, "ti": [0.036, 0], "to": [-0.055, 0]}, {"t": 104, "s": [-46.12, 0], "i": {"x": [0.833], "y": [0.599]}, "o": {"x": [0.167], "y": [0.155]}, "ti": [-0.073, 0], "to": [-0.036, 0]}, {"t": 108, "s": [-46.22, 0], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.105]}, "ti": [-0.209, 0], "to": [0.073, 0]}, {"t": 112, "s": [-45.68, 0], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}, "ti": [-0.266, 0], "to": [0.209, 0]}, {"t": 116, "s": [-44.97, 0], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.151]}, "ti": [-0.316, 0], "to": [0.266, 0]}, {"t": 120, "s": [-44.09, 0], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.156]}, "ti": [-0.36, 0], "to": [0.316, 0]}, {"t": 124, "s": [-43.07, 0], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.157]}, "ti": [-0.399, 0], "to": [0.36, 0]}, {"t": 128, "s": [-41.93, 0], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}, "ti": [-0.43, 0], "to": [0.399, 0]}, {"t": 132, "s": [-40.68, 0], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}, "ti": [-0.455, 0], "to": [0.43, 0]}, {"t": 136, "s": [-39.34, 0], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}, "ti": [-0.475, 0], "to": [0.455, 0]}, {"t": 140, "s": [-37.94, 0], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "ti": [-0.488, 0], "to": [0.475, 0]}, {"t": 144, "s": [-36.49, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "ti": [-0.494, 0], "to": [0.488, 0]}, {"t": 148, "s": [-35.02, 0], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}, "ti": [-0.494, 0], "to": [0.494, 0]}, {"t": 152, "s": [-33.53, 0], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [-0.487, 0], "to": [0.494, 0]}, {"t": 156, "s": [-32.06, 0], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}, "ti": [-0.473, 0], "to": [0.487, 0]}, {"t": 160, "s": [-30.61, 0], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}, "ti": [-0.454, 0], "to": [0.473, 0]}, {"t": 164, "s": [-29.22, 0], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.17]}, "ti": [-0.432, 0], "to": [0.454, 0]}, {"t": 168, "s": [-27.88, 0], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.172]}, "ti": [-0.4, 0], "to": [0.432, 0]}, {"t": 172, "s": [-26.63, 0], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.175]}, "ti": [-0.361, 0], "to": [0.4, 0]}, {"t": 176, "s": [-25.48, 0], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}, "ti": [-0.316, 0], "to": [0.361, 0]}, {"t": 180, "s": [-24.46, 0], "i": {"x": [0.833], "y": [0.862]}, "o": {"x": [0.167], "y": [0.18]}, "ti": [-0.241, 0], "to": [0.316, 0]}, {"t": 184, "s": [-23.59, 0], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.211]}, "ti": [-0.007, 0], "to": [0.241, 0]}, {"t": 188, "s": [-23.02, 0], "i": {"x": [0.833], "y": [0.863]}, "o": {"x": [0.167], "y": [0.174]}, "ti": [0.144, 0], "to": [0.007, 0]}, {"t": 192, "s": [-23.54, 0], "i": {"x": [0.833], "y": [0.887]}, "o": {"x": [0.167], "y": [0.214]}, "ti": [0.076, 0], "to": [-0.144, 0]}, {"t": 196, "s": [-23.88, 0], "i": {"x": [0.833], "y": [0.706]}, "o": {"x": [0.167], "y": [0.318]}, "ti": [0.071, 0], "to": [-0.076, 0]}, {"t": 200, "s": [-24, 0], "i": {"x": [0.833], "y": [0.669]}, "o": {"x": [0.167], "y": [0.116]}, "ti": [0.201, 0], "to": [-0.071, 0]}, {"t": 204, "s": [-24.3, 0], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.111]}, "ti": [0.39, 0], "to": [-0.201, 0]}, {"t": 208, "s": [-25.21, 0], "i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.136]}, "ti": [0.558, 0], "to": [-0.39, 0]}, {"t": 212, "s": [-26.64, 0], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}, "ti": [0.71, 0], "to": [-0.558, 0]}, {"t": 216, "s": [-28.55, 0], "i": {"x": [0.833], "y": [0.819]}, "o": {"x": [0.167], "y": [0.151]}, "ti": [0.849, 0], "to": [-0.71, 0]}, {"t": 220, "s": [-30.91, 0], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.155]}, "ti": [0.967, 0], "to": [-0.849, 0]}, {"t": 224, "s": [-33.65, 0], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "ti": [1.065, 0], "to": [-0.967, 0]}, {"t": 228, "s": [-36.71, 0], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}, "ti": [1.151, 0], "to": [-1.065, 0]}, {"t": 232, "s": [-40.04, 0], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.161]}, "ti": [1.221, 0], "to": [-1.151, 0]}, {"t": 236, "s": [-43.61, 0], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "ti": [1.271, 0], "to": [-1.221, 0]}, {"t": 240, "s": [-47.36, 0], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "ti": [1.305, 0], "to": [-1.271, 0]}, {"t": 244, "s": [-51.24, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.165]}, "ti": [1.321, 0], "to": [-1.305, 0]}, {"t": 248, "s": [-55.19, 0], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.166]}, "ti": [1.321, 0], "to": [-1.321, 0]}, {"t": 252, "s": [-59.16, 0], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [1.306, 0], "to": [-1.321, 0]}, {"t": 256, "s": [-63.12, 0], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}, "ti": [1.273, 0], "to": [-1.306, 0]}, {"t": 260, "s": [-67, 0], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}, "ti": [1.221, 0], "to": [-1.273, 0]}, {"t": 264, "s": [-70.76, 0], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.171]}, "ti": [1.153, 0], "to": [-1.221, 0]}, {"t": 268, "s": [-74.33, 0], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "ti": [1.067, 0], "to": [-1.153, 0]}, {"t": 272, "s": [-77.67, 0], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "ti": [0.966, 0], "to": [-1.067, 0]}, {"t": 276, "s": [-80.73, 0], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}, "ti": [0.845, 0], "to": [-0.966, 0]}, {"t": 280, "s": [-83.46, 0], "i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.181]}, "ti": [0.709, 0], "to": [-0.845, 0]}, {"t": 284, "s": [-85.8, 0], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.185]}, "ti": [0.559, 0], "to": [-0.709, 0]}, {"t": 288, "s": [-87.72, 0], "i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.194]}, "ti": [0.389, 0], "to": [-0.559, 0]}, {"t": 292, "s": [-89.16, 0], "i": {"x": [0.833], "y": [0.899]}, "o": {"x": [0.167], "y": [0.217]}, "ti": [0.14, 0], "to": [-0.389, 0]}, {"t": 296, "s": [-90.06, 0], "i": {"x": [0.833], "y": [0.718]}, "o": {"x": [0.167], "y": [0.478]}, "ti": [-0.085, 0], "to": [-0.14, 0]}, {"t": 300, "s": [-90, 0], "i": {"x": [0.833], "y": [0.672]}, "o": {"x": [0.167], "y": [0.118]}, "ti": [-0.296, 0], "to": [0.085, 0]}, {"t": 304, "s": [-89.55, 0], "i": {"x": [0.833], "y": [0.784]}, "o": {"x": [0.167], "y": [0.112]}, "ti": [-0.57, 0], "to": [0.296, 0]}, {"t": 308, "s": [-88.23, 0], "i": {"x": [0.833], "y": [0.806]}, "o": {"x": [0.167], "y": [0.136]}, "ti": [-0.815, 0], "to": [0.57, 0]}, {"t": 312, "s": [-86.13, 0], "i": {"x": [0.833], "y": [0.814]}, "o": {"x": [0.167], "y": [0.146]}, "ti": [-1.037, 0], "to": [0.815, 0]}, {"t": 316, "s": [-83.33, 0], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.151]}, "ti": [-1.233, 0], "to": [1.037, 0]}, {"t": 320, "s": [-79.91, 0], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}, "ti": [-1.402, 0], "to": [1.233, 0]}, {"t": 324, "s": [-75.94, 0], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.158]}, "ti": [-1.545, 0], "to": [1.402, 0]}, {"t": 328, "s": [-71.5, 0], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.16]}, "ti": [-1.663, 0], "to": [1.545, 0]}, {"t": 332, "s": [-66.67, 0], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}, "ti": [-1.756, 0], "to": [1.663, 0]}, {"t": 336, "s": [-61.52, 0], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.163]}, "ti": [-1.82, 0], "to": [1.756, 0]}, {"t": 340, "s": [-56.13, 0], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.164]}, "ti": [-1.856, 0], "to": [1.82, 0]}, {"t": 344, "s": [-50.6, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}, "ti": [-1.867, 0], "to": [1.856, 0]}, {"t": 348, "s": [-44.99, 0], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}, "ti": [-1.855, 0], "to": [1.867, 0]}, {"t": 352, "s": [-39.4, 0], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.168]}, "ti": [-1.819, 0], "to": [1.855, 0]}, {"t": 356, "s": [-33.87, 0], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.169]}, "ti": [-1.756, 0], "to": [1.819, 0]}, {"t": 360, "s": [-28.48, 0], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.17]}, "ti": [-1.663, 0], "to": [1.756, 0]}, {"t": 364, "s": [-23.33, 0], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.172]}, "ti": [-1.544, 0], "to": [1.663, 0]}, {"t": 368, "s": [-18.51, 0], "i": {"x": [0.833], "y": [0.842]}, "o": {"x": [0.167], "y": [0.174]}, "ti": [-1.403, 0], "to": [1.544, 0]}, {"t": 372, "s": [-14.07, 0], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.176]}, "ti": [-1.233, 0], "to": [1.403, 0]}, {"t": 376, "s": [-10.09, 0], "i": {"x": [0.833], "y": [0.848]}, "o": {"x": [0.167], "y": [0.18]}, "ti": [-1.037, 0], "to": [1.233, 0]}, {"t": 380, "s": [-6.67, 0], "i": {"x": [0.833], "y": [0.854]}, "o": {"x": [0.167], "y": [0.185]}, "ti": [-0.817, 0], "to": [1.037, 0]}, {"t": 384, "s": [-3.87, 0], "i": {"x": [0.833], "y": [0.864]}, "o": {"x": [0.167], "y": [0.194]}, "ti": [-0.57, 0], "to": [0.817, 0]}, {"t": 388, "s": [-1.77, 0], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.216]}, "ti": [-0.295, 0], "to": [0.57, 0]}, {"t": 392, "s": [-0.45, 0], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.328]}, "ti": [-0.075, 0], "to": [0.295, 0]}, {"t": 396, "s": [0, 0]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "p", "layers": [{"ddd": 0, "refId": "q", "w": 230, "h": 130, "ind": 149, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -14], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 150, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -14], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [125, 108], "ix": 2}, "p": {"a": 0, "k": [182.5, 68], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "f", "layers": [{"ddd": 0, "ind": 151, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -14], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "td": 1, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[244.25, 14], [120.25, 14], [120.25, 122], [244.25, 122], [244.25, 14]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "p", "w": 125, "h": 108, "ind": 114, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [182.5, 68], "ix": 2}, "a": {"a": 0, "k": [182.5, 68], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "q", "layers": [{"ddd": 0, "ind": 152, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "r", "w": 124, "h": 110, "ind": 153, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [120, 12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 152}]}, {"id": "s", "layers": [{"ddd": 0, "ind": 154, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 28], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 155, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 46], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 156, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 64], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 157, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 82], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 158, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 100], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 159, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [124, 110], "ix": 2}, "p": {"a": 0, "k": [182, 67], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "r", "layers": [{"ddd": 0, "ind": 160, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-120, -12], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "td": 1, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-106.5, -36], [106.5, -37.5], [-110.5, -30.5], [-110, -18], [60, -18.5], [60, -13], [-111.5, -13], [-110, -0.5], [108, 0], [108.5, 6], [-111, 4], [-110, 17.5], [88, 17], [87.5, 23.5], [-109.5, 25], [-110.5, 36.5], [98.5, 35]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 200, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 396, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 10, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [115, 65], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "s", "w": 124, "h": 110, "ind": 153, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [182, 67], "ix": 2}, "a": {"a": 0, "k": [182, 67], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "g", "layers": [{"ddd": 0, "ind": 161, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "t", "w": 454, "h": 194, "ind": 162, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [23, 78], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 161}, {"ddd": 0, "ind": 163, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[223.62, -96.89], [-223.62, -96.89], [-226.9, -93.61], [-226.9, 93.61], [-223.62, 96.89], [223.62, 96.89], [226.9, 93.61], [226.9, -93.61], [223.62, -96.89]], "i": [[0, 0], [0, 0], [0, -1.813], [0, 0], [-1.813, 0], [0, 0], [0, 1.813], [0, 0], [1.813, 0]], "o": [[0, 0], [-1.813, 0], [0, 0], [0, 1.813], [0, 0], [1.813, 0], [0, 0], [0, -1.813], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [250, 175], "ix": 2}, "o": {"a": 0, "k": 20, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "u", "layers": [{"ddd": 0, "ind": 164, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-23, -78], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 1, "k": [{"t": 0, "s": [{"c": true, "v": [[-237, 60], [-151, -59], [-75, 58], [0, -69], [72, 60], [126, -38], [189, 57], [232, 1], [230.06, 124.69], [-110.33, 137.73], [-255.15, 125.9], [-237, 60]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [37.026, -4.674], [107.505, 20.584], [21.005, 16.281], [0, 0]], "o": [[6, -1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-71.767, 9.06], [-38.851, -7.439], [-24.535, -19.017], [0, 0]]}], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 100, "s": [{"c": true, "v": [[-237, 60], [-151, -77], [-75, 58], [0, -50], [72, 60], [127, -63], [189, 57], [232, 1], [231.06, 138.69], [-260.72, 139.8], [-273.17, 83.77], [-237, 60]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [37.026, -4.674], [91.986, -11.551], [12.094, -1.518], [0, 0]], "o": [[6, -1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-86.597, 10.932], [-28.832, 3.62], [-6.353, 0.797], [0, 0]]}], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 200, "s": [{"c": true, "v": [[-237, 60], [-150, -53], [-75, 58], [2, -72], [72, 60], [129, -41], [189, 57], [232, 1], [236.06, 163.69], [-290.72, 143.8], [-272.17, 79.77], [-237, 60]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [37.026, -4.674], [91.986, -11.551], [12.094, -1.518], [0, 0]], "o": [[6, -1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-86.597, 10.932], [-28.832, 3.62], [-6.353, 0.797], [0, 0]]}], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 300, "s": [{"c": true, "v": [[-237, 60], [-150, -73], [-75, 58], [2, -17], [72, 60], [132, -68], [189, 57], [232, 1], [271.06, 157.69], [-185.72, 156.8], [-285.17, 129.77], [-237, 60]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [37.026, -4.674], [91.986, -11.551], [12.094, -1.518], [0, 0]], "o": [[6, -1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-86.597, 10.932], [-28.832, 3.62], [-6.353, 0.797], [0, 0]]}], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 396, "s": [{"c": true, "v": [[-237, 60], [-151, -59], [-75, 58], [0, -69], [72, 60], [126, -38], [189, 57], [232, 1], [244.06, 130.69], [-246.72, 150.8], [-305.17, 126.77], [-237, 60]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [37.026, -4.674], [91.986, -11.551], [12.094, -1.518], [0, 0]], "o": [[6, -1], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-86.597, 10.932], [-28.832, 3.62], [-6.353, 0.797], [0, 0]]}]}], "ix": 2}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 16, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [242.14, 213.37], "ix": 2}, "a": {"a": 0, "k": [-7.86, 38.37], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 165, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [-23, -78], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [454, 194], "ix": 2}, "p": {"a": 0, "k": [250, 175], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "t", "layers": [{"ddd": 0, "refId": "v", "w": 454, "h": 194, "ind": 162, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [250, 175], "ix": 2}, "a": {"a": 0, "k": [250, 175], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "td": 1}, {"ddd": 0, "refId": "u", "w": 454, "h": 194, "ind": 162, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [250, 175], "ix": 2}, "a": {"a": 0, "k": [250, 175], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "v", "layers": [{"ddd": 0, "ind": 166, "ty": 3, "sr": 1, "ks": {"p": {"a": 0, "k": [227, 97], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "w", "w": 454, "h": 194, "ind": 167, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [-227, -97], "ix": 2}, "o": {"a": 0, "k": 20, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 166}]}, {"id": "w", "layers": [{"ddd": 0, "ind": 168, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [227, 97], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[223.62, -96.89], [-223.62, -96.89], [-226.9, -93.61], [-226.9, 93.61], [-223.62, 96.89], [223.62, 96.89], [226.9, 93.61], [226.9, -93.61], [223.62, -96.89]], "i": [[0, 0], [0, 0], [0, -1.813], [0, 0], [-1.813, 0], [0, 0], [0, 1.813], [0, 0], [1.813, 0]], "o": [[0, 0], [-1.813, 0], [0, 0], [0, 1.813], [0, 0], [1.813, 0], [0, 0], [0, -1.813], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "h", "layers": [{"ddd": 0, "ind": 169, "ty": 3, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "x", "w": 261, "h": 115, "ind": 170, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [-17, 7], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "parent": 169}]}, {"id": "y", "layers": [{"ddd": 0, "ind": 171, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [17, -7], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 28], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 172, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [17, -7], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 46], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 173, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [17, -7], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 64], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 174, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [17, -7], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 82], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 175, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [17, -7], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-340, 68], [-132.5, 68]], "i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]]}}}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 8, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [114.75, 100], "ix": 2}, "a": {"a": 0, "k": [-236.25, 68], "ix": 2}, "o": {"a": 0, "k": 50, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 176, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [17, -7], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [261, 115], "ix": 2}, "p": {"a": 0, "k": [113.5, 64.5], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "r": 1, "bm": 0}], "ip": 0, "op": 401, "st": 0}]}, {"id": "x", "layers": [{"ddd": 0, "ind": 177, "ty": 4, "sr": 1, "ks": {"p": {"a": 0, "k": [17, -7], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "td": 1, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-106.5, -36], [106.5, -37.5], [-110.5, -30.5], [-110, -18], [60, -18.5], [60, -13], [-111.5, -13], [-110, -0.5], [108, 0], [108.5, 6], [-111, 4], [-110, 17.5], [88, 17], [87.5, 23.5], [-109.5, 25], [-110.5, 36.5], [98.5, 35]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 200, "s": [100], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 396, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 10, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [115, 65], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "y", "w": 261, "h": 115, "ind": 170, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [113.5, 64.5], "ix": 2}, "a": {"a": 0, "k": [113.5, 64.5], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0, "tt": 1}]}, {"id": "i", "layers": [{"ddd": 0, "ind": 178, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-97.5, 29.5], [-64, -15.5], [-32, 25.5], [21.5, -31.5], [63.5, 10.5], [102.5, -33.5]], "i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.357], "y": [1]}, "o": {"x": [0.333], "y": [0]}}, {"t": 200, "s": [100], "i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}}, {"t": 396, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 3, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [127.5, 48], "ix": 2}, "a": {"a": 0, "k": [2.5, -2], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "z", "layers": [{"ddd": 0, "ind": 179, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[13.78, 50.91], [69.8, -2.24], [36.89, 8.6], [40.74, -40.12], [1.36, -8.9], [-8.61, -51.94], [-29.61, -14.22], [-69.82, -21.09], [-42.1, 52.39]], "i": [[0, 0], [17.797, 24.611], [0, 0], [24.684, 11.381], [0, 0], [19.513, 5.322], [0, 0], [15.009, -27.054], [0, 0]], "o": [[0, 0], [-11.225, -15.524], [0, 0], [-25.628, -11.817], [0, 0], [-19.516, -5.322], [0, 0], [-13.618, 24.549], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.64, 0.836], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [1120.11, 547.77], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 180, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[138.87, 85.07], [86.28, 73.94], [134.22, 10.52], [19.33, 46.02], [59.12, -110.73], [-26.29, 24.78], [-73.51, -111.6], [-132.94, 112.9], [138.87, 85.07]], "i": [[0, 0], [23.385, -2.467], [10.253, 22.894], [23.646, -30.59], [65.631, 24.337], [2.31, -14.408], [67.407, -6.104], [0, 0], [-4.435, 47.398]], "o": [[1.27, -13.57], [31.081, -17.656], [-21.448, -47.9], [0, 0], [-58.639, -21.743], [2.121, -13.761], [-70.962, 6.425], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.471, 0.883, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [1161.48, 478.75], "ix": 2}, "a": {"a": 0, "k": [2.64, 1.96], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 181, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": false, "v": [[-13.78, 50.91], [-69.8, -2.24], [-36.89, 8.6], [-40.74, -40.12], [-1.36, -8.9], [8.61, -51.94], [29.61, -14.22], [69.82, -21.09], [42.1, 52.39]], "i": [[0, 0], [-17.796, 24.61], [0, 0], [-24.684, 11.381], [0, 0], [-19.514, 5.323], [0, 0], [-15.009, -27.054], [0, 0]], "o": [[0, 0], [11.226, -15.524], [0, 0], [25.627, -11.818], [0, 0], [19.516, -5.322], [0, 0], [13.618, 24.549], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.64, 0.836], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [766.85, 542.78], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 182, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[132.1, 121.03], [109.93, 45.86], [72.67, -103.47], [25.46, 32.91], [-59.95, -102.6], [-20.16, 54.15], [-126.18, 26.19], [-89.91, 81.81], [-128.52, 82.72], [-53.29, 123.91], [-15.65, 114.05], [-14.48, 114.41], [132.1, 121.03]], "i": [[0, 0], [0, 0], [70.963, 6.426], [-2.121, -13.761], [58.64, -21.744], [0, 0], [21.448, -47.9], [-25.579, -15.289], [5.423, -6.631], [-59.431, -1.182], [-5.191, 5.842], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-67.406, -6.103], [-2.31, -14.408], [-65.63, 24.337], [-23.645, -30.591], [-9.352, 20.885], [-16.365, -3.857], [-15.113, 18.481], [21.537, 0.428], [0.721, 0.228], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.471, 0.883, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [729.96, 478.21], "ix": 2}, "a": {"a": 0, "k": [-0.11, 9.55], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "A", "layers": [{"ddd": 0, "ind": 183, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0, 137.18], [-137.18, 0], [0, -137.18], [137.18, 0], [0, 137.18]], "i": [[0, 0], [0, 75.759], [-75.759, 0], [0, -75.763], [75.76, 0]], "o": [[-75.759, 0], [0, -75.763], [75.76, 0], [0, 75.759], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[176.28, -107.12], [218.1, -138.27], [138.27, -218.1], [107.12, -176.28], [48.9, -200.41], [56.46, -252.08], [0, -258.45], [-56.46, -252.08], [-48.9, -200.41], [-107.12, -176.28], [-138.27, -218.1], [-218.1, -138.27], [-176.28, -107.12], [-200.42, -48.9], [-252.08, -56.46], [-258.45, 0], [-252.08, 56.46], [-200.42, 48.9], [-176.28, 107.12], [-218.1, 138.27], [-138.27, 218.1], [-107.12, 176.28], [-48.9, 200.42], [-56.46, 252.08], [0, 258.45], [56.46, 252.08], [48.9, 200.42], [107.12, 176.28], [138.27, 218.1], [218.1, 138.27], [176.28, 107.12], [200.41, 48.9], [252.08, 56.46], [258.45, 0], [252.08, -56.46], [200.41, -48.9], [176.28, -107.12]], "i": [[0, 0], [0, 0], [32.144, 20.423], [0, 0], [20.839, 5.071], [0, 0], [19.418, 0], [18.191, -4.058], [0, 0], [17.826, -10.858], [0, 0], [20.422, -32.145], [0, 0], [5.07, -20.839], [0, 0], [0, -19.414], [-4.059, -18.193], [0, 0], [-10.861, -17.825], [0, 0], [-32.141, -20.422], [0, 0], [-20.84, -5.069], [0, 0], [-19.417, 0], [-18.194, 4.058], [0, 0], [-17.827, 10.859], [0, 0], [-20.422, 32.144], [0, 0], [-5.069, 20.838], [0, 0], [0, 19.422], [4.062, 18.193], [0, 0], [10.857, 17.826]], "o": [[0, 0], [-20.422, -32.141], [0, 0], [-17.827, -10.858], [0, 0], [-18.194, -4.058], [-19.417, 0], [0, 0], [-20.837, 5.071], [0, 0], [-32.141, 20.42], [0, 0], [-10.861, 17.826], [0, 0], [-4.059, 18.193], [0, 19.422], [0, 0], [5.07, 20.838], [0, 0], [20.422, 32.144], [0, 0], [17.826, 10.859], [0, 0], [18.192, 4.061], [19.418, 0], [0, 0], [20.839, -5.069], [0, 0], [32.144, -20.422], [0, 0], [10.857, -17.825], [0, 0], [4.062, -18.193], [0, -19.414], [0, 0], [-5.069, -20.839], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.64, 0.836], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [468.31, 468.76], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [368.31, 368.76], "ix": 2}, "a": {"a": 0, "k": [468.31, 468.76], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 396, "s": [90]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "B", "layers": [{"ddd": 0, "ind": 184, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0, 137.18], [-137.18, 0], [0, -137.18], [137.18, 0], [0, 137.18]], "i": [[0, 0], [0, 75.759], [-75.759, 0], [0, -75.763], [75.76, 0]], "o": [[-75.759, 0], [0, -75.763], [75.76, 0], [0, 75.759], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[176.28, -107.12], [218.1, -138.27], [138.27, -218.1], [107.12, -176.28], [48.9, -200.41], [56.46, -252.08], [0, -258.45], [-56.46, -252.08], [-48.9, -200.41], [-107.12, -176.28], [-138.27, -218.1], [-218.1, -138.27], [-176.28, -107.12], [-200.42, -48.9], [-252.08, -56.46], [-258.45, 0], [-252.08, 56.46], [-200.42, 48.9], [-176.28, 107.12], [-218.1, 138.27], [-138.27, 218.1], [-107.12, 176.28], [-48.9, 200.42], [-56.46, 252.08], [0, 258.45], [56.46, 252.08], [48.9, 200.42], [107.12, 176.28], [138.27, 218.1], [218.1, 138.27], [176.28, 107.12], [200.41, 48.9], [252.08, 56.46], [258.45, 0], [252.08, -56.46], [200.41, -48.9], [176.28, -107.12]], "i": [[0, 0], [0, 0], [32.144, 20.423], [0, 0], [20.839, 5.071], [0, 0], [19.418, 0], [18.191, -4.058], [0, 0], [17.826, -10.858], [0, 0], [20.422, -32.145], [0, 0], [5.07, -20.839], [0, 0], [0, -19.414], [-4.059, -18.193], [0, 0], [-10.861, -17.825], [0, 0], [-32.141, -20.422], [0, 0], [-20.84, -5.069], [0, 0], [-19.417, 0], [-18.194, 4.058], [0, 0], [-17.827, 10.859], [0, 0], [-20.422, 32.144], [0, 0], [-5.069, 20.838], [0, 0], [0, 19.422], [4.062, 18.193], [0, 0], [10.857, 17.826]], "o": [[0, 0], [-20.422, -32.141], [0, 0], [-17.827, -10.858], [0, 0], [-18.194, -4.058], [-19.417, 0], [0, 0], [-20.837, 5.071], [0, 0], [-32.141, 20.42], [0, 0], [-10.861, 17.826], [0, 0], [-4.059, 18.193], [0, 19.422], [0, 0], [5.07, 20.838], [0, 0], [20.422, 32.144], [0, 0], [17.826, 10.859], [0, 0], [18.192, 4.061], [19.418, 0], [0, 0], [20.839, -5.069], [0, 0], [32.144, -20.422], [0, 0], [10.857, -17.825], [0, 0], [4.062, -18.193], [0, -19.414], [0, 0], [-5.069, -20.839], [0, 0]]}}}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.64, 0.836], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [468.31, 468.76], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [368.31, 368.76], "ix": 2}, "a": {"a": 0, "k": [468.31, 468.76], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.75], "y": [0.75]}, "o": {"x": [0.25], "y": [0.25]}}, {"t": 396, "s": [90]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "C", "layers": [{"ddd": 0, "ind": 185, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-149.53, -52.65], [90.8, 155.26], [149.53, -155.26], [-149.53, -52.65]], "i": [[0, 0], [-112.441, -21.06], [0, 0], [0, 0]], "o": [[36.388, 106.122], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.81, 0.08, 0.765], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [417.75, 449.11], "ix": 2}, "a": {"a": 0, "k": [148, -152], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-145.61, 152.51], [-86.88, 158.01], [145.61, 56.1], [-86.88, -158.01], [-145.61, 152.51]], "i": [[0, 0], [-20.125, 0], [-57.8, 62.714], [0, 0], [0, 0]], "o": [[19.072, 3.628], [91.965, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.722, 0.922, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [506.15, 603.86], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[129.76, -158.07], [-129.76, -22.35], [129.76, 158.07], [129.76, -158.07]], "i": [[0, 0], [56.981, -82.021], [0, 0], [0, 0]], "o": [[-107.526, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.973, 0.695, 0.322], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [289.52, 287.77], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-158.07, -265.13], [-158.07, 51.01], [74.41, 265.13], [158.07, 51.01], [-158.07, -265.13]], "i": [[0, 0], [0, 0], [0, 0], [0, 82.605], [174.57, 0]], "o": [[0, 0], [0, 0], [51.832, -56.278], [0, -174.569], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.091, 0.74, 0.97], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [458.63, 413.58], "ix": 2}, "a": {"a": 0, "k": [-164, 48], "ix": 2}, "s": {"a": 1, "k": [{"t": 0, "s": [100, 100], "i": {"x": [0.833], "y": [0.719]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [100.54, 100.54], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.119]}}, {"t": 8, "s": [101.8, 101.8], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 12, "s": [103.5, 103.5], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 16, "s": [105.44, 105.44], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 20, "s": [107.45, 107.45], "i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.175]}}, {"t": 24, "s": [109.28, 109.28], "i": {"x": [0.833], "y": [1.077]}, "o": {"x": [0.167], "y": [0.336]}}, {"t": 28, "s": [109.88, 109.88], "i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.04]}}, {"t": 32, "s": [108.72, 108.72], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.147]}}, {"t": 36, "s": [107.21, 107.21], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 40, "s": [105.59, 105.59], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 44, "s": [103.95, 103.95], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 48, "s": [102.35, 102.35], "i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.175]}}, {"t": 52, "s": [100.9, 100.9], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.218]}}, {"t": 56, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.075]}}, {"t": 60, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 64, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 68, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 72, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 76, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 80, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 84, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 88, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 92, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 96, "s": [100, 100], "i": {"x": [0.833], "y": [0.955]}, "o": {"x": [0.167], "y": [0]}}, {"t": 100, "s": [100, 100], "i": {"x": [0.833], "y": [0.719]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 104, "s": [100.54, 100.54], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.119]}}, {"t": 108, "s": [101.8, 101.8], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 112, "s": [103.5, 103.5], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 116, "s": [105.44, 105.44], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 120, "s": [107.45, 107.45], "i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.175]}}, {"t": 124, "s": [109.28, 109.28], "i": {"x": [0.833], "y": [1.077]}, "o": {"x": [0.167], "y": [0.336]}}, {"t": 128, "s": [109.88, 109.88], "i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.04]}}, {"t": 132, "s": [108.72, 108.72], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.147]}}, {"t": 136, "s": [107.21, 107.21], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 140, "s": [105.59, 105.59], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 144, "s": [103.95, 103.95], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 148, "s": [102.35, 102.35], "i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.175]}}, {"t": 152, "s": [100.9, 100.9], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.218]}}, {"t": 156, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.075]}}, {"t": 160, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 164, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 168, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 172, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 176, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 180, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 184, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 188, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 192, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 196, "s": [100, 100], "i": {"x": [0.833], "y": [0.955]}, "o": {"x": [0.167], "y": [0]}}, {"t": 200, "s": [100, 100], "i": {"x": [0.833], "y": [0.719]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 204, "s": [100.54, 100.54], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.119]}}, {"t": 208, "s": [101.8, 101.8], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 212, "s": [103.5, 103.5], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 216, "s": [105.44, 105.44], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 220, "s": [107.45, 107.45], "i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.175]}}, {"t": 224, "s": [109.28, 109.28], "i": {"x": [0.833], "y": [1.077]}, "o": {"x": [0.167], "y": [0.336]}}, {"t": 228, "s": [109.88, 109.88], "i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.04]}}, {"t": 232, "s": [108.72, 108.72], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.147]}}, {"t": 236, "s": [107.21, 107.21], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 240, "s": [105.59, 105.59], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 244, "s": [103.95, 103.95], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 248, "s": [102.35, 102.35], "i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.175]}}, {"t": 252, "s": [100.9, 100.9], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.218]}}, {"t": 256, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.075]}}, {"t": 260, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 264, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 268, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 272, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 276, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 280, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 284, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 288, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 292, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 296, "s": [100, 100], "i": {"x": [0.833], "y": [0.955]}, "o": {"x": [0.167], "y": [0]}}, {"t": 300, "s": [100, 100], "i": {"x": [0.833], "y": [0.719]}, "o": {"x": [0.167], "y": [0.083]}}, {"t": 304, "s": [100.54, 100.54], "i": {"x": [0.833], "y": [0.805]}, "o": {"x": [0.167], "y": [0.119]}}, {"t": 308, "s": [101.8, 101.8], "i": {"x": [0.833], "y": [0.821]}, "o": {"x": [0.167], "y": [0.146]}}, {"t": 312, "s": [103.5, 103.5], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.156]}}, {"t": 316, "s": [105.44, 105.44], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 320, "s": [107.45, 107.45], "i": {"x": [0.833], "y": [0.889]}, "o": {"x": [0.167], "y": [0.175]}}, {"t": 324, "s": [109.28, 109.28], "i": {"x": [0.833], "y": [1.077]}, "o": {"x": [0.167], "y": [0.336]}}, {"t": 328, "s": [109.88, 109.88], "i": {"x": [0.833], "y": [0.808]}, "o": {"x": [0.167], "y": [0.04]}}, {"t": 332, "s": [108.72, 108.72], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.147]}}, {"t": 336, "s": [107.21, 107.21], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 340, "s": [105.59, 105.59], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 344, "s": [103.95, 103.95], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 348, "s": [102.35, 102.35], "i": {"x": [0.833], "y": [0.865]}, "o": {"x": [0.167], "y": [0.175]}}, {"t": 352, "s": [100.9, 100.9], "i": {"x": [0.833], "y": [0.917]}, "o": {"x": [0.167], "y": [0.218]}}, {"t": 356, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [-0.075]}}, {"t": 360, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 364, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 368, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 372, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 376, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 380, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 384, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 388, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 392, "s": [100, 100], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}}, {"t": 396, "s": [100, 100]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[-101.44, -141.52], [-158.07, 38.9], [-140.99, 141.52], [158.07, 38.9], [-101.44, -141.52]], "i": [[0, 0], [0, -67.042], [-11.116, -32.177], [0, 0], [0, 0]], "o": [[-35.686, 51.248], [0, 35.92], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0.091, 0.74, 0.97], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [261.21, 406.94], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [929.28, 535.85], "ix": 2}, "a": {"a": 0, "k": [419.28, 445.85], "ix": 2}, "r": {"a": 1, "k": [{"t": 0, "s": [0], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [0.02], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [0.07], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 12, "s": [0.14], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 16, "s": [0.25], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 20, "s": [0.39], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 24, "s": [0.56], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 28, "s": [0.75], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 32, "s": [0.96], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 36, "s": [1.2], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 40, "s": [1.46], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 44, "s": [1.74], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 48, "s": [2.03], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 52, "s": [2.35], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 56, "s": [2.68], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 60, "s": [3.02], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 64, "s": [3.38], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 68, "s": [3.75], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 72, "s": [4.14], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 76, "s": [4.53], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 80, "s": [4.93], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 84, "s": [5.33], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 88, "s": [5.75], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 92, "s": [6.16], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 96, "s": [6.58], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 100, "s": [7], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 104, "s": [7.42], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 108, "s": [7.84], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 112, "s": [8.25], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 116, "s": [8.67], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 120, "s": [9.07], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 124, "s": [9.47], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 128, "s": [9.86], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 132, "s": [10.24], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 136, "s": [10.62], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 140, "s": [10.98], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 144, "s": [11.32], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 148, "s": [11.65], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 152, "s": [11.97], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 156, "s": [12.27], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 160, "s": [12.54], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 164, "s": [12.8], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 168, "s": [13.04], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 172, "s": [13.25], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 176, "s": [13.44], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 180, "s": [13.61], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 184, "s": [13.75], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 188, "s": [13.86], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 192, "s": [13.94], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 196, "s": [13.98], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.329]}}, {"t": 200, "s": [14], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [13.98], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 208, "s": [13.94], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 212, "s": [13.86], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 216, "s": [13.75], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 220, "s": [13.61], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 224, "s": [13.44], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 228, "s": [13.25], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 232, "s": [13.04], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 236, "s": [12.8], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 240, "s": [12.54], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 244, "s": [12.27], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 248, "s": [11.97], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 252, "s": [11.65], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 256, "s": [11.32], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 260, "s": [10.98], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 264, "s": [10.62], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 268, "s": [10.24], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 272, "s": [9.86], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 276, "s": [9.47], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 280, "s": [9.07], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 284, "s": [8.67], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 288, "s": [8.25], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 292, "s": [7.84], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 296, "s": [7.42], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 300, "s": [7], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 304, "s": [6.58], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 308, "s": [6.16], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 312, "s": [5.75], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 316, "s": [5.33], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 320, "s": [4.93], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 324, "s": [4.53], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 328, "s": [4.14], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 332, "s": [3.75], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 336, "s": [3.38], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 340, "s": [3.02], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 344, "s": [2.68], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 348, "s": [2.35], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 352, "s": [2.03], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 356, "s": [1.74], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 360, "s": [1.46], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 364, "s": [1.2], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 368, "s": [0.96], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 372, "s": [0.75], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 376, "s": [0.56], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 380, "s": [0.39], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 384, "s": [0.25], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 388, "s": [0.14], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 392, "s": [0.07], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 396, "s": [0.02]}], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "D", "layers": [{"ddd": 0, "ind": 186, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 14, "ix": 2}, "o": {"a": 0, "k": 243.98, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [0.03, 0.63, 0.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 187, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 25, "ix": 2}, "o": {"a": 1, "k": [{"t": 0, "s": [-42], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [-41.85], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [-41.39], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 12, "s": [-40.64], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 16, "s": [-39.62], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 20, "s": [-38.33], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 24, "s": [-36.79], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 28, "s": [-35.02], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 32, "s": [-33.01], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 36, "s": [-30.8], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 40, "s": [-28.38], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 44, "s": [-25.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 48, "s": [-22.98], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 52, "s": [-20.04], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 56, "s": [-16.94], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 60, "s": [-13.7], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 64, "s": [-10.34], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 68, "s": [-6.87], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 72, "s": [-3.29], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 76, "s": [0.37], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 80, "s": [4.11], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 84, "s": [7.91], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 88, "s": [11.77], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 92, "s": [15.66], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 96, "s": [19.57], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 100, "s": [23.5], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 104, "s": [27.43], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 108, "s": [31.34], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 112, "s": [35.23], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 116, "s": [39.09], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 120, "s": [42.89], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 124, "s": [46.63], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 128, "s": [50.29], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 132, "s": [53.87], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 136, "s": [57.34], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 140, "s": [60.7], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 144, "s": [63.94], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 148, "s": [67.04], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 152, "s": [69.99], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 156, "s": [72.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 160, "s": [75.38], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 164, "s": [77.8], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 168, "s": [80.01], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 172, "s": [82.02], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 176, "s": [83.79], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 180, "s": [85.33], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 184, "s": [86.62], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 188, "s": [87.64], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 192, "s": [88.39], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 196, "s": [88.84], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.329]}}, {"t": 200, "s": [89], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [88.84], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 208, "s": [88.39], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 212, "s": [87.64], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 216, "s": [86.62], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 220, "s": [85.33], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 224, "s": [83.79], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 228, "s": [82.02], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 232, "s": [80.01], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 236, "s": [77.8], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 240, "s": [75.38], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 244, "s": [72.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 248, "s": [69.99], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 252, "s": [67.04], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 256, "s": [63.94], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 260, "s": [60.7], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 264, "s": [57.34], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 268, "s": [53.87], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 272, "s": [50.29], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 276, "s": [46.63], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 280, "s": [42.89], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 284, "s": [39.09], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 288, "s": [35.23], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 292, "s": [31.34], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 296, "s": [27.43], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 300, "s": [23.5], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 304, "s": [19.57], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 308, "s": [15.66], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 312, "s": [11.77], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 316, "s": [7.91], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 320, "s": [4.11], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 324, "s": [0.37], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 328, "s": [-3.29], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 332, "s": [-6.87], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 336, "s": [-10.34], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 340, "s": [-13.7], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 344, "s": [-16.94], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 348, "s": [-20.04], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 352, "s": [-22.98], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 356, "s": [-25.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 360, "s": [-28.38], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 364, "s": [-30.8], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 368, "s": [-33.01], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 372, "s": [-35.02], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 376, "s": [-36.79], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 380, "s": [-38.33], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 384, "s": [-39.62], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 388, "s": [-40.64], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 392, "s": [-41.39], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 396, "s": [-41.85]}], "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [0.291, 0.39, 0.997], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 188, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "st", "c": {"a": 0, "k": [0.095, 0.742, 0.97], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "E", "layers": [{"ddd": 0, "ind": 189, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 14, "ix": 2}, "o": {"a": 0, "k": 243.98, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [0.03, 0.63, 0.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 190, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 25, "ix": 2}, "o": {"a": 1, "k": [{"t": 0, "s": [-42], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [-41.85], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [-41.39], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 12, "s": [-40.64], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 16, "s": [-39.62], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 20, "s": [-38.33], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 24, "s": [-36.79], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 28, "s": [-35.02], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 32, "s": [-33.01], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 36, "s": [-30.8], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 40, "s": [-28.38], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 44, "s": [-25.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 48, "s": [-22.98], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 52, "s": [-20.04], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 56, "s": [-16.94], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 60, "s": [-13.7], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 64, "s": [-10.34], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 68, "s": [-6.87], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 72, "s": [-3.29], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 76, "s": [0.37], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 80, "s": [4.11], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 84, "s": [7.91], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 88, "s": [11.77], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 92, "s": [15.66], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 96, "s": [19.57], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 100, "s": [23.5], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 104, "s": [27.43], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 108, "s": [31.34], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 112, "s": [35.23], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 116, "s": [39.09], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 120, "s": [42.89], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 124, "s": [46.63], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 128, "s": [50.29], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 132, "s": [53.87], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 136, "s": [57.34], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 140, "s": [60.7], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 144, "s": [63.94], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 148, "s": [67.04], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 152, "s": [69.99], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 156, "s": [72.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 160, "s": [75.38], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 164, "s": [77.8], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 168, "s": [80.01], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 172, "s": [82.02], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 176, "s": [83.79], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 180, "s": [85.33], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 184, "s": [86.62], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 188, "s": [87.64], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 192, "s": [88.39], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 196, "s": [88.84], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.329]}}, {"t": 200, "s": [89], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [88.84], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 208, "s": [88.39], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 212, "s": [87.64], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 216, "s": [86.62], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 220, "s": [85.33], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 224, "s": [83.79], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 228, "s": [82.02], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 232, "s": [80.01], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 236, "s": [77.8], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 240, "s": [75.38], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 244, "s": [72.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 248, "s": [69.99], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 252, "s": [67.04], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 256, "s": [63.94], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 260, "s": [60.7], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 264, "s": [57.34], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 268, "s": [53.87], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 272, "s": [50.29], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 276, "s": [46.63], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 280, "s": [42.89], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 284, "s": [39.09], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 288, "s": [35.23], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 292, "s": [31.34], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 296, "s": [27.43], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 300, "s": [23.5], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 304, "s": [19.57], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 308, "s": [15.66], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 312, "s": [11.77], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 316, "s": [7.91], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 320, "s": [4.11], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 324, "s": [0.37], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 328, "s": [-3.29], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 332, "s": [-6.87], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 336, "s": [-10.34], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 340, "s": [-13.7], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 344, "s": [-16.94], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 348, "s": [-20.04], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 352, "s": [-22.98], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 356, "s": [-25.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 360, "s": [-28.38], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 364, "s": [-30.8], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 368, "s": [-33.01], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 372, "s": [-35.02], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 376, "s": [-36.79], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 380, "s": [-38.33], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 384, "s": [-39.62], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 388, "s": [-40.64], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 392, "s": [-41.39], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 396, "s": [-41.85]}], "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [0.291, 0.39, 0.997], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 191, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "st", "c": {"a": 0, "k": [0.095, 0.742, 0.97], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}, {"id": "F", "layers": [{"ddd": 0, "ind": 192, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 14, "ix": 2}, "o": {"a": 0, "k": 243.98, "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [0.03, 0.63, 0.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 193, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 25, "ix": 2}, "o": {"a": 1, "k": [{"t": 0, "s": [-42], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 4, "s": [-41.85], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 8, "s": [-41.39], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 12, "s": [-40.64], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 16, "s": [-39.62], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 20, "s": [-38.33], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 24, "s": [-36.79], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 28, "s": [-35.02], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 32, "s": [-33.01], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 36, "s": [-30.8], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 40, "s": [-28.38], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 44, "s": [-25.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 48, "s": [-22.98], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 52, "s": [-20.04], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 56, "s": [-16.94], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 60, "s": [-13.7], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 64, "s": [-10.34], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 68, "s": [-6.87], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 72, "s": [-3.29], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 76, "s": [0.37], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 80, "s": [4.11], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 84, "s": [7.91], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 88, "s": [11.77], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 92, "s": [15.66], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 96, "s": [19.57], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 100, "s": [23.5], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 104, "s": [27.43], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 108, "s": [31.34], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 112, "s": [35.23], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 116, "s": [39.09], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 120, "s": [42.89], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 124, "s": [46.63], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 128, "s": [50.29], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 132, "s": [53.87], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 136, "s": [57.34], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 140, "s": [60.7], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 144, "s": [63.94], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 148, "s": [67.04], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 152, "s": [69.99], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 156, "s": [72.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 160, "s": [75.38], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 164, "s": [77.8], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 168, "s": [80.01], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 172, "s": [82.02], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 176, "s": [83.79], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 180, "s": [85.33], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 184, "s": [86.62], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 188, "s": [87.64], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 192, "s": [88.39], "i": {"x": [0.833], "y": [0.888]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 196, "s": [88.84], "i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0.329]}}, {"t": 200, "s": [89], "i": {"x": [0.833], "y": [0.671]}, "o": {"x": [0.167], "y": [0]}}, {"t": 204, "s": [88.84], "i": {"x": [0.833], "y": [0.781]}, "o": {"x": [0.167], "y": [0.112]}}, {"t": 208, "s": [88.39], "i": {"x": [0.833], "y": [0.802]}, "o": {"x": [0.167], "y": [0.134]}}, {"t": 212, "s": [87.64], "i": {"x": [0.833], "y": [0.812]}, "o": {"x": [0.167], "y": [0.144]}}, {"t": 216, "s": [86.62], "i": {"x": [0.833], "y": [0.817]}, "o": {"x": [0.167], "y": [0.15]}}, {"t": 220, "s": [85.33], "i": {"x": [0.833], "y": [0.82]}, "o": {"x": [0.167], "y": [0.153]}}, {"t": 224, "s": [83.79], "i": {"x": [0.833], "y": [0.823]}, "o": {"x": [0.167], "y": [0.155]}}, {"t": 228, "s": [82.02], "i": {"x": [0.833], "y": [0.824]}, "o": {"x": [0.167], "y": [0.157]}}, {"t": 232, "s": [80.01], "i": {"x": [0.833], "y": [0.826]}, "o": {"x": [0.167], "y": [0.159]}}, {"t": 236, "s": [77.8], "i": {"x": [0.833], "y": [0.827]}, "o": {"x": [0.167], "y": [0.16]}}, {"t": 240, "s": [75.38], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 244, "s": [72.77], "i": {"x": [0.833], "y": [0.828]}, "o": {"x": [0.167], "y": [0.161]}}, {"t": 248, "s": [69.99], "i": {"x": [0.833], "y": [0.829]}, "o": {"x": [0.167], "y": [0.162]}}, {"t": 252, "s": [67.04], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 256, "s": [63.94], "i": {"x": [0.833], "y": [0.83]}, "o": {"x": [0.167], "y": [0.163]}}, {"t": 260, "s": [60.7], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 264, "s": [57.34], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 268, "s": [53.87], "i": {"x": [0.833], "y": [0.831]}, "o": {"x": [0.167], "y": [0.164]}}, {"t": 272, "s": [50.29], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 276, "s": [46.63], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 280, "s": [42.89], "i": {"x": [0.833], "y": [0.832]}, "o": {"x": [0.167], "y": [0.165]}}, {"t": 284, "s": [39.09], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 288, "s": [35.23], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 292, "s": [31.34], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 296, "s": [27.43], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.166]}}, {"t": 300, "s": [23.5], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 304, "s": [19.57], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 308, "s": [15.66], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 312, "s": [11.77], "i": {"x": [0.833], "y": [0.834]}, "o": {"x": [0.167], "y": [0.167]}}, {"t": 316, "s": [7.91], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 320, "s": [4.11], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 324, "s": [0.37], "i": {"x": [0.833], "y": [0.835]}, "o": {"x": [0.167], "y": [0.168]}}, {"t": 328, "s": [-3.29], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 332, "s": [-6.87], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 336, "s": [-10.34], "i": {"x": [0.833], "y": [0.836]}, "o": {"x": [0.167], "y": [0.169]}}, {"t": 340, "s": [-13.7], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 344, "s": [-16.94], "i": {"x": [0.833], "y": [0.837]}, "o": {"x": [0.167], "y": [0.17]}}, {"t": 348, "s": [-20.04], "i": {"x": [0.833], "y": [0.838]}, "o": {"x": [0.167], "y": [0.171]}}, {"t": 352, "s": [-22.98], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 356, "s": [-25.77], "i": {"x": [0.833], "y": [0.839]}, "o": {"x": [0.167], "y": [0.172]}}, {"t": 360, "s": [-28.38], "i": {"x": [0.833], "y": [0.84]}, "o": {"x": [0.167], "y": [0.173]}}, {"t": 364, "s": [-30.8], "i": {"x": [0.833], "y": [0.841]}, "o": {"x": [0.167], "y": [0.174]}}, {"t": 368, "s": [-33.01], "i": {"x": [0.833], "y": [0.843]}, "o": {"x": [0.167], "y": [0.176]}}, {"t": 372, "s": [-35.02], "i": {"x": [0.833], "y": [0.845]}, "o": {"x": [0.167], "y": [0.177]}}, {"t": 376, "s": [-36.79], "i": {"x": [0.833], "y": [0.847]}, "o": {"x": [0.167], "y": [0.18]}}, {"t": 380, "s": [-38.33], "i": {"x": [0.833], "y": [0.85]}, "o": {"x": [0.167], "y": [0.183]}}, {"t": 384, "s": [-39.62], "i": {"x": [0.833], "y": [0.856]}, "o": {"x": [0.167], "y": [0.188]}}, {"t": 388, "s": [-40.64], "i": {"x": [0.833], "y": [0.866]}, "o": {"x": [0.167], "y": [0.198]}}, {"t": 392, "s": [-41.39], "i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.219]}}, {"t": 396, "s": [-41.85]}], "ix": 2}, "m": 1}, {"ty": "st", "c": {"a": 0, "k": [0.291, 0.39, 0.997], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 194, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "el", "d": 1, "s": {"a": 0, "k": [562, 562], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}}, {"ty": "st", "c": {"a": 0, "k": [0.095, 0.742, 0.97], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "w": {"a": 0, "k": 60, "ix": 2}, "lc": 1, "lj": 1, "ml": 4}, {"ty": "tr", "p": {"a": 0, "k": [350, 350], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}]}], "layers": [{"ddd": 0, "ind": 12345679, "ty": 4, "nm": "Group Layer 8", "sr": 1, "ks": {"p": {"a": 0, "k": [955, 913.4016393442623, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [133.19672131147541, 133.19672131147541, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[220.741, 37.184], [225.501, 35.896], [228.749, 32.36800000000001], [229.981, 27.216000000000008], [228.749, 22.12], [225.501, 18.592], [220.741, 17.304], [215.981, 18.592], [212.677, 22.12], [211.501, 27.216000000000008], [212.677, 32.36800000000001], [215.981, 35.896], [220.741, 37.184], [220.741, 37.184], [220.741, 37.184]], "i": [[0, 0], [-1.380999999999972, 0.8586999999999989], [-0.7839999999999918, 1.493299999999991], [0, 1.903999999999996], [0.8220000000000027, 1.493299999999991], [1.382000000000062, 0.8586999999999989], [1.79200000000003, 0], [1.418999999999983, -0.8586999999999989], [0.8220000000000027, -1.493300000000005], [0, -1.904000000000011], [-0.7839999999999918, -1.5307000000000102], [-1.380999999999972, -0.8586999999999989], [-1.754000000000019, 0], [0, 0], [0, 0]], "o": [[1.79200000000003, 0], [1.382000000000062, -0.8586999999999989], [0.8220000000000027, -1.5307000000000102], [0, -1.904000000000011], [-0.7839999999999918, -1.493300000000005], [-1.380999999999972, -0.8586999999999989], [-1.754000000000019, 0], [-1.380999999999972, 0.8586999999999989], [-0.7839999999999918, 1.493299999999991], [0, 1.903999999999996], [0.8220000000000027, 1.493299999999991], [1.418999999999983, 0.8586999999999989], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[221.357, 43.06400000000001], [214.917, 41.608], [210.49300000000005, 37.408], [211.221, 36.232], [211.221, 42.392], [205.173, 42.392], [205.173, 0], [211.501, 0], [211.501, 18.36800000000001], [210.49300000000005, 16.912000000000006], [214.973, 12.88], [221.357, 11.424000000000007], [229.085, 13.49600000000001], [234.51700000000005, 19.152], [236.533, 27.216000000000008], [234.51700000000005, 35.28], [229.141, 40.992], [221.357, 43.06400000000001], [221.357, 43.06400000000001], [221.357, 43.06400000000001]], "i": [[0, 0], [1.942000000000007, 0.9706999999999937], [1.045999999999935, 1.829300000000003], [-0.2426666666666506, 0.3919999999999959], [0, -2.053333333333327], [2.015999999999963, 0], [0, 14.13066666666667], [-2.109333333333325, 0], [0, -6.122666666666674], [0.3360000000000127, 0.4853333333333296], [-1.865999999999985, 0.9707000000000079], [-2.38900000000001, 0], [-2.277000000000044, -1.3813000000000102], [-1.30600000000004, -2.389300000000006], [0, -2.986699999999999], [1.343999999999937, -2.389300000000006], [2.27800000000002, -1.4187000000000012], [2.912000000000035, 0], [0, 0], [0, 0]], "o": [[-2.351999999999975, 0], [-1.903999999999996, -0.9707000000000079], [0.2426666666666506, -0.3919999999999959], [0, 2.053333333333327], [-2.015999999999963, 0], [0, -14.13066666666667], [2.109333333333325, 0], [0, 6.122666666666667], [-0.3360000000000127, -0.4853333333333296], [1.120000000000005, -1.717300000000009], [1.867000000000075, -0.9706999999999937], [2.875, 0], [2.314999999999941, 1.381299999999996], [1.343999999999937, 2.389300000000006], [0, 2.986699999999999], [-1.30600000000004, 2.389300000000006], [-2.27699999999993, 1.381299999999996], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[181.87, 43.06400000000001], [176.438, 42], [172.854, 38.976], [171.566, 34.384], [172.63, 29.960000000000008], [176.046, 26.656000000000006], [181.814, 24.752], [192.342, 23.016000000000005], [192.342, 28], [183.046, 29.624], [179.35, 31.248], [178.174, 34.16], [179.462, 37.016000000000005], [182.878, 38.08], [187.35799999999995, 36.96000000000001], [190.38199999999995, 33.992], [191.446, 29.792], [191.446, 22.008], [189.766, 18.36800000000001], [185.398, 16.912000000000006], [180.974, 18.256], [178.23, 21.616], [172.966, 18.98400000000001], [175.71, 15.064000000000007], [180.134, 12.376], [185.566, 11.424000000000007], [191.894, 12.768], [196.206, 16.52], [197.774, 22.008], [197.774, 42.392], [191.726, 42.392], [191.726, 36.904], [193.014, 37.072], [190.27, 40.264], [186.518, 42.336], [181.87, 43.06400000000001], [181.87, 43.06400000000001], [181.87, 43.06400000000001]], "i": [[0, 0], [1.567999999999984, 0.7092999999999989], [0.8589999999999236, 1.2693000000000012], [0, 1.7547], [-0.7089999999999463, 1.306699999999992], [-1.530000000000086, 0.8960000000000008], [-2.313999999999965, 0.3733000000000004], [-3.509333333333302, 0.5786666666666633], [0, -1.661333333333332], [3.098666666666645, -0.541333333333327], [0.7839999999999918, -0.784000000000006], [0, -1.194699999999997], [-0.8579999999999472, -0.7467000000000041], [-1.381000000000085, 0], [-1.268999999999892, 0.7466999999999899], [-0.7089999999999463, 1.2319999999999993], [0, 1.530699999999996], [0, 2.594666666666669], [1.120000000000005, 0.9332999999999885], [1.829999999999927, 0], [1.269999999999982, -0.8960000000000008], [0.5979999999999563, -1.381299999999996], [1.754666666666708, 0.8773333333333255], [-1.269000000000005, 1.11999999999999], [-1.680000000000064, 0.6346999999999952], [-1.903999999999996, 0], [-1.828999999999951, -0.8960000000000008], [-1.008000000000038, -1.6053], [0, -2.090699999999998], [0, -6.794666666666672], [2.015999999999963, 0], [0, 1.829333333333338], [-0.4293333333333749, -0.05599999999999739], [1.120000000000005, -0.8959999999999866], [1.418999999999983, -0.4852999999999952], [1.717999999999961, 0], [0, 0], [0, 0]], "o": [[-2.052999999999997, 0], [-1.529999999999973, -0.7467000000000041], [-0.8580000000000609, -1.306699999999992], [0, -1.642700000000005], [0.7469999999999573, -1.306700000000006], [1.530999999999949, -0.8960000000000008], [3.509333333333302, -0.5786666666666633], [0, 1.661333333333332], [-3.098666666666645, 0.541333333333327], [-1.680000000000064, 0.2987000000000108], [-0.7839999999999918, 0.7467000000000041], [0, 1.157300000000006], [0.8959999999999582, 0.7092999999999989], [1.717999999999961, 0], [1.307000000000016, -0.7467000000000041], [0.7100000000000364, -1.2693000000000012], [0, -2.594666666666669], [0, -1.493299999999991], [-1.081999999999994, -0.9707000000000079], [-1.680000000000064, 0], [-1.232000000000085, 0.8586999999999989], [-1.754666666666708, -0.8773333333333255], [0.5599999999999454, -1.493300000000005], [1.269999999999982, -1.157300000000006], [1.717999999999961, -0.6347000000000094], [2.389999999999986, 0], [1.866999999999962, 0.8960000000000008], [1.045999999999935, 1.568000000000012], [0, 6.794666666666672], [-2.015999999999963, 0], [0, -1.829333333333338], [0.4293333333333749, 0.05599999999999739], [-0.70900000000006, 1.2319999999999993], [-1.081999999999994, 0.8960000000000008], [-1.380999999999972, 0.4853000000000094], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[159.072, 42.392], [159.072, 0], [165.4, 0], [165.4, 42.392], [159.072, 42.392], [159.072, 42.392], [159.072, 42.392]], "i": [[0, 0], [0, 14.13066666666667], [-2.109333333333325, 0], [0, -14.13066666666667], [2.109333333333325, 0], [0, 0], [0, 0]], "o": [[0, -14.13066666666667], [2.109333333333325, 0], [0, 14.13066666666667], [-2.109333333333325, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[138.952, 43.06400000000001], [130.888, 40.992], [125.456, 35.28], [123.496, 27.16], [125.456, 19.040000000000006], [130.832, 13.49600000000001], [138.448, 11.424000000000007], [144.552, 12.600000000000009], [149.088, 15.848], [151.888, 20.49600000000001], [152.896, 26.096], [152.84, 27.608], [152.616, 29.064000000000007], [128.48, 29.064000000000007], [128.48, 24.024], [149.032, 24.024], [146.008, 26.320000000000007], [145.616, 21.448000000000008], [142.816, 18.032], [138.448, 16.744], [133.968, 18.032], [130.944, 21.616], [130.104, 27.216000000000008], [130.944, 32.592], [134.192, 36.176], [139.008, 37.464], [143.65599999999995, 36.232], [146.736, 33.040000000000006], [151.888, 35.56], [149.088, 39.42400000000001], [144.60799999999995, 42.11200000000001], [138.952, 43.06400000000001], [138.952, 43.06400000000001], [138.952, 43.06400000000001]], "i": [[0, 0], [2.351999999999975, 1.381299999999996], [1.307000000000016, 2.389300000000006], [0, 2.986699999999999], [-1.307000000000016, 2.35199999999999], [-2.240000000000009, 1.343999999999994], [-2.836999999999989, 0], [-1.79200000000003, -0.784000000000006], [-1.231999999999971, -1.381299999999996], [-0.6349999999999909, -1.754700000000014], [0, -1.978700000000003], [0.03699999999992087, -0.5227000000000004], [0.1119999999999663, -0.4480000000000075], [8.04533333333336, 0], [0, 1.680000000000007], [-6.850666666666712, 0], [1.008000000000038, -0.7653333333333308], [0.6349999999999909, 1.4187000000000012], [1.269000000000005, 0.8213000000000079], [1.680000000000064, 0], [1.307000000000016, -0.8586999999999989], [0.70900000000006, -1.567999999999998], [-0.1490000000000009, -2.202700000000007], [-0.7469999999999573, -1.530699999999996], [-1.380999999999972, -0.8586999999999989], [-1.79200000000003, 0], [-1.268999999999892, 0.8213000000000079], [-0.7469999999999573, 1.306699999999992], [-1.717333333333386, -0.8400000000000034], [1.269000000000005, -1.157300000000006], [1.755000000000109, -0.6720000000000113], [2.052999999999997, 0], [0, 0], [0, 0]], "o": [[-3.024000000000001, 0], [-2.315000000000055, -1.4187000000000012], [-1.307000000000016, -2.426699999999997], [0, -3.061299999999989], [1.343999999999937, -2.352000000000004], [2.240000000000009, -1.3813000000000102], [2.277000000000044, 0], [1.79200000000003, 0.7839999999999918], [1.232000000000085, 1.344000000000008], [0.6720000000000255, 1.7547], [0, 0.4852999999999952], [-0.03700000000003456, 0.5227000000000004], [-8.04533333333336, 0], [0, -1.680000000000007], [6.850666666666712, 0], [-1.008000000000038, 0.7653333333333308], [0.3729999999999336, -1.829300000000003], [-0.59699999999998, -1.456000000000003], [-1.232000000000085, -0.8586999999999989], [-1.67999999999995, 0], [-1.306999999999903, 0.8213000000000079], [-0.7089999999999463, 1.530699999999996], [-0.1870000000000118, 2.053299999999993], [0.7839999999999918, 1.53070000000001], [1.418999999999983, 0.8586999999999989], [1.828999999999951, 0], [1.307000000000016, -0.8212999999999937], [1.717333333333386, 0.8400000000000034], [-0.59699999999998, 1.4187000000000012], [-1.231999999999971, 1.11999999999999], [-1.716999999999985, 0.6346999999999952], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[111.001, 7.951999999999998], [111.001, 0.6720000000000041], [117.329, 0.6720000000000041], [117.329, 7.951999999999998], [111.001, 7.951999999999998], [111.001, 7.951999999999998], [111.001, 7.951999999999998]], "i": [[0, 0], [0, 2.426666666666662], [-2.109333333333325, 0], [0, -2.426666666666662], [2.109333333333325, 0], [0, 0], [0, 0]], "o": [[0, -2.426666666666662], [2.109333333333325, 0], [0, 2.426666666666662], [-2.109333333333325, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[111.001, 42.392], [111.001, 12.096], [117.329, 12.096], [117.329, 42.392], [111.001, 42.392], [111.001, 42.392], [111.001, 42.392]], "i": [[0, 0], [0, 10.09866666666667], [-2.109333333333325, 0], [0, -10.09866666666667], [2.109333333333325, 0], [0, 0], [0, 0]], "o": [[0, -10.09866666666666], [2.109333333333325, 0], [0, 10.09866666666666], [-2.109333333333325, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[101.41, 42.72800000000001], [94.01800000000003, 40.040000000000006], [91.38599999999997, 32.48], [91.38599999999997, 17.808000000000007], [86.06600000000003, 17.808000000000007], [86.06600000000003, 12.096], [86.90599999999995, 12.096], [90.21000000000004, 10.864], [91.38599999999997, 7.504000000000005], [91.38599999999997, 5.152000000000001], [97.71400000000006, 5.152000000000001], [97.71400000000006, 12.096], [104.602, 12.096], [104.602, 17.808000000000007], [97.71400000000006, 17.808000000000007], [97.71400000000006, 32.2], [98.21799999999996, 34.888000000000005], [99.84199999999998, 36.568], [102.754, 37.128], [103.76200000000006, 37.072], [104.826, 36.96000000000001], [104.826, 42.392], [103.09, 42.616], [101.41, 42.72800000000001], [101.41, 42.72800000000001], [101.41, 42.72800000000001]], "i": [[0, 0], [1.754999999999995, 1.792000000000002], [0, 3.248000000000005], [0, 4.890666666666661], [1.773333333333312, 0], [0, 1.903999999999996], [-0.2799999999999727, 0], [-0.7839999999999918, 0.8212999999999937], [0, 1.4187000000000012], [0, 0.7839999999999989], [-2.109333333333325, 0], [0, -2.314666666666668], [-2.295999999999935, 0], [0, -1.903999999999996], [2.295999999999935, 0], [0, -4.797333333333327], [-0.3360000000000127, -0.7467000000000041], [-0.7469999999999573, -0.4106999999999914], [-1.19500000000005, 0], [-0.3730000000000473, 0.03730000000000189], [-0.3360000000000127, 0.03729999999998768], [0, -1.810666666666663], [0.6349999999999909, -0.07469999999999288], [0.4850000000000136, 0], [0, 0], [0, 0]], "o": [[-3.173000000000002, 0], [-1.754999999999995, -1.792000000000002], [0, -4.890666666666661], [-1.773333333333312, 0], [0, -1.903999999999996], [0.2799999999999727, 0], [1.419000000000096, 0], [0.7839999999999918, -0.8213000000000079], [0, -0.7839999999999989], [2.109333333333325, 0], [0, 2.314666666666668], [2.295999999999935, 0], [0, 1.903999999999996], [-2.295999999999935, 0], [0, 4.797333333333327], [0, 1.045299999999997], [0.3360000000000127, 0.7092999999999989], [0.7470000000000709, 0.3733000000000004], [0.2989999999999782, 0], [0.3729999999999336, -0.03730000000000189], [0, 1.810666666666663], [-0.5230000000000246, 0.0747000000000071], [-0.6349999999999909, 0.0747000000000071], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[78.71499999999997, 42.72800000000001], [71.32299999999998, 40.040000000000006], [68.69100000000003, 32.48], [68.69100000000003, 17.808000000000007], [63.37099999999998, 17.808000000000007], [63.37099999999998, 12.096], [64.21100000000001, 12.096], [67.51499999999999, 10.864], [68.69100000000003, 7.504000000000005], [68.69100000000003, 5.152000000000001], [75.019, 5.152000000000001], [75.019, 12.096], [81.90700000000004, 12.096], [81.90700000000004, 17.808000000000007], [75.019, 17.808000000000007], [75.019, 32.2], [75.52300000000002, 34.888000000000005], [77.14699999999999, 36.568], [80.05900000000003, 37.128], [81.06700000000001, 37.072], [82.13099999999997, 36.96000000000001], [82.13099999999997, 42.392], [80.39499999999998, 42.616], [78.71499999999997, 42.72800000000001], [78.71499999999997, 42.72800000000001], [78.71499999999997, 42.72800000000001]], "i": [[0, 0], [1.754000000000019, 1.792000000000002], [0, 3.248000000000005], [0, 4.890666666666661], [1.773333333333369, 0], [0, 1.903999999999996], [-0.2800000000000296, 0], [-0.7839999999999918, 0.8212999999999937], [0, 1.4187000000000012], [0, 0.7839999999999989], [-2.109333333333325, 0], [0, -2.314666666666668], [-2.295999999999992, 0], [0, -1.903999999999996], [2.295999999999992, 0], [0, -4.797333333333327], [-0.3360000000000127, -0.7467000000000041], [-0.7470000000000141, -0.4106999999999914], [-1.19500000000005, 0], [-0.3740000000000236, 0.03730000000000189], [-0.3360000000000127, 0.03729999999998768], [0, -1.810666666666663], [0.6340000000000146, -0.07469999999999288], [0.4850000000000136, 0], [0, 0], [0, 0]], "o": [[-3.173999999999978, 0], [-1.754999999999995, -1.792000000000002], [0, -4.890666666666661], [-1.773333333333369, 0], [0, -1.903999999999996], [0.2800000000000296, 0], [1.418000000000006, 0], [0.7839999999999918, -0.8213000000000079], [0, -0.7839999999999989], [2.109333333333325, 0], [0, 2.314666666666668], [2.295999999999992, 0], [0, 1.903999999999996], [-2.295999999999992, 0], [0, 4.797333333333327], [0, 1.045299999999997], [0.3359999999999559, 0.7092999999999989], [0.7460000000000377, 0.3733000000000004], [0.297999999999945, 0], [0.3730000000000473, -0.03730000000000189], [0, 1.810666666666663], [-0.5230000000000246, 0.0747000000000071], [-0.6349999999999909, 0.0747000000000071], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[44.18799999999999, 37.184], [48.94799999999998, 35.896], [52.19600000000003, 32.36800000000001], [53.428, 27.216000000000008], [52.19600000000003, 22.12], [48.94799999999998, 18.592], [44.18799999999999, 17.304], [39.428, 18.592], [36.12400000000002, 22.12], [34.94799999999998, 27.216000000000008], [36.12400000000002, 32.36800000000001], [39.428, 35.896], [44.18799999999999, 37.184], [44.18799999999999, 37.184], [44.18799999999999, 37.184]], "i": [[0, 0], [-1.381999999999948, 0.8586999999999989], [-0.7840000000000487, 1.493299999999991], [0, 1.903999999999996], [0.8209999999999695, 1.493299999999991], [1.381000000000029, 0.8586999999999989], [1.79200000000003, 0], [1.418000000000006, -0.8586999999999989], [0.8209999999999695, -1.493300000000005], [0, -1.904000000000011], [-0.7840000000000487, -1.5307000000000102], [-1.382000000000005, -0.8586999999999989], [-1.754999999999995, 0], [0, 0], [0, 0]], "o": [[1.79200000000003, 0], [1.381000000000029, -0.8586999999999989], [0.8209999999999695, -1.5307000000000102], [0, -1.904000000000011], [-0.7840000000000487, -1.493300000000005], [-1.381999999999948, -0.8586999999999989], [-1.754999999999995, 0], [-1.382000000000005, 0.8586999999999989], [-0.7840000000000487, 1.493299999999991], [0, 1.903999999999996], [0.8209999999999695, 1.493299999999991], [1.418000000000006, 0.8586999999999989], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[44.18799999999999, 43.06400000000001], [36.18000000000001, 40.992], [30.46800000000002, 35.336], [28.33999999999997, 27.216000000000008], [30.46800000000002, 19.096], [36.18000000000001, 13.49600000000001], [44.18799999999999, 11.424000000000007], [52.19600000000003, 13.49600000000001], [57.85199999999998, 19.096], [59.98000000000002, 27.216000000000008], [57.85199999999998, 35.392], [52.139999999999986, 41.048], [44.18799999999999, 43.06400000000001], [44.18799999999999, 43.06400000000001], [44.18799999999999, 43.06400000000001]], "i": [[0, 0], [2.425999999999988, 1.381299999999996], [1.418000000000006, 2.389300000000006], [0, 3.024000000000001], [-1.41900000000004, 2.352000000000004], [-2.389999999999986, 1.343999999999994], [-2.949999999999989, 0], [-2.352000000000032, -1.3813000000000102], [-1.381999999999948, -2.389300000000006], [0, -3.061300000000003], [1.418000000000006, -2.389299999999992], [2.38900000000001, -1.381299999999996], [2.912000000000035, 0], [0, 0], [0, 0]], "o": [[-2.911999999999978, 0], [-2.389999999999986, -1.381299999999996], [-1.41900000000004, -2.389299999999992], [0, -3.061300000000003], [1.418000000000006, -2.389300000000006], [2.38900000000001, -1.3813000000000102], [2.98599999999999, 0], [2.388999999999953, 1.343999999999994], [1.418000000000006, 2.352000000000004], [0, 3.061299999999989], [-1.418999999999983, 2.389300000000006], [-2.389999999999986, 1.343999999999994], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0, 42.392], [0, 0.6720000000000041], [6.608000000000004, 0.6720000000000041], [6.608000000000004, 36.512], [24.639999999999986, 36.512], [24.639999999999986, 42.392], [0, 42.392], [0, 42.392], [0, 42.392]], "i": [[0, 0], [0, 13.90666666666666], [-2.202666666666687, 0], [0, -11.94666666666666], [-6.01066666666668, 0], [0, -1.959999999999994], [8.21333333333331, 0], [0, 0], [0, 0]], "o": [[0, -13.90666666666667], [2.202666666666687, 0], [0, 11.94666666666667], [6.01066666666668, 0], [0, 1.959999999999994], [-8.21333333333331, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [98.08047485351562, -21.67217254638672], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [99.99999403953552, 99.99999403953552], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[246.681, 42.392], [246.681, 0], [253.009, 0], [253.009, 18.032], [252.001, 17.248], [255.585, 12.936000000000007], [261.297, 11.424000000000007], [267.23299999999995, 12.88], [271.265, 16.912000000000006], [272.721, 22.792], [272.721, 42.392], [266.449, 42.392], [266.449, 24.528000000000006], [265.553, 20.664], [263.201, 18.2], [259.729, 17.304], [256.25699999999995, 18.2], [253.849, 20.664], [253.009, 24.528000000000006], [253.009, 42.392], [246.681, 42.392], [246.681, 42.392], [246.681, 42.392]], "i": [[0, 0], [0, 14.13066666666667], [-2.109333333333325, 0], [0, -6.010666666666665], [0.3360000000000127, 0.2613333333333259], [-1.643000000000029, 0.9706999999999937], [-2.166000000000054, 0], [-1.717999999999961, -0.9706999999999937], [-0.9710000000000036, -1.717300000000009], [0, -2.202699999999993], [0, -6.533333333333331], [2.090666666666721, 0], [0, 5.954666666666668], [0.59699999999998, 1.045299999999997], [1.007999999999925, 0.5600000000000023], [1.305999999999926, 0], [1.045000000000073, -0.5973000000000042], [0.59699999999998, -1.082700000000003], [0, -1.493300000000005], [0, -5.954666666666668], [2.109333333333325, 0], [0, 0], [0, 0]], "o": [[0, -14.13066666666667], [2.109333333333325, 0], [0, 6.010666666666665], [-0.3360000000000127, -0.2613333333333259], [0.7459999999999809, -1.903999999999996], [1.641999999999967, -1.0080000000000098], [2.240000000000009, 0], [1.717000000000098, 0.9707000000000079], [0.9700000000000273, 1.717299999999994], [0, 6.533333333333331], [-2.090666666666721, 0], [0, -5.954666666666668], [0, -1.5307000000000102], [-0.5599999999999454, -1.082700000000003], [-1.008000000000038, -0.5973000000000042], [-1.270000000000095, 0], [-1.007999999999953, 0.5600000000000023], [-0.5600000000000023, 1.082700000000003], [0, 5.954666666666668], [-2.109333333333325, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-354.5325317382812, -77.50520324707031], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[237.089, 42.72800000000001], [229.697, 40.040000000000006], [227.065, 32.48], [227.065, 17.808000000000007], [221.745, 17.808000000000007], [221.745, 12.096], [222.585, 12.096], [225.889, 10.864], [227.065, 7.504000000000005], [227.065, 5.152000000000001], [233.393, 5.152000000000001], [233.393, 12.096], [240.281, 12.096], [240.281, 17.808000000000007], [233.393, 17.808000000000007], [233.393, 32.2], [233.897, 34.888000000000005], [235.521, 36.568], [238.433, 37.128], [239.441, 37.072], [240.505, 36.96000000000001], [240.505, 42.392], [238.769, 42.616], [237.089, 42.72800000000001], [237.089, 42.72800000000001], [237.089, 42.72800000000001]], "i": [[0, 0], [1.755000000000052, 1.792000000000002], [0, 3.248000000000005], [0, 4.890666666666661], [1.773333333333369, 0], [0, 1.903999999999996], [-0.2800000000000296, 0], [-0.7839999999999918, 0.8212999999999937], [0, 1.4187000000000012], [0, 0.7839999999999989], [-2.109333333333325, 0], [0, -2.314666666666668], [-2.295999999999992, 0], [0, -1.903999999999996], [2.295999999999992, 0], [0, -4.797333333333327], [-0.3360000000000127, -0.7467000000000041], [-0.7459999999999809, -0.4106999999999914], [-1.194000000000017, 0], [-0.3729999999999905, 0.03730000000000189], [-0.3360000000000127, 0.03729999999998768], [0, -1.810666666666663], [0.6350000000000477, -0.07469999999999288], [0.48599999999999, 0], [0, 0], [0, 0]], "o": [[-3.173000000000002, 0], [-1.753999999999962, -1.792000000000002], [0, -4.890666666666661], [-1.773333333333369, 0], [0, -1.903999999999996], [0.2800000000000296, 0], [1.418999999999983, 0], [0.7839999999999918, -0.8213000000000079], [0, -0.7839999999999989], [2.109333333333325, 0], [0, 2.314666666666668], [2.295999999999992, 0], [0, 1.903999999999996], [-2.295999999999992, 0], [0, 4.797333333333327], [0, 1.045299999999997], [0.3359999999999559, 0.7092999999999989], [0.7470000000000141, 0.3733000000000004], [0.2989999999999782, 0], [0.3740000000000236, -0.03730000000000189], [0, 1.810666666666663], [-0.5220000000000482, 0.0747000000000071], [-0.6339999999999577, 0.0747000000000071], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-354.5325317382812, -77.50520324707031], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[210.259, 7.951999999999998], [210.259, 0.6720000000000041], [216.587, 0.6720000000000041], [216.587, 7.951999999999998], [210.259, 7.951999999999998], [210.259, 7.951999999999998], [210.259, 7.951999999999998]], "i": [[0, 0], [0, 2.426666666666662], [-2.109333333333325, 0], [0, -2.426666666666662], [2.109333333333325, 0], [0, 0], [0, 0]], "o": [[0, -2.426666666666662], [2.109333333333325, 0], [0, 2.426666666666662], [-2.109333333333325, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-354.5325317382812, -77.50520324707031], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[210.259, 42.392], [210.259, 12.096], [216.587, 12.096], [216.587, 42.392], [210.259, 42.392], [210.259, 42.392], [210.259, 42.392]], "i": [[0, 0], [0, 10.09866666666667], [-2.109333333333325, 0], [0, -10.09866666666667], [2.109333333333325, 0], [0, 0], [0, 0]], "o": [[0, -10.09866666666666], [2.109333333333325, 0], [0, 10.09866666666666], [-2.109333333333325, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-354.5325317382812, -77.50520324707031], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[169.688, 42.392], [159.272, 12.096], [165.992, 12.096], [173.944, 36.232], [171.592, 36.232], [179.712, 12.096], [185.48, 12.096], [193.544, 36.232], [191.192, 36.232], [199.2, 12.096], [205.92, 12.096], [195.448, 42.392], [189.736, 42.392], [181.56, 17.696], [183.632, 17.696], [175.456, 42.392], [169.688, 42.392], [169.688, 42.392], [169.688, 42.392]], "i": [[0, 0], [3.47199999999998, 10.09866666666667], [-2.240000000000009, 0], [-2.650666666666666, -8.045333333333332], [0.7839999999999918, 0], [-2.706666666666649, 8.045333333333332], [-1.922666666666657, 0], [-2.687999999999988, -8.045333333333332], [0.7839999999999918, 0], [-2.669333333333327, 8.045333333333332], [-2.240000000000009, 0], [3.490666666666641, -10.09866666666667], [1.903999999999996, 0], [2.725333333333367, 8.232], [-0.6906666666666865, 0], [2.72533333333331, -8.232], [1.922666666666657, 0], [0, 0], [0, 0]], "o": [[-3.47199999999998, -10.09866666666666], [2.240000000000009, 0], [2.650666666666666, 8.045333333333332], [-0.7839999999999918, 0], [2.706666666666649, -8.045333333333332], [1.922666666666657, 0], [2.687999999999988, 8.045333333333332], [-0.7839999999999918, 0], [2.669333333333327, -8.045333333333332], [2.240000000000009, 0], [-3.490666666666641, 10.09866666666666], [-1.903999999999996, 0], [-2.725333333333367, -8.232], [0.6906666666666865, 0], [-2.72533333333331, 8.232], [-1.922666666666657, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-354.5325317382812, -77.50520324707031], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [155.86146545410156, 56.001014709472656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [99.99999403953552, 99.99999403953552], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[132.444, 43.06400000000001], [124.38, 40.992], [118.948, 35.28], [116.988, 27.16], [118.948, 19.040000000000006], [124.324, 13.49600000000001], [131.94, 11.424000000000007], [138.044, 12.600000000000009], [142.58, 15.848], [145.38, 20.49600000000001], [146.388, 26.096], [146.332, 27.608], [146.108, 29.064000000000007], [121.972, 29.064000000000007], [121.972, 24.024], [142.524, 24.024], [139.5, 26.320000000000007], [139.108, 21.448000000000008], [136.308, 18.032], [131.94, 16.744], [127.46, 18.032], [124.436, 21.616], [123.596, 27.216000000000008], [124.436, 32.592], [127.684, 36.176], [132.5, 37.464], [137.148, 36.232], [140.228, 33.040000000000006], [145.38, 35.56], [142.58, 39.42400000000001], [138.1, 42.11200000000001], [132.444, 43.06400000000001], [132.444, 43.06400000000001], [132.444, 43.06400000000001]], "i": [[0, 0], [2.351999999999975, 1.381299999999996], [1.305999999999983, 2.389300000000006], [0, 2.986699999999999], [-1.307000000000016, 2.35199999999999], [-2.240000000000009, 1.343999999999994], [-2.838000000000022, 0], [-1.79200000000003, -0.784000000000006], [-1.232000000000028, -1.381299999999996], [-0.6350000000000477, -1.754700000000014], [0, -1.978700000000003], [0.03699999999997772, -0.5227000000000004], [0.1120000000000232, -0.4480000000000075], [8.045333333333303, 0], [0, 1.680000000000007], [-6.850666666666655, 0], [1.007999999999981, -0.7653333333333308], [0.6340000000000146, 1.4187000000000012], [1.269000000000005, 0.8213000000000079], [1.67999999999995, 0], [1.305999999999983, -0.8586999999999989], [0.7090000000000032, -1.567999999999998], [-0.1499999999999773, -2.202700000000007], [-0.7470000000000141, -1.530699999999996], [-1.382000000000005, -0.8586999999999989], [-1.791999999999973, 0], [-1.269999999999982, 0.8213000000000079], [-0.7469999999999573, 1.306699999999992], [-1.717333333333329, -0.8400000000000034], [1.269000000000005, -1.157300000000006], [1.754000000000019, -0.6720000000000113], [2.052999999999997, 0], [0, 0], [0, 0]], "o": [[-3.024000000000001, 0], [-2.314999999999998, -1.4187000000000012], [-1.307000000000016, -2.426699999999997], [0, -3.061299999999989], [1.343999999999994, -2.352000000000004], [2.240000000000009, -1.3813000000000102], [2.276999999999987, 0], [1.791999999999973, 0.7839999999999918], [1.231999999999971, 1.344000000000008], [0.6719999999999686, 1.7547], [0, 0.4852999999999952], [-0.03800000000001091, 0.5227000000000004], [-8.045333333333303, 0], [0, -1.680000000000007], [6.850666666666655, 0], [-1.007999999999981, 0.7653333333333308], [0.3730000000000473, -1.829300000000003], [-0.5979999999999563, -1.456000000000003], [-1.232000000000028, -0.8586999999999989], [-1.680000000000007, 0], [-1.307000000000016, 0.8213000000000079], [-0.7100000000000364, 1.530699999999996], [-0.186999999999955, 2.053299999999993], [0.7839999999999918, 1.53070000000001], [1.418000000000006, 0.8586999999999989], [1.829000000000008, 0], [1.305999999999983, -0.8212999999999937], [1.717333333333329, 0.8400000000000034], [-0.5980000000000132, 1.4187000000000012], [-1.232000000000028, 1.11999999999999], [-1.718000000000018, 0.6346999999999952], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-211.7300415039062, -77.67320251464844], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[95.32, 37.184], [100.024, 35.896], [103.328, 32.36800000000001], [104.56, 27.216000000000008], [103.328, 22.12], [100.024, 18.592], [95.32, 17.304], [90.56, 18.592], [87.256, 22.12], [86.08000000000001, 27.216000000000008], [87.256, 32.36800000000001], [90.50399999999999, 35.896], [95.32, 37.184], [95.32, 37.184], [95.32, 37.184]], "i": [[0, 0], [-1.381, 0.8586999999999989], [-0.7839999999999918, 1.493299999999991], [0, 1.903999999999996], [0.820999999999998, 1.493299999999991], [1.419000000000011, 0.8586999999999989], [1.754999999999995, 0], [1.418999999999983, -0.8586999999999989], [0.7839999999999918, -1.493300000000005], [0, -1.904000000000011], [-0.7839999999999918, -1.5307000000000102], [-1.381, -0.8586999999999989], [-1.792000000000002, 0], [0, 0], [0, 0]], "o": [[1.754999999999995, 0], [1.419000000000011, -0.8586999999999989], [0.820999999999998, -1.5307000000000102], [0, -1.904000000000011], [-0.7839999999999918, -1.493300000000005], [-1.381, -0.8586999999999989], [-1.754999999999995, 0], [-1.419000000000011, 0.8586999999999989], [-0.7839999999999918, 1.493299999999991], [0, 1.903999999999996], [0.7839999999999918, 1.493299999999991], [1.419000000000011, 0.8586999999999989], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-211.7300415039062, -77.67320251464844], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[94.70400000000001, 43.06400000000001], [86.864, 40.992], [81.43199999999999, 35.28], [79.47200000000001, 27.216000000000008], [81.488, 19.152], [86.91999999999999, 13.49600000000001], [94.648, 11.424000000000007], [101.088, 12.88], [105.512, 16.912000000000006], [104.56, 18.36800000000001], [104.56, 0], [110.832, 0], [110.832, 42.392], [104.84, 42.392], [104.84, 36.232], [105.568, 37.408], [101.088, 41.608], [94.70400000000001, 43.06400000000001], [94.70400000000001, 43.06400000000001], [94.70400000000001, 43.06400000000001]], "i": [[0, 0], [2.314999999999998, 1.381299999999996], [1.344000000000023, 2.389300000000006], [0, 2.986699999999999], [-1.343999999999994, 2.389300000000006], [-2.276999999999987, 1.381299999999996], [-2.875, 0], [-1.8669999999999902, -0.9706999999999937], [-1.082999999999998, -1.717300000000009], [0.3173333333333233, -0.4853333333333296], [0, 6.122666666666674], [-2.090666666666664, 0], [0, -14.13066666666667], [1.99733333333333, 0], [0, 2.053333333333327], [-0.242666666666679, -0.3919999999999959], [1.941000000000003, -0.9707000000000079], [2.314999999999998, 0], [0, 0], [0, 0]], "o": [[-2.912000000000006, 0], [-2.277000000000015, -1.4187000000000012], [-1.306999999999988, -2.389300000000006], [0, -2.986699999999999], [1.343999999999994, -2.389300000000006], [2.277000000000015, -1.3813000000000102], [2.426999999999992, 0], [1.867000000000019, 0.9707000000000079], [-0.3173333333333233, 0.4853333333333296], [0, -6.122666666666674], [2.090666666666664, 0], [0, 14.13066666666667], [-1.99733333333333, 0], [0, -2.053333333333327], [0.242666666666679, 0.3919999999999959], [-1.045000000000016, 1.829300000000003], [-1.941000000000003, 0.9706999999999937], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-211.7300415039062, -77.67320251464844], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[57.40100000000001, 43.06400000000001], [51.968999999999994, 42], [48.38499999999999, 38.976], [47.09700000000001, 34.384], [48.161, 29.960000000000008], [51.577, 26.656000000000006], [57.345, 24.752], [67.87299999999999, 23.016000000000005], [67.87299999999999, 28], [58.577, 29.624], [54.881, 31.248], [53.70500000000001, 34.16], [54.992999999999995, 37.016000000000005], [58.40899999999999, 38.08], [62.88900000000001, 36.96000000000001], [65.91300000000001, 33.992], [66.977, 29.792], [66.977, 22.008], [65.297, 18.36800000000001], [60.929, 16.912000000000006], [56.505, 18.256], [53.761, 21.616], [48.496999999999986, 18.98400000000001], [51.240999999999985, 15.064000000000007], [55.66499999999999, 12.376], [61.09700000000001, 11.424000000000007], [67.42500000000001, 12.768], [71.737, 16.52], [73.305, 22.008], [73.305, 42.392], [67.257, 42.392], [67.257, 36.904], [68.54499999999999, 37.072], [65.80099999999999, 40.264], [62.04900000000001, 42.336], [57.40100000000001, 43.06400000000001], [57.40100000000001, 43.06400000000001], [57.40100000000001, 43.06400000000001]], "i": [[0, 0], [1.568000000000012, 0.7092999999999989], [0.8590000000000089, 1.2693000000000012], [0, 1.7547], [-0.7090000000000032, 1.306699999999992], [-1.531000000000006, 0.8960000000000008], [-2.314999999999998, 0.3733000000000004], [-3.509333333333331, 0.5786666666666633], [0, -1.661333333333332], [3.098666666666674, -0.541333333333327], [0.7839999999999918, -0.784000000000006], [0, -1.194699999999997], [-0.8590000000000089, -0.7467000000000041], [-1.381, 0], [-1.269000000000005, 0.7466999999999899], [-0.7090000000000032, 1.2319999999999993], [0, 1.530699999999996], [0, 2.594666666666669], [1.120000000000005, 0.9332999999999885], [1.829000000000008, 0], [1.269000000000005, -0.8960000000000008], [0.5970000000000084, -1.381299999999996], [1.754666666666679, 0.8773333333333255], [-1.268999999999977, 1.11999999999999], [-1.680000000000007, 0.6346999999999952], [-1.903999999999996, 0], [-1.829000000000008, -0.8960000000000008], [-1.0080000000000098, -1.6053], [0, -2.090699999999998], [0, -6.794666666666672], [2.015999999999991, 0], [0, 1.829333333333338], [-0.429333333333318, -0.05599999999999739], [1.120000000000005, -0.8959999999999866], [1.418999999999983, -0.4852999999999952], [1.716999999999985, 0], [0, 0], [0, 0]], "o": [[-2.053000000000026, 0], [-1.531000000000006, -0.7467000000000041], [-0.8589999999999804, -1.306699999999992], [0, -1.642700000000005], [0.7469999999999857, -1.306700000000006], [1.531000000000006, -0.8960000000000008], [3.509333333333331, -0.5786666666666633], [0, 1.661333333333332], [-3.098666666666674, 0.541333333333327], [-1.680000000000007, 0.2987000000000108], [-0.7839999999999918, 0.7467000000000041], [0, 1.157300000000006], [0.896000000000015, 0.7092999999999989], [1.717000000000013, 0], [1.306999999999988, -0.7467000000000041], [0.7089999999999748, -1.2693000000000012], [0, -2.594666666666669], [0, -1.493299999999991], [-1.082999999999998, -0.9707000000000079], [-1.680000000000007, 0], [-1.2319999999999993, 0.8586999999999989], [-1.754666666666679, -0.8773333333333255], [0.5600000000000023, -1.493300000000005], [1.269000000000005, -1.157300000000006], [1.717000000000013, -0.6347000000000094], [2.388999999999982, 0], [1.86699999999999, 0.8960000000000008], [1.045000000000016, 1.568000000000012], [0, 6.794666666666672], [-2.015999999999991, 0], [0, -1.829333333333338], [0.429333333333318, 0.05599999999999739], [-0.7089999999999748, 1.2319999999999993], [-1.082999999999998, 0.8960000000000008], [-1.381, 0.4853000000000094], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-211.7300415039062, -77.67320251464844], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[0, 42.392], [0, 0.6720000000000041], [6.159999999999997, 0.6720000000000041], [21.84, 22.400000000000006], [18.75999999999999, 22.400000000000006], [34.16, 0.6720000000000041], [40.31999999999999, 0.6720000000000041], [40.31999999999999, 42.392], [33.768, 42.392], [33.768, 8.456000000000003], [36.232, 9.128], [20.49600000000001, 30.632000000000005], [19.824000000000012, 30.632000000000005], [4.424000000000007, 9.128], [6.608000000000004, 8.456000000000003], [6.608000000000004, 42.392], [0, 42.392], [0, 42.392], [0, 42.392]], "i": [[0, 0], [0, 13.90666666666666], [-2.053333333333342, 0], [-5.226666666666659, -7.242666666666665], [1.026666666666671, 0], [-5.133333333333326, 7.242666666666672], [-2.053333333333342, 0], [0, -13.90666666666667], [2.183999999999997, 0], [0, 11.312], [-0.8213333333333424, -0.2240000000000038], [5.245333333333321, -7.168000000000006], [0.2239999999999895, 0], [5.133333333333326, 7.168000000000006], [-0.7280000000000086, 0.2240000000000038], [0, -11.312], [2.202666666666659, 0], [0, 0], [0, 0]], "o": [[0, -13.90666666666667], [2.053333333333342, 0], [5.226666666666659, 7.242666666666672], [-1.026666666666671, 0], [5.133333333333326, -7.242666666666665], [2.053333333333342, 0], [0, 13.90666666666666], [-2.183999999999997, 0], [0, -11.312], [0.8213333333333424, 0.2240000000000038], [-5.245333333333321, 7.168000000000006], [-0.2239999999999895, 0], [-5.133333333333326, -7.168000000000006], [0.7280000000000086, -0.2240000000000038], [0, 11.312], [-2.202666666666659, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [-211.7300415039062, -77.67320251464844], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [100, 100], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [2.91259765625, 56.001014709472656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [99.99999403953552, 99.99999403953552], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [702.6863719370097, 144], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 2}, "r": {"a": 0, "k": 72, "ix": 2}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 2}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 2}, "m": 1}, {"ty": "tr", "p": {"a": 0, "k": [56.54167175292969, -2.2762338630855083e-05], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 2}, "s": {"a": 0, "k": [99.99999403953552, 99.99999403953552], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 80, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}, {"ty": "tr", "p": {"a": 0, "k": [122.0000003294881, 25.00000012138912], "ix": 2}, "a": {"a": 0, "k": [56.54167175292969, -2.288818359375e-05], "ix": 2}, "s": {"a": 0, "k": [34.403572049765366, 34.403572049765366], "ix": 2}, "r": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "sk": {"a": 0, "k": 0, "ix": 2}, "sa": {"a": 0, "k": 0, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0, "bm": 0}, {"ddd": 0, "refId": "0", "w": 900, "h": 900, "ind": 195, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [1080, 399], "ix": 2}, "a": {"a": 0, "k": [450, 450], "ix": 2}, "s": {"a": 0, "k": [34, 34], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "3", "w": 1920, "h": 1080, "ind": 196, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [1088.12, 647.96], "ix": 2}, "a": {"a": 0, "k": [960, 540], "ix": 2}, "s": {"a": 0, "k": [37, 37], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "4", "w": 1920, "h": 1080, "ind": 197, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [364, 422], "ix": 2}, "a": {"a": 0, "k": [960, 540], "ix": 2}, "s": {"a": 0, "k": [15, 15], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "7", "w": 900, "h": 900, "ind": 198, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [866, 235.5], "ix": 2}, "a": {"a": 0, "k": [450, 450], "ix": 2}, "s": {"a": 0, "k": [30, 30], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "8", "w": 900, "h": 900, "ind": 199, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [885, 604], "ix": 2}, "a": {"a": 0, "k": [450, 450], "ix": 2}, "s": {"a": 0, "k": [50, 50], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "b", "w": 900, "h": 900, "ind": 200, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [448, 586], "ix": 2}, "a": {"a": 0, "k": [450, 450], "ix": 2}, "s": {"a": 0, "k": [50, 50], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "c", "w": 900, "h": 900, "ind": 201, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [682, 460], "ix": 2}, "a": {"a": 0, "k": [450, 450], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "z", "w": 1920, "h": 1080, "ind": 202, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [678, 728], "ix": 2}, "a": {"a": 0, "k": [960, 540], "ix": 2}, "s": {"a": 0, "k": [48, 48], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "A", "w": 700, "h": 700, "ind": 203, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [186.2, 640], "ix": 2}, "a": {"a": 0, "k": [350, 350], "ix": 2}, "s": {"a": 0, "k": [25, 25], "ix": 2}, "o": {"a": 0, "k": 15, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "B", "w": 700, "h": 700, "ind": 204, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [1132, 180], "ix": 2}, "a": {"a": 0, "k": [350, 350], "ix": 2}, "s": {"a": 0, "k": [34, 34], "ix": 2}, "o": {"a": 0, "k": 15, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "C", "w": 1920, "h": 1080, "ind": 205, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [481, 214], "ix": 2}, "a": {"a": 0, "k": [960, 540], "ix": 2}, "s": {"a": 0, "k": [25, 25], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "D", "w": 700, "h": 700, "ind": 206, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [285, 422], "ix": 2}, "a": {"a": 0, "k": [350, 350], "ix": 2}, "s": {"a": 0, "k": [30, 30], "ix": 2}, "r": {"a": 0, "k": -88, "ix": 2}, "o": {"a": 0, "k": 22, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "E", "w": 700, "h": 700, "ind": 207, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [758, 185], "ix": 2}, "a": {"a": 0, "k": [350, 350], "ix": 2}, "s": {"a": 0, "k": [9, 9], "ix": 2}, "r": {"a": 0, "k": -93, "ix": 2}, "o": {"a": 0, "k": 22, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "refId": "F", "w": 700, "h": 700, "ind": 208, "ty": 0, "sr": 1, "ks": {"p": {"a": 0, "k": [672, 91], "ix": 2}, "a": {"a": 0, "k": [350, 350], "ix": 2}, "s": {"a": 0, "k": [15, 15], "ix": 2}, "o": {"a": 0, "k": 22, "ix": 2}}, "ao": 0, "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 209, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[403.81, -480.68], [201.49, -391.14], [-84.48, 203.02], [-138.07, 204.54], [-234.81, 200.3], [-312.29, 196.06], [-455.49, 237.6], [-563.39, 480.68], [593.61, 480.68], [593.61, -409.36], [403.81, -480.68]], "i": [[0, 0], [71.212, -67.737], [185.927, -10.512], [16.729, 0], [28.377, 2.121], [23.933, 0], [53.152, -33.203], [0, 0], [0, 0], [0, 0], [110.128, 0]], "o": [[-60.899, 0], [-221.827, 211.002], [-19.061, 1.078], [-36.772, 0], [-28.376, -2.121], [-47.453, 0], [-138.11, 86.275], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.644, 0.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [722.1, 519.32], "ix": 2}, "a": {"a": 0, "k": [13.71, 0], "ix": 2}, "o": {"a": 0, "k": 10, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}, {"ddd": 0, "ind": 210, "ty": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "sh", "d": 1, "ks": {"a": 0, "k": {"c": true, "v": [[489.61, -210.02], [437.57, -203.35], [163.12, 36.42], [52.39, 45.45], [-164.21, 35.47], [-382.17, 25.48], [-503.18, 36.42], [-643.48, 210.02], [643.48, 210.02], [643.48, -143.01], [489.61, -210.02]], "i": [[0, 0], [18.327, -4.676], [138.11, -32.61], [44.516, 0], [76.574, 4.991], [64.588, 0], [29.539, -8.034], [0, 0], [0, 0], [0, 0], [94.672, 0]], "o": [[-16.335, 0], [-187.278, 47.786], [-27.886, 6.584], [-63.68, 0], [-76.571, -4.991], [-49.296, 0], [-134.001, 36.445], [0, 0], [0, 0], [0, 0], [0, 0]]}}}, {"ty": "fl", "c": {"a": 0, "k": [0, 0.644, 0.84], "ix": 2}, "o": {"a": 0, "k": 100, "ix": 2}, "r": 1, "bm": 0}, {"ty": "tr", "p": {"a": 0, "k": [658.52, 789.98], "ix": 2}, "o": {"a": 0, "k": 10, "ix": 2}}]}], "ip": 0, "op": 401, "st": 0}], "markers": []}