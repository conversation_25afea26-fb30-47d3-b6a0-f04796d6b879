import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/app_controller.dart';

class DeviceSetupController with ChangeNotifier {
  Future<void> deviceIdSetup() async {
    try {
      var deviceId = AppController.deviceId;
      var url = '/Counter/ManageDevice';
      var result = await Api.post(action: url, body: {"deviceId": deviceId});
      if (result?.isSuccess != null && result?.isSuccess == true) {
        LocaleManager.instance
            .setString(PreferencesKeys.DeviceCode, result?.data ?? "");
      }
    } catch (e) {
      print(e);
    }
  }
}
