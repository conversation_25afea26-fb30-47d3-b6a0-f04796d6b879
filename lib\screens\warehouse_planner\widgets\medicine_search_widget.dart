import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../providers/warehouse_planner_provider.dart';
import '../../../models/warehouse_planner/warehouse_layout.dart';
import '../../../models/warehouse_planner/editor_state.dart';

class MedicineSearchWidget extends StatefulWidget {
  const MedicineSearchWidget({Key? key}) : super(key: key);

  @override
  State<MedicineSearchWidget> createState() => _MedicineSearchWidgetState();
}

class _MedicineSearchWidgetState extends State<MedicineSearchWidget> {
  final TextEditingController _searchController = TextEditingController();
  List<SearchResult> _searchResults = [];
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _performSearch(String query) {
    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    final provider =
        Provider.of<WarehousePlannerProvider>(context, listen: false);
    final layout = provider.currentLayout;

    if (layout == null) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    List<SearchResult> results = [];
    final lowercaseQuery = query.toLowerCase();

    // البحث في جميع الخزائن والخانات
    for (final shelf in layout.shelves) {
      for (final bin in shelf.bins) {
        if (bin.productName != null) {
          bool matches = false;
          String matchType = '';

          // البحث في اسم المنتج
          if (bin.productName!.toLowerCase().contains(lowercaseQuery)) {
            matches = true;
            matchType = 'اسم المنتج';
          }

          // البحث في كود المنتج
          if (bin.productCode != null &&
              bin.productCode!.toLowerCase().contains(lowercaseQuery)) {
            matches = true;
            matchType = 'كود المنتج';
          }

          // البحث في الباركود
          if (bin.barcode != null &&
              bin.barcode!.toLowerCase().contains(lowercaseQuery)) {
            matches = true;
            matchType = 'الباركود';
          }

          if (matches) {
            results.add(SearchResult(
              shelf: shelf,
              bin: bin,
              matchType: matchType,
            ));
          }
        }
      }
    }

    setState(() {
      _searchResults = results;
      _isSearching = false;
    });
  }

  void _highlightResult(SearchResult result) {
    final provider =
        Provider.of<WarehousePlannerProvider>(context, listen: false);

    // تحديد الخزانة والخانة في الواجهة
    provider.selectObject(result.shelf.id, ObjectType.shelf);

    // عرض رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          '✨ تم العثور على ${result.bin.productName} في ${result.shelf.name} '
          'في المستوى ${result.bin.level + 1} الخانة ${result.bin.slot + 1}',
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // شريط البحث
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black12,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'ابحث عن دواء (الاسم، الكود، الباركود)',
              prefixIcon: _isSearching
                  ? const Padding(
                      padding: EdgeInsets.all(12),
                      child: SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        _performSearch('');
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.white,
            ),
            onChanged: _performSearch,
          ),
        ),

        const SizedBox(height: 12),

        // النتائج
        if (_searchResults.isNotEmpty) ...[
          Text(
            'نتائج البحث (${_searchResults.length})',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              itemCount: _searchResults.length,
              itemBuilder: (context, index) {
                final result = _searchResults[index];
                return _buildResultCard(result);
              },
            ),
          ),
        ] else if (_searchController.text.isNotEmpty && !_isSearching) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFFF8F9FA),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: const [
                Icon(Icons.info_outline, color: Color(0xFF6C757D)),
                SizedBox(width: 8),
                Text(
                  'لا توجد نتائج للبحث المطلوب',
                  style: TextStyle(color: Color(0xFF6C757D)),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildResultCard(SearchResult result) {
    final bin = result.bin;
    final shelf = result.shelf;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: bin.statusColor,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Text(
              bin.productCode?.substring(0, 2) ?? 'XX',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
        ),
        title: Text(
          bin.productName!,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📦 ${shelf.name} • L${bin.level + 1}-S${bin.slot + 1}',
              style: const TextStyle(fontSize: 12),
            ),
            Text(
              '🔍 تطابق في: ${result.matchType}',
              style: const TextStyle(
                fontSize: 11,
                color: Color(0xFF3498DB),
                fontWeight: FontWeight.bold,
              ),
            ),
            Row(
              children: [
                Text(
                  '📊 ${bin.quantity} ${bin.unitName ?? 'وحدة'}',
                  style: const TextStyle(fontSize: 11),
                ),
                const SizedBox(width: 16),
                if (bin.expiryDate != null) ...[
                  Icon(
                    Icons.schedule,
                    size: 12,
                    color: _getExpiryColor(bin.expiryDate!),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatExpiryDate(bin.expiryDate!),
                    style: TextStyle(
                      fontSize: 11,
                      color: _getExpiryColor(bin.expiryDate!),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton(
              icon: const Icon(Icons.location_on, color: Color(0xFF3498DB)),
              onPressed: () => _highlightResult(result),
              tooltip: 'إظهار الموقع',
            ),
          ],
        ),
        onTap: () => _highlightResult(result),
      ),
    );
  }

  Color _getExpiryColor(DateTime expiryDate) {
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate.difference(now).inDays;

    if (daysUntilExpiry <= 0) {
      return Colors.red;
    } else if (daysUntilExpiry <= 30) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  String _formatExpiryDate(DateTime expiryDate) {
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate.difference(now).inDays;

    if (daysUntilExpiry <= 0) {
      return 'منتهي الصلاحية';
    } else if (daysUntilExpiry <= 30) {
      return 'ينتهي خلال $daysUntilExpiry يوم';
    } else {
      return 'صالح حتى ${expiryDate.toString().substring(0, 10)}';
    }
  }
}

class SearchResult {
  final Shelf shelf;
  final Bin bin;
  final String matchType;

  SearchResult({
    required this.shelf,
    required this.bin,
    required this.matchType,
  });
}
