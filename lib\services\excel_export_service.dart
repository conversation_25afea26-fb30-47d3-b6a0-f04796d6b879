import 'dart:io';
import 'package:flutter/material.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'package:intl/intl.dart';

/// خدمة تصدير التقارير إلى Excel
class ExcelExportService {
  /// تصدير تقرير عام إلى Excel
  static Future<void> exportGenericReport({
    required BuildContext context,
    required String reportTitle,
    required List<Map<String, dynamic>> data,
    required List<String> headers,
    String? fileName,
    DateTime? fromDate,
    DateTime? toDate,
    List<Map<String, String>>? customFields,
  }) async {
    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('جاري تصدير التقرير...'),
            ],
          ),
        );
      },
    );

    try {
      if (data.isEmpty) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد بيانات للتصدير'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // طلب الصلاحيات
      var status = await Permission.storage.request();
      if (!status.isGranted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى منح صلاحية الوصول للتخزين'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // إنشاء ملف Excel
      var excel = Excel.createExcel();
      Sheet sheet = excel[reportTitle];

      int currentRow = 0;

      // إضافة عنوان التقرير
      var titleCell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      titleCell.value = TextCellValue(reportTitle);
      titleCell.cellStyle = CellStyle(
        bold: true,
        fontSize: 18,
        horizontalAlign: HorizontalAlign.Center,
        fontColorHex: ExcelColor.blue700,
      );
      currentRow++;

      // إضافة معلومات الفترة
      if (fromDate != null || toDate != null) {
        var periodCell = sheet.cell(
            CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
        String periodText = '';
        if (fromDate != null && toDate != null) {
          periodText =
              'الفترة: من ${DateFormat('dd/MM/yyyy').format(fromDate)} إلى ${DateFormat('dd/MM/yyyy').format(toDate)}';
        } else if (fromDate != null) {
          periodText = 'من تاريخ: ${DateFormat('dd/MM/yyyy').format(fromDate)}';
        } else if (toDate != null) {
          periodText = 'إلى تاريخ: ${DateFormat('dd/MM/yyyy').format(toDate)}';
        }
        periodCell.value = TextCellValue(periodText);
        periodCell.cellStyle = CellStyle(
          fontSize: 12,
          horizontalAlign: HorizontalAlign.Center,
          fontColorHex: ExcelColor.grey700,
        );
        currentRow++;
      }

      // إضافة تاريخ التصدير
      var exportDateCell = sheet.cell(
          CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      exportDateCell.value = TextCellValue(
          'تاريخ التصدير: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}');
      exportDateCell.cellStyle = CellStyle(
        fontSize: 10,
        horizontalAlign: HorizontalAlign.Center,
        fontColorHex: ExcelColor.grey700,
      );
      currentRow += 2;
      if (customFields != null) {
        for (var field in customFields) {
          var fieldCell = sheet.cell(
              CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
          fieldCell.value =
              TextCellValue('${field['label']}: ${field['value']}');
          fieldCell.cellStyle = CellStyle(
            fontSize: 12,
            horizontalAlign: HorizontalAlign.Center,
            fontColorHex: ExcelColor.grey700,
          );
          currentRow++;
        }
        currentRow++;
      }

      // إضافة رؤوس الأعمدة
      for (int colIndex = 0; colIndex < headers.length; colIndex++) {
        var headerCell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex,
          rowIndex: currentRow,
        ));
        headerCell.value = TextCellValue(headers[colIndex]);
        headerCell.cellStyle = CellStyle(
          bold: true,
          backgroundColorHex: ExcelColor.grey200,
          horizontalAlign: HorizontalAlign.Center,
          fontColorHex: ExcelColor.grey800,
        );
      }
      currentRow++;

      // إضافة البيانات
      for (int rowIndex = 0; rowIndex < data.length; rowIndex++) {
        final rowData = data[rowIndex];

        for (int colIndex = 0; colIndex < headers.length; colIndex++) {
          var cell = sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: currentRow,
          ));

          String key = headers[colIndex];
          dynamic value = rowData[key] ?? '';
          cell.value = TextCellValue(value.toString());

          // تنسيق خاص للأرقام
          if (value is num) {
            cell.cellStyle = CellStyle(
              horizontalAlign: HorizontalAlign.Center,
              fontColorHex: ExcelColor.green700,
              bold: true,
            );
          } else {
            cell.cellStyle = CellStyle(
              horizontalAlign: HorizontalAlign.Right,
            );
          }
        }
        currentRow++;
      }

      // تحسين تنسيق الأعمدة للطباعة
      _optimizeColumnsForPrinting(sheet, headers.length);

      // إعداد صفحة الطباعة
      _setupPrintArea(sheet, currentRow, headers.length);

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final finalFileName =
          fileName ?? 'تقرير_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${directory.path}/$finalFileName';

      List<int>? fileBytes = excel.save();
      if (fileBytes != null) {
        File file = File(filePath);
        await file.writeAsBytes(fileBytes);

        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        // مشاركة الملف
        await Share.shareXFiles(
          [XFile(filePath)],
          text:
              'تقرير $reportTitle - ${DateTime.now().toString().split(' ')[0]}',
          subject: 'تقرير $reportTitle',
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('✅ تم تصدير التقرير بنجاح!'),
                const SizedBox(height: 4),
                Text('📄 $finalFileName', style: const TextStyle(fontSize: 12)),
                const SizedBox(height: 4),
                Text('📊 عدد الصفوف: ${data.length}',
                    style: const TextStyle(fontSize: 11)),
                const SizedBox(height: 4),
                Text('🖨️ مُحسّن للطباعة على A4',
                    style: const TextStyle(fontSize: 11, color: Colors.blue)),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// تحسين عرض الأعمدة للطباعة
  static void _optimizeColumnsForPrinting(Sheet sheet, int columnCount) {
    // تقليل عرض الأعمدة لتناسب A4 بشكل أفضل
    // استخدام عرض أضيق لكل عمود
    double baseWidth = 2.0; // عرض أساسي أضيق

    // تطبيق العرض على كل الأعمدة
    for (int i = 0; i < columnCount; i++) {
      // استخدام عرض ثابت أضيق لكل عمود
      double widthInUnits = baseWidth * 8; // تحويل إلى Excel units

      // تعيين عرض العمود
      sheet.setColumnWidth(i, widthInUnits);
    }
  }

  /// إعداد منطقة الطباعة
  static void _setupPrintArea(Sheet sheet, int lastRow, int lastColumn) {
    // ملاحظة: مكتبة Excel الحالية لا تدعم إعدادات الطباعة المتقدمة
    // سيتم الاعتماد على تحسين عرض الأعمدة فقط
    debugPrint('تم تحسين عرض الأعمدة للطباعة على A4');
  }

  /// تحويل رقم العمود إلى حرف (A, B, C, ...)
  static String _getColumnLetter(int columnIndex) {
    String result = '';
    while (columnIndex >= 0) {
      result = String.fromCharCode(65 + (columnIndex % 26)) + result;
      columnIndex = (columnIndex ~/ 26) - 1;
    }
    return result;
  }

  /// تصدير تقرير المبيعات إلى Excel
  static Future<void> exportSalesReport({
    required BuildContext context,
    required List<Map<String, dynamic>> salesData,
    required String reportTitle,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    List<String> headers = [
      'التاريخ',
      'رقم الفاتورة',
      'العميل',
      'المنتجات',
      'المبلغ الإجمالي',
      'طريقة الدفع',
      'المستخدم',
    ];

    await exportGenericReport(
      context: context,
      reportTitle: reportTitle,
      data: salesData,
      headers: headers,
      fileName: 'تقرير_المبيعات_${DateTime.now().millisecondsSinceEpoch}.xlsx',
      fromDate: fromDate,
      toDate: toDate,
    );
  }

  /// تصدير تقرير المخزون إلى Excel
  static Future<void> exportInventoryReport({
    required BuildContext context,
    required List<Map<String, dynamic>> inventoryData,
    required String reportTitle,
  }) async {
    List<String> headers = [
      'اسم المنتج',
      'الرمز',
      'الكمية المتوفرة',
      'الكمية المباعة',
      'سعر البيع',
      'التكلفة',
      'الربح',
      'آخر تحديث',
    ];

    await exportGenericReport(
      context: context,
      reportTitle: reportTitle,
      data: inventoryData,
      headers: headers,
      fileName: 'تقرير_المخزون_${DateTime.now().millisecondsSinceEpoch}.xlsx',
    );
  }

  /// تصدير تقرير العملاء إلى Excel
  static Future<void> exportCustomersReport({
    required BuildContext context,
    required List<Map<String, dynamic>> customersData,
    required String reportTitle,
  }) async {
    List<String> headers = [
      'اسم العميل',
      'رقم الهاتف',
      'البريد الإلكتروني',
      'العنوان',
      'عدد المشتريات',
      'إجمالي المشتريات',
      'تاريخ التسجيل',
    ];

    await exportGenericReport(
      context: context,
      reportTitle: reportTitle,
      data: customersData,
      headers: headers,
      fileName: 'تقرير_العملاء_${DateTime.now().millisecondsSinceEpoch}.xlsx',
    );
  }

  /// تصدير تقرير تفاصيل الكارت إلى Excel
  static Future<void> exportCardDetailReport({
    required BuildContext context,
    required Map<String, dynamic> cardInfo,
    required Map<String, dynamic> balanceSummary,
    required List<Map<String, dynamic>> transactions,
    required String cardNumber,
  }) async {
    // إنشاء ملف Excel منفصل
    var excel = Excel.createExcel();
    Sheet sheet = excel['تقرير تفاصيل الكارت'];

    int currentRow = 0;

    // إضافة عنوان التقرير
    var titleCell = sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
    titleCell.value = TextCellValue('تقرير تفاصيل الكارت - $cardNumber');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 18,
      horizontalAlign: HorizontalAlign.Center,
      fontColorHex: ExcelColor.blue700,
    );
    currentRow += 2;

    // إضافة معلومات الكارت
    _addCardInfo(sheet, currentRow, cardInfo);
    currentRow += 6;

    // إضافة الملخص المالي في أعمدة منفصلة
    _addFinancialSummary(sheet, currentRow, balanceSummary);
    currentRow += 3;

    // إضافة عنوان المعاملات
    var transactionsTitleCell = sheet
        .cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
    transactionsTitleCell.value = TextCellValue('=== تفاصيل المعاملات ===');
    transactionsTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      horizontalAlign: HorizontalAlign.Center,
      fontColorHex: ExcelColor.blue600,
    );
    currentRow += 2;

    // إضافة رؤوس أعمدة المعاملات
    List<String> transactionHeaders = [
      'التاريخ',
      'نوع المعاملة',
      'المبلغ',
      'الرصيد بعد المعاملة',
      'المشرف/المتجر',
      'الشركة',
    ];

    for (int colIndex = 0; colIndex < transactionHeaders.length; colIndex++) {
      var headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: colIndex,
        rowIndex: currentRow,
      ));
      headerCell.value = TextCellValue(transactionHeaders[colIndex]);
      headerCell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.grey200,
        horizontalAlign: HorizontalAlign.Center,
        fontColorHex: ExcelColor.grey800,
      );
    }
    currentRow++;

    // إضافة المعاملات
    for (var transaction in transactions) {
      final isRecharge = transaction['type'] == 'recharge';

      List<String> rowData = [
        transaction['date'] ?? '',
        isRecharge ? 'شحن' : 'صرف',
        transaction['amount']?.toString() ?? '0',
        transaction['balanceAfter']?.toString() ?? '0',
        isRecharge
            ? (transaction['admin'] ?? '')
            : (transaction['store'] ?? ''),
        isRecharge ? '' : (transaction['company'] ?? ''),
      ];

      for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
        var cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: colIndex,
          rowIndex: currentRow,
        ));
        cell.value = TextCellValue(rowData[colIndex]);

        // تنسيق خاص للمبالغ
        if (colIndex == 2 || colIndex == 3) {
          cell.cellStyle = CellStyle(
            horizontalAlign: HorizontalAlign.Center,
            fontColorHex: ExcelColor.green700,
            bold: true,
          );
        } else {
          cell.cellStyle = CellStyle(
            horizontalAlign: HorizontalAlign.Right,
          );
        }
      }
      currentRow++;
    }

    // تحسين عرض الأعمدة
    _optimizeColumnsForPrinting(sheet, transactionHeaders.length);

    // حفظ الملف
    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        'تقرير_تفاصيل_الكارت_${cardNumber}_${DateTime.now().millisecondsSinceEpoch}.xlsx';
    final filePath = '${directory.path}/$fileName';

    List<int>? fileBytes = excel.save();
    if (fileBytes != null) {
      File file = File(filePath);
      await file.writeAsBytes(fileBytes);

      // مشاركة الملف
      await Share.shareXFiles(
        [XFile(filePath)],
        text:
            'تقرير تفاصيل الكارت $cardNumber - ${DateTime.now().toString().split(' ')[0]}',
        subject: 'تقرير تفاصيل الكارت - $cardNumber',
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('✅ تم تصدير التقرير بنجاح!'),
              const SizedBox(height: 4),
              Text('📄 $fileName', style: const TextStyle(fontSize: 12)),
              const SizedBox(height: 4),
              Text('📊 عدد المعاملات: ${transactions.length}',
                  style: const TextStyle(fontSize: 11)),
              const SizedBox(height: 4),
              Text('🖨️ مُحسّن للطباعة على A4',
                  style: const TextStyle(fontSize: 11, color: Colors.blue)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 5),
        ),
      );
    }
  }

  /// إضافة معلومات الكارت
  static void _addCardInfo(
      Sheet sheet, int startRow, Map<String, dynamic> cardInfo) {
    List<Map<String, String>> cardData = [
      {
        'المعلومة': 'رقم الكارت',
        'القيمة': cardInfo['cardNumber'] ?? 'غير محدد'
      },
      {'المعلومة': 'اسم العضو', 'القيمة': cardInfo['memberName'] ?? 'غير محدد'},
      {'المعلومة': 'رقم الجوال', 'القيمة': cardInfo['mobile'] ?? 'غير محدد'},
      {
        'المعلومة': 'تاريخ التسجيل',
        'القيمة': cardInfo['memberSince'] ?? 'غير محدد'
      },
      {
        'المعلومة': 'الرصيد الحالي',
        'القيمة': cardInfo['currentBalance']?.toString() ?? '0'
      },
      {
        'المعلومة': 'حالة العضو',
        'القيمة': cardInfo['memberState'] ?? 'غير محدد'
      },
    ];

    for (int i = 0; i < cardData.length; i++) {
      var infoCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 0,
        rowIndex: startRow + i,
      ));
      infoCell.value = TextCellValue(cardData[i]['المعلومة']!);
      infoCell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.grey100,
        horizontalAlign: HorizontalAlign.Right,
      );

      var valueCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 1,
        rowIndex: startRow + i,
      ));
      valueCell.value = TextCellValue(cardData[i]['القيمة']!);
      valueCell.cellStyle = CellStyle(
        horizontalAlign: HorizontalAlign.Right,
        fontColorHex: ExcelColor.blue700,
      );
    }
  }

  /// إضافة الملخص المالي في أعمدة منفصلة
  static void _addFinancialSummary(
      Sheet sheet, int startRow, Map<String, dynamic> balanceSummary) {
    // عنوان الملخص المالي
    var summaryTitleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: startRow,
    ));
    summaryTitleCell.value = TextCellValue('الملخص المالي');
    summaryTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: 14,
      horizontalAlign: HorizontalAlign.Center,
      fontColorHex: ExcelColor.blue600,
    );

    // رؤوس الأعمدة للملخص المالي
    List<String> summaryHeaders = [
      'إجمالي الشحن',
      'إجمالي الإنفاق',
      'إجمالي الهدايا',
      'معاملات الشحن',
    ];

    for (int colIndex = 0; colIndex < summaryHeaders.length; colIndex++) {
      var headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: colIndex,
        rowIndex: startRow + 1,
      ));
      headerCell.value = TextCellValue(summaryHeaders[colIndex]);
      headerCell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.orange200,
        horizontalAlign: HorizontalAlign.Center,
        fontColorHex: ExcelColor.orange800,
      );
    }

    // قيم الملخص المالي
    List<String> summaryValues = [
      balanceSummary['totalRechargeAmount']?.toString() ?? '0',
      balanceSummary['totalSpentAmount']?.toString() ?? '0',
      balanceSummary['totalGiftAmount']?.toString() ?? '0',
      balanceSummary['totalRechargeTransactions']?.toString() ?? '0',
    ];

    for (int colIndex = 0; colIndex < summaryValues.length; colIndex++) {
      var valueCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: colIndex,
        rowIndex: startRow + 2,
      ));
      valueCell.value = TextCellValue(summaryValues[colIndex]);
      valueCell.cellStyle = CellStyle(
        horizontalAlign: HorizontalAlign.Center,
        fontColorHex: ExcelColor.green700,
        bold: true,
        fontSize: 12,
      );
    }
  }
}
