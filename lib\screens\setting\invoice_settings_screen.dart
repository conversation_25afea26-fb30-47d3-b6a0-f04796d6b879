import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/unit_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/customer_model.dart';
import 'package:inventory_application/screens/components/back_button_header.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';

class InvoiceSettingsScreen extends StatefulWidget {
  const InvoiceSettingsScreen({super.key});

  @override
  State<InvoiceSettingsScreen> createState() => _InvoiceSettingsScreenState();
}

class _InvoiceSettingsScreenState extends State<InvoiceSettingsScreen> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initControllers();
  }

  Future<void> _initControllers() async {
    setState(() {
      _isLoading = true;
    });

    final customerController =
        Provider.of<CustomerController>(context, listen: false);
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);
    final unitController = Provider.of<UnitController>(context, listen: false);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context, listen: false);

    // Reload invoice settings for the current branch
    await invoiceSettingsController.reloadSettingsForCurrentBranch();

    // Load customers if not already loaded
    if (customerController.customers.isEmpty) {
      await customerController.fetchCustomersFromServer();
    }

    // Load warehouses if not already loaded
    if (warehouseController.warehouses.isEmpty) {
      await warehouseController.fetchWarehouses();
    }

    // Load units if not already loaded
    if (unitController.units.isEmpty) {
      await unitController.fetchUnits();
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          BackButtonHeader(
            title: T('Invoice Settings'),
            icon: Icons.settings,
          ),
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        _buildSettingsHeader(),
                        const SizedBox(height: 24),
                        _buildDefaultCustomerSection(),
                        const SizedBox(height: 24),
                        _buildDefaultWarehouseSection(),
                        const SizedBox(height: 24),
                        _buildDefaultUnitSection(),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
      decoration: BoxDecoration(
        color: context.newPrimaryColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: context.newPrimaryColor.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.receipt_long,
                  size: 25,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      T("Invoice Defaults"),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      T("Set default values for POS and invoices"),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.business,
                  color: Colors.white.withOpacity(0.9),
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    T("Current Branch: ") + (AppController.currentBranchName),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    "ID: ${AppController.currentBranchId}",
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultCustomerSection() {
    final customerController = Provider.of<CustomerController>(context);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context);

    // Get selected customer
    CustomerModel? selectedCustomer;
    if (invoiceSettingsController.defaultCustomerId != null) {
      try {
        selectedCustomer = customerController.customers.firstWhere(
          (customer) =>
              customer.iD == invoiceSettingsController.defaultCustomerId,
        );
      } catch (e) {
        // Customer not found - might have been deleted or not loaded yet
      }
    }

    return _buildSettingSection(
      icon: Icons.person,
      title: T('Default Customer'),
      content: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              T('Select the customer that will be used by default in POS and Sales Invoice screens'),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: _buildCustomerSelector(context, selectedCustomer),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () async {
                    // Clear default customer
                    await invoiceSettingsController.clearDefaultCustomer();
                  },
                  icon: const Icon(Icons.clear),
                  label: Text(T('Clear Default')),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDefaultWarehouseSection() {
    final warehouseController = Provider.of<WarehouseController>(context);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context);

    // Get selected warehouse
    ComboBoxDataModel? selectedWarehouse;
    if (invoiceSettingsController.defaultWarehouseId != null) {
      try {
        selectedWarehouse = warehouseController.warehouses.firstWhere(
          (warehouse) =>
              warehouse.id == invoiceSettingsController.defaultWarehouseId,
        );
      } catch (e) {
        // Warehouse not found - might have been deleted or not loaded yet
      }
    }

    return _buildSettingSection(
      icon: Icons.store,
      title: T('Default Warehouse'),
      content: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              T('Select the warehouse that will be used by default in POS and Sales Invoice screens'),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: _buildWarehouseSelector(context, selectedWarehouse),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () async {
                    // Clear default warehouse
                    await invoiceSettingsController.clearDefaultWarehouse();
                  },
                  icon: const Icon(Icons.clear),
                  label: Text(T('Clear Default')),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingSection({
    required IconData icon,
    required String title,
    required Widget content,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          colorScheme: ColorScheme.light(
            primary: context.newPrimaryColor,
          ),
        ),
        child: ExpansionTile(
          initiallyExpanded: true,
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: context.newPrimaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: context.newPrimaryColor,
              size: 22,
            ),
          ),
          title: Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: context.newTextColor,
            ),
          ),
          children: [
            Container(
              decoration: BoxDecoration(
                color: context.newBackgroundColor.withOpacity(0.5),
              ),
              child: content,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomerSelector(
      BuildContext context, CustomerModel? selectedCustomer) {
    final customerController = Provider.of<CustomerController>(context);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context, listen: false);

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: selectedCustomer?.iD,
          isExpanded: true,
          hint: Text(T('Select Default Customer')),
          icon: const Icon(Icons.arrow_drop_down),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          items: customerController.customers.map((customer) {
            return DropdownMenuItem(
              value: customer.iD,
              child: Text(
                customer.name ?? T('Unknown'),
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (int? id) async {
            if (id != null) {
              final selectedCustomer = customerController.customers.firstWhere(
                (customer) => customer.iD == id,
              );

              await invoiceSettingsController.setDefaultCustomer(
                id,
                selectedCustomer.name ?? T('Unknown'),
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildWarehouseSelector(
      BuildContext context, ComboBoxDataModel? selectedWarehouse) {
    final warehouseController = Provider.of<WarehouseController>(context);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context, listen: false);

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: selectedWarehouse?.id,
          isExpanded: true,
          hint: Text(T('Select Default Warehouse')),
          icon: const Icon(Icons.arrow_drop_down),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          items: warehouseController.warehouses.map((warehouse) {
            return DropdownMenuItem(
              value: warehouse.id,
              child: Text(
                warehouse.name,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (int? id) async {
            if (id != null) {
              final selectedWarehouse =
                  warehouseController.warehouses.firstWhere(
                (warehouse) => warehouse.id == id,
              );

              await invoiceSettingsController.setDefaultWarehouse(
                id,
                selectedWarehouse.name,
              );
            }
          },
        ),
      ),
    );
  }

  Widget _buildDefaultUnitSection() {
    final unitController = Provider.of<UnitController>(context);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context);

    // Get selected unit
    ComboBoxDataModel? selectedUnit;
    if (invoiceSettingsController.defaultUnitId != null) {
      try {
        selectedUnit = unitController.units.firstWhere(
          (unit) => unit.id == invoiceSettingsController.defaultUnitId,
        );
      } catch (e) {
        // Unit not found - might have been deleted or not loaded yet
      }
    }

    return _buildSettingSection(
      icon: Icons.straighten,
      title: T('Default Unit'),
      content: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              T('Select the unit that will be used by default when adding products to invoices'),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: _buildUnitSelector(context, selectedUnit),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () async {
                    // Clear default unit
                    await invoiceSettingsController.clearDefaultUnit();
                  },
                  icon: const Icon(Icons.clear),
                  label: Text(T('Clear Default')),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnitSelector(
      BuildContext context, ComboBoxDataModel? selectedUnit) {
    final unitController = Provider.of<UnitController>(context);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context, listen: false);

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<int>(
          value: selectedUnit?.id,
          isExpanded: true,
          hint: Text(T('Select Default Unit')),
          icon: const Icon(Icons.arrow_drop_down),
          padding: const EdgeInsets.symmetric(horizontal: 12),
          items: unitController.units.map((unit) {
            return DropdownMenuItem(
              value: unit.id,
              child: Text(
                unit.name,
                overflow: TextOverflow.ellipsis,
              ),
            );
          }).toList(),
          onChanged: (int? id) async {
            if (id != null) {
              final selectedUnit = unitController.units.firstWhere(
                (unit) => unit.id == id,
              );

              await invoiceSettingsController.setDefaultUnit(
                id,
                selectedUnit.name,
              );
            }
          },
        ),
      ),
    );
  }
}
