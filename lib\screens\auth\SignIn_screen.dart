import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/branch_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/auth/login_dto.dart';
import 'package:inventory_application/screens/auth/%C4%B0magebgWidget.dart';
import 'package:inventory_application/screens/auth/widget/custom_input_field.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:provider/provider.dart';

class SignInScreen extends StatefulWidget {
  const SignInScreen({super.key});

  @override
  State<SignInScreen> createState() => _SignInScreenState();
}

class _SignInScreenState extends State<SignInScreen> {
  bool isRememberMeChecked = false;
  final TextEditingController usernameController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  var model = LogInDto();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<BranchController>(context, listen: false).fetchBranches();
    });
  }

  @override
  Widget build(BuildContext context) {
    if (Provider.of<BranchController>(context).branches.isNotEmpty) {
      if (model.currentBranchID == null) {
        model.currentBranchID =
            Provider.of<BranchController>(context).branches.first.id;

        model.currentBranchName =
            Provider.of<BranchController>(context).branches.first.name;
        setState(() {});
      }
    }
    return Container(
      height: context.height,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Color(0xFF56A6ED), Color(0xFF895FF9)],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: SingleChildScrollView(
          child: Column(
            children: [
              ImageBgWidget(),
              const SizedBox(
                height: 30,
              ),

              Text(
                "أهلا بعودتك",
                style: TextStyle(
                    color: context.backgroundColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 28),
              ),
              const SizedBox(
                height: 10,
              ),
              Text(
                "قم بتسجيل الدخول الى حسابك",
                style: TextStyle(color: context.backgroundColor, fontSize: 12),
              ),
              const SizedBox(
                height: 30,
              ),
              CustomInputField(
                controller: usernameController,
                icon: Icons.person,
                hintText: "اسم المستخدم",
                onChange: (value) {
                  setState(() {
                    model.usernameOrEmail = value;
                  });
                },
              ),

              const SizedBox(
                height: 10,
              ),
              CustomInputField(
                icon: Icons.lock,
                hintText: "كلمة المرور",
                isPassword: true,
                controller: passwordController,
                onChange: (value) {
                  setState(() {
                    model.password = value;
                  });
                },
              ),
              const SizedBox(
                height: 10,
              ),
              MyComboBox(
                width: context.width - 30,
                fontColor: Colors.white,
                backColor: Colors.white.withOpacity(0.2),
                caption: model.currentBranchName ?? T("Branch"),
                height: 50,
                selectedValue: model.currentBranchID,
                onSelect: (int id, String name) {
                  model.currentBranchID = id;
                  model.currentBranchName = name;
                  AppController.currentBranchId = id;
                  AppController.currentBranchName = name;
                  setState(() {});
                },
                onRefresh: () {
                  print("askjhdkljashjdkiasd");
                  Provider.of<BranchController>(context, listen: false)
                      .fetchBranches();
                  Navigator.of(context).pop();
                },
                modalTitle: T("Branch"),
                data: Provider.of<BranchController>(context).branches,
                isShowLabel: true,
                labelText: "asdasdsa",
                isSmallLookup: false,
                // borderWidth: 5,
              ),
              const SizedBox(
                height: 5,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Checkbox(
                          value: isRememberMeChecked,
                          onChanged: (bool? value) {
                            setState(() {
                              isRememberMeChecked = value ?? false;
                            });
                          },
                          activeColor: Colors.white, // Checkbox border color
                          checkColor: Colors.blue.shade700, // Tick color
                        ),
                        const Text(
                          "تذكرني",
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    // InkWell(
                    //   onTap: () {},
                    //   child: const Text(
                    //     "نسيت كلمة المرور؟",
                    //     style: TextStyle(
                    //       color: Colors.white,
                    //       fontSize: 14,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ),
              const SizedBox(
                height: 45,
              ),
              InkWell(
                onTap: () async {
                  if (model.currentBranchID == null) {
                    errorSnackBar(message: "Please select branch");
                    return;
                  }
                  if (AppController.isThereConnection == true) {
                    await Provider.of<AuthController>(context, listen: false)
                        .login(context: context, model: model);
                  } else {
                    await Provider.of<AuthController>(context, listen: false)
                        .loginLocaly(context: context, model: model);
                  }
                  await Provider.of<AuthController>(context, listen: false)
                      .login(context: context, model: model);
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 15, vertical: 12),
                    width: context.width,
                    decoration: BoxDecoration(
                      color: Colors.blue.shade900,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.4),
                          spreadRadius: 2,
                          blurRadius: 5,
                          offset: const Offset(0, 3),
                        ),
                      ],
                      borderRadius:
                          BorderRadius.circular(25), // Rounded corners
                    ),
                    child: const Center(
                      child: Text(
                        "تسجيل الدخول",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              Container(
                padding: const EdgeInsets.all(8),
                width: 200,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: SvgPicture.asset(
                  'assets/images/base_images/pal logo white.svg',
                  width: context.width - 100,
                  height: 50,
                  color: context.backgroundColor,
                  fit: BoxFit.fill,
                  // color: Colors.black,
                ),
              ),

              // InkWell(
              //   onTap: () {
              //     Navigator.of(context).push(MaterialPageRoute(
              //       builder: (context) => const SignUpScreen(),
              //     ));
              //   },
              //   child: const Text.rich(
              //     TextSpan(
              //       children: [
              //         TextSpan(
              //             text: "ليس لديك حساب؟ ",
              //             style: TextStyle(color: Colors.white)),
              //         TextSpan(
              //           text: "قم بالتسجيل الان",
              //           style: TextStyle(
              //               fontWeight: FontWeight.bold,
              //               color: Colors.white,
              //               fontSize: 12),
              //         ),
              //       ],
              //     ),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }
}
