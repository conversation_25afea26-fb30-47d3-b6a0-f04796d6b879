import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/monthly_sales_report_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:intl/intl.dart';

class MonthlySalesReportWidget extends StatefulWidget {
  final MonthlySalesReportDTO report;

  const MonthlySalesReportWidget({
    Key? key,
    required this.report,
  }) : super(key: key);

  @override
  State<MonthlySalesReportWidget> createState() =>
      _MonthlySalesReportWidgetState();
}

class _MonthlySalesReportWidgetState extends State<MonthlySalesReportWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab bar at the top (fixed)
        TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: T('Summary')),
            Tab(text: T('Invoices')),
          ],
          labelColor: context.newPrimaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: context.newPrimaryColor,
        ),

        // Content area (scrollable)
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Summary tab
              _buildScrollableTab(_buildSummaryContent()),

              // Invoices tab
              _buildScrollableTab(_buildInvoicesContent()),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to create a scrollable tab
  Widget _buildScrollableTab(Widget content) {
    return ListView(
      children: [
        // Report header at the top of each tab
        _buildReportHeader(),

        // Tab content
        content,
      ],
    );
  }

  // Build report header with key metrics
  Widget _buildReportHeader() {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final fromDate = dateFormat.format(widget.report.fromDate);
    final toDate = dateFormat.format(widget.report.toDate);
    final dateRange =
        fromDate == toDate ? fromDate : '$fromDate ${T('to')} $toDate';

    return Container(
      padding: const EdgeInsets.all(16),
      color: context.newPrimaryColor.withOpacity(0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            T('Monthly Sales Report'),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: context.newPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            dateRange,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),

          // Key metrics in a responsive layout
          LayoutBuilder(
            builder: (context, constraints) {
              // Check if we're on a small screen (mobile)
              final isSmallScreen = constraints.maxWidth < 600;

              return isSmallScreen
                  ? Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Total Sales'),
                                value:
                                    widget.report.totalSales.toStringAsFixed(2),
                                icon: Icons.attach_money,
                                color: Colors.green,
                              ),
                            ),
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Discounts'),
                                value: widget.report.totalDiscount
                                    .toStringAsFixed(2),
                                icon: Icons.discount,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Net Sales'),
                                value:
                                    widget.report.netSales.toStringAsFixed(2),
                                icon: Icons.account_balance_wallet,
                                color: Colors.blue,
                              ),
                            ),
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Invoices'),
                                value: widget.report.invoiceCount.toString(),
                                icon: Icons.receipt_long,
                                color: Colors.purple,
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Total Sales'),
                            value: widget.report.totalSales.toStringAsFixed(2),
                            icon: Icons.attach_money,
                            color: Colors.green,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Discounts'),
                            value:
                                widget.report.totalDiscount.toStringAsFixed(2),
                            icon: Icons.discount,
                            color: Colors.orange,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Net Sales'),
                            value: widget.report.netSales.toStringAsFixed(2),
                            icon: Icons.account_balance_wallet,
                            color: Colors.blue,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Invoices'),
                            value: widget.report.invoiceCount.toString(),
                            icon: Icons.receipt_long,
                            color: Colors.purple,
                          ),
                        ),
                      ],
                    );
            },
          ),
        ],
      ),
    );
  }

  // Build a metric card for the header
  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build the summary tab content
  Widget _buildSummaryContent() {
    final salesByMonth = widget.report.calculateSalesByMonth();
    final salesByCustomer = widget.report.calculateSalesByCustomer();
    final salesByWarehouse = widget.report.calculateSalesByWarehouse();
    // final salesByPaymentMethod = widget.report.calculateSalesByPaymentMethod();
    final growthRates = widget.report.calculateMonthlyGrowthRate();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Monthly sales chart
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildMonthlyChart(salesByMonth),
            ),
          ),

          // Monthly growth chart if available
          if (growthRates.isNotEmpty)
            Card(
              elevation: 2,
              margin: const EdgeInsets.only(bottom: 24),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: _buildGrowthChart(growthRates),
              ),
            ),

          // Sales by customer
          _buildSummarySection(
            title: T('Sales by Customer'),
            data: salesByCustomer,
            icon: Icons.person,
          ),

          const SizedBox(height: 24),

          // Sales by warehouse
          _buildSummarySection(
            title: T('Sales by Warehouse'),
            data: salesByWarehouse,
            icon: Icons.warehouse,
          ),

          const SizedBox(height: 24),

          // Sales by payment method
          // _buildSummarySection(
          //   title: T('Sales by Payment Method'),
          //   data: salesByPaymentMethod,
          //   icon: Icons.payment,
          // ),

          // Add some bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build the invoices tab content
  Widget _buildInvoicesContent() {
    final invoices = widget.report.invoices;

    if (invoices.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No invoices found for the selected period'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...invoices.map((invoice) => _buildInvoiceCard(invoice)),
          // Add some bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build a summary section with a title and data table
  Widget _buildSummarySection({
    required String title,
    required Map<String, double> data,
    required IconData icon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: context.newPrimaryColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Data table
        if (data.isEmpty)
          Text(
            T('No data available'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: data.length,
            itemBuilder: (context, index) {
              final entry = data.entries.elementAt(index);
              return Card(
                elevation: 1,
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  title: Text(
                    entry.key,
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  trailing: Text(
                    entry.value.toStringAsFixed(2),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  // Build a card for a single invoice
  Widget _buildInvoiceCard(InvoiceModel invoice) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final formattedDate = invoice.entryDate != null
        ? dateFormat.format(invoice.entryDate!)
        : T('Unknown date');

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invoice header - responsive layout
            LayoutBuilder(
              builder: (context, constraints) {
                // Check if we're on a small screen (mobile)
                final isSmallScreen = constraints.maxWidth < 400;

                return isSmallScreen
                    ? Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${T('Invoice')}: ${invoice.appReferanceCode ?? invoice.code ?? T('Unknown')}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text(
                              '${T('Invoice')}: ${invoice.appReferanceCode ?? invoice.code ?? T('Unknown')}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      );
              },
            ),
            const Divider(),

            // Invoice details
            _buildInvoiceDetailRow(
              label: T('Customer'),
              value: invoice.customerName ?? T('Unknown'),
              icon: Icons.person,
            ),
            _buildInvoiceDetailRow(
              label: T('Warehouse'),
              value: invoice.storeName ?? T('Unknown'),
              icon: Icons.warehouse,
            ),
            _buildInvoiceDetailRow(
              label: T('Total'),
              value: (invoice.total ?? 0.0).toStringAsFixed(2),
              icon: Icons.attach_money,
              valueColor: Colors.green.shade700,
              valueBold: true,
            ),
            if (invoice.discountValue != null && invoice.discountValue! > 0)
              _buildInvoiceDetailRow(
                label: T('Discount'),
                value: invoice.discountValue!.toStringAsFixed(2),
                icon: Icons.discount,
                valueColor: Colors.orange.shade700,
              ),

            // Invoice notes if available
            if (invoice.notes != null && invoice.notes!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.only(top: 8),
                child: Text(
                  '${T('Notes')}: ${invoice.notes}',
                  style: TextStyle(
                    fontStyle: FontStyle.italic,
                    color: Colors.grey.shade700,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  // Build a detail row for the invoice card
  Widget _buildInvoiceDetailRow({
    required String label,
    required String value,
    required IconData icon,
    Color? valueColor,
    bool valueBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Check if we're on a very small screen
          final isVerySmallScreen = constraints.maxWidth < 300;

          return isVerySmallScreen
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          icon,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          label,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: TextStyle(
                        fontWeight:
                            valueBold ? FontWeight.bold : FontWeight.normal,
                        color: valueColor,
                      ),
                    ),
                  ],
                )
              : Row(
                  children: [
                    Icon(
                      icon,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '$label:',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      value,
                      style: TextStyle(
                        fontWeight:
                            valueBold ? FontWeight.bold : FontWeight.normal,
                        color: valueColor,
                      ),
                    ),
                  ],
                );
        },
      ),
    );
  }

  // Build monthly sales chart
  Widget _buildMonthlyChart(Map<String, double> salesByMonth) {
    if (salesByMonth.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No monthly sales data available'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    // Sort months
    final sortedMonths = salesByMonth.keys.toList()..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Monthly Sales'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 300,
          child: BarChart(
            BarChartData(
              alignment: BarChartAlignment.spaceAround,
              maxY: salesByMonth.values.reduce((a, b) => a > b ? a : b) * 1.2,
              barTouchData: BarTouchData(
                touchTooltipData: BarTouchTooltipData(
                  tooltipBgColor: Colors.grey.shade800,
                  getTooltipItem: (group, groupIndex, rod, rodIndex) {
                    final month = sortedMonths[groupIndex];
                    final value = salesByMonth[month]!;
                    return BarTooltipItem(
                      '$month\n${NumberFormat('#,##0.00').format(value)}',
                      const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value < 0 || value >= sortedMonths.length) {
                        return const SizedBox.shrink();
                      }

                      final month = sortedMonths[value.toInt()];
                      // Format to show month name
                      final monthName = month.split('-')[1];

                      return Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          monthName,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    },
                    reservedSize: 30,
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value == 0) {
                        return const SizedBox.shrink();
                      }

                      String text = NumberFormat.compact().format(value);

                      return Text(
                        text,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                    reservedSize: 40,
                  ),
                ),
              ),
              borderData: FlBorderData(
                show: false,
              ),
              barGroups: List.generate(
                sortedMonths.length,
                (index) {
                  final month = sortedMonths[index];
                  final value = salesByMonth[month]!;

                  return BarChartGroupData(
                    x: index,
                    barRods: [
                      BarChartRodData(
                        toY: value,
                        color: context.newPrimaryColor,
                        width: 20,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(4),
                          topRight: Radius.circular(4),
                        ),
                      ),
                    ],
                  );
                },
              ),
              gridData: const FlGridData(
                show: true,
                drawVerticalLine: false,
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Build growth rate chart
  Widget _buildGrowthChart(Map<String, double> growthRates) {
    if (growthRates.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No growth data available'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    // Sort months
    final sortedMonths = growthRates.keys.toList()..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Monthly Growth Rate (%)'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 300,
          child: LineChart(
            LineChartData(
              lineTouchData: LineTouchData(
                touchTooltipData: LineTouchTooltipData(
                  tooltipBgColor: Colors.grey.shade800,
                  getTooltipItems: (touchedSpots) {
                    return touchedSpots.map((spot) {
                      final month = sortedMonths[spot.x.toInt()];
                      final value = spot.y;
                      return LineTooltipItem(
                        '$month\n${value.toStringAsFixed(1)}%',
                        const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    }).toList();
                  },
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value < 0 || value >= sortedMonths.length) {
                        return const SizedBox.shrink();
                      }

                      final month = sortedMonths[value.toInt()];
                      // Format to show month name
                      final monthName = month.split('-')[1];

                      return Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          monthName,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    },
                    reservedSize: 30,
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      return Text(
                        '${value.toInt()}%',
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                    reservedSize: 40,
                  ),
                ),
              ),
              borderData: FlBorderData(
                show: false,
              ),
              gridData: const FlGridData(
                show: true,
                drawVerticalLine: false,
              ),
              lineBarsData: [
                LineChartBarData(
                  spots: List.generate(
                    sortedMonths.length,
                    (index) {
                      final month = sortedMonths[index];
                      final value = growthRates[month]!;
                      return FlSpot(index.toDouble(), value);
                    },
                  ),
                  isCurved: true,
                  color: Colors.green,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: FlDotData(show: true),
                  belowBarData: BarAreaData(
                    show: true,
                    color: Colors.green.withOpacity(0.2),
                  ),
                ),
              ],
              minY: growthRates.values.reduce((a, b) => a < b ? a : b) < 0
                  ? growthRates.values.reduce((a, b) => a < b ? a : b) * 1.2
                  : -10,
              maxY: growthRates.values.reduce((a, b) => a > b ? a : b) * 1.2,
            ),
          ),
        ),
      ],
    );
  }
}
