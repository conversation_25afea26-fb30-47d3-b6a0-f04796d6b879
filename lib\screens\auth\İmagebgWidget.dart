// ignore: file_names
import 'package:flutter/material.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';

// ignore: must_be_immutable
class ImageBgWidget extends StatelessWidget {
  double? height;
  String? image;
  ImageBgWidget({super.key, this.height, this.image});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 250,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(125),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipPath(
        clipper: OvalBottomBorderClipper(),
        child: Image.asset(
          height: 210,
          image ?? 'assets/images/home/<USER>',
          width: double.infinity,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
