{"v": "5.6.5", "fr": 24, "ip": 0, "op": 28, "w": 30, "h": 30, "nm": "dollar", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "lottie Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15, 15, 0], "ix": 2}, "a": {"a": 0, "k": [12.25, 12.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0.115, -0.303], [0, 0], [0, 0], [0, 0], [0.004, 1.836], [0, 0], [-0.069, 0], [0, 0.851], [0.072, 0.297], [0, 0], [0, 1.719], [-0.105, 0.323], [0, 0], [0, 0], [0, 0], [-0.003, -1.733], [0, 0], [0.059, 0], [0, -0.859], [-0.069, -0.281], [0, 0], [0, -1.75]], "o": [[0, 0], [0, 0], [0, 0], [-0.111, -0.283], [0, 0], [0.006, 0.915], [0.064, 0], [0, -0.719], [0, 0], [-0.107, -0.43], [0, -1.83], [0, 0], [0, 0], [0, 0], [0.102, 0.317], [0, 0], [-0.006, -0.891], [-0.061, 0], [0, 0.695], [0, 0], [0.118, 0.477], [0, 1.982]], "v": [[0.052, 5.857], [0.052, 8.001], [-0.052, 8.001], [-0.052, 5.863], [-0.237, 2.503], [-0.118, 2.503], [0.005, 4.003], [0.114, 2.559], [0.013, 1.128], [-0.064, 0.816], [-0.223, -2.402], [-0.052, -5.842], [-0.052, -8.001], [0.052, -8.001], [0.052, -5.844], [0.226, -2.527], [0.11, -2.527], [0.002, -4.012], [-0.1, -2.605], [-0.003, -1.23], [0.068, -0.941], [0.236, 2.262]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0.368, -0.303], [0, 0], [0, 0], [0, 0], [0.012, 1.836], [0, 0], [-0.221, 0], [0, 0.851], [0.23, 0.297], [0, 0], [0, 1.719], [-0.335, 0.323], [0, 0], [0, 0], [0, 0], [-0.01, -1.733], [0, 0], [0.189, 0], [0, -0.859], [-0.219, -0.281], [0, 0], [0, -1.75]], "o": [[0, 0], [0, 0], [0, 0], [-0.354, -0.283], [0, 0], [0.018, 0.915], [0.204, 0], [0, -0.719], [0, 0], [-0.342, -0.43], [0, -1.83], [0, 0], [0, 0], [0, 0], [0.325, 0.317], [0, 0], [-0.018, -0.891], [-0.196, 0], [0, 0.695], [0, 0], [0.377, 0.477], [0, 1.982]], "v": [[0.166, 5.857], [0.166, 8.001], [-0.166, 8.001], [-0.166, 5.863], [-0.756, 2.503], [-0.376, 2.503], [0.016, 4.003], [0.364, 2.559], [0.04, 1.128], [-0.204, 0.816], [-0.713, -2.402], [-0.166, -5.842], [-0.166, -8.001], [0.166, -8.001], [0.166, -5.844], [0.721, -2.527], [0.352, -2.527], [0.007, -4.012], [-0.319, -2.605], [-0.01, -1.23], [0.216, -0.941], [0.754, 2.262]], "c": true}]}, {"t": 25, "s": [{"i": [[2.214, -0.303], [0, 0], [0, 0], [0, 0], [0.075, 1.836], [0, 0], [-1.328, 0], [0, 0.851], [1.383, 0.297], [0, 0], [0, 1.719], [-2.014, 0.323], [0, 0], [0, 0], [0, 0], [-0.061, -1.733], [0, 0], [1.14, 0], [0, -0.859], [-1.32, -0.281], [0, 0], [0, -1.75]], "o": [[0, 0], [0, 0], [0, 0], [-2.13, -0.283], [0, 0], [0.109, 0.915], [1.227, 0], [0, -0.719], [0, 0], [-2.055, -0.43], [0, -1.83], [0, 0], [0, 0], [0, 0], [1.956, 0.317], [0, 0], [-0.109, -0.891], [-1.18, 0], [0, 0.695], [0, 0], [2.266, 0.477], [0.001, 1.982]], "v": [[1, 5.857], [1, 8.001], [-1, 8.001], [-1, 5.863], [-4.546, 2.503], [-2.265, 2.503], [0.094, 4.003], [2.188, 2.559], [0.243, 1.128], [-1.226, 0.816], [-4.288, -2.402], [-1, -5.842], [-1, -8.001], [1, -8.001], [1, -5.844], [4.337, -2.527], [2.118, -2.527], [0.04, -4.012], [-1.921, -2.605], [-0.062, -1.23], [1.297, -0.941], [4.539, 2.262]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0.32, -0.008], [0.008, -0.654], [0, -4.911], [-0.03, -1.912], [-0.359, -0.009], [-0.003, 0.649], [0, 4.918], [0.024, 1.862]], "o": [[-0.32, 0.008], [-0.022, 1.849], [0, 4.912], [0.001, 0.635], [0.367, -0.001], [0.007, -1.927], [0, -4.927], [-0.008, -0.642]], "v": [[0, -12.001], [-0.564, -10.99], [-0.625, -0.001], [-0.595, 10.99], [0, 12.001], [0.602, 10.997], [0.625, -0.001], [0.577, -11.01]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0.095, 0], [0.093, -0.081], [0, -6.043], [-0.966, -0.833], [-0.097, 0], [-0.093, 0.079], [0, 6.049], [0.969, 0.821]], "o": [[-0.097, 0], [-0.966, 0.834], [0, 6.045], [0.093, 0.08], [0.096, 0], [0.967, -0.828], [0, -6.054], [-0.092, -0.078]], "v": [[0, -12.001], [-0.286, -11.878], [-1.995, -0.001], [-0.286, 11.878], [0, 12.001], [0.284, 11.88], [1.995, -0.001], [0.281, -11.882]], "c": true}]}, {"t": 25, "s": [{"i": [[0.573, 0], [0.561, -0.081], [0, -6.043], [-5.813, -0.833], [-0.583, 0], [-0.557, 0.079], [0, 6.049], [5.827, 0.821]], "o": [[-0.584, 0], [-5.813, 0.834], [0, 6.045], [0.561, 0.08], [0.579, 0], [5.819, -0.828], [0, -6.054], [-0.552, -0.078]], "v": [[0, -12.001], [-1.719, -11.878], [-12, -0.001], [-1.718, 11.878], [0, 12.001], [1.706, 11.88], [12, -0.001], [1.689, -11.882]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.25, 12.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 14, "op": 28, "st": 15, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "lottie Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15, 15, 0], "ix": 2}, "a": {"a": 0, "k": [12.25, 12.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 3, "s": [{"i": [[2.214, -0.303], [0, 0], [0, 0], [0, 0], [0.075, 1.836], [0, 0], [-1.328, 0], [0, 0.851], [1.383, 0.297], [0, 0], [0, 1.719], [-2.014, 0.323], [0, 0], [0, 0], [0, 0], [-0.061, -1.733], [0, 0], [1.14, 0], [0, -0.859], [-1.32, -0.281], [0, 0], [0, -1.75]], "o": [[0, 0], [0, 0], [0, 0], [-2.13, -0.283], [0, 0], [0.109, 0.915], [1.227, 0], [0, -0.719], [0, 0], [-2.055, -0.43], [0, -1.83], [0, 0], [0, 0], [0, 0], [1.956, 0.317], [0, 0], [-0.109, -0.891], [-1.18, 0], [0, 0.695], [0, 0], [2.266, 0.477], [0.001, 1.982]], "v": [[1, 5.857], [1, 8.001], [-1, 8.001], [-1, 5.863], [-4.546, 2.503], [-2.265, 2.503], [0.094, 4.003], [2.188, 2.559], [0.243, 1.128], [-1.226, 0.816], [-4.288, -2.402], [-1, -5.842], [-1, -8.001], [1, -8.001], [1, -5.844], [4.337, -2.527], [2.118, -2.527], [0.04, -4.012], [-1.921, -2.605], [-0.062, -1.23], [1.297, -0.941], [4.539, 2.262]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0.368, -0.303], [0, 0], [0, 0], [0, 0], [0.012, 1.836], [0, 0], [-0.221, 0], [0, 0.851], [0.23, 0.297], [0, 0], [0, 1.719], [-0.335, 0.323], [0, 0], [0, 0], [0, 0], [-0.01, -1.733], [0, 0], [0.189, 0], [0, -0.859], [-0.219, -0.281], [0, 0], [0, -1.75]], "o": [[0, 0], [0, 0], [0, 0], [-0.354, -0.283], [0, 0], [0.018, 0.915], [0.204, 0], [0, -0.719], [0, 0], [-0.342, -0.43], [0, -1.83], [0, 0], [0, 0], [0, 0], [0.325, 0.317], [0, 0], [-0.018, -0.891], [-0.196, 0], [0, 0.695], [0, 0], [0.377, 0.477], [0, 1.982]], "v": [[0.166, 5.857], [0.166, 8.001], [-0.166, 8.001], [-0.166, 5.863], [-0.756, 2.503], [-0.376, 2.503], [0.016, 4.003], [0.364, 2.559], [0.04, 1.128], [-0.204, 0.816], [-0.713, -2.402], [-0.166, -5.842], [-0.166, -8.001], [0.166, -8.001], [0.166, -5.844], [0.721, -2.527], [0.352, -2.527], [0.007, -4.012], [-0.319, -2.605], [-0.01, -1.23], [0.216, -0.941], [0.754, 2.262]], "c": true}]}, {"t": 13, "s": [{"i": [[0.115, -0.303], [0, 0], [0, 0], [0, 0], [0.004, 1.836], [0, 0], [-0.069, 0], [0, 0.851], [0.072, 0.297], [0, 0], [0, 1.719], [-0.105, 0.323], [0, 0], [0, 0], [0, 0], [-0.003, -1.733], [0, 0], [0.059, 0], [0, -0.859], [-0.069, -0.281], [0, 0], [0, -1.75]], "o": [[0, 0], [0, 0], [0, 0], [-0.111, -0.283], [0, 0], [0.006, 0.915], [0.064, 0], [0, -0.719], [0, 0], [-0.107, -0.43], [0, -1.83], [0, 0], [0, 0], [0, 0], [0.102, 0.317], [0, 0], [-0.006, -0.891], [-0.061, 0], [0, 0.695], [0, 0], [0.118, 0.477], [0, 1.982]], "v": [[0.052, 5.857], [0.052, 8.001], [-0.052, 8.001], [-0.052, 5.863], [-0.237, 2.503], [-0.118, 2.503], [0.005, 4.003], [0.114, 2.559], [0.013, 1.128], [-0.064, 0.816], [-0.223, -2.402], [-0.052, -5.842], [-0.052, -8.001], [0.052, -8.001], [0.052, -5.844], [0.226, -2.527], [0.11, -2.527], [0.002, -4.012], [-0.1, -2.605], [-0.003, -1.23], [0.068, -0.941], [0.236, 2.262]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 3, "s": [{"i": [[0.589, 0], [0.565, -0.082], [0, -6.039], [-5.819, -0.828], [-0.579, 0], [-0.561, 0.08], [0, 6.045], [5.806, 0.84]], "o": [[-0.588, 0], [-5.807, 0.839], [0, 6.049], [0.557, 0.079], [0.583, 0], [5.814, -0.833], [0, -6.038], [-0.566, -0.082]], "v": [[0, -12.001], [-1.731, -11.877], [-12, -0.001], [-1.705, 11.88], [0, 12.001], [1.716, 11.879], [12, -0.001], [1.734, -11.876]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0.098, 0], [0.094, -0.082], [0, -6.039], [-0.967, -0.828], [-0.096, 0], [-0.093, 0.08], [0, 6.045], [0.965, 0.84]], "o": [[-0.098, 0], [-0.965, 0.839], [0, 6.049], [0.093, 0.079], [0.097, 0], [0.966, -0.833], [0, -6.038], [-0.094, -0.082]], "v": [[0, -12.001], [-0.288, -11.877], [-1.995, -0.001], [-0.283, 11.88], [0, 12.001], [0.285, 11.879], [1.995, -0.001], [0.288, -11.876]], "c": true}]}, {"t": 13, "s": [{"i": [[0.414, 0], [-0.001, -0.659], [0, -4.904], [-0.007, -1.898], [-0.359, 0.03], [-0.002, 0.653], [0, 4.912], [0.029, 1.809]], "o": [[-0.43, 0.031], [0.002, 1.827], [0, 4.918], [0.003, 0.649], [0.375, -0.001], [0.007, -1.906], [0, -4.902], [0.013, -0.636]], "v": [[0, -12.001], [-0.58, -10.983], [-0.625, -0.001], [-0.618, 11.007], [0, 12.001], [0.579, 10.992], [0.625, -0.001], [0.581, -10.966]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [12.25, 12.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 13, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "dollar(line) Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15, 15, 0], "ix": 2}, "a": {"a": 0, "k": [15, 15, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[15, 3.5], [15, 26.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 13, "op": 14, "st": 0, "bm": 0}], "markers": []}