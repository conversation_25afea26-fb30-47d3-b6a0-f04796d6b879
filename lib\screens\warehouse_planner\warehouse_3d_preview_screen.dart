import 'package:flutter/material.dart';
import 'package:vector_math/vector_math.dart' as vector;

import '../../models/warehouse_planner/warehouse_layout.dart';

/// شاشة المعاينة ثلاثية الأبعاد البسيطة
class Warehouse3DPreviewScreen extends StatefulWidget {
  final WarehouseLayout layout;

  const Warehouse3DPreviewScreen({
    Key? key,
    required this.layout,
  }) : super(key: key);

  @override
  State<Warehouse3DPreviewScreen> createState() =>
      _Warehouse3DPreviewScreenState();
}

class _Warehouse3DPreviewScreenState extends State<Warehouse3DPreviewScreen> {
  double _rotationX = -0.3;
  double _rotationY = 0.5;
  double _zoom = 1.0;

  Offset? _lastPanPosition;
  double _lastScale = 1.0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF2C3E50),
      appBar: AppBar(
        title: Text('معاينة ${widget.layout.name}'),
        backgroundColor: const Color(0xFF34495E),
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _resetView,
            tooltip: 'إعادة تعيين العرض',
          ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showInfo,
            tooltip: 'معلومات',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط التحكم
          _buildControlPanel(),

          // العرض ثلاثي الأبعاد
          Expanded(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              child: GestureDetector(
                onScaleStart: (details) {
                  _lastScale = 1.0;
                  _lastPanPosition = details.focalPoint;
                },
                onScaleUpdate: (details) {
                  if (details.scale != 1.0) {
                    // تكبير/تصغير
                    setState(() {
                      _zoom =
                          (_zoom * details.scale / _lastScale).clamp(0.3, 3.0);
                      _lastScale = details.scale;
                    });
                  } else if (_lastPanPosition != null) {
                    // دوران
                    final delta = details.focalPoint - _lastPanPosition!;
                    setState(() {
                      _rotationY += delta.dx * 0.01;
                      _rotationX += delta.dy * 0.01;
                      _rotationX = _rotationX.clamp(-1.5, 0.5);
                    });
                    _lastPanPosition = details.focalPoint;
                  }
                },
                onScaleEnd: (details) {
                  _lastScale = 1.0;
                  _lastPanPosition = null;
                },
                child: CustomPaint(
                  size: Size.infinite,
                  painter: Warehouse3DPainter(
                    layout: widget.layout,
                    rotationX: _rotationX,
                    rotationY: _rotationY,
                    zoom: _zoom,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlPanel() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: const BoxDecoration(
        color: Color(0xFF34495E),
        border: Border(
          bottom: BorderSide(color: Colors.white24),
        ),
      ),
      child: Row(
        children: [
          const Icon(Icons.view_in_ar, color: Colors.white, size: 20),
          const SizedBox(width: 8),
          const Text(
            'معاينة ثلاثية الأبعاد',
            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          ),

          const Spacer(),

          // معلومات التحكم
          Text(
            'اسحب للدوران • قرص للتكبير',
            style: TextStyle(color: Colors.white70, fontSize: 12),
          ),

          const SizedBox(width: 16),

          // مؤشر التكبير
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.white24,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${(_zoom * 100).toInt()}%',
              style: const TextStyle(color: Colors.white, fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _resetView() {
    setState(() {
      _rotationX = -0.3;
      _rotationY = 0.5;
      _zoom = 1.0;
    });
  }

  void _showInfo() {
    final stats = _calculateStats();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('معلومات المستودع'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('الاسم', widget.layout.name),
            _buildInfoRow('الأبعاد',
                '${widget.layout.width.toInt()} × ${widget.layout.height.toInt()} سم'),
            _buildInfoRow(
                'ارتفاع الجدران', '${widget.layout.wallHeight.toInt()} سم'),
            const SizedBox(height: 16),
            _buildInfoRow('عدد الجدران', '${stats['walls']}'),
            _buildInfoRow('عدد المداخل', '${stats['entrances']}'),
            _buildInfoRow('عدد الخزائن', '${stats['shelves']}'),
            _buildInfoRow('إجمالي الخانات', '${stats['bins']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Map<String, int> _calculateStats() {
    int totalBins = 0;
    for (final shelf in widget.layout.shelves) {
      totalBins += shelf.bins.length;
    }

    return {
      'walls': widget.layout.walls.length,
      'entrances': widget.layout.entrances.length,
      'shelves': widget.layout.shelves.length,
      'bins': totalBins,
    };
  }
}

/// رسام المعاينة ثلاثية الأبعاد
class Warehouse3DPainter extends CustomPainter {
  final WarehouseLayout layout;
  final double rotationX;
  final double rotationY;
  final double zoom;

  Warehouse3DPainter({
    required this.layout,
    required this.rotationX,
    required this.rotationY,
    required this.zoom,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    canvas.translate(center.dx, center.dy);
    canvas.scale(zoom);

    // إنشاء مصفوفة التحويل ثلاثي الأبعاد
    final transform = _createTransform();

    // رسم أرضية المستودع
    _drawFloor(canvas, transform);

    // رسم الجدران
    _drawWalls(canvas, transform);

    // رسم المداخل
    _drawEntrances(canvas, transform);

    // رسم الخزائن
    _drawShelves(canvas, transform);

    // رسم محاور الإحداثيات
    _drawAxes(canvas, transform);
  }

  vector.Matrix4 _createTransform() {
    final transform = vector.Matrix4.identity();
    transform.rotateX(rotationX);
    transform.rotateY(rotationY);
    return transform;
  }

  void _drawFloor(Canvas canvas, vector.Matrix4 transform) {
    final floorPaint = Paint()
      ..color = const Color(0xFF95A5A6).withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = const Color(0xFF7F8C8D)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final corners = [
      _project3D(
          vector.Vector3(-layout.width / 2, -layout.height / 2, 0), transform),
      _project3D(
          vector.Vector3(layout.width / 2, -layout.height / 2, 0), transform),
      _project3D(
          vector.Vector3(layout.width / 2, layout.height / 2, 0), transform),
      _project3D(
          vector.Vector3(-layout.width / 2, layout.height / 2, 0), transform),
    ];

    if (corners.every((c) => c != null)) {
      final path = Path();
      path.moveTo(corners[0]!.dx, corners[0]!.dy);
      for (int i = 1; i < corners.length; i++) {
        path.lineTo(corners[i]!.dx, corners[i]!.dy);
      }
      path.close();

      canvas.drawPath(path, floorPaint);
      canvas.drawPath(path, borderPaint);
    }
  }

  void _drawWalls(Canvas canvas, vector.Matrix4 transform) {
    for (final wall in layout.walls) {
      _drawWall(canvas, transform, wall);
    }
  }

  void _drawWall(Canvas canvas, vector.Matrix4 transform, Wall wall) {
    final wallPaint = Paint()
      ..color = wall.color.withOpacity(0.7)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = wall.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    for (int i = 0; i < wall.points.length - 1; i++) {
      final start = wall.points[i];
      final end = wall.points[i + 1];

      // إنشاء جدار ثلاثي الأبعاد
      final bottomStart =
          _project3D(vector.Vector3(start.x, start.y, 0), transform);
      final bottomEnd = _project3D(vector.Vector3(end.x, end.y, 0), transform);
      final topStart = _project3D(
          vector.Vector3(start.x, start.y, layout.wallHeight), transform);
      final topEnd = _project3D(
          vector.Vector3(end.x, end.y, layout.wallHeight), transform);

      if ([bottomStart, bottomEnd, topStart, topEnd].every((p) => p != null)) {
        final path = Path();
        path.moveTo(bottomStart!.dx, bottomStart.dy);
        path.lineTo(bottomEnd!.dx, bottomEnd.dy);
        path.lineTo(topEnd!.dx, topEnd.dy);
        path.lineTo(topStart!.dx, topStart.dy);
        path.close();

        canvas.drawPath(path, wallPaint);
        canvas.drawPath(path, borderPaint);
      }
    }
  }

  void _drawEntrances(Canvas canvas, vector.Matrix4 transform) {
    for (final entrance in layout.entrances) {
      _drawEntrance(canvas, transform, entrance);
    }
  }

  void _drawEntrance(
      Canvas canvas, vector.Matrix4 transform, Entrance entrance) {
    // العثور على الجدار
    final wall = layout.walls.firstWhere((w) => w.id == entrance.wallId);

    // حساب موضع المدخل
    final startPoint = wall.getPointAtDistance(entrance.startDistance);
    final endPoint = wall.getPointAtDistance(entrance.endDistance);

    if (startPoint == null || endPoint == null) return;

    final entrancePaint = Paint()
      ..color = const Color(0xFFE67E22)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6;

    final bottom1 =
        _project3D(vector.Vector3(startPoint.x, startPoint.y, 0), transform);
    final bottom2 =
        _project3D(vector.Vector3(endPoint.x, endPoint.y, 0), transform);
    final top1 = _project3D(
        vector.Vector3(startPoint.x, startPoint.y, layout.wallHeight * 0.8),
        transform);
    final top2 = _project3D(
        vector.Vector3(endPoint.x, endPoint.y, layout.wallHeight * 0.8),
        transform);

    if ([bottom1, bottom2, top1, top2].every((p) => p != null)) {
      // رسم إطار المدخل
      canvas.drawLine(bottom1!, top1!, entrancePaint);
      canvas.drawLine(bottom2!, top2!, entrancePaint);
      canvas.drawLine(top1, top2, entrancePaint);
    }
  }

  void _drawShelves(Canvas canvas, vector.Matrix4 transform) {
    for (final shelf in layout.shelves) {
      _drawShelf(canvas, transform, shelf);
    }
  }

  void _drawShelf(Canvas canvas, vector.Matrix4 transform, Shelf shelf) {
    final shelfPaint = Paint()
      ..color = shelf.color
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = shelf.color.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // رسم قاعدة الخزانة
    final corners = shelf.corners;
    final bottomCorners = corners
        .map((corner) =>
            _project3D(vector.Vector3(corner.x, corner.y, 0), transform))
        .toList();

    final topCorners = corners
        .map((corner) => _project3D(
            vector.Vector3(corner.x, corner.y, shelf.height), transform))
        .toList();

    if (bottomCorners.every((c) => c != null) &&
        topCorners.every((c) => c != null)) {
      // رسم الوجه العلوي
      final topPath = Path();
      topPath.moveTo(topCorners[0]!.dx, topCorners[0]!.dy);
      for (int i = 1; i < topCorners.length; i++) {
        topPath.lineTo(topCorners[i]!.dx, topCorners[i]!.dy);
      }
      topPath.close();
      canvas.drawPath(topPath, shelfPaint);
      canvas.drawPath(topPath, borderPaint);

      // رسم الجوانب
      for (int i = 0; i < 4; i++) {
        final next = (i + 1) % 4;
        final sidePath = Path();
        sidePath.moveTo(bottomCorners[i]!.dx, bottomCorners[i]!.dy);
        sidePath.lineTo(bottomCorners[next]!.dx, bottomCorners[next]!.dy);
        sidePath.lineTo(topCorners[next]!.dx, topCorners[next]!.dy);
        sidePath.lineTo(topCorners[i]!.dx, topCorners[i]!.dy);
        sidePath.close();

        final sidePaint = Paint()
          ..color = shelf.color.withOpacity(0.6)
          ..style = PaintingStyle.fill;

        canvas.drawPath(sidePath, sidePaint);
        canvas.drawPath(sidePath, borderPaint);
      }
    }
  }

  void _drawAxes(Canvas canvas, vector.Matrix4 transform) {
    final axisPaint = Paint()
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final axisLength = 50.0;

    // محور X (أحمر)
    final xStart = _project3D(vector.Vector3(0, 0, 0), transform);
    final xEnd = _project3D(vector.Vector3(axisLength, 0, 0), transform);
    if (xStart != null && xEnd != null) {
      axisPaint.color = Colors.red;
      canvas.drawLine(xStart, xEnd, axisPaint);
    }

    // محور Y (أخضر)
    final yStart = _project3D(vector.Vector3(0, 0, 0), transform);
    final yEnd = _project3D(vector.Vector3(0, axisLength, 0), transform);
    if (yStart != null && yEnd != null) {
      axisPaint.color = Colors.green;
      canvas.drawLine(yStart, yEnd, axisPaint);
    }

    // محور Z (أزرق)
    final zStart = _project3D(vector.Vector3(0, 0, 0), transform);
    final zEnd = _project3D(vector.Vector3(0, 0, axisLength), transform);
    if (zStart != null && zEnd != null) {
      axisPaint.color = Colors.blue;
      canvas.drawLine(zStart, zEnd, axisPaint);
    }
  }

  Offset? _project3D(vector.Vector3 point, vector.Matrix4 transform) {
    final transformed = transform.transform3(point);

    // إسقاط متوازي بسيط
    return Offset(transformed.x, -transformed.y);
  }

  @override
  bool shouldRepaint(covariant Warehouse3DPainter oldDelegate) {
    return oldDelegate.rotationX != rotationX ||
        oldDelegate.rotationY != rotationY ||
        oldDelegate.zoom != zoom ||
        oldDelegate.layout != layout;
  }
}
