import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/models/model/category_model.dart';
import 'package:sqflite/sqflite.dart';

class CategoryController with ChangeNotifier {
  List<CateogryModel> categories = [];
  bool runningSyncization = false;
  int fetchedCategoryCount = 0;

  Future<void> fetchCategories() async {
    var category = await getCategoriesFromLocal();
    if (category.isNotEmpty) {
      categories = category;
      fetchedCategoryCount = category.length;
      categories.insert(0, CateogryModel(iD: 0, name: "الكل"));
      notifyListeners();
      return;
    }

    bool isStillThereCategories = true;

    while (isStillThereCategories) {
      String url =
          '/ItemsCategory/GetAllCategories?skip=$fetchedCategoryCount&take=500';
      var result = await Api.getOne(action: url);

      if (result != null && result.isSuccess) {
        for (var element in result.data) {
          var categoryModel = CateogryModel.fromJson(element);
          await setCategory(categoryModel);
          categories.add(categoryModel);
          fetchedCategoryCount++;
          notifyListeners();
        }

        if (result.data.length < 500) {
          isStillThereCategories = false;
          runningSyncization = false;
        }
      } else {
        isStillThereCategories = false;
        runningSyncization = false;
      }
    }
    categories.insert(0, CateogryModel(iD: 0, name: "الكل"));
    notifyListeners();
  }

  //----------------------------------------------------
  Future<void> fetchCategoriesFromServer() async {
    try {
      bool isStillThereCategories = true;
      fetchedCategoryCount = 0;
      categories.clear();

      while (isStillThereCategories) {
        runningSyncization = true;
        String url =
            '/ItemsCategory/GetAllCategories?skip=$fetchedCategoryCount&take=500';
        var result = await Api.getOne(action: url);

        if (result != null && result.isSuccess) {
          for (var element in result.data) {
            var categoryModel = CateogryModel.fromJson(element);
            await setCategory(categoryModel);
            categories.add(categoryModel);
            fetchedCategoryCount++;
            notifyListeners();
          }

          if (result.data.length < 500) {
            isStillThereCategories = false;
            runningSyncization = false;
          }
        } else {
          isStillThereCategories = false;
          runningSyncization = false;
        }
      }
      categories.insert(0, CateogryModel(iD: 0, name: "الكل"));
      notifyListeners();
    } catch (e) {
      print(e);
    }
  }
  //----------------------------------------------------

  Future<bool> checkIfCategoryDataExists() async {
    try {
      final db = await DatabaseHelper().database;
      final List<Map<String, dynamic>> result =
          await db.rawQuery('SELECT COUNT(*) as count FROM Category');

      return result.isNotEmpty && result[0]['count'] > 0;
    } catch (e) {
      print("Error checking Category data: $e");
      return false;
    }
  }

  Future<int> setCategory(CateogryModel category) async {
    final db = await DatabaseHelper().database;

    final result = await db.insert(
      'Category',
      {'ID': category.iD, 'Name': category.name},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    return result;
  }

//-----------------------------------------------------------
  Future<List<CateogryModel>> getCategoriesFromLocal() async {
    final db = await DatabaseHelper().database;
    var result = await db.query('Category');
    var resultMapped = result.map((e) => CateogryModel.fromJson(e)).toList();

    return resultMapped;
  }

//-----------------------------------------------------------
  Future<int> getCategoryCount() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery('SELECT COUNT(*) as count FROM Category');

    int count = Sqflite.firstIntValue(result) ?? 0;
    return count;
  }

  //--------------------------------------------------------------------------------
  Future<bool> clearAndRefetchData() async {
    try {
      final db = await DatabaseHelper().database;

      await db.transaction((txn) async {
        await txn.delete('Category');
      });

      await fetchCategoriesFromServer();
      return true;
    } catch (e) {
      return false;
    }
  }
  //--------------------------------------------------------------------------------
}
