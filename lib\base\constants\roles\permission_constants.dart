/// Permission constants for better maintainability
/// Based on the ERP system permission structure
class PermissionConstants {
  // Sales & Invoice Permissions
  static const int salesInvoiceCreate = 418;
  static const int salesInvoiceView = 421;
  static const int salesInvoiceEdit = 419;
  static const int salesInvoiceDelete = 420;

  // Purchase Permissions
  static const int purchaseInvoiceCreate = 507;
  static const int purchaseInvoiceView = 509;
  static const int purchaseInvoiceEdit = 508;
  static const int purchaseInvoiceDelete = 510;

  // Inventory Permissions
  static const int inventoryView = 412;
  static const int inventoryOutgoing = 413;
  static const int inventoryIncoming = 447;
  static const int inventoryTransfer = 452;
  static const int inventoryDamaged = 456;

  // Customer Management
  static const int customerCreate = 337;
  static const int customerView = 340;
  static const int customerEdit = 338;
  static const int customerDelete = 339;
  // Return Invoice Management
  static const int returninvoiceCreate = 398;
  static const int returninvoiceView = 401;
  static const int returninvoiceEdit = 399;
  static const int returninvoiceDelete = 400;
// Items Management
  static const int itemsCreate = 398;
  static const int itemseView = 401;
  static const int itemsEdit = 399;
  static const int itemsDelete = 400;
  // Item Transfer Management
  static const int itemTransferCreate = 453;
  static const int itemTransferView = 456;
  static const int itemTransferEdit = 454;
  static const int itemTransferDelete = 455;
  // Incoming Management
  static const int incomingCreate = 438;
  static const int incomingView = 441;
  static const int incomingEdit = 439;
  static const int incomingDelete = 440;
  // Outgoing Management
  static const int outgoingCreate = 443;
  static const int outgoingView = 446;
  static const int outgoingEdit = 444;
  static const int outgoingDelete = 445;
  // damaged Management
  static const int damagedCreate = 433;
  static const int damagedView = 436;
  static const int damagedEdit = 434;
  static const int damagedDelete = 435;
  // Order Invocie
  static const int orderInvocieCreate = 458;
  static const int orderInvocieeView = 461;
  static const int orderInvocieEdit = 459;
  static const int orderInvocieDelete = 460;
  // Product Management
  static const int productCreate = 384;
  static const int productView = 385;
  static const int productEdit = 386;
  static const int productDelete = 387;

  // Reports
  static const int salesReports = 388;
  static const int inventoryReports = 389;
  static const int financialReports = 390;

  // User Management
  static const int userCreate = 67;
  static const int userView = 70;
  static const int userEdit = 68;
  static const int userDelete = 69;

  // Role Management
  static const int roleCreate = 62;
  static const int roleView = 65;
  static const int roleEdit = 63;
  static const int roleDelete = 64;

  // System Settings
  static const int systemSettings = 391;
  static const int branchSettings = 392;
  static const int deviceSettings = 393;

  // Accounting
  static const int journalEntryCreate = 44;

  /// Helper method to get permission name
  static String getPermissionName(int permissionId) {
    switch (permissionId) {
      case salesInvoiceCreate:
        return 'Sales Invoice - Create';
      case salesInvoiceView:
        return 'Sales Invoice - View';
      case salesInvoiceEdit:
        return 'Sales Invoice - Edit';
      case salesInvoiceDelete:
        return 'Sales Invoice - Delete';

      case customerCreate:
        return 'Customer - Create';
      case productView:
        return 'Product - View';
      // Add more cases as needed
      default:
        return 'Unknown Permission ($permissionId)';
    }
  }

  /// Common permission groups for easier management
  static const List<int> allSalesPermissions = [
    salesInvoiceCreate,
    salesInvoiceView,
    salesInvoiceEdit,
    salesInvoiceDelete,
  ];

  static const List<int> allInventoryPermissions = [
    inventoryView,
    inventoryOutgoing,
    inventoryIncoming,
    inventoryTransfer,
    inventoryDamaged,
  ];

  static const List<int> allCustomerPermissions = [
    customerCreate,
    customerView,
    customerEdit,
    customerDelete,
  ];

  static const List<int> allProductPermissions = [
    productCreate,
    productView,
    productEdit,
    productDelete,
  ];
}
