المنتجات:
اولا عند تشغيل التطبيق لاول مرة يجب ان يتوفر انترنت لكي يتم سحب المنتجات بالكامل من السيرفر و ادراجهم في قاعدة البيانات الداخلة لا يمكن اضافة او تعديل منتج من التطبيق 
عندما ينقطع النت في حالة سحب المنتجات من السيرفر يقوم التطبيق بحفظ االعدد المسحوب وعندما يعود النت يبدا بالمزامنة من العدد الذي تم الوصول له
يوجد كبسة في الاعدادات في المزامنة تحذف كل المنتجات الموجودة في قاعدة البيانات و ادراجها من جديد في حال توفر الانترنت

العملاء:
طريقة سحب العملاء مشابهة جدا للمنتجات
ولكن يختلف بأن يمكن اضافة او تعديل عميل حتى في حال عدم توفر الانترنت 
عندما يتوفر اتصال بالانترنت يقوم التطبيق بحسب كل العملاء من قاعدة البيانات الداخلة الغير مزامنين ويقوم بمزامنة بالمزامنة

الفواتير:
يمكن للمستخدم تخزين فاتورة بدون انترنت حيث انها تحفظ في قاعدة البيانات الداخلية وعندما يتوفر انترنت يقوم التطبيق برفع الفواتير للسيرفر
خطوة جاري العمل عليها: في حال عدم توفر الانترنت يمكن اضافة عميل لقاعدة البيانات الداخلية واضافة فاتورة له وعند توفر الانترنت يقوم التطبيق برفع العميل اولا بعدها يقوم برفع الفاتورة
لكن تم اقاف العملية بسبب تعقد السناريوهات المحتملة يجب التحليل و الدراسة اولاً



build_release_admin 