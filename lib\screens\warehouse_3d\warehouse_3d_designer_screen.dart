import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/controllers/warehouse_3d_controller.dart';
import 'package:inventory_application/models/model/warehouse_3d_model.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/warehouse_3d/widgets/warehouse_3d_viewer_widget.dart';

class Warehouse3DDesignerScreen extends StatefulWidget {
  final Warehouse3D? existingWarehouse;

  const Warehouse3DDesignerScreen({
    Key? key,
    this.existingWarehouse,
  }) : super(key: key);

  @override
  State<Warehouse3DDesignerScreen> createState() =>
      _Warehouse3DDesignerScreenState();
}

class _Warehouse3DDesignerScreenState extends State<Warehouse3DDesignerScreen> {
  late Warehouse3DController _controller;
  Warehouse3D? _currentWarehouse;
  bool _isEditMode = false;
  bool _isAddingCabinet = false;
  bool _isAddingShelf = false;

  // Controllers for input dialogs
  final _warehouseNameController = TextEditingController();
  final _warehouseDescController = TextEditingController();
  final _warehouseLengthController = TextEditingController();
  final _warehouseWidthController = TextEditingController();
  final _warehouseHeightController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller = Provider.of<Warehouse3DController>(context, listen: false);

    if (widget.existingWarehouse != null) {
      _currentWarehouse = widget.existingWarehouse;
      _isEditMode = true;
      _populateFields();
    } else {
      _createNewWarehouse();
    }
  }

  void _populateFields() {
    if (_currentWarehouse != null) {
      _warehouseNameController.text = _currentWarehouse!.name ?? '';
      _warehouseDescController.text = _currentWarehouse!.description ?? '';
      _warehouseLengthController.text =
          _currentWarehouse!.length?.toString() ?? '';
      _warehouseWidthController.text =
          _currentWarehouse!.width?.toString() ?? '';
      _warehouseHeightController.text =
          _currentWarehouse!.height?.toString() ?? '';
    }
  }

  void _createNewWarehouse() {
    _currentWarehouse = Warehouse3D(
      name: 'مستودع جديد',
      description: 'وصف المستودع',
      length: 100.0,
      width: 80.0,
      height: 40.0,
      xPosition: 0.0,
      yPosition: 0.0,
      zPosition: 0.0,
      cabinets: [],
    );
    _populateFields();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: Text(
          _isEditMode ? T('تعديل المستودع') : T('تصميم مستودع جديد'),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2C3E50),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.save, color: Colors.white),
            onPressed: _saveWarehouse,
          ),
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: _showWarehouseSettings,
          ),
        ],
      ),
      body: Row(
        children: [
          // لوحة التحكم الجانبية
          Container(
            width: 350,
            color: const Color(0xFFF8F9FA),
            child: _buildControlPanel(),
          ),

          // العرض ثلاثي الأبعاد الرئيسي
          Expanded(
            child: Column(
              children: [
                // شريط الحالة العلوي
                _buildStatusBar(),

                // العارض ثلاثي الأبعاد
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(16),
                      child: _currentWarehouse != null
                          ? Warehouse3DViewerWidget(
                              warehouse: _currentWarehouse!,
                              onCabinetTap: _onCabinetTap,
                              onShelfTap: _onShelfTap,
                              onEmptySpaceTap: _onEmptySpaceTap,
                              isAddingCabinet: _isAddingCabinet,
                              isAddingShelf: _isAddingShelf,
                            )
                          : const Center(
                              child: CircularProgressIndicator(),
                            ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildControlPanel() {
    return Column(
      children: [
        // عنوان لوحة التحكم
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: const BoxDecoration(
            color: Color(0xFF34495E),
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(12),
            ),
          ),
          child: const Text(
            '🎮 لوحة التحكم',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),

        // أدوات إضافة العناصر
        Expanded(
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildSectionHeader('➕ إضافة عناصر'),
              _buildAddCabinetButton(),
              const SizedBox(height: 12),
              _buildAddShelfButton(),
              const SizedBox(height: 24),
              _buildSectionHeader('🏗️ إعدادات المستودع'),
              _buildWarehouseSettings(),
              const SizedBox(height: 24),
              _buildSectionHeader('📦 الخزائن الموجودة'),
              _buildCabinetList(),
              const SizedBox(height: 24),
              _buildSectionHeader('💡 نصائح'),
              _buildTipsPanel(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildStatusBar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          // حالة الأداة الحالية
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _isAddingCabinet
                  ? Colors.blue.shade100
                  : _isAddingShelf
                      ? Colors.green.shade100
                      : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _isAddingCabinet
                      ? Icons.add_box
                      : _isAddingShelf
                          ? Icons.view_agenda
                          : Icons.pan_tool,
                  size: 16,
                  color: _isAddingCabinet
                      ? Colors.blue.shade700
                      : _isAddingShelf
                          ? Colors.green.shade700
                          : Colors.grey.shade700,
                ),
                const SizedBox(width: 6),
                Text(
                  _isAddingCabinet
                      ? 'إضافة خزانة'
                      : _isAddingShelf
                          ? 'إضافة رف'
                          : 'وضع التنقل',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: _isAddingCabinet
                        ? Colors.blue.shade700
                        : _isAddingShelf
                            ? Colors.green.shade700
                            : Colors.grey.shade700,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // إحصائيات سريعة
          _buildQuickStat(
              'خزائن', '${_currentWarehouse?.cabinets?.length ?? 0}'),
          const SizedBox(width: 16),
          _buildQuickStat('أرفف', '${_getTotalShelves()}'),
          const SizedBox(width: 16),
          _buildQuickStat('أدوية', '${_getTotalMedicines()}'),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2C3E50),
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Color(0xFF2C3E50),
        ),
      ),
    );
  }

  Widget _buildAddCabinetButton() {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _isAddingCabinet
              ? [Color(0xFF3498DB), Color(0xFF2980B9)]
              : [Color(0xFFECF0F1), Color(0xFFD5DBDB)],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: _startAddingCabinet,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: _isAddingCabinet
                        ? Colors.white.withOpacity(0.2)
                        : Colors.blue.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.add_box,
                    color:
                        _isAddingCabinet ? Colors.white : Colors.blue.shade700,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'إضافة خزانة',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: _isAddingCabinet
                              ? Colors.white
                              : Color(0xFF2C3E50),
                        ),
                      ),
                      Text(
                        'انقر على المستودع لوضع خزانة',
                        style: TextStyle(
                          fontSize: 11,
                          color: _isAddingCabinet
                              ? Colors.white70
                              : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAddShelfButton() {
    return Container(
      width: double.infinity,
      height: 60,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _isAddingShelf
              ? [Color(0xFF27AE60), Color(0xFF2ECC71)]
              : [Color(0xFFECF0F1), Color(0xFFD5DBDB)],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: _startAddingShelf,
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: _isAddingShelf
                        ? Colors.white.withOpacity(0.2)
                        : Colors.green.shade100,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.view_agenda,
                    color:
                        _isAddingShelf ? Colors.white : Colors.green.shade700,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'إضافة رف',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color:
                              _isAddingShelf ? Colors.white : Color(0xFF2C3E50),
                        ),
                      ),
                      Text(
                        'انقر على خزانة لإضافة رف',
                        style: TextStyle(
                          fontSize: 11,
                          color: _isAddingShelf
                              ? Colors.white70
                              : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWarehouseSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            _buildSettingRow(
                'الطول', '${_currentWarehouse?.length?.toInt() ?? 0} م'),
            _buildSettingRow(
                'العرض', '${_currentWarehouse?.width?.toInt() ?? 0} م'),
            _buildSettingRow(
                'الارتفاع', '${_currentWarehouse?.height?.toInt() ?? 0} م'),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _showWarehouseSettings,
                icon: const Icon(Icons.settings, size: 16),
                label: const Text('تعديل الإعدادات'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: Colors.grey.shade600)),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  Widget _buildCabinetList() {
    final cabinets = _currentWarehouse?.cabinets ?? [];
    if (cabinets.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.grey.shade100,
          borderRadius: BorderRadius.circular(8),
        ),
        child: const Text(
          'لا توجد خزائن\nابدأ بإضافة خزانة جديدة',
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return Column(
      children: cabinets.map((cabinet) => _buildCabinetItem(cabinet)).toList(),
    );
  }

  Widget _buildCabinetItem(Cabinet3D cabinet) {
    final shelfCount = cabinet.shelves?.length ?? 0;
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: Color(int.parse(
                cabinet.color?.replaceFirst('#', '0xFF') ?? '0xFF3498DB')),
            shape: BoxShape.circle,
          ),
        ),
        title: Text(
          cabinet.name ?? 'خزانة',
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        ),
        subtitle: Text('$shelfCount أرفف'),
        trailing: PopupMenuButton(
          icon: const Icon(Icons.more_vert, size: 16),
          itemBuilder: (context) => [
            PopupMenuItem(
              child: const Text('تعديل'),
              onTap: () => _editCabinet(cabinet),
            ),
            PopupMenuItem(
              child: const Text('حذف'),
              onTap: () => _deleteCabinet(cabinet),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipsPanel() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: const [
          Text(
            '💡 نصائح سريعة:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 8),
          Text('• اسحب بالماوس للدوران', style: TextStyle(fontSize: 12)),
          Text('• استخدم العجلة للزوم', style: TextStyle(fontSize: 12)),
          Text('• انقر على خزانة لتحديدها', style: TextStyle(fontSize: 12)),
          Text('• احفظ عملك بانتظام', style: TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  int _getTotalShelves() {
    return _currentWarehouse?.cabinets?.fold<int>(
            0, (sum, cabinet) => sum + (cabinet.shelves?.length ?? 0)) ??
        0;
  }

  int _getTotalMedicines() {
    return _currentWarehouse?.cabinets?.fold<int>(
            0,
            (sum, cabinet) =>
                sum +
                (cabinet.shelves?.fold<int>(
                        0,
                        (shelveSum, shelf) =>
                            shelveSum + (shelf.products?.length ?? 0)) ??
                    0)) ??
        0;
  }

  void _editCabinet(Cabinet3D cabinet) {
    // TODO: تنفيذ تعديل الخزانة
  }

  void _deleteCabinet(Cabinet3D cabinet) {
    setState(() {
      _currentWarehouse?.cabinets?.remove(cabinet);
    });
  }

  Widget _buildWarehouseInfoPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // مقبض السحب
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 12),

          // معلومات المستودع
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _currentWarehouse?.name ?? 'مستودع جديد',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF2C3E50),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _currentWarehouse?.description ?? 'وصف المستودع',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: const Color(0xFF3498DB).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_currentWarehouse?.cabinets?.length ?? 0} خزانة',
                  style: const TextStyle(
                    color: Color(0xFF3498DB),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // أبعاد المستودع
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildDimensionChip(
                'الطول',
                '${_currentWarehouse?.length?.toStringAsFixed(1) ?? '0'} م',
                Icons.straighten,
              ),
              _buildDimensionChip(
                'العرض',
                '${_currentWarehouse?.width?.toStringAsFixed(1) ?? '0'} م',
                Icons.width_normal,
              ),
              _buildDimensionChip(
                'الارتفاع',
                '${_currentWarehouse?.height?.toStringAsFixed(1) ?? '0'} م',
                Icons.height,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDimensionChip(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: const Color(0xFF34495E), size: 20),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2C3E50),
          ),
        ),
      ],
    );
  }

  void _startAddingCabinet() {
    setState(() {
      _isAddingCabinet = true;
      _isAddingShelf = false;
    });
  }

  void _startAddingShelf() {
    if (_controller.selectedCabinetId == null) {
      errorSnackBar(
        message: T('يرجى اختيار خزانة أولاً'),
        context: context,
      );
      return;
    }

    setState(() {
      _isAddingShelf = true;
      _isAddingCabinet = false;
    });
  }

  void _toggleMode() {
    setState(() {
      _isAddingCabinet = false;
      _isAddingShelf = false;
    });
  }

  void _onCabinetTap(Cabinet3D cabinet) {
    _controller.selectCabinet(cabinet.id.toString());
    _showCabinetDetails(cabinet);
  }

  void _onShelfTap(Shelf3D shelf) {
    _controller.selectShelf(shelf.id.toString());
    _showShelfDetails(shelf);
  }

  void _onEmptySpaceTap(double x, double y, double z) {
    if (_isAddingCabinet) {
      _showAddCabinetDialog(x, y, z);
    } else if (_isAddingShelf) {
      _showAddShelfDialog(x, y, z);
    }
  }

  void _showAddCabinetDialog(double x, double y, double z) {
    final nameController = TextEditingController();
    final codeController = TextEditingController();
    final lengthController = TextEditingController(text: '20');
    final widthController = TextEditingController(text: '15');
    final heightController = TextEditingController(text: '25');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T('إضافة خزانة جديدة')),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: T('اسم الخزانة'),
                  border: const OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: codeController,
                decoration: InputDecoration(
                  labelText: T('رمز الخزانة'),
                  border: const OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: lengthController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('الطول (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: widthController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('العرض (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: heightController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('الارتفاع (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(T('إلغاء')),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.isNotEmpty) {
                final cabinet = Cabinet3D(
                  name: nameController.text,
                  code: codeController.text,
                  warehouseId: _currentWarehouse!.id,
                  length: double.tryParse(lengthController.text) ?? 20,
                  width: double.tryParse(widthController.text) ?? 15,
                  height: double.tryParse(heightController.text) ?? 25,
                  xPosition: x,
                  yPosition: y,
                  zPosition: z,
                  color: '#3498DB',
                  shelves: [],
                );

                final success = await _controller.createCabinet3D(cabinet);
                if (success) {
                  _currentWarehouse!.cabinets ??= [];
                  _currentWarehouse!.cabinets!.add(cabinet);
                  setState(() {
                    _isAddingCabinet = false;
                  });
                  successSnackBar(
                    message: T('تم إضافة الخزانة بنجاح'),
                    context: context,
                  );
                }
                Navigator.pop(context);
              }
            },
            child: Text(T('إضافة')),
          ),
        ],
      ),
    );
  }

  void _showAddShelfDialog(double x, double y, double z) {
    final nameController = TextEditingController();
    final codeController = TextEditingController();
    final lengthController = TextEditingController(text: '10');
    final widthController = TextEditingController(text: '8');
    final heightController = TextEditingController(text: '5');
    final capacityController = TextEditingController(text: '50');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T('إضافة رف جديد')),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: InputDecoration(
                  labelText: T('اسم الرف'),
                  border: const OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: codeController,
                decoration: InputDecoration(
                  labelText: T('رمز الرف'),
                  border: const OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: lengthController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('الطول (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: widthController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('العرض (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: heightController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('الارتفاع (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              TextField(
                controller: capacityController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: T('السعة القصوى'),
                  border: const OutlineInputBorder(),
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(T('إلغاء')),
          ),
          ElevatedButton(
            onPressed: () async {
              if (nameController.text.isNotEmpty) {
                final shelf = Shelf3D(
                  name: nameController.text,
                  code: codeController.text,
                  cabinetId: int.parse(_controller.selectedCabinetId!),
                  length: double.tryParse(lengthController.text) ?? 10,
                  width: double.tryParse(widthController.text) ?? 8,
                  height: double.tryParse(heightController.text) ?? 5,
                  xPosition: x,
                  yPosition: y,
                  zPosition: z,
                  status: ShelfStatus.empty,
                  maxCapacity: int.tryParse(capacityController.text) ?? 50,
                  currentOccupancy: 0,
                  products: [],
                );

                final success = await _controller.createShelf3D(shelf);
                if (success) {
                  setState(() {
                    _isAddingShelf = false;
                  });
                  successSnackBar(
                    message: T('تم إضافة الرف بنجاح'),
                    context: context,
                  );
                }
                Navigator.pop(context);
              }
            },
            child: Text(T('إضافة')),
          ),
        ],
      ),
    );
  }

  void _showCabinetDetails(Cabinet3D cabinet) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF3498DB),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.inbox, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          cabinet.name ?? 'خزانة',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          cabinet.code ?? '',
                          style: const TextStyle(
                            color: Colors.white70,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: const EdgeInsets.all(16),
                children: [
                  Text(
                    T('الأرفف'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...?cabinet.shelves?.map((shelf) => Card(
                        child: ListTile(
                          leading: Icon(
                            Icons.shelves,
                            color: _getShelfStatusColor(shelf.status),
                          ),
                          title: Text(shelf.name ?? 'رف'),
                          subtitle: Text(
                            '${shelf.currentOccupancy ?? 0}/${shelf.maxCapacity ?? 0} عنصر',
                          ),
                          trailing: Text(
                            _getShelfStatusText(shelf.status),
                            style: TextStyle(
                              color: _getShelfStatusColor(shelf.status),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          onTap: () => _onShelfTap(shelf),
                        ),
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showShelfDetails(Shelf3D shelf) {
    // TODO: تطبيق عرض تفاصيل الرف
  }

  Color _getShelfStatusColor(ShelfStatus? status) {
    switch (status) {
      case ShelfStatus.empty:
        return Colors.grey;
      case ShelfStatus.partial:
        return Colors.orange;
      case ShelfStatus.full:
        return Colors.green;
      case ShelfStatus.overloaded:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getShelfStatusText(ShelfStatus? status) {
    switch (status) {
      case ShelfStatus.empty:
        return T('فارغ');
      case ShelfStatus.partial:
        return T('جزئي');
      case ShelfStatus.full:
        return T('ممتلئ');
      case ShelfStatus.overloaded:
        return T('زائد');
      default:
        return T('غير محدد');
    }
  }

  void _showWarehouseSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T('إعدادات المستودع')),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: _warehouseNameController,
                decoration: InputDecoration(
                  labelText: T('اسم المستودع'),
                  border: const OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _warehouseDescController,
                decoration: InputDecoration(
                  labelText: T('وصف المستودع'),
                  border: const OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _warehouseLengthController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('الطول (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _warehouseWidthController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('العرض (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _warehouseHeightController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: T('الارتفاع (م)'),
                        border: const OutlineInputBorder(),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(T('إلغاء')),
          ),
          ElevatedButton(
            onPressed: () {
              _updateWarehouseSettings();
              Navigator.pop(context);
            },
            child: Text(T('حفظ')),
          ),
        ],
      ),
    );
  }

  void _updateWarehouseSettings() {
    setState(() {
      _currentWarehouse?.name = _warehouseNameController.text;
      _currentWarehouse?.description = _warehouseDescController.text;
      _currentWarehouse?.length =
          double.tryParse(_warehouseLengthController.text);
      _currentWarehouse?.width =
          double.tryParse(_warehouseWidthController.text);
      _currentWarehouse?.height =
          double.tryParse(_warehouseHeightController.text);
    });
  }

  Future<void> _saveWarehouse() async {
    if (_currentWarehouse == null) return;

    _updateWarehouseSettings();

    bool success;
    if (_isEditMode) {
      // TODO: تطبيق تحديث المستودع
      success = true;
    } else {
      success = await _controller.createWarehouse3D(_currentWarehouse!);
    }

    if (success) {
      successSnackBar(
        message: T('تم حفظ المستودع بنجاح'),
        context: context,
      );
      Navigator.pop(context);
    } else {
      errorSnackBar(
        message: T('فشل في حفظ المستودع'),
        context: context,
      );
    }
  }

  @override
  void dispose() {
    _warehouseNameController.dispose();
    _warehouseDescController.dispose();
    _warehouseLengthController.dispose();
    _warehouseWidthController.dispose();
    _warehouseHeightController.dispose();
    super.dispose();
  }
}
