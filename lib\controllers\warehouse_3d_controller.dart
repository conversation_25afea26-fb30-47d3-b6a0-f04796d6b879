import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/models/model/warehouse_3d_model.dart';
import 'package:sqflite/sqflite.dart';

class Warehouse3DController with ChangeNotifier {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  List<Warehouse3D> warehouses3D = [];
  List<Cabinet3D> cabinets = [];
  List<Shelf3D> shelves = [];
  List<ProductLocation3D> productLocations = [];

  bool isLoading = false;
  String? selectedWarehouseId;
  String? selectedCabinetId;
  String? selectedShelfId;

  // جلب جميع المستودعات ثلاثية الأبعاد
  Future<void> fetchWarehouses3D() async {
    try {
      isLoading = true;
      notifyListeners();

      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'Warehouse3D',
        where: 'BranchId = ?',
        whereArgs: [AppController.currentBranchId],
      );

      warehouses3D = maps.map((map) => Warehouse3D.fromJson(map)).toList();

      // جلب الخزائن لكل مستودع
      for (var warehouse in warehouses3D) {
        await _loadCabinetsForWarehouse(warehouse.id!);
      }

      isLoading = false;
      notifyListeners();
    } catch (e) {
      isLoading = false;
      notifyListeners();
      if (kDebugMode) {
        print('Error fetching warehouses 3D: $e');
      }
    }
  }

  // جلب الخزائن لمستودع معين
  Future<void> _loadCabinetsForWarehouse(int warehouseId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> cabinetMaps = await db.query(
        'Cabinet3D',
        where: 'warehouseId = ? AND BranchId = ?',
        whereArgs: [warehouseId, AppController.currentBranchId],
      );

      List<Cabinet3D> warehouseCabinets =
          cabinetMaps.map((map) => Cabinet3D.fromJson(map)).toList();

      // جلب الأرفف لكل خزانة
      for (var cabinet in warehouseCabinets) {
        await _loadShelvesForCabinet(cabinet.id!);
      }

      // إضافة الخزائن إلى المستودع
      final warehouse = warehouses3D.firstWhere((w) => w.id == warehouseId);
      warehouse.cabinets = warehouseCabinets;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading cabinets for warehouse $warehouseId: $e');
      }
    }
  }

  // جلب الأرفف لخزانة معينة
  Future<void> _loadShelvesForCabinet(int cabinetId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> shelfMaps = await db.query(
        'Shelf3D',
        where: 'cabinetId = ? AND BranchId = ?',
        whereArgs: [cabinetId, AppController.currentBranchId],
      );

      List<Shelf3D> cabinetShelves =
          shelfMaps.map((map) => Shelf3D.fromJson(map)).toList();

      // جلب المنتجات لكل رف
      for (var shelf in cabinetShelves) {
        await _loadProductsForShelf(shelf.id!);
      }

      // إضافة الأرفف إلى الخزانة
      final warehouse = warehouses3D.firstWhere(
          (w) => w.cabinets?.any((c) => c.id == cabinetId) ?? false);
      final cabinet = warehouse.cabinets?.firstWhere((c) => c.id == cabinetId);
      cabinet?.shelves = cabinetShelves;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading shelves for cabinet $cabinetId: $e');
      }
    }
  }

  // جلب المنتجات لرف معين
  Future<void> _loadProductsForShelf(int shelfId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> productMaps = await db.query(
        'ProductLocation3D',
        where: 'shelfId = ? AND BranchId = ?',
        whereArgs: [shelfId, AppController.currentBranchId],
      );

      List<ProductLocation3D> shelfProducts =
          productMaps.map((map) => ProductLocation3D.fromJson(map)).toList();

      // إضافة المنتجات إلى الرف
      final warehouse = warehouses3D.firstWhere((w) =>
          w.cabinets
              ?.any((c) => c.shelves?.any((s) => s.id == shelfId) ?? false) ??
          false);
      final cabinet = warehouse.cabinets
          ?.firstWhere((c) => c.shelves?.any((s) => s.id == shelfId) ?? false);
      final shelf = cabinet?.shelves?.firstWhere((s) => s.id == shelfId);
      shelf?.products = shelfProducts;
    } catch (e) {
      if (kDebugMode) {
        print('Error loading products for shelf $shelfId: $e');
      }
    }
  }

  // إنشاء مستودع جديد
  Future<bool> createWarehouse3D(Warehouse3D warehouse) async {
    try {
      final db = await _dbHelper.database;

      final id = await db.insert('Warehouse3D', {
        'name': warehouse.name,
        'description': warehouse.description,
        'length': warehouse.length,
        'width': warehouse.width,
        'height': warehouse.height,
        'xPosition': warehouse.xPosition,
        'yPosition': warehouse.yPosition,
        'zPosition': warehouse.zPosition,
        'BranchId': AppController.currentBranchId,
      });

      warehouse.id = id;
      warehouses3D.add(warehouse);
      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating warehouse 3D: $e');
      }
      return false;
    }
  }

  // إنشاء خزانة جديدة
  Future<bool> createCabinet3D(Cabinet3D cabinet) async {
    try {
      final db = await _dbHelper.database;

      final id = await db.insert('Cabinet3D', {
        'name': cabinet.name,
        'code': cabinet.code,
        'warehouseId': cabinet.warehouseId,
        'length': cabinet.length,
        'width': cabinet.width,
        'height': cabinet.height,
        'xPosition': cabinet.xPosition,
        'yPosition': cabinet.yPosition,
        'zPosition': cabinet.zPosition,
        'color': cabinet.color,
        'BranchId': AppController.currentBranchId,
      });

      cabinet.id = id;

      // إضافة الخزانة إلى المستودع المناسب
      final warehouse =
          warehouses3D.firstWhere((w) => w.id == cabinet.warehouseId);
      warehouse.cabinets ??= [];
      warehouse.cabinets!.add(cabinet);

      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating cabinet 3D: $e');
      }
      return false;
    }
  }

  // إنشاء رف جديد
  Future<bool> createShelf3D(Shelf3D shelf) async {
    try {
      final db = await _dbHelper.database;

      final id = await db.insert('Shelf3D', {
        'name': shelf.name,
        'code': shelf.code,
        'cabinetId': shelf.cabinetId,
        'length': shelf.length,
        'width': shelf.width,
        'height': shelf.height,
        'xPosition': shelf.xPosition,
        'yPosition': shelf.yPosition,
        'zPosition': shelf.zPosition,
        'status': shelf.status?.toString().split('.').last,
        'maxCapacity': shelf.maxCapacity,
        'currentOccupancy': shelf.currentOccupancy,
        'BranchId': AppController.currentBranchId,
      });

      shelf.id = id;

      // إضافة الرف إلى الخزانة المناسبة
      final warehouse = warehouses3D.firstWhere(
          (w) => w.cabinets?.any((c) => c.id == shelf.cabinetId) ?? false);
      final cabinet =
          warehouse.cabinets?.firstWhere((c) => c.id == shelf.cabinetId);
      cabinet?.shelves ??= [];
      cabinet?.shelves!.add(shelf);

      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating shelf 3D: $e');
      }
      return false;
    }
  }

  // إضافة موقع منتج في المستودع
  Future<bool> addProductLocation(ProductLocation3D productLocation) async {
    try {
      final db = await _dbHelper.database;

      final id = await db.insert('ProductLocation3D', {
        'productId': productLocation.productId,
        'productName': productLocation.productName,
        'productCode': productLocation.productCode,
        'shelfId': productLocation.shelfId,
        'cabinetId': productLocation.cabinetId,
        'warehouseId': productLocation.warehouseId,
        'quantity': productLocation.quantity,
        'xPosition': productLocation.xPosition,
        'yPosition': productLocation.yPosition,
        'zPosition': productLocation.zPosition,
        'lastUpdated': DateTime.now().toIso8601String(),
        'batchNumber': productLocation.batchNumber,
        'expiryDate': productLocation.expiryDate?.toIso8601String(),
        'BranchId': AppController.currentBranchId,
      });

      productLocation.id = id;

      // إضافة الموقع إلى الرف المناسب
      final warehouse =
          warehouses3D.firstWhere((w) => w.id == productLocation.warehouseId);
      final cabinet = warehouse.cabinets
          ?.firstWhere((c) => c.id == productLocation.cabinetId);
      final shelf =
          cabinet?.shelves?.firstWhere((s) => s.id == productLocation.shelfId);
      shelf?.products ??= [];
      shelf?.products!.add(productLocation);

      // تحديث إشغال الرف
      if (shelf != null) {
        shelf.currentOccupancy = (shelf.currentOccupancy ?? 0) + 1;
        await _updateShelfOccupancy(shelf.id!, shelf.currentOccupancy!);
      }

      notifyListeners();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error adding product location: $e');
      }
      return false;
    }
  }

  // تحديث إشغال الرف
  Future<void> _updateShelfOccupancy(int shelfId, int occupancy) async {
    try {
      final db = await _dbHelper.database;
      await db.update(
        'Shelf3D',
        {'currentOccupancy': occupancy},
        where: 'id = ?',
        whereArgs: [shelfId],
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error updating shelf occupancy: $e');
      }
    }
  }

  // البحث عن منتج بالكود أو الاسم
  Future<List<ProductLocation3D>> searchProduct(String searchTerm) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> results = await db.query(
        'ProductLocation3D',
        where: '''
          (productName LIKE ? OR productCode LIKE ? OR batchNumber LIKE ?) 
          AND BranchId = ?
        ''',
        whereArgs: [
          '%$searchTerm%',
          '%$searchTerm%',
          '%$searchTerm%',
          AppController.currentBranchId
        ],
      );

      return results.map((map) => ProductLocation3D.fromJson(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error searching product: $e');
      }
      return [];
    }
  }

  // الحصول على موقع منتج معين
  Future<ProductLocation3D?> getProductLocation(int productId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> results = await db.query(
        'ProductLocation3D',
        where: 'productId = ? AND BranchId = ?',
        whereArgs: [productId, AppController.currentBranchId],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return ProductLocation3D.fromJson(results.first);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting product location: $e');
      }
      return null;
    }
  }

  // حفظ تخطيط المستودع
  Future<bool> saveWarehouseLayout(WarehouseLayout layout) async {
    try {
      final db = await _dbHelper.database;

      await db.insert(
        'WarehouseLayout',
        {
          'warehouseId': layout.warehouseId,
          'layoutName': layout.layoutName,
          'description': layout.description,
          'createdDate': layout.createdDate?.toIso8601String(),
          'lastModified': DateTime.now().toIso8601String(),
          'createdBy': layout.createdBy,
          'layoutData': jsonEncode(layout.layoutData),
          'BranchId': AppController.currentBranchId,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving warehouse layout: $e');
      }
      return false;
    }
  }

  // جلب تخطيط المستودع
  Future<WarehouseLayout?> getWarehouseLayout(
      int warehouseId, String layoutName) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> results = await db.query(
        'WarehouseLayout',
        where: 'warehouseId = ? AND layoutName = ? AND BranchId = ?',
        whereArgs: [warehouseId, layoutName, AppController.currentBranchId],
        limit: 1,
      );

      if (results.isNotEmpty) {
        final map = results.first;
        map['layoutData'] = jsonDecode(map['layoutData']);
        return WarehouseLayout.fromJson(map);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting warehouse layout: $e');
      }
      return null;
    }
  }

  // تحديد المستودع المحدد
  void selectWarehouse(String? warehouseId) {
    selectedWarehouseId = warehouseId;
    selectedCabinetId = null;
    selectedShelfId = null;
    notifyListeners();
  }

  // تحديد الخزانة المحددة
  void selectCabinet(String? cabinetId) {
    selectedCabinetId = cabinetId;
    selectedShelfId = null;
    notifyListeners();
  }

  // تحديد الرف المحدد
  void selectShelf(String? shelfId) {
    selectedShelfId = shelfId;
    notifyListeners();
  }

  // مسح جميع البيانات (للتطوير فقط)
  Future<void> clearAllData() async {
    try {
      final db = await _dbHelper.database;
      await db.transaction((txn) async {
        await txn.delete('ProductLocation3D',
            where: 'BranchId = ?', whereArgs: [AppController.currentBranchId]);
        await txn.delete('Shelf3D',
            where: 'BranchId = ?', whereArgs: [AppController.currentBranchId]);
        await txn.delete('Cabinet3D',
            where: 'BranchId = ?', whereArgs: [AppController.currentBranchId]);
        await txn.delete('Warehouse3D',
            where: 'BranchId = ?', whereArgs: [AppController.currentBranchId]);
        await txn.delete('WarehouseLayout',
            where: 'BranchId = ?', whereArgs: [AppController.currentBranchId]);
      });

      warehouses3D.clear();
      cabinets.clear();
      shelves.clear();
      productLocations.clear();
      selectedWarehouseId = null;
      selectedCabinetId = null;
      selectedShelfId = null;

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing data: $e');
      }
    }
  }
}
