import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/payment_type_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_calculator_widget.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/unit_selection_dialog.dart';
import 'package:inventory_application/services/printer_service.dart';
import 'package:provider/provider.dart';
import 'dart:io' show Platform;

class POSCartWidget extends StatefulWidget {
  final List<ProductDTO> products;

  const POSCartWidget({
    Key? key,
    required this.products,
  }) : super(key: key);

  @override
  State<POSCartWidget> createState() => POSCartWidgetState();
}

class POSCartWidgetState extends State<POSCartWidget> {
  // Public method to increase the quantity of the last added product
  void increaseLastProductQuantity() {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final products = saleInvoiceController.selectedSaleInvoiceProduct;

    if (products.isEmpty) {
      // No products in cart
      return;
    }

    // Get the last product in the cart
    final lastProduct = products.last;
    final currentQuantity = lastProduct.quantity ?? 1.0;

    // Increase quantity by 1
    saleInvoiceController.updateProductQuantity(
      lastProduct.id!,
      currentQuantity + 1,
      lastProduct.virtualProductId,
    );
  }

  // Public method to decrease the quantity of the last added product
  void decreaseLastProductQuantity() {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final products = saleInvoiceController.selectedSaleInvoiceProduct;

    if (products.isEmpty) {
      // No products in cart
      return;
    }

    // Get the last product in the cart
    final lastProduct = products.last;
    final currentQuantity = lastProduct.quantity ?? 1.0;

    if (currentQuantity <= 1) {
      // Don't decrease below 1
      return;
    }

    // Decrease quantity by 1
    saleInvoiceController.updateProductQuantity(
      lastProduct.id!,
      currentQuantity - 1,
      lastProduct.virtualProductId,
    );
  }

  // Public method to edit the price of the last added product
  void editLastProductPrice() {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final products = saleInvoiceController.selectedSaleInvoiceProduct;

    if (products.isEmpty) {
      // No products in cart
      return;
    }

    // Get the last product in the cart
    final lastProduct = products.last;

    // Show the price edit dialog
    _showPriceEditDialogWithFocus(context, lastProduct, saleInvoiceController);
  }

  void showNotesDialog(
      BuildContext context, String? note, Function(String) onSave) {
    TextEditingController _notesController = TextEditingController();
    if (note != null) _notesController.text = note;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: SizedBox(
            width: context.width / 1.5, // Set the desired width here
            child: Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "ادخل ملاحظاتك",
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 15),
                  TextField(
                    controller: _notesController,
                    maxLines: 5,
                    decoration: InputDecoration(
                      hintText: "اكتب ملاحظاتك هنا...",
                      border: OutlineInputBorder(),
                    ),
                  ),
                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        child: Text("الغاء"),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                      SizedBox(width: 10),
                      ElevatedButton(
                        child: Text("حفظ"),
                        onPressed: () {
                          String notes = _notesController.text.trim();
                          onSave(notes);
                          Navigator.of(context).pop();
                        },
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Show price edit dialog with focus on the price input field
  // Show unit selection dialog
  Future<void> _showUnitSelectionDialog(
      BuildContext context, ProductDTO product) async {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);

    // Show the unit selection dialog
    final selectedUnit = await showUnitSelectionDialog(
      context: context,
      product: product,
    );

    // If a unit was selected, update the product
    if (selectedUnit != null) {
      // Update the unit in the controller
      saleInvoiceController.updateProductUnit(
        product.id!,
        selectedUnit,
        product.virtualProductId,
      );

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(T('Unit updated to ${selectedUnit.unitName}')),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Colors.green.shade600,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _showPriceEditDialogWithFocus(BuildContext context, ProductDTO product,
      SaleInvoiceController saleInvoiceController) {
    final price = product.price ?? 0.0;
    bool isValidPrice = true;

    // Create a text editing controller with the current price
    final TextEditingController priceController = TextEditingController(
      text: price.toStringAsFixed(2),
    );

    // Create a focus node to auto-focus the price input field
    final FocusNode priceFocusNode = FocusNode();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(builder: (context, setState) {
        // Auto-focus the price input field when the dialog opens
        WidgetsBinding.instance.addPostFrameCallback((_) {
          priceFocusNode.requestFocus();
        });

        return AlertDialog(
          title: Text(T('Edit Price')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Product info
              Text(
                product.title ?? T('Unknown product'),
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                product.code ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Current price info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    T('Current Price:'),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  Text(
                    price.toStringAsFixed(2),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // New price input
              TextField(
                controller: priceController,
                focusNode: priceFocusNode,
                decoration: InputDecoration(
                  labelText: T('New Price'),
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.price_change_outlined),
                  errorText:
                      !isValidPrice ? T('Please enter a valid price') : null,
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                autofocus: true,
                onChanged: (value) {
                  final newPrice = double.tryParse(value);
                  setState(() {
                    isValidPrice = newPrice != null && newPrice >= 0;
                  });
                },
                // Handle Enter key press to update the price
                onSubmitted: (value) {
                  if (isValidPrice) {
                    final newPrice = double.tryParse(value) ?? 0.0;
                    if (newPrice >= 0) {
                      saleInvoiceController.updateProductPrice(
                        product.id!,
                        newPrice,
                        product.virtualProductId,
                      );
                      Navigator.of(context).pop();

                      // Show success message
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(T(
                              'Price updated from ${price.toStringAsFixed(2)} to ${newPrice.toStringAsFixed(2)}')),
                          behavior: SnackBarBehavior.floating,
                          backgroundColor: Colors.green.shade600,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(T('Cancel')),
            ),
            ElevatedButton(
              onPressed: !isValidPrice
                  ? null
                  : () {
                      final newPrice =
                          double.tryParse(priceController.text) ?? 0.0;
                      if (newPrice >= 0) {
                        saleInvoiceController.updateProductPrice(
                          product.id!,
                          newPrice,
                          product.virtualProductId,
                        );
                        Navigator.of(context).pop();

                        // Show success message
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(T(
                                'Price updated from ${price.toStringAsFixed(2)} to ${newPrice.toStringAsFixed(2)}')),
                            behavior: SnackBarBehavior.floating,
                            backgroundColor: Colors.green.shade600,
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      }
                    },
              child: Text(T('Update Price')),
            ),
          ],
        );
      }),
    );
  }

  void printLastInvoice() async {
    var lastInvoiceNumber =
        Provider.of<SaleInvoiceController>(context, listen: false)
            .lastAddedInvoiceCode;

    if (lastInvoiceNumber.isEmpty) {
      errorSnackBar(message: T("No last order found to print"));
      return;
    }

    // Get the invoice details
    var invoiceResult =
        await Provider.of<InvoiceController>(context, listen: false)
            .getInoviceByCode(invoiceLocalCode: lastInvoiceNumber);

    if (invoiceResult == null) {
      errorSnackBar(message: T("Failed to retrieve last invoice for printing"));
      return;
    }

    // Print the invoice
    if (mounted) {
      try {
        await PrinterService.printInvoice(invoiceResult, context);
      } catch (e) {
        errorSnackBar(message: T("Error printing invoice: $e"));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final saleInvoiceController = Provider.of<SaleInvoiceController>(context);

    final invoice = saleInvoiceController.invoice;
    final total = invoice.total ?? 0.0;
    final discountValue = invoice.discountValue ?? 0.0;
    final totalAfterDiscount = invoice.totalAfterDiscount ?? 0.0;
    final products = widget.products;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: context.newPrimaryColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.shopping_cart,
                  color: Colors.white,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '${saleInvoiceController.invoice.appReferanceCode}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${products.length} ${T('items')}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Empty cart message
          if (products.isEmpty)
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.shopping_cart_outlined,
                      size: 60,
                      color: Colors.grey.shade400,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      T('Your cart is empty'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      T('Add products by clicking on them'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),

          // Cart items
          if (products.isNotEmpty)
            Expanded(
              child: ListView.builder(
                itemCount: products.length,
                itemBuilder: (context, index) {
                  final product = products[index];
                  return _buildCartItem(context, product, index);
                },
              ),
            ),

          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(
                top: BorderSide(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                InkWell(
                  onTap: () {
                    showBottomSheetWithCalculator(context);
                  },
                  child: Column(
                    children: [
                      const Icon(
                        Icons.calculate_outlined,
                        size: 30,
                      ),
                      if (Platform.isWindows)
                        Text(
                          T('F5'),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ),
                // InkWell(
                //   onTap: () {
                //     _showPaymentDialog(context);
                //   },
                //   child: Column(
                //     children: [
                //       const Icon(
                //         Icons.payment_outlined,
                //         size: 30,
                //       ),
                //       if (Platform.isWindows)
                //         Text(
                //           T('F8'),
                //           style: TextStyle(
                //             fontSize: 10,
                //             color: Colors.grey.shade600,
                //           ),
                //         ),
                //     ],
                //   ),
                // ),
                InkWell(
                  onTap: () {
                    showNotesDialog(context, invoice.note, (notes) {
                      invoice.note = notes;
                      // Save to your backend or local state
                    });
                  },
                  child: Column(
                    children: [
                      const Icon(
                        Icons.note_add,
                        size: 30,
                      ),
                      if (Platform.isWindows)
                        Text(
                          T('F6'),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () async {
                    var confirmation = await showConfirmDialog(
                        content: "هل انت متاكد من طباعة الفاتورة الاخيرة؟",
                        title: "تاكيد عميلة الطباعة",
                        confirmText: "نعم",
                        backText: "لا");
                    if (confirmation == true) printLastInvoice();
                  },
                  child: Column(
                    children: [
                      const Icon(
                        Icons.print,
                        size: 30,
                      ),
                      if (Platform.isWindows)
                        Text(
                          T('F7'),
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Summary
          if (products.isNotEmpty)
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                border: Border(
                  top: BorderSide(
                    color: Colors.grey.shade200,
                    width: 1,
                  ),
                ),
              ),
              child: Column(
                children: [
                  // Subtotal
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        T('المجموع الفرعي'),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                        ),
                      ),
                      Text(
                        total.toStringAsFixed(2),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey.shade800,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 5),

                  // Discount
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Text(
                            "${T('Discount')} ${Platform.isWindows ? "CTRL+D" : ""}",
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          IconButton(
                            icon: Icon(
                              Icons.edit,
                              size: 14,
                              color: context.newPrimaryColor,
                            ),
                            onPressed: () =>
                                _showDiscountDialogWithFocus(context, total),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                      Text(
                        discountValue.toStringAsFixed(2),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade700,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),
                  const Divider(),

                  // Total
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        T('Total'),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        totalAfterDiscount.toStringAsFixed(2),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: context.newPrimaryColor,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Payment info
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Text(
                            T('Payment'),
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade700,
                            ),
                          ),
                          const SizedBox(width: 5),
                        ],
                      ),
                      Text(
                        (invoice.paymentValue ?? 0.0).toStringAsFixed(2),
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.green.shade700,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCartItem(BuildContext context, ProductDTO product, int index) {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final quantity = product.quantity ?? 1.0;
    final price = product.price ?? 0.0;
    final total = (quantity * price);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Product details
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  product.title ?? T('Unknown product'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 4),
                Text(
                  product.code ?? '',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Text(
                      '${price.toStringAsFixed(2)} × ${quantity.toStringAsFixed(quantity.truncateToDouble() == quantity ? 0 : 2)} = ${total.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: context.newPrimaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 6),
                    // Price edit button
                  ],
                ),
                Row(
                  children: [
                    // Edit Price Button
                    InkWell(
                      onTap: () => _showPriceEditDialog(
                          context, product, saleInvoiceController),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.edit,
                              size: 10,
                              color: Colors.blue.shade700,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              T('Edit Price'),
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.blue.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Add some space between buttons
                    const SizedBox(width: 8),

                    // Edit Unit Button
                    InkWell(
                      onTap: () => _showUnitSelectionDialog(context, product),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.green.shade200),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.straighten,
                              size: 10,
                              color: Colors.green.shade700,
                            ),
                            const SizedBox(width: 2),
                            Text(
                              T('Unit: ${product.uniteName ?? "N/A"}'),
                              style: TextStyle(
                                fontSize: 10,
                                color: Colors.green.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Quantity controls
          Row(
            children: [
              // Decrease button
              _buildQuantityButton(
                context: context,
                icon: Icons.remove,
                onPressed: () {
                  if (quantity > 1) {
                    saleInvoiceController.updateProductQuantity(
                      product.id!,
                      quantity - 1,
                      product.virtualProductId,
                    );
                  }
                },
              ),

              // Quantity display
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  quantity.toStringAsFixed(
                      quantity.truncateToDouble() == quantity ? 0 : 2),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // Increase button
              _buildQuantityButton(
                context: context,
                icon: Icons.add,
                onPressed: () {
                  saleInvoiceController.updateProductQuantity(
                    product.id!,
                    quantity + 1,
                    product.virtualProductId,
                  );
                },
              ),

              const SizedBox(width: 16),

              // Remove button
              _buildQuantityButton(
                context: context,
                icon: Icons.delete_outline,
                color: Colors.red.shade400,
                onPressed: () {
                  saleInvoiceController.deleteProductFromSelectedList(
                    product.id!,
                    virtualProductId: product.virtualProductId,
                  );
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuantityButton({
    required BuildContext context,
    required IconData icon,
    Color? color,
    required VoidCallback onPressed,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(6),
      child: Container(
        padding: const EdgeInsets.all(6),
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.grey.shade300,
          ),
          borderRadius: BorderRadius.circular(6),
        ),
        child: Icon(
          icon,
          size: 16,
          color: color ?? Colors.grey.shade700,
        ),
      ),
    );
  }

  // Public method to open the discount dialog
  void openDiscountDialog() {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final total = saleInvoiceController.invoice.total ?? 0.0;

    _showDiscountDialogWithFocus(context, total);
  }

  void _showDiscountDialogWithFocus(BuildContext context, double total) {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final discountValue = saleInvoiceController.invoice.discountValue ?? 0.0;
    final discountPercentage =
        saleInvoiceController.invoice.discountPercentage ?? 0.0;

    final TextEditingController valueController = TextEditingController(
      text: discountValue.toStringAsFixed(2),
    );
    final TextEditingController percentageController = TextEditingController(
      text: discountPercentage.toStringAsFixed(2),
    );

    // Create a focus node to auto-focus the value input field
    final FocusNode valueFocusNode = FocusNode();

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(builder: (context, setState) {
        // Auto-focus the value input field when the dialog opens
        WidgetsBinding.instance.addPostFrameCallback((_) {
          valueFocusNode.requestFocus();
        });

        return AlertDialog(
          title: Text(T('Apply Discount')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Value discount
              TextField(
                controller: valueController,
                focusNode: valueFocusNode,
                decoration: InputDecoration(
                  labelText: T('Discount Value'),
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final discount = double.tryParse(value) ?? 0.0;
                  percentageController.text =
                      (discount / total * 100).toStringAsFixed(2);
                },
                // Handle Enter key press to apply the discount
                onSubmitted: (value) {
                  final discount = double.tryParse(valueController.text) ?? 0.0;
                  final percentage =
                      double.tryParse(percentageController.text) ?? 0.0;

                  saleInvoiceController.invoice.discountValue = discount;
                  saleInvoiceController.invoice.discountPercentage = percentage;
                  saleInvoiceController.calculateInvoiceTotal();

                  Navigator.of(context).pop();

                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(T(
                          'Discount applied: ${discount.toStringAsFixed(2)}')),
                      behavior: SnackBarBehavior.floating,
                      backgroundColor: Colors.green.shade600,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
              ),

              const SizedBox(height: 16),

              // Percentage discount
              TextField(
                controller: percentageController,
                decoration: InputDecoration(
                  labelText: T('Discount Percentage (%)'),
                  border: const OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final percentage = double.tryParse(value) ?? 0.0;
                  valueController.text =
                      (total * percentage / 100).toStringAsFixed(2);
                },
                // Handle Enter key press to apply the discount
                onSubmitted: (value) {
                  final discount = double.tryParse(valueController.text) ?? 0.0;
                  final percentage =
                      double.tryParse(percentageController.text) ?? 0.0;

                  saleInvoiceController.invoice.discountValue = discount;
                  saleInvoiceController.invoice.discountPercentage = percentage;
                  saleInvoiceController.calculateInvoiceTotal();

                  Navigator.of(context).pop();

                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(T(
                          'Discount applied: ${discount.toStringAsFixed(2)}')),
                      behavior: SnackBarBehavior.floating,
                      backgroundColor: Colors.green.shade600,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(T('Cancel')),
            ),
            ElevatedButton(
              onPressed: () {
                final discount = double.tryParse(valueController.text) ?? 0.0;
                final percentage =
                    double.tryParse(percentageController.text) ?? 0.0;

                saleInvoiceController.invoice.discountValue = discount;
                saleInvoiceController.invoice.discountPercentage = percentage;
                saleInvoiceController.calculateInvoiceTotal();

                Navigator.of(context).pop();

                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        T('Discount applied: ${discount.toStringAsFixed(2)}')),
                    behavior: SnackBarBehavior.floating,
                    backgroundColor: Colors.green.shade600,
                    duration: const Duration(seconds: 2),
                  ),
                );
              },
              child: Text(T('Apply')),
            ),
          ],
        );
      }),
    );
  }

  // Show payment dialog with payment method options
  void _showPaymentDialog(BuildContext context) {
    final saleInvoiceController =
        Provider.of<SaleInvoiceController>(context, listen: false);
    final invoice = saleInvoiceController.invoice;
    final totalAfterDiscount = invoice.totalAfterDiscount ?? 0.0;
    final currentPaymentValue = invoice.paymentValue ?? totalAfterDiscount;
    final paymentTypes =
        Provider.of<PaymentTypeController>(context, listen: false).paymentTypes;

    // Determine current payment method
    String selectedPaymentMethod = 'cash'; // Default to cash
    if (invoice.invoicePaymentType == InvoicePaymentType.dibt) {
      selectedPaymentMethod = 'card'; // Use card as default for non-cash
    }

    // Controllers for payment amounts
    final TextEditingController cashAmountController = TextEditingController(
      text: selectedPaymentMethod == 'cash'
          ? currentPaymentValue.toStringAsFixed(2)
          : '0.00',
    );
    final TextEditingController cardAmountController = TextEditingController(
      text: selectedPaymentMethod == 'card'
          ? currentPaymentValue.toStringAsFixed(2)
          : '0.00',
    );
    final TextEditingController checkAmountController = TextEditingController(
      text: selectedPaymentMethod == 'check'
          ? currentPaymentValue.toStringAsFixed(2)
          : '0.00',
    );

    // Show the dialog
    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: Text(T('Payment')),
            content: SizedBox(
              width: MediaQuery.of(context).size.width * 0.5,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Total amount
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          T('Total Amount:'),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                        Text(
                          totalAfterDiscount.toStringAsFixed(2),
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: context.newPrimaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Payment method selection
                    Text(
                      T('Payment Method:'),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: 10),

                    // Payment method radio buttons
                    Row(
                      children: paymentTypes.map((paymentType) {
                        return Expanded(
                          child: RadioListTile<int>(
                            title: Text(paymentType.name),
                            value: paymentType.id,
                            groupValue: invoice.pyamentMethodId,
                            onChanged: (value) {
                              setState(() {
                                invoice.pyamentMethodId = value!;

                                // Reset all to zero first
                                cashAmountController.text =
                                    totalAfterDiscount.toStringAsFixed(2);
                                // cardAmountController.text = '0.00';
                                // checkAmountController.text = '0.00';

                                // // Apply logic based on payment type name (or id if preferred)
                                // switch (paymentType.name.toLowerCase()) {
                                //   case 'cash':
                                //     cashAmountController.text =
                                //         totalAfterDiscount.toStringAsFixed(2);
                                //     break;
                                //   case 'card':
                                //     cardAmountController.text =
                                //         totalAfterDiscount.toStringAsFixed(2);
                                //     break;
                                //   case 'check':
                                //     checkAmountController.text =
                                //         totalAfterDiscount.toStringAsFixed(2);
                                //     break;
                                // }
                              });
                            },
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(T('Cancel')),
              ),
              ElevatedButton(
                onPressed: () {
                  // Get the payment amount based on selected method
                  double paymentAmount = 0.0;
                  if (selectedPaymentMethod == 'cash') {
                    paymentAmount =
                        double.tryParse(cashAmountController.text) ?? 0.0;
                  } else if (selectedPaymentMethod == 'card') {
                    paymentAmount =
                        double.tryParse(cardAmountController.text) ?? 0.0;
                  } else if (selectedPaymentMethod == 'check') {
                    paymentAmount =
                        double.tryParse(checkAmountController.text) ?? 0.0;
                  }

                  // Set payment type in invoice
                  if (selectedPaymentMethod == 'cash') {
                    invoice.invoicePaymentType = InvoicePaymentType.chash;
                  } else {
                    invoice.invoicePaymentType = InvoicePaymentType.dibt;
                  }

                  // Set payment amount
                  saleInvoiceController.setPaymentValue(paymentAmount);

                  // Close dialog
                  Navigator.of(context).pop();

                  // Show success message
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(T(
                          'Payment of ${paymentAmount.toStringAsFixed(2)} set successfully')),
                      behavior: SnackBarBehavior.floating,
                      backgroundColor: Colors.green.shade600,
                      duration: const Duration(seconds: 2),
                    ),
                  );
                },
                child: Text(T('Set Payment')),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showPriceEditDialog(BuildContext context, ProductDTO product,
      SaleInvoiceController saleInvoiceController) {
    final price = product.price ?? 0.0;
    // Removed unused quantity variable
    bool isValidPrice = true;

    final TextEditingController priceController = TextEditingController(
      text: price.toStringAsFixed(2),
    );

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(builder: (context, setState) {
        return AlertDialog(
          title: Text(T('Edit Price')),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Product info
              Text(
                product.title ?? T('Unknown product'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
              Text(
                product.code ?? '',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Current price info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    T('Current Price:'),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  Text(
                    price.toStringAsFixed(2),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // New price input
              TextField(
                controller: priceController,
                decoration: InputDecoration(
                  labelText: T('New Price'),
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.price_change_outlined),
                  errorText:
                      !isValidPrice ? T('Please enter a valid price') : null,
                ),
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                autofocus: true,
                onChanged: (value) {
                  final newPrice = double.tryParse(value);
                  setState(() {
                    isValidPrice = newPrice != null && newPrice >= 0;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(T('Cancel')),
            ),
            ElevatedButton(
              onPressed: !isValidPrice
                  ? null
                  : () {
                      final newPrice =
                          double.tryParse(priceController.text) ?? 0.0;
                      if (newPrice >= 0) {
                        saleInvoiceController.updateProductPrice(
                          product.id!,
                          newPrice,
                          product.virtualProductId,
                        );
                        Navigator.of(context).pop();

                        // Show success message
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(T(
                                'Price updated from ${price.toStringAsFixed(2)} to ${newPrice.toStringAsFixed(2)}')),
                            behavior: SnackBarBehavior.floating,
                            backgroundColor: Colors.green.shade600,
                            duration: const Duration(seconds: 2),
                          ),
                        );
                      }
                    },
              child: Text(T('Update Price')),
            ),
          ],
        );
      }),
    );
  }
}
