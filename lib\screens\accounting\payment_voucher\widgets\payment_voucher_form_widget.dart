import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';

class PaymentVoucherFormWidget extends StatefulWidget {
  final Function(String field, dynamic value) onFieldChanged;

  const PaymentVoucherFormWidget({
    super.key,
    required this.onFieldChanged,
  });

  @override
  State<PaymentVoucherFormWidget> createState() =>
      _PaymentVoucherFormWidgetState();
}

class _PaymentVoucherFormWidgetState extends State<PaymentVoucherFormWidget> {
  final _amountController = TextEditingController();
  final _noteController = TextEditingController();
  final _customerController = TextEditingController();
  final _invoiceNoController = TextEditingController();
  final _exchangeRateController = TextEditingController(text: "1.0");
  final _dateController = TextEditingController();

  String? _selectedDate;
  int? _selectedDebitAccount;
  int? _selectedCreditAccount;
  int? _selectedCurrency;
  int? _selectedPaymentType;

  @override
  void initState() {
    super.initState();
    // Set default date to today
    _selectedDate = DateFormat('dd/MM/yyyy').format(DateTime.now());
    _dateController.text = _selectedDate!;
    widget.onFieldChanged('date', _selectedDate);
  }

  @override
  void dispose() {
    _amountController.dispose();
    _noteController.dispose();
    _customerController.dispose();
    _invoiceNoController.dispose();
    _exchangeRateController.dispose();
    _dateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Amount and Date Row
        Row(
          children: [
            Expanded(
              flex: 2,
              child: _buildAmountField(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildDateField(),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Accounts Section
        _buildSectionHeader(T("Account Information"), Icons.account_balance),
        const SizedBox(height: 12),

        _buildDebitAccountField(),
        const SizedBox(height: 16),

        _buildCreditAccountField(),
        const SizedBox(height: 20),

        // Payment Details Section
        _buildSectionHeader(T("Payment Details"), Icons.payment),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: _buildCurrencyField(),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildExchangeRateField(),
            ),
          ],
        ),

        const SizedBox(height: 16),

        _buildPaymentTypeField(),
        const SizedBox(height: 20),

        // Additional Information Section
        _buildSectionHeader(T("Additional Information"), Icons.info_outline),
        const SizedBox(height: 12),

        _buildCustomerField(),
        const SizedBox(height: 16),

        _buildInvoiceNoField(),
        const SizedBox(height: 16),

        _buildNoteField(),
      ],
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: const Color(0xFFE53E3E).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: const Color(0xFFE53E3E),
            size: 18,
          ),
        ),
        const SizedBox(width: 12),
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Colors.grey[800],
          ),
        ),
      ],
    );
  }

  Widget _buildAmountField() {
    return _buildFieldWithIcon(
      icon: Icons.attach_money,
      child: CommonTextField(
        controller: _amountController,
        label: T("Amount") + " *",
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        validator: (value) {
          if (value == null || value.isEmpty) {
            return T("Amount is required");
          }
          if (double.tryParse(value) == null || double.parse(value) <= 0) {
            return T("Please enter a valid amount");
          }
          return null;
        },
        onChanged: (value) {
          if (value != null) {
            double? amount = double.tryParse(value);
            widget.onFieldChanged('amount', amount);
          }
        },
        floatingLabelBehavior: FloatingLabelBehavior.auto,
      ),
    );
  }

  Widget _buildDateField() {
    return _buildFieldWithIcon(
      icon: Icons.calendar_today,
      child: CommonTextField(
        controller: _dateController,
        label: T("Date") + " *",
        enabled: false,
        onTap: () async {
          final DateTime? picked = await showDatePicker(
            context: context,
            initialDate: DateTime.now(),
            firstDate: DateTime(2020),
            lastDate: DateTime(2030),
          );
          if (picked != null) {
            setState(() {
              _selectedDate = DateFormat('dd/MM/yyyy').format(picked);
              _dateController.text = _selectedDate!;
            });
            widget.onFieldChanged('date', _selectedDate);
          }
        },
        floatingLabelBehavior: FloatingLabelBehavior.auto,
      ),
    );
  }

  Widget _buildDebitAccountField() {
    return _buildFieldWithIcon(
      icon: Icons.account_balance_wallet,
      child: DropdownButtonFormField<int>(
        decoration: InputDecoration(
          labelText: T("Debit Account") + " *",
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        value: _selectedDebitAccount,
        items: _getDummyAccounts(),
        validator: (value) {
          if (value == null) {
            return T("Please select debit account");
          }
          return null;
        },
        onChanged: (value) {
          setState(() {
            _selectedDebitAccount = value;
          });
          widget.onFieldChanged('debitAccount', value);
        },
      ),
    );
  }

  Widget _buildCreditAccountField() {
    return _buildFieldWithIcon(
      icon: Icons.account_balance,
      child: DropdownButtonFormField<int>(
        decoration: InputDecoration(
          labelText: T("Credit Account") + " *",
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        value: _selectedCreditAccount,
        items: _getDummyAccounts(),
        validator: (value) {
          if (value == null) {
            return T("Please select credit account");
          }
          return null;
        },
        onChanged: (value) {
          setState(() {
            _selectedCreditAccount = value;
          });
          widget.onFieldChanged('creditAccount', value);
        },
      ),
    );
  }

  Widget _buildCurrencyField() {
    return _buildFieldWithIcon(
      icon: Icons.monetization_on,
      child: DropdownButtonFormField<int>(
        decoration: InputDecoration(
          labelText: T("Currency"),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        value: _selectedCurrency,
        items: _getDummyCurrencies(),
        onChanged: (value) {
          setState(() {
            _selectedCurrency = value;
          });
          widget.onFieldChanged('currency', value);
        },
      ),
    );
  }

  Widget _buildExchangeRateField() {
    return _buildFieldWithIcon(
      icon: Icons.currency_exchange,
      child: CommonTextField(
        controller: _exchangeRateController,
        label: T("Exchange Rate"),
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        onChanged: (value) {
          if (value != null) {
            double? rate = double.tryParse(value);
            widget.onFieldChanged('exchangeRate', rate ?? 1.0);
          }
        },
        floatingLabelBehavior: FloatingLabelBehavior.auto,
      ),
    );
  }

  Widget _buildPaymentTypeField() {
    return _buildFieldWithIcon(
      icon: Icons.payment,
      child: DropdownButtonFormField<int>(
        decoration: InputDecoration(
          labelText: T("Payment Type"),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        value: _selectedPaymentType,
        items: _getPaymentTypes(),
        onChanged: (value) {
          setState(() {
            _selectedPaymentType = value;
          });
          widget.onFieldChanged('paymentType', value);
        },
      ),
    );
  }

  Widget _buildCustomerField() {
    return _buildFieldWithIcon(
      icon: Icons.business,
      child: CommonTextField(
        controller: _customerController,
        label: T("Supplier/Vendor Name"),
        onChanged: (value) {
          if (value != null) {
            widget.onFieldChanged('customer', value);
          }
        },
        floatingLabelBehavior: FloatingLabelBehavior.auto,
      ),
    );
  }

  Widget _buildInvoiceNoField() {
    return _buildFieldWithIcon(
      icon: Icons.receipt,
      child: CommonTextField(
        controller: _invoiceNoController,
        label: T("Invoice Number"),
        onChanged: (value) {
          if (value != null) {
            widget.onFieldChanged('invoiceNo', value);
          }
        },
        floatingLabelBehavior: FloatingLabelBehavior.auto,
      ),
    );
  }

  Widget _buildNoteField() {
    return _buildFieldWithIcon(
      icon: Icons.note,
      child: Container(
        height: 80, // Fixed height for multiline
        child: CommonTextField(
          controller: _noteController,
          label: T("Notes"),
          onChanged: (value) {
            if (value != null) {
              widget.onFieldChanged('note', value);
            }
          },
          floatingLabelBehavior: FloatingLabelBehavior.auto,
        ),
      ),
    );
  }

  BoxDecoration _fieldDecoration() {
    return BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.1),
          spreadRadius: 1,
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    );
  }

  List<DropdownMenuItem<int>> _getDummyAccounts() {
    return [
      DropdownMenuItem(value: 1, child: Text(T("Cash Account"))),
      DropdownMenuItem(value: 2, child: Text(T("Bank Account"))),
      DropdownMenuItem(value: 3, child: Text(T("Suppliers Account"))),
      DropdownMenuItem(value: 4, child: Text(T("Expenses Account"))),
    ];
  }

  List<DropdownMenuItem<int>> _getDummyCurrencies() {
    return [
      DropdownMenuItem(value: 1, child: Text("USD - ${T('US Dollar')}")),
      DropdownMenuItem(value: 2, child: Text("EUR - ${T('Euro')}")),
      DropdownMenuItem(value: 3, child: Text("SAR - ${T('Saudi Riyal')}")),
    ];
  }

  List<DropdownMenuItem<int>> _getPaymentTypes() {
    return [
      DropdownMenuItem(value: 1, child: Text(T("Cash"))),
      DropdownMenuItem(value: 2, child: Text(T("Bank Transfer"))),
      DropdownMenuItem(value: 3, child: Text(T("Check"))),
      DropdownMenuItem(value: 4, child: Text(T("Credit Card"))),
    ];
  }

  Widget _buildFieldWithIcon({
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      decoration: _fieldDecoration(),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            margin: const EdgeInsets.only(left: 12, right: 8),
            child: Icon(
              icon,
              color: const Color(0xFFE53E3E),
              size: 20,
            ),
          ),
          Expanded(child: child),
        ],
      ),
    );
  }
}
