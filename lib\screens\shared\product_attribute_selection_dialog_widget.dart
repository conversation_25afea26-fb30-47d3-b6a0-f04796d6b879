import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';

class AttributeSelectionDialog extends StatefulWidget {
  final ProductDTO product;

  const AttributeSelectionDialog({super.key, required this.product});

  @override
  State<AttributeSelectionDialog> createState() =>
      _AttributeSelectionDialogState();
}

class _AttributeSelectionDialogState extends State<AttributeSelectionDialog> {
  Map<int, ItemAttributeOption> selectedOptions = {};

  @override
  Widget build(BuildContext context) {
    final product = widget.product;

    return AlertDialog(
      title: Text(
        T('Select Product Options'),
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: context.primaryColor,
        ),
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.5,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                product.title ?? '',
                style:
                    const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
              ),
              const SizedBox(height: 16),

              // ترتيب الخصائص حسب order
              ...(() {
                final sortedAttributes =
                    List<ItemAttribute>.from(product.itemAttributes ?? []);
                sortedAttributes
                    .sort((a, b) => (a.order ?? 0).compareTo(b.order ?? 0));
                return sortedAttributes;
              })()
                  .map((attribute) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      attribute.attributeName ?? '',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                        color: context.surfaceColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children:
                          (attribute.itemsAttributeOptions ?? []).map((option) {
                        final isSelected =
                            selectedOptions[attribute.id] == option;
                        return InkWell(
                          onTap: () {
                            setState(() {
                              if (attribute.id != null) {
                                selectedOptions[attribute.id!] = option;
                              }
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? context.primaryColor
                                  : Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: isSelected
                                    ? context.primaryColor
                                    : Colors.grey.shade300,
                              ),
                            ),
                            child: Text(
                              option.optionName ?? '',
                              style: TextStyle(
                                color: isSelected
                                    ? Colors.white
                                    : context.surfaceColor,
                                fontWeight: isSelected
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: 16),
                  ],
                );
              }).toList(),

              if (selectedOptions.isNotEmpty) ...[
                const Divider(),
                const SizedBox(height: 8),
                Text(
                  T('Selected Options:'),
                  style: const TextStyle(
                      fontWeight: FontWeight.w500, fontSize: 14),
                ),
                const SizedBox(height: 8),
                Text(
                  _getFormattedSelectedOptions(),
                  style: TextStyle(
                      fontWeight: FontWeight.bold, color: context.primaryColor),
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            T('Cancel'),
            style: const TextStyle(color: Colors.grey),
          ),
        ),
        ElevatedButton(
          onPressed: (product.itemAttributes != null &&
                  selectedOptions.length == product.itemAttributes!.length)
              ? () => Navigator.of(context).pop(selectedOptions)
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: context.primaryColor,
            foregroundColor: Colors.white,
          ),
          child: Text(T('Add to Cart')),
        ),
      ],
    );
  }

  String _getFormattedSelectedOptions() {
    final sortedKeys = selectedOptions.keys.toList()..sort();
    return sortedKeys
        .map((key) => selectedOptions[key]?.optionName ?? '')
        .join('/');
  }
}
