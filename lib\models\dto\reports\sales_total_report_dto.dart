class SalesTotalReportDTO {
  final double totalSales;
  final double totalReturns;
  final double netSales;
  final double totalDiscount;
  final double totalReceive;
  final double totalPaid;
  final double netTotal;
  final double totalRemaining;

  SalesTotalReportDTO({
    required this.totalSales,
    required this.totalReturns,
    required this.netSales,
    required this.totalDiscount,
    required this.totalReceive,
    required this.totalPaid,
    required this.netTotal,
    required this.totalRemaining,
  });

  factory SalesTotalReportDTO.fromJson(Map<String, dynamic> json) {
    return SalesTotalReportDTO(
      totalSales: _parseDouble(json['totalSales']),
      totalReturns: _parseDouble(json['totalReturns']),
      netSales: _parseDouble(json['netSales']),
      totalDiscount: _parseDouble(json['totalDiscount']),
      totalReceive:
          _parseDouble(json['totalRecive']), // Note: API has typo 'totalRecive'
      totalPaid: _parseDouble(json['totalPaid']),
      netTotal: _parseDouble(json['netTotal']),
      totalRemaining: _parseDouble(json['totalRemaining']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalSales': totalSales,
      'totalReturns': totalReturns,
      'netSales': netSales,
      'totalDiscount': totalDiscount,
      'totalRecive': totalReceive, // Note: API expects 'totalRecive'
      'totalPaid': totalPaid,
      'netTotal': netTotal,
      'totalRemaining': totalRemaining,
    };
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  @override
  String toString() {
    return 'SalesTotalReportDTO(totalSales: $totalSales, totalReturns: $totalReturns, netSales: $netSales, totalDiscount: $totalDiscount, totalReceive: $totalReceive, totalPaid: $totalPaid, netTotal: $netTotal, totalRemaining: $totalRemaining)';
  }
}
