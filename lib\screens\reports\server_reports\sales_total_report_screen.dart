import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/device_user_controller.dart';

import 'package:inventory_application/models/dto/reports/sales_total_report_dto.dart';
import 'package:inventory_application/models/dto/device_dto.dart';
import 'package:inventory_application/models/dto/user_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/services/server_reports_service.dart';
import 'package:inventory_application/services/report_printer_service.dart';
import 'package:inventory_application/screens/reports/widgets/report_print_helper.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

enum DateRangeType {
  daily, // يومي
  monthly, // شهري
  yearly, // سنوي
  custom // تاريخ مخصص
}

// SearchableDropdown Widget مخصص
class SearchableDropdown extends StatefulWidget {
  final String? selectedValue;
  final String caption;
  final String labelText;
  final String modalTitle;
  final List<DropdownItem> items;
  final Function(String?, String?) onChanged;
  final double height;
  final bool isShowLabel;

  const SearchableDropdown({
    Key? key,
    this.selectedValue,
    required this.caption,
    required this.labelText,
    required this.modalTitle,
    required this.items,
    required this.onChanged,
    this.height = 60,
    this.isShowLabel = true,
  }) : super(key: key);

  @override
  State<SearchableDropdown> createState() => _SearchableDropdownState();
}

class _SearchableDropdownState extends State<SearchableDropdown> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.isShowLabel)
          Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: Text(
              widget.labelText,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        Container(
          height: widget.height,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: InkWell(
            onTap: () => _showSearchDialog(context),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    widget.caption,
                    style: TextStyle(
                      color: widget.selectedValue != null
                          ? Colors.black87
                          : Colors.grey[600],
                      fontSize: 14,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: Colors.grey[600],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _showSearchDialog(BuildContext context) async {
    final result = await showDialog<DropdownItem>(
      context: context,
      builder: (context) => _SearchDialog(
        title: widget.modalTitle,
        items: widget.items,
        selectedValue: widget.selectedValue,
      ),
    );

    if (result != null) {
      widget.onChanged(result.value, result.label);
    }
  }
}

class DropdownItem {
  final String value;
  final String label;

  DropdownItem({required this.value, required this.label});
}

class _SearchDialog extends StatefulWidget {
  final String title;
  final List<DropdownItem> items;
  final String? selectedValue;

  const _SearchDialog({
    Key? key,
    required this.title,
    required this.items,
    this.selectedValue,
  }) : super(key: key);

  @override
  State<_SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<_SearchDialog> {
  late TextEditingController _searchController;
  List<DropdownItem> _filteredItems = [];

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredItems = widget.items;
    _searchController.addListener(_filterItems);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterItems() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredItems = widget.items
          .where((item) => item.label.toLowerCase().contains(query))
          .toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.6,
        child: Column(
          children: [
            // Search field
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'البحث...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              autofocus: true,
            ),
            const SizedBox(height: 16),
            // Results list
            Expanded(
              child: ListView.builder(
                itemCount: _filteredItems.length,
                itemBuilder: (context, index) {
                  final item = _filteredItems[index];
                  final isSelected = item.value == widget.selectedValue;

                  return ListTile(
                    title: Text(item.label),
                    leading: isSelected
                        ? const Icon(Icons.check_circle, color: Colors.green)
                        : const Icon(Icons.radio_button_unchecked),
                    onTap: () => Navigator.of(context).pop(item),
                    selected: isSelected,
                    selectedTileColor: Colors.blue.shade50,
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
      ],
    );
  }
}

class SalesTotalReportScreen extends StatefulWidget {
  const SalesTotalReportScreen({Key? key}) : super(key: key);

  @override
  State<SalesTotalReportScreen> createState() => _SalesTotalReportScreenState();
}

class _SalesTotalReportScreenState extends State<SalesTotalReportScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  SalesTotalReportDTO? _reportData;
  bool _isLoading = false;
  DateTime? _fromDate;
  DateTime? _toDate;
  DateRangeType? _selectedDateRangeType;

  final ServerReportsService _reportsService = ServerReportsService();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    // Date range is now optional - start with no date filter
    _fromDate = null;
    _toDate = null;

    // Load devices and initial report
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DeviceUserController>().loadDevices();
      _loadReport();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // وظيفة طباعة التقرير
  Future<void> _printReport() async {
    if (_reportData != null) {
      try {
        // تحويل بيانات التقرير إلى Map
        final reportMap = {
          'إجمالي المبيعات': _reportData!.totalSales.toStringAsFixed(2),
          'إجمالي المرتجعات': _reportData!.totalReturns.toStringAsFixed(2),
          'صافي المبيعات': _reportData!.netSales.toStringAsFixed(2),
          'إجمالي الخصم': _reportData!.totalDiscount.toStringAsFixed(2),
          'إجمالي المستلم': _reportData!.totalReceive.toStringAsFixed(2),
          'إجمالي المدفوع': _reportData!.totalPaid.toStringAsFixed(2),
          'الإجمالي الصافي': _reportData!.netTotal.toStringAsFixed(2),
          'إجمالي المتبقي': _reportData!.totalRemaining.toStringAsFixed(2),
        };

        await ReportPrinterService.printGenericReport(
          reportMap,
          'تقرير إجمالي المبيعات',
          context,
          fromDate: _fromDate,
          toDate: _toDate,
        );
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في الطباعة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _loadReport() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final deviceController = context.read<DeviceUserController>();

      // Use selected device ID, or fallback to current device
      String? deviceId = deviceController.selectedDevice?.id;

      // Use selected user ID
      int? userId = deviceController.selectedUser?.id;

      final report = await _reportsService.getSalesTotalWithPaidAmount(
        deviceId: deviceId,
        userId: userId,
        fromDate: _fromDate,
        toDate: _toDate,
      );

      setState(() {
        _reportData = report;
        _isLoading = false;
      });

      if (report != null) {
        _animationController.forward();
      } else {
        errorSnackBar(message: 'فشل في تحميل بيانات التقرير');
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      errorSnackBar(message: 'خطأ في تحميل التقرير: ${e.toString()}');
    }
  }

  Future<void> _showFiltersDialog() async {
    await showDialog(
      context: context,
      builder: (context) => _FilterDialog(
        initialFromDate: _fromDate,
        initialToDate: _toDate,
        initialDateRangeType: _selectedDateRangeType,
        onApplyFilters: (fromDate, toDate, dateRangeType) {
          setState(() {
            _fromDate = fromDate;
            _toDate = toDate;
            _selectedDateRangeType = dateRangeType;
          });
          _loadReport();
        },
      ),
    );
  }

  void _applyDateRangeType(DateRangeType type) {
    final now = DateTime.now();
    setState(() {
      _selectedDateRangeType = type;
      switch (type) {
        case DateRangeType.daily:
          _fromDate = DateTime(now.year, now.month, now.day);
          _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
          break;
        case DateRangeType.monthly:
          _fromDate = DateTime(now.year, now.month, 1);
          _toDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
          break;
        case DateRangeType.yearly:
          _fromDate = DateTime(now.year, 1, 1);
          _toDate = DateTime(now.year, 12, 31, 23, 59, 59);
          break;
        case DateRangeType.custom:
          // سيتم اختيار التاريخ يدوياً
          break;
      }
    });
  }

  String _getDateRangeTypeDisplayName(DateRangeType type) {
    switch (type) {
      case DateRangeType.daily:
        return 'يومي';
      case DateRangeType.monthly:
        return 'شهري';
      case DateRangeType.yearly:
        return 'سنوي';
      case DateRangeType.custom:
        return 'تاريخ مخصص';
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _fromDate != null && _toDate != null
          ? DateTimeRange(start: _fromDate!, end: _toDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: const Color(0xFF6366F1),
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
      });
      _loadReport();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'تقرير إجمالي المبيعات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: const Color(0xFF6366F1),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_alt),
            onPressed: _showFiltersDialog,
            tooltip: 'فلاتر التقرير',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadReport,
            tooltip: 'تحديث',
          ),
          // زر الطباعة في الـ AppBar
          if (_reportData != null && !_isLoading)
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'print') {
                  _printReport();
                }
              },
              itemBuilder: (context) => [
                ReportPrintHelper.buildPrintMenuItem(),
              ],
            ),
        ],
      ),
      floatingActionButton: _reportData != null && !_isLoading
          ? ReportPrintHelper.buildFloatingPrintButton(
              context: context,
              hasData: _reportData != null,
              onPressed: _printReport,
            )
          : null,
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF6366F1)),
              ),
            )
          : _reportData == null
              ? _buildEmptyState()
              : _buildReportContent(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assessment_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'لا توجد بيانات تقرير متاحة',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'جرب تعديل الفترة الزمنية أو تحقق من الاتصال',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadReport,
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6366F1),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportContent() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateRangeCard(),
            const SizedBox(height: 16),
            _buildSummaryCards(),
            const SizedBox(height: 24),
            _buildDetailedBreakdown(),

            // زر الطباعة داخل المحتوى
            ReportPrintHelper.buildGenericPrintButton(
              context: context,
              reportData: {
                'إجمالي المبيعات': _reportData!.totalSales.toStringAsFixed(2),
                'إجمالي المرتجعات':
                    _reportData!.totalReturns.toStringAsFixed(2),
                'صافي المبيعات': _reportData!.netSales.toStringAsFixed(2),
                'إجمالي الخصم': _reportData!.totalDiscount.toStringAsFixed(2),
                'إجمالي المستلم': _reportData!.totalReceive.toStringAsFixed(2),
                'إجمالي المدفوع': _reportData!.totalPaid.toStringAsFixed(2),
                'الإجمالي الصافي': _reportData!.netTotal.toStringAsFixed(2),
                'إجمالي المتبقي':
                    _reportData!.totalRemaining.toStringAsFixed(2),
              },
              reportTitle: 'تقرير إجمالي المبيعات',
              fromDate: _fromDate,
              toDate: _toDate,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDateRangeCard() {
    final dateFormat = DateFormat('dd/MM/yyyy');
    final deviceController = context.watch<DeviceUserController>();

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF6366F1).withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.filter_alt,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              const Text(
                'فلاتر التقرير',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildFilterChip('الجهاز',
              deviceController.selectedDevice?.name ?? 'جميع الأجهزة'),
          const SizedBox(height: 4),
          _buildFilterChip('المستخدم',
              deviceController.selectedUser?.name ?? 'جميع المستخدمين'),
          const SizedBox(height: 4),
          _buildFilterChip(
              'الفترة الزمنية',
              _fromDate != null && _toDate != null
                  ? '${dateFormat.format(_fromDate!)} - ${dateFormat.format(_toDate!)}'
                  : 'جميع الفترات'),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String value) {
    return Row(
      children: [
        Text(
          '$label: ',
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCards() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                title: 'صافي المبيعات',
                value: _reportData!.netSales,
                icon: Icons.trending_up,
                color: const Color(0xFF10B981),
                isMainMetric: true,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                title: 'إجمالي الخصومات',
                value: _reportData!.totalDiscount,
                icon: Icons.local_offer,
                color: const Color(0xFFF59E0B),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                title: 'إجمالي المبيعات',
                value: _reportData!.totalSales,
                icon: Icons.point_of_sale,
                color: const Color(0xFF3B82F6),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildMetricCard(
                title: 'إجمالي المرتجعات',
                value: _reportData!.totalReturns,
                icon: Icons.keyboard_return,
                color: const Color(0xFFEF4444),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard({
    required String title,
    required double value,
    required IconData icon,
    required Color color,
    bool isMainMetric = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: isMainMetric ? Border.all(color: color, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            NumberFormat('#,##0.00').format(value),
            style: TextStyle(
              fontSize: isMainMetric ? 18 : 16,
              fontWeight: FontWeight.bold,
              color: isMainMetric ? color : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedBreakdown() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: Colors.grey[700],
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'التفاصيل المالية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildDetailRow('إجمالي المُستلم', _reportData!.totalReceive,
              Icons.call_received),
          _buildDetailRow(
              'إجمالي المدفوع', _reportData!.totalPaid, Icons.payment),
          _buildDetailRow(
              'صافي الإجمالي', _reportData!.netTotal, Icons.calculate),
          const SizedBox(height: 16),
          _buildDetailRow('إجمالي المتبقي', _reportData!.totalRemaining,
              Icons.account_balance_wallet),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, double value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 18,
            color: Colors.grey[600],
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            NumberFormat('#,##0.00').format(value),
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF374151),
            ),
          ),
        ],
      ),
    );
  }
}

class _FilterDialog extends StatefulWidget {
  final DateTime? initialFromDate;
  final DateTime? initialToDate;
  final DateRangeType? initialDateRangeType;
  final Function(DateTime?, DateTime?, DateRangeType?) onApplyFilters;

  const _FilterDialog({
    Key? key,
    this.initialFromDate,
    this.initialToDate,
    this.initialDateRangeType,
    required this.onApplyFilters,
  }) : super(key: key);

  @override
  State<_FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<_FilterDialog> {
  DateTime? _fromDate;
  DateTime? _toDate;
  DateRangeType? _selectedDateRangeType;

  @override
  void initState() {
    super.initState();
    _fromDate = widget.initialFromDate;
    _toDate = widget.initialToDate;
    _selectedDateRangeType = widget.initialDateRangeType;
  }

  List<DeviceDTO> _getDevicesData() {
    final deviceController = context.read<DeviceUserController>();
    List<DeviceDTO> data = [
      DeviceDTO(id: "", name: 'جميع الأجهزة'),
    ];
    for (var device in deviceController.devices) {
      if (device.id != null && device.name != null) {
        data.add(DeviceDTO(id: device.id, name: device.name!));
      }
    }
    return data;
  }

  List<ComboBoxDataModel> _getUsersData() {
    final deviceController = context.read<DeviceUserController>();
    List<ComboBoxDataModel> data = [
      ComboBoxDataModel(id: 0, name: 'جميع المستخدمين'),
    ];
    for (var user in deviceController.users) {
      if (user.id != null && user.name != null) {
        data.add(ComboBoxDataModel(id: user.id!, name: user.name!));
      }
    }
    return data;
  }

  List<ComboBoxDataModel> _getDateRangeTypesData() {
    return [
      ComboBoxDataModel(id: 0, name: 'جميع الفترات'),
      ComboBoxDataModel(id: 1, name: 'يومي'),
      ComboBoxDataModel(id: 2, name: 'شهري'),
      ComboBoxDataModel(id: 3, name: 'سنوي'),
      ComboBoxDataModel(id: 4, name: 'تاريخ مخصص'),
    ];
  }

  DateRangeType? _getDateRangeTypeFromId(int id) {
    switch (id) {
      case 1:
        return DateRangeType.daily;
      case 2:
        return DateRangeType.monthly;
      case 3:
        return DateRangeType.yearly;
      case 4:
        return DateRangeType.custom;
      default:
        return null;
    }
  }

  int _getIdFromDateRangeType(DateRangeType? type) {
    switch (type) {
      case DateRangeType.daily:
        return 1;
      case DateRangeType.monthly:
        return 2;
      case DateRangeType.yearly:
        return 3;
      case DateRangeType.custom:
        return 4;
      default:
        return 0;
    }
  }

  void _applyDateRangeType(DateRangeType type) {
    final now = DateTime.now();
    setState(() {
      _selectedDateRangeType = type;
      switch (type) {
        case DateRangeType.daily:
          _fromDate = DateTime(now.year, now.month, now.day);
          _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
          break;
        case DateRangeType.monthly:
          _fromDate = DateTime(now.year, now.month, 1);
          _toDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
          break;
        case DateRangeType.yearly:
          _fromDate = DateTime(now.year, 1, 1);
          _toDate = DateTime(now.year, 12, 31, 23, 59, 59);
          break;
        case DateRangeType.custom:
          // سيتم اختيار التاريخ يدوياً
          break;
      }
    });
  }

  String _getDateRangeTypeDisplayName(DateRangeType type) {
    switch (type) {
      case DateRangeType.daily:
        return 'يومي';
      case DateRangeType.monthly:
        return 'شهري';
      case DateRangeType.yearly:
        return 'سنوي';
      case DateRangeType.custom:
        return 'تاريخ مخصص';
    }
  }

  @override
  Widget build(BuildContext context) {
    final deviceController = context.watch<DeviceUserController>();

    return AlertDialog(
      title: const Text(
        'فلاتر التقرير',
        style: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (AuthController.getIsMainAdmin())

              // Device Selection
              if (!deviceController.isLoadingDevices)
                SearchableDropdown(
                  caption:
                      deviceController.selectedDevice?.name ?? 'جميع الأجهزة',
                  labelText: 'الجهاز',
                  selectedValue: deviceController.selectedDevice?.id,
                  modalTitle: 'اختر الجهاز',
                  items: _getDevicesData()
                      .map((e) => DropdownItem(
                          value: e.id.toString(), label: e.name ?? ""))
                      .toList(),
                  height: 60,
                  isShowLabel: false,
                  onChanged: (value, name) {
                    deviceController
                        .setSelectedDevice(DeviceDTO(id: value, name: name));
                    deviceController.loadUsersForDevice(value);
                    deviceController.notifyListeners();
                  },
                )
              else
                Container(
                  height: 60,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(child: CircularProgressIndicator()),
                ),

            const SizedBox(height: 16),

            // User Selection
            if (AuthController.getIsMainAdmin())
              if (!deviceController.isLoadingUsers)
                SearchableDropdown(
                  caption:
                      deviceController.selectedUser?.name ?? 'جميع المستخدمين',
                  labelText: 'المستخدم',
                  selectedValue: deviceController.selectedUser?.id != null
                      ? deviceController.selectedUser!.id.toString()
                      : null,
                  modalTitle: 'اختر المستخدم',
                  items: _getUsersData()
                      .map((e) =>
                          DropdownItem(value: e.id.toString(), label: e.name))
                      .toList(),
                  height: 60,
                  isShowLabel: false,
                  onChanged: (value, name) {
                    deviceController.setSelectedUser(
                        UserDTO(id: int.parse(value!), name: name));
                    deviceController.notifyListeners();
                  },
                )
              else
                Container(
                  height: 60,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Center(child: CircularProgressIndicator()),
                ),

            const SizedBox(height: 16),

            // Date Range Type Selection
            SearchableDropdown(
              caption: _selectedDateRangeType != null
                  ? _getDateRangeTypeDisplayName(_selectedDateRangeType!)
                  : 'اختر نوع الفترة',
              labelText: 'نوع الفترة الزمنية',
              selectedValue: _selectedDateRangeType != null
                  ? _getIdFromDateRangeType(_selectedDateRangeType).toString()
                  : null,
              modalTitle: 'اختر نوع الفترة',
              items: _getDateRangeTypesData()
                  .map((e) =>
                      DropdownItem(value: e.id.toString(), label: e.name))
                  .toList(),
              height: 60,
              isShowLabel: false,
              onChanged: (value, name) {
                final dateRangeType =
                    _getDateRangeTypeFromId(int.parse(value!));
                if (dateRangeType != null) {
                  _applyDateRangeType(dateRangeType);
                } else {
                  setState(() {
                    _selectedDateRangeType = null;
                    _fromDate = null;
                    _toDate = null;
                  });
                }
              },
            ),

            const SizedBox(height: 16),

            // Custom Date Range Selection (only for custom type)
            if (_selectedDateRangeType == DateRangeType.custom) ...[
              const Text(
                'الفترة الزمنية المخصصة',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 8),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _fromDate != null && _toDate != null
                                ? '${DateFormat('dd/MM/yyyy').format(_fromDate!)} - ${DateFormat('dd/MM/yyyy').format(_toDate!)}'
                                : 'اختر الفترة الزمنية',
                            style: TextStyle(
                              color: _fromDate != null && _toDate != null
                                  ? Colors.black87
                                  : Colors.grey[600],
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.date_range),
                          onPressed: _selectDateRange,
                        ),
                      ],
                    ),
                    if (_fromDate != null || _toDate != null)
                      Align(
                        alignment: Alignment.centerRight,
                        child: TextButton(
                          onPressed: () {
                            setState(() {
                              _fromDate = null;
                              _toDate = null;
                            });
                          },
                          child: const Text('مسح الفترة الزمنية'),
                        ),
                      ),
                  ],
                ),
              ),
            ] else if (_selectedDateRangeType != null) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle,
                        color: Colors.green.shade600, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        _fromDate != null && _toDate != null
                            ? '${DateFormat('dd/MM/yyyy').format(_fromDate!)} - ${DateFormat('dd/MM/yyyy').format(_toDate!)}'
                            : 'تم تطبيق الفترة ${_getDateRangeTypeDisplayName(_selectedDateRangeType!)}',
                        style: TextStyle(
                          color: Colors.green.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onApplyFilters(_fromDate, _toDate, _selectedDateRangeType);
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF6366F1),
            foregroundColor: Colors.white,
          ),
          child: const Text('تطبيق'),
        ),
      ],
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _fromDate != null && _toDate != null
          ? DateTimeRange(start: _fromDate!, end: _toDate!)
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: const Color(0xFF6366F1),
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
      });
    }
  }
}
