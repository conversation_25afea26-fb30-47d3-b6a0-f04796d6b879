import 'dart:io';

import 'package:file_selector/file_selector.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path/path.dart' as path;
import 'package:inventory_application/controllers/app_controller.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._();
  static Database? _database;

  DatabaseHelper._();

  factory DatabaseHelper() => _instance;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    final dbPath = await getDatabasesPath();

    final path = join(dbPath, 'invoices.db');
    // await deleteDatabase(path); // تم إلغاء حذف قاعدة البيانات للاحتفاظ بالبيانات
    return await openDatabase(
      path,
      version: 2,
      onUpgrade: (db, oldVersion, newVersion) async {
        // إضافة الجداول الجديدة للمستودعات ثلاثية الأبعاد
        if (oldVersion < 2) {
          await _create3DWarehouseTables(db);
        }
        //   var sdsad = "";
        //   if (oldVersion < 5) {
        //     await db.execute('''
        //       ALTER TABLE Barcodes
        //       ADD COLUMN SelectedAttributeAsString TEXT;
        //     ''');

        //     await db.execute('''
        //         CREATE TABLE Supplier (
        //       Code TEXT,
        //       Name TEXT,
        //       Name_En TEXT,
        //       ID INTEGER,
        //       local_Id INTEGER PRIMARY KEY AUTOINCREMENT,
        //       BranchId INTEGER
        //     )
        //       ''');
        //     await db.execute('''
        //   CREATE TABLE PurchaseInvoice (
        //    ID INTEGER PRIMARY KEY AUTOINCREMENT,
        //     data TEXT,
        //     status TEXT,
        //     localCode TEXT,
        //     inseatedUserId INTEGER,
        //     inseatedUserName TEXT,
        //     type TEXT,
        //     BranchId INTEGER
        //   )
        // ''');
        //   }
      },
      onCreate: (db, version) async {
        // Create Invoice table first
        await db.execute('''
        CREATE TABLE Invoice (
         ID INTEGER PRIMARY KEY AUTOINCREMENT,
          data TEXT,
          status TEXT,
          localCode TEXT,
          inseatedUserId INTEGER,
          inseatedUserName TEXT,
          type TEXT,
          BranchId INTEGER
        )
      ''');
        await db.execute('''
        CREATE TABLE PurchaseInvoice (
         ID INTEGER PRIMARY KEY AUTOINCREMENT,
          data TEXT,
          status TEXT,
          localCode TEXT,
          inseatedUserId INTEGER,
          inseatedUserName TEXT,
          type TEXT,
          BranchId INTEGER
        )
      ''');

        await db.execute('''
        CREATE TABLE Warehouse (
         id INTEGER PRIMARY KEY,
          name TEXT,
          BranchId INTEGER
        )
      ''');

        // جداول المستودع ثلاثي الأبعاد
        await db.execute('''
        CREATE TABLE Warehouse3D (
          id INTEGER PRIMARY KEY,
          name TEXT,
          description TEXT,
          length REAL,
          width REAL,
          height REAL,
          xPosition REAL,
          yPosition REAL,
          zPosition REAL,
          BranchId INTEGER
        )
      ''');

        await db.execute('''
        CREATE TABLE Cabinet3D (
          id INTEGER PRIMARY KEY,
          name TEXT,
          code TEXT,
          warehouseId INTEGER,
          length REAL,
          width REAL,
          height REAL,
          xPosition REAL,
          yPosition REAL,
          zPosition REAL,
          color TEXT,
          BranchId INTEGER,
          FOREIGN KEY (warehouseId) REFERENCES Warehouse3D(id)
        )
      ''');

        await db.execute('''
        CREATE TABLE Shelf3D (
          id INTEGER PRIMARY KEY,
          name TEXT,
          code TEXT,
          cabinetId INTEGER,
          length REAL,
          width REAL,
          height REAL,
          xPosition REAL,
          yPosition REAL,
          zPosition REAL,
          status TEXT,
          maxCapacity INTEGER,
          currentOccupancy INTEGER,
          BranchId INTEGER,
          FOREIGN KEY (cabinetId) REFERENCES Cabinet3D(id)
        )
      ''');

        await db.execute('''
        CREATE TABLE ProductLocation3D (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          productId INTEGER,
          productName TEXT,
          productCode TEXT,
          shelfId INTEGER,
          cabinetId INTEGER,
          warehouseId INTEGER,
          quantity REAL,
          xPosition REAL,
          yPosition REAL,
          zPosition REAL,
          lastUpdated TEXT,
          batchNumber TEXT,
          expiryDate TEXT,
          BranchId INTEGER,
          FOREIGN KEY (productId) REFERENCES ProductModel(ID),
          FOREIGN KEY (shelfId) REFERENCES Shelf3D(id),
          FOREIGN KEY (cabinetId) REFERENCES Cabinet3D(id),
          FOREIGN KEY (warehouseId) REFERENCES Warehouse3D(id)
        )
      ''');

        await db.execute('''
        CREATE TABLE WarehouseLayout (
          warehouseId INTEGER,
          layoutName TEXT,
          description TEXT,
          createdDate TEXT,
          lastModified TEXT,
          createdBy TEXT,
          layoutData TEXT,
          BranchId INTEGER,
          PRIMARY KEY (warehouseId, layoutName),
          FOREIGN KEY (warehouseId) REFERENCES Warehouse3D(id)
        )
      ''');
        await db.execute('''
        CREATE TABLE Salesmen (
         id INTEGER PRIMARY KEY,
          name TEXT,
          code TEXT,
          BranchId INTEGER
        )
      ''');
        await db.execute('''
        CREATE TABLE Customer (
          Code TEXT,
          Name TEXT,
          Name_En TEXT,
          Name_Tr TEXT,
          Name_Localized TEXT,
          Customer_Type_Name TEXT,
          Accounting_Name TEXT,
          Accounting_Number TEXT,
          ID INTEGER,
          local_Id INTEGER PRIMARY KEY AUTOINCREMENT,
          status TEXT,
          Phone TEXT,
          Notes TEXT,
          Address TEXT,
          BranchId INTEGER
        )
      ''');
        await db.execute('''
        CREATE TABLE Supplier (
          Code TEXT,
          Name TEXT,
          Name_En TEXT,
          ID INTEGER,
          local_Id INTEGER PRIMARY KEY AUTOINCREMENT,
          BranchId INTEGER
        )
      ''');
        await db.execute('''
        CREATE TABLE CostCenter (
          Code TEXT,
          CustomCode TEXT,
          Name TEXT,
          Name_En TEXT,
          ID INTEGER,
          local_Id INTEGER PRIMARY KEY AUTOINCREMENT,
          BranchId INTEGER
        )
      ''');
        await db.execute('''
        CREATE TABLE ProductModel (
          ID INT PRIMARY KEY,
          Name VARCHAR(255),
          Main_Image_Url TEXT,
          Code VARCHAR(255),
          Parent_ID INT,
          Parent_Name VARCHAR(255),
          Level_Type INT,
          isParent BIT,
          BranchId INTEGER
        )
      ''');
        await db.execute('''
        CREATE TABLE Inventory (
          ID2 INTEGER PRIMARY KEY AUTOINCREMENT,
          ID INT,
          Store_Name VARCHAR(255),
          Quantity_Balance INT,
          Product_ID INT,
          BranchId INTEGER,
          
          FOREIGN KEY (Product_ID) REFERENCES ProductModel(ID)
        )
      ''');
        await db.execute('''
        CREATE TABLE ItemPrice (
           ID INT PRIMARY KEY,
            Item_ID INT,
            Unit_ID INT,
            Unit_Name VARCHAR(255),
            Is_Defult BIT,
            Sales_Price INT,
            Product_ID INT,
              BranchId INTEGER,
            FOREIGN KEY (Product_ID) REFERENCES ProductModel(ID)
        )
      ''');
        await db.execute('''
        CREATE TABLE Barcodes (
          ID2 INTEGER PRIMARY KEY AUTOINCREMENT,
          BarCode TEXT,
          BarCodeName TEXT,
          IsFinalBarcode BIT,
          ItemID INT,
          SelectedAttributeAsString TEXT,
          BranchId INTEGER,
          FOREIGN KEY (ItemID) REFERENCES ProductModel(ID)
        )
      ''');
        await db.execute('''
        CREATE TABLE ItemAttributes (
            ID INTEGER PRIMARY KEY,
            Attribute_Type_Id INTEGER,
            Attribute_Name TEXT,
            Item_ID INTEGER,
            Attribute_Order INTEGER,
              BranchId INTEGER,
            FOREIGN KEY (Item_ID) REFERENCES ProductModel(ID)
        )
    ''');

        await db.execute('''
        CREATE TABLE ItemsAttributeOptions (
            ID INTEGER PRIMARY KEY,
            Attribute_ID INTEGER,
            Option_ID INTEGER,
            Option_Name TEXT,
               BranchId INTEGER,
            FOREIGN KEY (Attribute_ID) REFERENCES ItemAttributes(ID)
        )
    ''');
        await db.execute('''
        CREATE TABLE Category (
           ID INTEGER PRIMARY KEY,
            Name TEXT,
            Code TEXT,
            Parent_ID INTEGER,
            Parent_Name TEXT,
            Level_Type INTEGER,
            IsParent BOOLEAN,
            BranchId INTEGER
    )
''');
        await db.execute('''
            CREATE TABLE User (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
                usernameOrEmail TEXT,
                token TEXT,
                password TEXT,
                BranchId INTEGER
        )
      ''');
        await db.execute('''
            CREATE TABLE Units (
              id INTEGER PRIMARY KEY,
              name TEXT
            )
      ''');
        await db.execute('''
            CREATE TABLE PaymentType (
              ID INTEGER PRIMARY KEY,
              Name_AR TEXT,
              BranchId INTEGER
            )
      ''');
        await db.execute('''
        CREATE TABLE InventoryOperation (
         ID INTEGER PRIMARY KEY AUTOINCREMENT,
          data TEXT,
          status TEXT,
          localCode TEXT,
          type TEXT,
          inseatedUserId INTEGER,
          inseatedUserName TEXT,
          BranchId INTEGER
        )
      ''');
        await db.execute('''
      CREATE TABLE Roles (
        RoleID INTEGER PRIMARY KEY,
        RoleName TEXT
      )
    ''');

        await db.execute('''
      CREATE TABLE Permissions (
        ID INTEGER PRIMARY KEY AUTOINCREMENT,
        PermissionID INTEGER,
        PermissionName TEXT,
        Parent_ID INTEGER,
        RoleID INTEGER,
        FOREIGN KEY (RoleID) REFERENCES Roles(RoleID)
      )
    ''');
        await db.execute('''
      CREATE TABLE StocktakingDrafts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        stocktakingId TEXT UNIQUE,
        warehouseId INTEGER,
        warehouseName TEXT,
        startDate TEXT,
        items TEXT,
        status TEXT,
        lastModified TEXT,
        BranchId INTEGER
      )
    ''');

        // Create branches table
        await db.execute('''
        CREATE TABLE Branches (
          Id INTEGER PRIMARY KEY,
          Name TEXT NOT NULL,
          Code TEXT,
          Address TEXT,
          Phone TEXT,
          IsActive INTEGER DEFAULT 1,
          CreatedDate TEXT,
          ModifiedDate TEXT
        )
      ''');

        // إنشاء جداول المستودعات ثلاثية الأبعاد
        await _create3DWarehouseTables(db);
      },
    );
  }

  // دالة إنشاء جداول المستودعات ثلاثية الأبعاد
  Future<void> _create3DWarehouseTables(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS Warehouse3D (
        id INTEGER PRIMARY KEY,
        name TEXT,
        description TEXT,
        length REAL,
        width REAL,
        height REAL,
        xPosition REAL,
        yPosition REAL,
        zPosition REAL,
        BranchId INTEGER
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS Cabinet3D (
        id INTEGER PRIMARY KEY,
        name TEXT,
        code TEXT,
        warehouseId INTEGER,
        length REAL,
        width REAL,
        height REAL,
        xPosition REAL,
        yPosition REAL,
        zPosition REAL,
        color TEXT,
        BranchId INTEGER,
        FOREIGN KEY (warehouseId) REFERENCES Warehouse3D(id)
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS Shelf3D (
        id INTEGER PRIMARY KEY,
        name TEXT,
        code TEXT,
        cabinetId INTEGER,
        length REAL,
        width REAL,
        height REAL,
        xPosition REAL,
        yPosition REAL,
        zPosition REAL,
        status TEXT,
        maxCapacity INTEGER,
        currentOccupancy INTEGER,
        BranchId INTEGER,
        FOREIGN KEY (cabinetId) REFERENCES Cabinet3D(id)
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS ProductLocation3D (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        productId INTEGER,
        productName TEXT,
        productCode TEXT,
        shelfId INTEGER,
        cabinetId INTEGER,
        warehouseId INTEGER,
        quantity REAL,
        xPosition REAL,
        yPosition REAL,
        zPosition REAL,
        lastUpdated TEXT,
        batchNumber TEXT,
        expiryDate TEXT,
        BranchId INTEGER,
        FOREIGN KEY (productId) REFERENCES ProductModel(ID),
        FOREIGN KEY (shelfId) REFERENCES Shelf3D(id),
        FOREIGN KEY (cabinetId) REFERENCES Cabinet3D(id),
        FOREIGN KEY (warehouseId) REFERENCES Warehouse3D(id)
      )
    ''');

    await db.execute('''
      CREATE TABLE IF NOT EXISTS WarehouseLayout (
        warehouseId INTEGER,
        layoutName TEXT,
        description TEXT,
        createdDate TEXT,
        lastModified TEXT,
        createdBy TEXT,
        layoutData TEXT,
        BranchId INTEGER,
        PRIMARY KEY (warehouseId, layoutName),
        FOREIGN KEY (warehouseId) REFERENCES Warehouse3D(id)
      )
    ''');
  }

//-------------------------------------------------------------------------------
  Future<void> selectAndRestoreDatabase() async {
    try {
      // Step 1: Open file picker to select a file
      final XFile? file = await openFile(
        acceptedTypeGroups: [
          const XTypeGroup(label: 'Database', extensions: ['db'])
        ],
      );

      if (file != null) {
        // Step 2: Get file path and read file
        final String selectedFilePath = file.path;
        print('Selected file path: $selectedFilePath');

        // Step 3: Define the destination for the database

        final Directory appDir = await getApplicationDocumentsDirectory();
        final String dbPath = path.join(appDir.path, 'invoices.db');

        // Step 4: Copy the selected file to the app's database directory
        final File selectedFile = File(selectedFilePath);
        await selectedFile.copy(dbPath);

        // Step 5: Notify the user
      } else {
        // User canceled the file picker
      }
    } catch (e) {
      // Handle errors
    }
  }

//-----------------------------------------------------------------------
  Future<void> createBackup() async {
    try {
      // Request storage permissions
      final status = await Permission.manageExternalStorage.request();
      if (status.isGranted) {
        final backupFolder = await getBackupFolder();
        // Access Downloads directory
        final downloadsDir = Directory(backupFolder);
        if (!downloadsDir.existsSync()) {
          await Directory(downloadsDir.path).create(recursive: true);
        }

        // Define backup file name with timestamp
        final String timestamp = DateTime.now().toString().replaceAll(':', '-');
        final String backupFileName = 'backup_database-$timestamp.db';
        final String backupFilePath =
            path.join(downloadsDir.path, backupFileName);

        // Replace this with your database path
        final currentDbPath = await getDatabasesPath();
        final currentPath = join(currentDbPath, 'invoices.db');
        final File currentDatabaseFile = File(currentPath);

        if (await currentDatabaseFile.exists()) {
          // Copy database file to Downloads
          await currentDatabaseFile.copy(backupFilePath);
          print('Backup created at: $backupFilePath');
        } else {
          print('Database file does not exist.');
        }
      } else {
        print('Storage permission not granted.');
      }
    } catch (e) {
      print('Failed to create backup: $e');
    }
  }

//-----------------------------------------------------------------------
  Future<String> getBackupFolder() async {
    final prefs = LocaleManager.instance;
    String? folderPath = prefs.getStringValue(PreferencesKeys.BackupPath);

    // If no backup folder is set, return a default folder
    if (folderPath.isEmpty) {
      folderPath = '/storage/emulated/0/Pal-ERP-Backup'; // Default folder
      await setBackupFolder();
    }

    return folderPath;
  }

//-----------------------------------------------------------------------
  Future<void> setBackupFolder() async {
    try {
      final prefs = LocaleManager.instance;
      final String? folderPath = await getDirectoryPath();

      if (folderPath != null) {
        // Save the folder path in shared preferences

        prefs.setString(PreferencesKeys.BackupPath, folderPath);
        print('Backup folder set to: $folderPath');
      } else {
        prefs.setString(
            PreferencesKeys.BackupPath, '/storage/emulated/0/Pal-ERP-Backup');
        print('No folder selected');
      }
    } catch (e) {
      print('Failed to set backup folder: $e');
    }
  }

  // Helper method to add BranchId to insert/update data
  Map<String, dynamic> _addBranchIdToData(Map<String, dynamic> data) {
    final newData = Map<String, dynamic>.from(data);
    newData['BranchId'] = AppController.currentBranchId;
    print(
        'Adding BranchId: ${AppController.currentBranchId} to data: $newData');
    return newData;
  }

  // Helper method to add BranchId to where clause
  String _addBranchIdCondition(String whereClause) {
    final condition = whereClause.isEmpty
        ? 'BranchId = ?'
        : 'BranchId = ? AND  $whereClause ';

    print('Adding BranchId condition: $condition');
    return condition;
  }

  // Helper method to add BranchId to a list of data
  List<Map<String, dynamic>> _addBranchIdToList(
      List<Map<String, dynamic>> dataList) {
    return dataList.map((data) => _addBranchIdToData(data)).toList();
  }

  // Bulk insert with BranchId
  Future<List<Object?>> bulkInsert(
      String table, List<Map<String, dynamic>> dataList) async {
    final db = await database;
    final batch = db.batch();

    final dataListWithBranchId = _addBranchIdToList(dataList);
    print('Bulk inserting into $table with data: $dataListWithBranchId');

    for (var data in dataListWithBranchId) {
      batch.insert(
        table,
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    return await batch.commit();
  }

  // Bulk insert without BranchId (for tables like Branches, Units, etc.)
  Future<List<Object?>> bulkInsertWithoutBranchId(
      String table, List<Map<String, dynamic>> dataList) async {
    final db = await database;
    final batch = db.batch();

    print('Bulk inserting into $table without BranchId with data: $dataList');

    for (var data in dataList) {
      batch.insert(
        table,
        data,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }

    return await batch.commit();
  }

  // Modified insert method
  Future<int> insert(String table, Map<String, dynamic> data) async {
    final db = await database;
    final dataWithBranchId = _addBranchIdToData(data);
    print('Inserting into $table with data: $dataWithBranchId');

    // Use insert with conflict resolution
    return await db.insert(
      table,
      dataWithBranchId,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  // Modified update method
  Future<int> update(
    String table,
    Map<String, dynamic> data, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    final dataWithBranchId = _addBranchIdToData(data);
    final whereWithBranchId = _addBranchIdCondition(where ?? '');
    final finalWhereArgs = [...?whereArgs, AppController.currentBranchId];

    print('Updating $table with data: $dataWithBranchId');
    print('Where: $whereWithBranchId, Args: $finalWhereArgs');

    return await db.update(
      table,
      dataWithBranchId,
      where: whereWithBranchId,
      whereArgs: finalWhereArgs,
    );
  }

  // Modified query method
  Future<List<Map<String, dynamic>>> query(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    final db = await database;
    final whereWithBranchId = _addBranchIdCondition(where ?? '');
    final finalWhereArgs = [AppController.currentBranchId, ...?whereArgs];

    print('Querying $table with where: $whereWithBranchId');

    final results = await db.query(
      table,
      where: whereWithBranchId,
      whereArgs: finalWhereArgs,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );

    print('Query results: $results');
    return results;
  }

  // Modified delete method
  Future<int> delete(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    final whereWithBranchId = _addBranchIdCondition(where ?? '');
    final finalWhereArgs = [...?whereArgs, AppController.currentBranchId];

    print('Deleting from $table');
    print('Where: $whereWithBranchId, Args: $finalWhereArgs');

    return await db.delete(
      table,
      where: whereWithBranchId,
      whereArgs: finalWhereArgs,
    );
  }

  // Raw query with BranchId
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql,
    List<dynamic>? arguments,
  ) async {
    final db = await database;
    List<dynamic> finalArgs = arguments ?? [];

    // Add BranchId condition to the SQL query
    if (!sql.toLowerCase().contains('where')) {
      sql += ' WHERE BranchId = ?';
      finalArgs.add(AppController.currentBranchId);
    } else {
      sql = sql.replaceAll('WHERE', 'WHERE BranchId = ? AND');
      finalArgs.insert(0, AppController.currentBranchId);
    }

    print('Raw query: $sql with args: $finalArgs');
    final results = await db.rawQuery(sql, finalArgs);
    print('Raw query results: $results');
    return results;
  }

  // Get count with BranchId
  Future<int> getCount(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    return Sqflite.firstIntValue(await db.rawQuery(
          'SELECT COUNT(*) FROM $table WHERE ${_addBranchIdCondition(where ?? '')}',
          whereArgs,
        )) ??
        0;
  }

  // Get single item with BranchId
  Future<Map<String, dynamic>?> getItem(
    String table, {
    String? where,
    List<dynamic>? whereArgs,
  }) async {
    final db = await database;
    final List<Map<String, dynamic>> results = await db.query(
      table,
      where: _addBranchIdCondition(where ?? ''),
      whereArgs: whereArgs,
      limit: 1,
    );
    return results.isNotEmpty ? results.first : null;
  }
}
