import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:inventory_application/main.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_response_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';

import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:provider/provider.dart';

class InventoryOperationExpiredController with ChangeNotifier {
  List<ProductDTO> selectedExpiredProduct = [];
  String? returnTransfarCode;
  InventoryOperationModel inventoryExpired = InventoryOperationModel();

  ///--------------------------------------------------------------------------
  Future<bool> repeatExpiredInventory() async {
    try {
      // Reset the ID to mark it as new
      inventoryExpired.iD = 0;

      // Generate new reference number
      inventoryExpired.aPPReferanceCode = await getExpiredNumber();

      // Save the new operation
      var result = await saveInventoryExpired();

      // Clear the state if save was successful
      if (result.isSuccess) {
        inventoryExpired = InventoryOperationModel();
        selectedExpiredProduct.clear();
        notifyListeners();
      }

      return result.isSuccess;
    } catch (e) {
      print("Error in repeatExpiredInventory: $e");
      return false;
    }
  }

  void addProductToSelectedList(ProductDTO model) {
    // IMPORTANT: Apply default unit first, before any other processing
    final invoiceSettingsController = Provider.of<InvoiceSettingsController>(
      navigatorKey.currentContext!,
      listen: false,
    );

    // Force apply default unit if default is set (regardless of whether product already has a unit)
    if (invoiceSettingsController.defaultUnitId != null) {
      model.uniteId = invoiceSettingsController.defaultUnitId;
      model.uniteName = invoiceSettingsController.defaultUnitName;
    }

    // Apply default warehouse if available
    if (inventoryExpired.fromStoreID != null) {
      model.warehouseId = inventoryExpired.fromStoreID;
      model.warehouseName = inventoryExpired.storeName ?? "";
    }

    // Create a copy of the model to ensure it's a completely separate object
    ProductDTO newProduct = ProductDTO(
        id: model.id,
        title: model.title,
        barcode: model.barcode,
        barcodeName: model.barcodeName,
        code: model.code,
        description: model.description,
        price: model.price,
        total: model.total,
        discountValue: model.discountValue,
        stock: model.stock,
        uniteId: model.uniteId,
        uniteName: model.uniteName,
        category: model.category,
        quantity: model.quantity,
        warehouseId: model.warehouseId,
        warehouseName: model.warehouseName,
        thumbnail: model.thumbnail,
        warehouse: model.warehouse,
        units: model.units,
        attribute: model.attribute,
        barcodes: model.barcodes,
        hasSelectedAttributes: model.hasSelectedAttributes,
        virtualProductId: model.virtualProductId,
        itemAttributes: model.itemAttributes);

    // Check if this exact product + attributes combination already exists
    int existingIndex = -1;
    if (model.virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      existingIndex = selectedExpiredProduct.indexWhere(
        (element) => element.virtualProductId == model.virtualProductId,
      );
    } else {
      // Otherwise, fall back to checking by regular ID
      existingIndex = selectedExpiredProduct.indexWhere(
        (element) =>
            element.id == model.id &&
            element.barcode == model.barcode &&
            !element.hasSelectedAttributes,
      );
    }

    // If we found the exact same product, increase its quantity instead of adding a new one
    if (existingIndex >= 0) {
      // Create a new list to trigger UI update
      List<ProductDTO> updatedList = List.from(selectedExpiredProduct);

      double newQuantity = (updatedList[existingIndex].quantity ?? 1) +
          (newProduct.quantity ?? 1);

      // Update the quantity
      updatedList[existingIndex].quantity = newQuantity;

      // Update the total
      double price = updatedList[existingIndex].price ?? 0;
      updatedList[existingIndex].total = price * newQuantity;

      // Replace the list with the updated one
      selectedExpiredProduct = updatedList;
    } else {
      // Add as a new item
      selectedExpiredProduct.add(newProduct);
    }

    calculateInvoiceTotal();
    notifyListeners();
  }

  ///--------------------------------------------------------------------------
  void deleteProductFromSelectedList(int id, {String? virtualProductId}) {
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedExpiredProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedExpiredProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      selectedExpiredProduct.removeAt(productIndex);
      calculateInvoiceTotal();
      notifyListeners();
    }
  }

  //--------------------------------------------------------------------------
  //--------------------------------------------------------------------------
  void updateProductPrice(int id, double price, [String? virtualProductId]) {
    if (virtualProductId != null) {
      // Find the product by virtual ID
      var productIndex = selectedExpiredProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );

      if (productIndex >= 0) {
        selectedExpiredProduct[productIndex].price = price;
        selectedExpiredProduct[productIndex].total =
            price * (selectedExpiredProduct[productIndex].quantity ?? 1);
      }
    } else {
      // Find the product by regular ID
      var productIndex = selectedExpiredProduct.indexWhere(
        (element) => element.id == id,
      );

      if (productIndex >= 0) {
        selectedExpiredProduct[productIndex].price = price;
        selectedExpiredProduct[productIndex].total =
            price * (selectedExpiredProduct[productIndex].quantity ?? 1);
      }
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

  //--------------------------------------------------------------------------
  //--------------------------------------------------------------------------
  void updateProductQuantity(int id, double quantity,
      [String? virtualProductId]) async {
    // Find the product index
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedExpiredProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedExpiredProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      // Update the quantity
      selectedExpiredProduct[productIndex].quantity = quantity;

      // Update the total amount based on the new quantity and existing price
      double price = selectedExpiredProduct[productIndex].price ?? 0;
      selectedExpiredProduct[productIndex].total = price * quantity;

      // Calculate the invoice total
      calculateInvoiceTotal();

      // Notify listeners for UI update
      notifyListeners();

      // Small delay to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 100));

      // Notify again to ensure UI is updated
      notifyListeners();
    }
  }

  //--------------------------------------------------------------------------
  void updateProductUnit(int id, ItemPriceDTO unit,
      [String? virtualProductId]) {
    if (virtualProductId != null) {
      // Find the product by virtual ID
      var productIndex = selectedExpiredProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );

      if (productIndex >= 0) {
        // Only update the unit ID and name, not the price
        selectedExpiredProduct[productIndex].uniteId = unit.unitID;
        selectedExpiredProduct[productIndex].uniteName = unit.unitName;

        // Keep the existing price and total
        double price = selectedExpiredProduct[productIndex].price ?? 0;
        double quantity = selectedExpiredProduct[productIndex].quantity ?? 1;
        selectedExpiredProduct[productIndex].total = price * quantity;
      }
    } else {
      // Find the product by regular ID
      var productIndex = selectedExpiredProduct.indexWhere(
        (element) => element.id == id,
      );

      if (productIndex >= 0) {
        // Only update the unit ID and name, not the price
        selectedExpiredProduct[productIndex].uniteId = unit.unitID;
        selectedExpiredProduct[productIndex].uniteName = unit.unitName;

        // Keep the existing price and total
        double price = selectedExpiredProduct[productIndex].price ?? 0;
        double quantity = selectedExpiredProduct[productIndex].quantity ?? 1;
        selectedExpiredProduct[productIndex].total = price * quantity;
      }
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

  //--------------------------------------------------------------------------
  Future<dynamic> getItemByBarcode({String? barcode}) async {
    try {
      if (barcode == null || barcode.isEmpty) {
        return false;
      }

      // Notify that we're starting to process
      notifyListeners();

      // Use getItemByBarcodeWithWriting instead to handle final barcodes
      var result =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);
      if (result != null) {
        // If the product has attributes but they haven't been selected yet,
        // and it's not a final barcode, return the product so the UI can show the attribute dialog
        if (result.itemAttributes != null &&
            result.itemAttributes!.isNotEmpty &&
            !result.hasSelectedAttributes) {
          return result; // Return the product so the UI can show the attribute selection dialog
        }

        // First check if we already have this product with the same barcode
        int existingIndex = -1;

        // Handle products with virtual ID (products with attributes)
        if (result.virtualProductId != null) {
          existingIndex = selectedExpiredProduct.indexWhere(
            (element) => element.virtualProductId == result.virtualProductId,
          );
        } else {
          // UPDATED LOGIC: Match by exact barcode for both final and non-final barcodes
          for (int i = 0; i < selectedExpiredProduct.length; i++) {
            var product = selectedExpiredProduct[i];

            // IMPORTANT: For FINAL barcodes, we need to match by exact barcode
            // regardless of hasSelectedAttributes status
            if (product.barcode != null &&
                result.barcode != null &&
                product.barcode!.isNotEmpty &&
                result.barcode!.isNotEmpty &&
                product.barcode == barcode) {
              // For final barcodes (hasSelectedAttributes = true), match by exact barcode
              if (result.hasSelectedAttributes == true &&
                  product.hasSelectedAttributes == true) {
                existingIndex = i;
                break;
              }
              // For non-final barcodes (hasSelectedAttributes = false), also match by exact barcode
              else if (result.hasSelectedAttributes != true &&
                  product.hasSelectedAttributes != true) {
                existingIndex = i;
                break;
              }
            }
          }

          // If no barcode match found, check by product ID (only if both have no barcode)
          if (existingIndex == -1) {
            for (int i = 0; i < selectedExpiredProduct.length; i++) {
              var product = selectedExpiredProduct[i];

              // Skip items that have virtual IDs
              if (product.virtualProductId != null) {
                continue;
              }

              // Only match by ID if both products have no barcode or empty barcode
              bool bothHaveNoBarcode =
                  (result.barcode == null || result.barcode!.isEmpty) &&
                      (product.barcode == null || product.barcode!.isEmpty);

              if (bothHaveNoBarcode &&
                  product.id != null &&
                  result.id != null &&
                  product.id == result.id &&
                  result.hasSelectedAttributes ==
                      product.hasSelectedAttributes) {
                existingIndex = i;
                break;
              }
            }
          }
        }

        // If we found an existing product, increase its quantity
        if (existingIndex >= 0) {
          var existingProduct = selectedExpiredProduct[existingIndex];
          double newQuantity = (existingProduct.quantity ?? 1) + 1;

          // Update the quantity
          selectedExpiredProduct[existingIndex].quantity = newQuantity;

          // Update the total
          double price = selectedExpiredProduct[existingIndex].price ?? 0;
          selectedExpiredProduct[existingIndex].total = price * newQuantity;

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        } else {
          // This is a new product, add it to the list
          // Apply default unit if default is set
          final invoiceSettingsController =
              Provider.of<InvoiceSettingsController>(
            navigatorKey.currentContext!,
            listen: false,
          );

          // Force apply default unit if default is set
          if (invoiceSettingsController.defaultUnitId != null) {
            result.uniteId = invoiceSettingsController.defaultUnitId;
            result.uniteName = invoiceSettingsController.defaultUnitName;
          }

          // Set warehouse if available
          if (inventoryExpired.fromStoreID != null) {
            result.warehouseId = inventoryExpired.fromStoreID;
            result.warehouseName = inventoryExpired.storeName ?? "";
          }

          // IMPORTANT: For products with final barcodes (hasSelectedAttributes = true)
          // that don't have a virtualProductId, generate one to ensure proper identification in the UI
          if (result.virtualProductId == null &&
              result.hasSelectedAttributes == true &&
              result.barcode != null &&
              result.barcode!.isNotEmpty) {
            // Generate a consistent virtual product ID based on barcode for final barcodes
            result.virtualProductId = '${result.id}_final_${result.barcode}';
          }

          // Ensure quantity is at least 1
          result.quantity = result.quantity ?? 1;

          // Calculate total
          result.total = (result.price ?? 0) * result.quantity!;

          // Add to list
          selectedExpiredProduct.add(result);

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        }
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  //---------------------------------------------------------------------------
  void setProductWarehouse(int id, int warehouseId, String warehouseName,
      [String? virtualProductId]) {
    ProductDTO product;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      product = selectedExpiredProduct.firstWhere(
        (element) => element.virtualProductId == virtualProductId,
        orElse: () => selectedExpiredProduct.firstWhere(
          (element) => element.id == id,
          orElse: () => ProductDTO(),
        ),
      );
    } else {
      // Otherwise use the regular product ID
      product = selectedExpiredProduct.firstWhere(
        (element) => element.id == id,
        orElse: () => ProductDTO(),
      );
    }

    if (product.id != null) {
      product.warehouseId = warehouseId;
      product.warehouseName = warehouseName;
      notifyListeners();
    }
  }

  //---------------------------------------------------------------------------
  Future<String> getExpiredNumber() async {
    try {
      var number =
          await InventoryOperationCounterGenerator.getNextCounterByType(
              InventoryOperationType.DamagedExpired);
      inventoryExpired.aPPReferanceCode ??= number;

      notifyListeners();
      return inventoryExpired.aPPReferanceCode ?? "";
    } catch (e) {
      return "";
    }
  }

  //---------------------------------------------------------------------------

  void calculateInvoiceTotal() {
    double total = 0;

    for (var product in selectedExpiredProduct) {
      total += product.total ?? 0;
    }

    inventoryExpired.total = total;

    // Ensure UI updates with immediate notification
    notifyListeners();

    // Add a small delay and notify again to ensure UI updates
    Future.delayed(const Duration(milliseconds: 100), () {
      notifyListeners();
    });
  }

  //---------------------------------------------------------------------------
  //---------------------------------------------------------- -----------------
  Future<ResponseResultModel> saveInventoryExpired() async {
    try {
      var url = '/InventoryOperation/Manage?transactions_type=DamagedExpired';
      final db = InventoryOperationController();

      var mapeditems =
          mapListProductDtoToInventoryOperationItem(selectedExpiredProduct);
      inventoryExpired.inventoryOperationItems = mapeditems;

      // mapedInvoice.paidAmount ??= 0.0;

      inventoryExpired.operationType =
          InventoryOperationType.DamagedExpired.index;
      inventoryExpired.entryDateFormated =
          DateFormat('dd/MM/yyyy', 'en').format(DateTime.now());
      if (inventoryExpired.iD == null || (inventoryExpired.iD ?? 0) == 0) {
        inventoryExpired.entryDate = DateTime.now();
        inventoryExpired.code = "*";
      }

      if (inventoryExpired.aPPReferanceCode == null ||
          inventoryExpired.aPPReferanceCode == "") {
        inventoryExpired.aPPReferanceCode = await getExpiredNumber();
      } else {
        if (await InventoryOperationCounterGenerator.checkIfCounterUsed(
            InventoryOperationType.DamagedExpired,
            inventoryExpired.aPPReferanceCode ?? "")) {
          inventoryExpired.aPPReferanceCode = await getExpiredNumber();
        }
      }
      inventoryExpired.iD ??= 0;
      // mapedInvoice.iD = 0;
      // mapedInvoice.vATPercent = 0;

      if (await isThereNetworkConnection() == false) {
        var localId = await db.insertOrUpdateInventoryOperation(
            SqlLiteInvoiceModel(
                    data: jsonEncode(inventoryExpired.toJson()),
                    status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
                    id: null,
                    localCode: inventoryExpired.aPPReferanceCode,
                    type: InventoryOperationType.DamagedExpired.name)
                .toJson());
        inventoryExpired = InventoryOperationModel();
        selectedExpiredProduct.clear();
        if (inventoryExpired.iD == 0) {
          await InventoryOperationCounterGenerator.setCounterByTypeAuto(
              type: InventoryOperationType.DamagedExpired);
        }
        notifyListeners();

        return ResponseResultModel(
            isSuccess: true, data: InvoiceResponseDto(localId: localId));
      }

      var result = await Api.post(
        action: url,
        body: inventoryExpired.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          if (inventoryExpired.iD == 0) {
            await InventoryOperationCounterGenerator
                .setInventoryOperationCounterInServer();
            await InventoryOperationCounterGenerator.setCounterByTypeAuto(
                type: InventoryOperationType.DamagedExpired);
          }
          var response = InvoiceResponseDto.fromJson(result.data);
          inventoryExpired.iD = response.id;
          inventoryExpired.code = response.code;
          var localId = await db.insertOrUpdateInventoryOperation(
              SqlLiteInvoiceModel(
                      data: jsonEncode(inventoryExpired.toJson()),
                      status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                      id: null,
                      localCode: inventoryExpired.aPPReferanceCode,
                      type: InventoryOperationType.DamagedExpired.name)
                  .toJson());
          response.localId = localId;
          // await db.saveInventoryOperationToEcommerce(
          //     inventoryExpired, TransactionTypes.Damaged);
          inventoryExpired = InventoryOperationModel();
          selectedExpiredProduct.clear();
          // await db.saveReturnToEcommerce(mapedInvoice);
          notifyListeners();
          return ResponseResultModel(data: response, isSuccess: true);
        }
      }
      return result ?? ResponseResultModel(isSuccess: false);
    } catch (e) {
      print(e);
      return ResponseResultModel(isSuccess: false);
    }
  }
}
