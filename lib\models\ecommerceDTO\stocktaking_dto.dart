class StocktakingBalanceRequestDTO {
  int? itemId;
  List<int>? optionsIds;
  String? finalBarcode;

  StocktakingBalanceRequestDTO({
    this.itemId,
    this.optionsIds,
    this.finalBarcode,
  });

  factory StocktakingBalanceRequestDTO.fromJson(Map<String, dynamic> json) {
    return StocktakingBalanceRequestDTO(
      itemId: json['itemId'],
      optionsIds: json['optionsIds'] != null
          ? List<int>.from(json['optionsIds'])
          : null,
      finalBarcode: json['finalBarcode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'itemId': itemId,
      'optionsIds': optionsIds,
      'finalBarcode': finalBarcode,
    };
  }
}

class StocktakingBalanceResponseDTO {
  int? itemId;
  List<int>? optionsIds;
  String? finalBarcode;
  double? balance;
  bool? isHaveCombination;

  StocktakingBalanceResponseDTO({
    this.itemId,
    this.optionsIds,
    this.finalBarcode,
    this.balance,
    this.isHaveCombination,
  });

  factory StocktakingBalanceResponseDTO.fromJson(Map<String, dynamic> json) {
    return StocktakingBalanceResponseDTO(
      itemId: json['itemId'],
      optionsIds: json['optionsIds'] != null
          ? List<int>.from(json['optionsIds'])
          : null,
      finalBarcode: json['finalBarcode'],
      balance: (json['balance'] as num?)?.toDouble(),
      isHaveCombination: json['isHaveCombination'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'itemId': itemId,
      'optionsIds': optionsIds,
      'finalBarcode': finalBarcode,
      'balance': balance,
      'isHaveCombination': isHaveCombination,
    };
  }
}
