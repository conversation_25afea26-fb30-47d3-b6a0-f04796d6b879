import 'package:flutter/material.dart';
import 'package:inventory_application/base/constants/roles/permission_constants.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/reports/custom%20reports/card%20detail%20report/card_detail_report_screen.dart';

import 'package:inventory_application/screens/reports/customer_sales_report_screen.dart';
import 'package:inventory_application/screens/reports/daily_sales_report_screen.dart';
import 'package:inventory_application/screens/reports/monthly_sales_report_screen.dart';
import 'package:inventory_application/screens/reports/product_sales_report_screen.dart';
import 'package:inventory_application/screens/reports/server_reports/sales_total_report_screen.dart';
import 'package:inventory_application/screens/reports/server_reports/product_sales_report_screen.dart'
    as ServerProductSalesReport;
import 'package:inventory_application/screens/reports/server_reports/customer_sales_report_screen.dart'
    as ServerCustomerSalesReport;
import 'package:inventory_application/screens/reports/server_reports/sales_summary_report_screen.dart'
    as ServerSalesSummaryReport;
import 'package:inventory_application/screens/reports/server_reports/inventory_attributes_report_screen.dart';
import 'package:inventory_application/screens/reports/server_reports/product_transaction_details_report_screen.dart';
import 'package:inventory_application/screens/reports/custom%20reports/closging%20entrie%20report/closing_entries_report_screen.dart';
import 'package:inventory_application/screens/reports/comprehensive_report_screen.dart';
import 'package:inventory_application/services/authentication_service.dart';
import 'package:provider/provider.dart';

class ReportsMenuScreen extends StatefulWidget {
  const ReportsMenuScreen({super.key});

  @override
  State<ReportsMenuScreen> createState() => _ReportsMenuScreenState();
}

class _ReportsMenuScreenState extends State<ReportsMenuScreen> {
  bool isUsingEcommerce = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {});
  }

  @override
  Widget build(BuildContext context) {
    isUsingEcommerce = AppController.getIsUsingEcommerceFromShared();
    AppController.currentBranchId;
    AppController.currentBranchName;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Modern Header
            _buildModernHeader(context),

            const SizedBox(height: 32),

            // Local Reports Section
            _buildReportsSection(
              context,
              title: '💾 ${T('التقارير المحلية')}',
              subtitle: T('تقارير سريعة من البيانات المحلية'),
              color: const Color(0xFF3B82F6),
              reports: _getInternalReports(context),
            ),
            const SizedBox(height: 32),
            // Server Reports Section
            _buildReportsSection(
              context,
              title: '🌐 ${T('تقارير السيرفر')}',
              subtitle: T('تقارير متقدمة وتحليلات شاملة'),
              color: const Color(0xFF10B981),
              reports: _getServerReports(context),
            ),
            const SizedBox(height: 32),

            // Custom Reports Section
            _buildReportsSection(
              context,
              title: '⚙️ ${T('تقارير مخصصة')}',
              subtitle: T('تقارير متخصصة للعمليات المحاسبية والمالية'),
              color: const Color(0xFF8B5CF6),
              reports: _getCustomReports(context),
            ),
            const SizedBox(height: 32),

            const SizedBox(height: 60),
          ],
        ),
      ),
    );
  }

  Widget _buildModernHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF667EEA),
            Color(0xFF764BA2),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF667EEA).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      T('📊 لوحة التقارير'),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      T('استكشف بيانات عملك بذكاء ووضوح'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.insights,
                  size: 32,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildHeaderStat('11', T('تقارير متاحة')),
              const SizedBox(width: 24),
              _buildHeaderStat('4', T('أقسام رئيسية')),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String number, String label) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          number,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w700,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withOpacity(0.8),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildReportsSection(
    BuildContext context, {
    required String title,
    required String subtitle,
    required Color color,
    required List<ReportItem> reports,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: color.withOpacity(0.1),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: color.withOpacity(0.08),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w700,
                        color: color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF64748B),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${reports.length} ${T('تقارير')}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Reports Grid
        LayoutBuilder(
          builder: (context, constraints) {
            final crossAxisCount = constraints.maxWidth > 600 ? 4 : 2;
            final cardWidth =
                (constraints.maxWidth - ((crossAxisCount - 1) * 16)) /
                    crossAxisCount;

            return Wrap(
              spacing: 16,
              runSpacing: 16,
              children: reports.map((report) {
                return SizedBox(
                  width: cardWidth,
                  child: _buildSimpleReportCard(
                    context,
                    report: report,
                    color: color,
                  ),
                );
              }).toList(),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSimpleReportCard(
    BuildContext context, {
    required ReportItem report,
    required Color color,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: report.onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color(0xFFE2E8F0),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.04),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Icon and Badge Row
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      report.icon,
                      size: 24,
                      color: color,
                    ),
                  ),
                  if (report.isNew)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        T('جديد'),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 16),

              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      report.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF1E293B),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 6),
                    Text(
                      report.subtitle,
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF64748B),
                        fontWeight: FontWeight.w500,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    // const Spacer(),

                    // Action Button
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<ReportItem> _getInternalReports(BuildContext context) {
    return [
      ReportItem(
        title: T('المبيعات اليومية لهذا الجهاز'),
        subtitle: T('إحصائيات وتحليلات المبيعات اليومية'),
        icon: Icons.today_rounded,
        onTap: () => _navigateToReport(context, ReportType.daily),
        isNew: false,
      ),
      // ReportItem(
      //   title: T(' الشهرية'),
      //   subtitle: T('تتبع الأداء الشهري والاتجاهات'),
      //   icon: Icons.calendar_month_rounded,
      //   onTap: () => _navigateToReport(context, ReportType.monthly),
      //   isNew: false,
      // ),
      ReportItem(
        title: T(' المنتجات المباعة لهذا الجهاز'),
        subtitle: T('تحليل أداء المنتجات والمخزون'),
        icon: Icons.inventory_2_rounded,
        onTap: () => _navigateToReport(context, ReportType.product),
        isNew: false,
      ),
      ReportItem(
        title: T('مبيعات العملاء لهذا الجهاز'),
        subtitle: T('فهم سلوك العملاء وتفضيلاتهم'),
        icon: Icons.people_rounded,
        onTap: () => _navigateToReport(context, ReportType.customer),
        isNew: false,
      ),
    ];
  }

  List<ReportItem> _getServerReports(BuildContext context) {
    return [
      ReportItem(
        title: T('(ملخص مالي)  إجمالي المبيعات'),
        subtitle: T('تقرير شامل يومي / شهري'),
        icon: Icons.analytics_rounded,
        onTap: () =>
            _navigateToServerReport(context, ServerReportType.salesTotal),
        isNew: false,
      ),
      ReportItem(
        title: T('المنتجات'),
        subtitle: T('تحليل مفصل لأداء المنتجات'),
        icon: Icons.shopping_basket_rounded,
        onTap: () =>
            _navigateToServerReport(context, ServerReportType.productSales),
        isNew: false,
      ),
      ReportItem(
        title: T('مبيعات العملاء'),
        subtitle: T('إحصائيات العملاء والكميات المباعة'),
        icon: Icons.person_outline_rounded,
        onTap: () {
          if (Provider.of<AuthenticationService>(context, listen: false)
                  .isMainAdmin !=
              true) {
            errorSnackBar(
              message: T("You don't have permission to access this feature"),
              context: context,
            );
            return;
          }
          _navigateToServerReport(context, ServerReportType.customerSales);
        },
        isNew: true,
      ),
      ReportItem(
        title: T('ملخص'),
        subtitle: T('تحليل شامل للمستخدمين والأجهزة'),
        icon: Icons.dashboard_rounded,
        onTap: () {
          if (Provider.of<AuthenticationService>(context, listen: false)
                  .isMainAdmin !=
              true) {
            errorSnackBar(
              message: T("You don't have permission to access this feature"),
              context: context,
            );
            return;
          }
          _navigateToServerReport(context, ServerReportType.salesSummary);
        },
        isNew: true,
      ),
      ReportItem(
        title: T('تقرير خصائص المخزون'),
        subtitle: T('تقرير مفصل لخصائص الأصناف حسب الفروع'),
        icon: Icons.inventory_2,
        onTap: () => _navigateToServerReport(
            context, ServerReportType.inventoryAttributes),
        isNew: true,
      ),
      ReportItem(
        title: T('تقرير تفاصيل معاملات المنتج'),
        subtitle: T('تقرير شامل لجميع معاملات المنتج مع الفلترة والترتيب'),
        icon: Icons.receipt_long,
        onTap: () => _navigateToServerReport(
            context, ServerReportType.productTransactionDetails),
        isNew: true,
      ),
    ];
  }

  List<ReportItem> _getCustomReports(BuildContext context) {
    return [
      // ReportItem(
      //   title: T('التقرير الشامل المجمع'),
      //   subtitle: T('دمج بيانات المبيعات وقيود الإقفال من مصدرين مختلفين'),
      //   icon: Icons.merge_type,
      //   onTap: () =>
      //       _navigateToCustomReport(context, CustomReportType.comprehensive),
      //   isNew: true,
      // ),
      ReportItem(
        title: T('تقرير قيود الإقفال'),
        subtitle: T('ملخص مالي شامل لعمليات الشحن والمبيعات'),
        icon: Icons.account_balance_outlined,
        onTap: () {
          if (!Provider.of<AuthenticationService>(context, listen: false)
              .hasPermission(PermissionConstants.journalEntryCreate)) {
            errorSnackBar(
              message: T("You don't have permission to access this feature"),
              context: context,
            );
            return;
          }
          _navigateToCustomReport(context, CustomReportType.closingEntries);
        },
        isNew: true,
      ),
      ReportItem(
        title: T('تقرير تفاصيل الكارت'),
        subtitle: T('تفاصيل معاملات الشحن والمبيعات للكارت'),
        icon: Icons.card_membership_outlined,
        onTap: () {
          if (!Provider.of<AuthenticationService>(context, listen: false)
              .hasPermission(PermissionConstants.salesInvoiceView)) {
            errorSnackBar(
              message: T("You don't have permission to access this feature"),
              context: context,
            );
            return;
          }
          _navigateToCustomReport(
              context, CustomReportType.cardTransacionsDetail);
        },
        isNew: true,
      ),
    ];
  }

  // Navigation methods
  void _navigateToReport(BuildContext context, ReportType type) {
    Widget reportScreen;

    switch (type) {
      case ReportType.daily:
        reportScreen = const DailySalesReportScreen();
        break;
      case ReportType.monthly:
        reportScreen = const MonthlySalesReportScreen();
        break;
      case ReportType.product:
        reportScreen = const ProductSalesReportScreen();
        break;
      case ReportType.customer:
        reportScreen = const CustomerSalesReportScreen();
        break;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => reportScreen),
    );
  }

  void _navigateToServerReport(BuildContext context, ServerReportType type) {
    Widget reportScreen;

    switch (type) {
      case ServerReportType.salesTotal:
        reportScreen = const SalesTotalReportScreen();
        break;
      case ServerReportType.productSales:
        reportScreen =
            const ServerProductSalesReport.ProductSalesReportScreen();
        break;
      case ServerReportType.customerSales:
        reportScreen =
            const ServerCustomerSalesReport.CustomerSalesReportScreen();
        break;
      case ServerReportType.salesSummary:
        reportScreen =
            const ServerSalesSummaryReport.SalesSummaryReportScreen();
        break;
      case ServerReportType.inventoryAttributes:
        reportScreen = const InventoryAttributesReportScreen();
        break;
      case ServerReportType.productTransactionDetails:
        reportScreen = const ProductTransactionDetailsReportScreen();
        break;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => reportScreen),
    );
  }

  void _navigateToCustomReport(BuildContext context, CustomReportType type) {
    Widget reportScreen = const ClosingEntriesReportScreen(); // Default value

    switch (type) {
      case CustomReportType.comprehensive:
        reportScreen = const ComprehensiveReportScreen();
        break;
      case CustomReportType.closingEntries:
        reportScreen = const ClosingEntriesReportScreen();
        break;
      case CustomReportType.cardTransacionsDetail:
        reportScreen = const CardDetailReportScreen();
        break;
    }

    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => reportScreen),
    );
  }
}

// Data class for report items
class ReportItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final VoidCallback onTap;
  final bool isNew;

  ReportItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.onTap,
    this.isNew = false,
  });
}

// Enums
enum ReportType {
  daily,
  monthly,
  product,
  customer,
}

enum ServerReportType {
  salesTotal,
  productSales,
  customerSales,
  salesSummary,
  inventoryAttributes,
  productTransactionDetails,
}

enum CustomReportType {
  comprehensive,
  closingEntries,
  cardTransacionsDetail,
}

enum EcommerceReportType {
  productTransactions,
  productQuantities,
}
