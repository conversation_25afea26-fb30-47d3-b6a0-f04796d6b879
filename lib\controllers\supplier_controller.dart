import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper .dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/supplier_model.dart';
import 'package:sqflite/sqflite.dart';

class SupplierController with ChangeNotifier {
  List<SupplierModel> suppliers = [];
  bool runningSyncization = false;
  int fetchedSuppliersCount = 0;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  Future<void> getSuppliers() async {
    try {
      // return;
      suppliers.clear();
      var fromLocalDatabase = await getSuppliersFromLocal();
      if (fromLocalDatabase.isNotEmpty) {
        suppliers.addAll(
          fromLocalDatabase.map((e) => SupplierModel.fromJson(e)).toList(),
        );
        notifyListeners();
        return;
      } else {
        await fetchSuppliersFromServer();
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> fetchSuppliersFromServer() async {
    if (runningSyncization) return;
    try {
      // return;
      var fromLocalDatabase = await getSuppliersFromLocal();

      if (await isThereNetworkConnection() == false) {
        suppliers.clear();
        suppliers.addAll(
          fromLocalDatabase.map((e) => SupplierModel.fromJson(e)).toList(),
        );
        notifyListeners();
        return;
      }

      suppliers.clear();
      bool isStillThereSuppliers = true;
      fetchedSuppliersCount = 0;
      while (isStillThereSuppliers) {
        runningSyncization = true;

        var requestBody = {
          "Supplier_ID": "",
          "Mobile": "",
          "Supplier_Type_ID": "",
          "dataTableParameters": {
            "Skip": fetchedSuppliersCount,
            "Take": 200,
            "ColumnName": "Code",
            "Dir": "desc"
          }
        };

        var result = await Api.post(
            action: 'Suppliers/SuppliersList', body: requestBody);
        if (result != null && result.isSuccess) {
          for (var element in result.data) {
            var supplier = SupplierModel.fromJson(element);
            insertSupplierLocally(supplier);
            suppliers.add(supplier);
            fetchedSuppliersCount++;
            notifyListeners();
          }
          if (result.data.length < 200) {
            isStillThereSuppliers = false;
            runningSyncization = false;
          }
        } else {
          isStillThereSuppliers = false;
          runningSyncization = false;
        }
        // getSuppliers();
        notifyListeners();
      }
    } catch (e) {
      print(e);
    }
  }

  Future<bool> insertSupplier(SupplierModel supplier) async {
    try {
      var status = AppController.isThereConnection;

      if (status == true) {
        await syncSupplierWithServer(supplier);
        return await insertSupplierLocally(supplier);
      } else {
        return await insertSupplierLocally(supplier);
      }
    } catch (e) {
      print("Error in inserting supplier: $e");
      return false;
    }
  }

  Future<bool> insertSupplierLocally(SupplierModel supplier) async {
    try {
      final Database db = await _dbHelper.database;
      var supplierJson = supplier.toJson();
      await db.insert(
        'Supplier',
        supplierJson,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      suppliers.add(supplier);
      notifyListeners();
      return true;
    } catch (e) {
      print("Error in local supplier insertion: $e");
      return false;
    }
  }

  Future<bool> syncSupplierWithServer(SupplierModel supplier) async {
    try {
      var result = await Api.post(
        action: "Supplier/CreateSupplier",
        body: supplier.toJson(),
      );

      if (result != null && result.isSuccess) {
        supplier.iD = result.data['ID'];
        supplier.code = result.data['Code'];
        return true;
      }
      return false;
    } catch (e) {
      print("Error syncing supplier with server: $e");
      return false;
    }
  }

  Future<List<Map<String, dynamic>>> getSuppliersFromLocal() async {
    try {
      final Database db = await _dbHelper.database;
      return await db.query('Supplier');
    } catch (e) {
      print("Error getting suppliers from local: $e");
      return [];
    }
  }

  Future<bool> updateSupplier(SupplierModel supplier) async {
    try {
      var status = AppController.isThereConnection;

      if (status == true) {
        var result = await Api.put(
          action: "Supplier/UpdateSupplier",
          body: supplier.toJson(),
        );

        if (result != null && result.isSuccess) {
          await updateSupplierLocally(supplier);
          return true;
        }
        return false;
      } else {
        return await updateSupplierLocally(supplier);
      }
    } catch (e) {
      print("Error updating supplier: $e");
      return false;
    }
  }

  Future<bool> updateSupplierLocally(SupplierModel supplier) async {
    try {
      final Database db = await _dbHelper.database;
      await db.update(
        'Supplier',
        supplier.toJson(),
        where: 'ID = ?',
        whereArgs: [supplier.iD],
      );

      int index = suppliers.indexWhere((s) => s.iD == supplier.iD);
      if (index != -1) {
        suppliers[index] = supplier;
        notifyListeners();
      }
      return true;
    } catch (e) {
      print("Error updating supplier locally: $e");
      return false;
    }
  }

  Future<bool> deleteSupplier(int supplierId) async {
    try {
      var status = AppController.isThereConnection;

      if (status == true) {
        var result =
            await Api.delete(action: "Supplier/DeleteSupplier/$supplierId");

        if (result != null && result.isSuccess) {
          await deleteSupplierLocally(supplierId);
          return true;
        }
        return false;
      } else {
        return await deleteSupplierLocally(supplierId);
      }
    } catch (e) {
      print("Error deleting supplier: $e");
      return false;
    }
  }

  Future<bool> deleteSupplierLocally(int supplierId) async {
    try {
      final Database db = await _dbHelper.database;
      await db.delete(
        'Supplier',
        where: 'ID = ?',
        whereArgs: [supplierId],
      );

      suppliers.removeWhere((supplier) => supplier.iD == supplierId);
      notifyListeners();
      return true;
    } catch (e) {
      print("Error deleting supplier locally: $e");
      return false;
    }
  }

  SupplierModel? getSupplierById(int id) {
    try {
      return suppliers.firstWhere((supplier) => supplier.iD == id);
    } catch (e) {
      return null;
    }
  }

  List<SupplierModel> searchSuppliers(String searchTerm) {
    if (searchTerm.isEmpty) {
      return suppliers;
    }

    return suppliers.where((supplier) {
      return (supplier.name?.toLowerCase().contains(searchTerm.toLowerCase()) ??
              false) ||
          (supplier.code?.toLowerCase().contains(searchTerm.toLowerCase()) ??
              false);
    }).toList();
  }
}
