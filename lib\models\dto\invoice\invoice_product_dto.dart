import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';

//***************************************************************************************** */
enum InvoiceDiscountType { persantage, value }

enum InvoicePaymentType { chash, dibt }

enum SalesType {
  Order,
  ProformaInvoice,
  DeliveryNote,
  Invoice,
  RetrunInvoice,
  DiscountInvoice
}

extension SalesTypeExtension on SalesType {
  String get title {
    switch (this) {
      case SalesType.Order:
        return 'طلبيات مبيعات';
      case SalesType.Invoice:
        return 'فاتورة مبيعات';
      case SalesType.RetrunInvoice:
        return 'فاتورة مرتجعات';

      default:
        return 'غير معروف';
    }
  }
}

enum InvoiceSyncStatus { pending, syncing, synced }

class InvoiceDto {
  int? id;
  int? localId;
  String? invoiceCode;
  String? appReferanceCode;
  int? customerId;
  String? custoemrName;
  int? salesmanId;
  String? salesmanName;
  InvoicePaymentType? invoicePaymentType;
  SalesType? invoiceType;
  String? invoiceTypeName;
  InvoiceDiscountType? discountType;
  double? discountValue;
  double? paymentValue;
  double? discountPercentage;
  double? totalDiscount;
  double? totalAfterDiscount;
  double? total;
  double? productsTotalCount;
  int? warehouseId;
  String? warehouseName;
  String? note;
  String? connectedToInvoiceCode;
  String? connectedToInvoiceLocalCode;
  String? salesOrderID;
  DateTime? invoiceDate;
  int? pyamentMethodId;
  List<ProductDTO>? salesItems;

  InvoiceDto({
    this.id,
    this.localId,
    this.invoiceCode,
    this.appReferanceCode,
    this.customerId,
    this.salesmanId,
    this.salesmanName,
    this.custoemrName,
    this.invoicePaymentType,
    this.discountValue,
    this.paymentValue,
    this.discountPercentage,
    this.warehouseId,
    this.warehouseName,
    this.note,
    this.totalDiscount,
    this.totalAfterDiscount,
    this.productsTotalCount,
    this.total,
    this.invoiceType,
    this.invoiceTypeName,
    this.invoiceDate,
    this.connectedToInvoiceCode,
    this.salesOrderID,
    this.pyamentMethodId,
    this.connectedToInvoiceLocalCode,
    this.salesItems,
  });
  double calculateTotal() {
    double total = 0.0;
    if (salesItems != null) {
      for (var item in salesItems!) {
        total += item.price! * item.quantity!;
      }
    }
    return total;
  }

  // Method to calculate the discount (total discount value)
  double calculateTotalDiscount() {
    return discountValue ?? 0.0;
  }

  // Method to calculate total after discount
  double calculateTotalAfterDiscount() {
    return total! - calculateTotalDiscount();
  }

  // Method to calculate the net total (after discount)
  double calculateNetTotal() {
    return totalAfterDiscount! - (paymentValue ?? 0.0);
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'invoiceCode': invoiceCode,
      'customerId': customerId,
      'custoemrName': custoemrName,
      'salesmanId': salesmanId,
      'salesmanName': salesmanName,
      'invoicePaymentType': invoicePaymentType?.toString(),
      'invoiceType': invoiceType?.toString(),
      'invoiceTypeName': invoiceTypeName,
      'discountType': discountType?.toString(),
      'discountValue': discountValue,
      'paymentValue': paymentValue,
      'discountPercentage': discountPercentage,
      'totalDiscount': totalDiscount,
      'totalAfterDiscount': totalAfterDiscount,
      'total': total,
      'productsTotalCount': productsTotalCount,
      'warehouseId': warehouseId,
      'warehouseName': warehouseName,
      'note': note,
      'connectedToInvoiceCode': connectedToInvoiceCode,
      'salesOrderID': salesOrderID,
      'pyamentMethodId': pyamentMethodId,
      'invoiceDate': invoiceDate?.toIso8601String(),
      'salesItems': salesItems?.map((e) => e.toJson()).toList(),
    };
  }

  // Convert from JSON
  factory InvoiceDto.fromJson(Map<String, dynamic> json) {
    return InvoiceDto(
      id: json['id'],
      invoiceCode: json['invoiceCode'],
      customerId: json['customerId'],
      custoemrName: json['custoemrName'],
      salesmanId: json['salesmanId'],
      salesmanName: json['salesmanName'],
      invoiceTypeName: json['invoiceTypeName'],
      discountValue: json['discountValue'],
      paymentValue: json['paymentValue'],
      discountPercentage: json['discountPercentage'],
      totalDiscount: json['totalDiscount'],
      totalAfterDiscount: json['totalAfterDiscount'],
      total: json['total'],
      productsTotalCount: json['productsTotalCount'],
      warehouseId: json['warehouseId'],
      note: json['note'],
      pyamentMethodId: json['pyamentMethodId'],
      connectedToInvoiceCode: json['connectedToInvoiceCode'],
      salesOrderID: json['salesOrderID'],
      invoiceDate: json['invoiceDate'] != null
          ? DateTime.parse(json['invoiceDate'])
          : null,
      salesItems: (json['salesItems'] as List?)
          ?.map((item) => ProductDTO.fromJson(item))
          .toList(),
    );
  }
}

//----------------------------------------------------------
class InvoiceDtoWithLiteId {
  int? id;
  String? status;
  InvoiceModel? data;
  InvoiceDtoWithLiteId({this.id, this.data, this.status});

  factory InvoiceDtoWithLiteId.fromJson(Map<String, dynamic> json) {
    return InvoiceDtoWithLiteId(
      id: json['id'] as int?, // Parse the id from the JSON
      data: json['data'] != null ? InvoiceModel.fromJson(json['data']) : null,
    );
  }
}
