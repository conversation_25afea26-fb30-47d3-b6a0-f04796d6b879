class EcommerceLoginDTO {
  String? emailOrUserName;
  String? password;
  bool? rememberMe;
  bool? isCustomerApp;

  EcommerceLoginDTO(
      {this.emailOrUserName,
      this.password,
      this.rememberMe,
      this.isCustomerApp});

  EcommerceLoginDTO.fromJson(Map<String, dynamic> json) {
    emailOrUserName = json['emailOrUserName'];
    password = json['password'];
    rememberMe = json['rememberMe'];
    isCustomerApp = json['isCustomerApp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['emailOrUserName'] = this.emailOrUserName;
    data['password'] = this.password;
    data['rememberMe'] = this.rememberMe;
    data['isCustomerApp'] = this.isCustomerApp;
    return data;
  }
}
