class ProductModel {
  int? iD;
  String? name;
  String? code;
  int? parentID;
  String? parentName;
  String? mainImageUrl;
  int? levelType;
  bool? isParent;
  List<Inventory>? inventory;
  List<Barcodes>? barcodes;
  List<ItemAttribute>? itemAttributes;

  List<ItemPrice>? itemPrice;

  ProductModel(
      {this.iD,
      this.name,
      this.code,
      this.parentID,
      this.mainImageUrl,
      this.parentName,
      this.levelType,
      this.isParent,
      this.barcodes,
      this.inventory,
      this.itemPrice,
      this.itemAttributes});

  ProductModel.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    name = json['Name'];
    mainImageUrl = json['Main_Image_Url'];
    code = json['Code'];
    parentID = json['Parent_ID'];
    parentName = json['Parent_Name'];
    levelType = json['Level_Type'];
    if (json['Barcodes'] != null) {
      barcodes = <Barcodes>[];
      json['Barcodes'].forEach((v) {
        barcodes!.add(new Barcodes.fromJson(v));
      });
    }
    // isParent = json['isParent'];
    if (json['Inventory'] != null) {
      inventory = <Inventory>[];
      json['Inventory'].forEach((v) {
        inventory!.add(new Inventory.fromJson(v));
      });
    }
    if (json['ItemAttributes'] != null) {
      itemAttributes = <ItemAttribute>[];
      json['ItemAttributes'].forEach((v) {
        itemAttributes!.add(ItemAttribute.fromJson(v));
      });
    }
    if (json['Item_Price'] != null) {
      itemPrice = <ItemPrice>[];
      json['Item_Price'].forEach((v) {
        itemPrice!.add(new ItemPrice.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ID'] = iD;
    data['Name'] = name;
    data['Main_Image_Url'] = mainImageUrl;
    data['Code'] = code;
    data['Parent_ID'] = parentID;
    data['Level_Type'] = levelType;
    data['isParent'] = isParent;
    if (barcodes != null) {
      data['Barcodes'] = barcodes!.map((v) => v.toJson()).toList();
    }
    if (inventory != null) {
      data['Inventory'] = inventory!.map((v) => v.toJson()).toList();
    }
    if (itemPrice != null) {
      data['Item_Price'] = itemPrice!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Inventory {
  int? iD;
  String? storeName;
  double? quantityBalance;

  Inventory({this.iD, this.storeName, this.quantityBalance});

  Inventory.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    storeName = json['Store_Name'];
    quantityBalance = json['Quantity_Balance'] != null
        ? double.parse(json['Quantity_Balance'].toString())
        : 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ID'] = iD;
    data['Store_Name'] = storeName;
    data['Quantity_Balance'] = quantityBalance;
    return data;
  }
}

class ItemPrice {
  int? iD;
  int? itemID;
  int? unitID;
  String? unitName;
  bool? isDefult;
  // int? purchasePrice;
  double? salesPrice;

  ItemPrice(
      {this.iD,
      this.itemID,
      this.unitID,
      this.unitName,
      this.isDefult,
      // this.purchasePrice,
      this.salesPrice});

  ItemPrice.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    itemID = json['Item_ID'];
    unitID = json['Unit_ID'];
    unitName = json['Unit_Name'];
    // isDefult = json['Is_Defult'];
    // purchasePrice = json['Purchase_Price'];
    salesPrice = json['Sales_Price'] != null
        ? double.parse(json['Sales_Price'].toString())
        : 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ID'] = iD;
    data['Item_ID'] = itemID;
    data['Unit_ID'] = unitID;
    data['Unit_Name'] = unitName;
    data['Is_Defult'] = isDefult;
    // data['Purchase_Price'] = this.purchasePrice;
    data['Sales_Price'] = salesPrice;
    return data;
  }
}

class Barcodes {
  int? itemID;
  String? barCode;
  String? barCodeName;
  bool? isFinalBarcode;
  String? selectedAttributeAsString;

  Barcodes(
      {this.itemID,
      this.barCode,
      this.barCodeName,
      this.isFinalBarcode,
      this.selectedAttributeAsString});

  Barcodes.fromJson(Map<String, dynamic> json) {
    itemID = json['ItemID'];
    barCode = json['BarCode'];
    barCodeName = json['BarCodeName'];
    selectedAttributeAsString = json['SelectedAttributeAsString'];
    if (json['IsFinalBarcode'] is int) {
      isFinalBarcode = json['IsFinalBarcode'] == 1;
    } else {
      isFinalBarcode = json['IsFinalBarcode']; // API sends true/false directly
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ItemID'] = itemID;
    data['BarCode'] = barCode;
    data['BarCodeName'] = barCodeName;
    data['SelectedAttributeAsString'] = selectedAttributeAsString;
    data['IsFinalBarcode'] = isFinalBarcode;
    return data;
  }
}

class ItemAttribute {
  int? id;
  int? attributeTypeId;
  String? attributeName;
  int? itemId;
  int? order;
  List<ItemAttributeOption>? itemsAttributeOptions;

  ItemAttribute(
      {this.id,
      this.attributeTypeId,
      this.attributeName,
      this.itemId,
      this.order,
      this.itemsAttributeOptions});

  ItemAttribute.fromJson(Map<String, dynamic> json) {
    id = json['ID'];
    attributeTypeId = json['Attribute_Type_Id'];
    attributeName = json['Attribute_Name'];
    itemId = json['Item_ID'];
    order = json['Order'];
    if (json['ItemsAttributeOptions'] != null) {
      itemsAttributeOptions = <ItemAttributeOption>[];
      json['ItemsAttributeOptions'].forEach((v) {
        itemsAttributeOptions!.add(ItemAttributeOption.fromJson(v));
      });
    }
    if (json['ItemsAttributeOptionsModel'] != null) {
      itemsAttributeOptions = <ItemAttributeOption>[];
      json['ItemsAttributeOptionsModel'].forEach((v) {
        itemsAttributeOptions!.add(ItemAttributeOption.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['ID'] = id;
    data['Attribute_Type_Id'] = attributeTypeId;
    data['Attribute_Name'] = attributeName;
    data['Item_ID'] = itemId;
    data['Order'] = order;
    if (itemsAttributeOptions != null) {
      data['ItemsAttributeOptions'] =
          itemsAttributeOptions!.map((v) => v.toJson()).toList();
      data['ItemsAttributeOptionsModel'] =
          itemsAttributeOptions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ItemAttributeOption {
  int? id;
  int? attributeId;
  int? optionId;
  String? optionName;

  ItemAttributeOption(
      {this.id, this.attributeId, this.optionId, this.optionName});

  ItemAttributeOption.fromJson(Map<String, dynamic> json) {
    id = json['ID'];
    attributeId = json['Attribute_ID'];
    optionId = json['Option_ID'];
    optionName = json['Option_Name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['ID'] = id;
    data['Attribute_ID'] = attributeId;
    data['Option_ID'] = optionId;
    data['Option_Name'] = optionName;
    return data;
  }
}
