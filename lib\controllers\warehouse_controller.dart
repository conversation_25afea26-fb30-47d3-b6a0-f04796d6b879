import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:sqflite/sqflite.dart';

class WarehouseController with ChangeNotifier {
  List<ComboBoxDataModel> warehouses = [];
  List<ComboBoxDataModel> warehousesBySection = [];
  List<ComboBoxDataModel> sections = [];
  int fetchedWarehouseCount = 0;
  bool runningSyncization = false;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  Future<void> fetchWarehouses() async {
    try {
      var fromlocalDatabase = await getWarehouses();
      if (fromlocalDatabase.isNotEmpty) {
        warehouses.clear();
        for (var element in fromlocalDatabase) {
          warehouses.add(ComboBoxDataModel.fromJson(element));
        }
        notifyListeners();
        return;
      }

      warehouses.clear();
      var url = '/Warehouse/GetWarehouse';
      var result = await Api.getOne(action: url);
      if (result != null && result.isSuccess && result.data != null) {
        List<Map<String, dynamic>> warehousesToInsert = [];
        for (var element in result.data) {
          warehousesToInsert.add({
            'id': element["ID"],
            'name': element["Name"],
            'BranchId': AppController.currentBranchId
          });
          warehouses
              .add(ComboBoxDataModel(id: element["ID"], name: element["Name"]));
        }
        // Use bulk insert for better performance
        await _dbHelper.bulkInsert('Warehouse', warehousesToInsert);
        notifyListeners();
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> fetchWarehousesFromServer() async {
    try {
      bool isStillThereWarehouses = true;
      fetchedWarehouseCount = 0;
      warehouses.clear();
      while (isStillThereWarehouses) {
        runningSyncization = true;
        var url =
            '/Warehouse/GetWarehouse?skip=$fetchedWarehouseCount&take=200';
        var result = await Api.getOne(action: url);
        if (result != null && result.isSuccess && result.data != null) {
          List<Map<String, dynamic>> warehousesToInsert = [];
          for (var element in result.data) {
            warehousesToInsert.add({
              'id': element["ID"],
              'name': element["Name"],
              'BranchId': AppController.currentBranchId
            });
            warehouses.add(
                ComboBoxDataModel(id: element["ID"], name: element["Name"]));
            fetchedWarehouseCount++;
          }
          // Use bulk insert for better performance
          await _dbHelper.bulkInsert('Warehouse', warehousesToInsert);
          notifyListeners();
        }

        if (result?.data?.length ?? 0 < 200) {
          isStillThereWarehouses = false;
          runningSyncization = false;
        }
      }
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  Future<int> setWarehouse(int id, String name) async {
    return await _dbHelper.insert('Warehouse',
        {'id': id, 'name': name, 'BranchId': AppController.currentBranchId});
  }

  Future<List<Map<String, dynamic>>> getWarehouses() async {
    return await _dbHelper.query('Warehouse');
  }

  Future<int> getWarehouseCount() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM Warehouse WHERE BranchId = ${AppController.currentBranchId}');
    int count = Sqflite.firstIntValue(result) ?? 0;
    fetchedWarehouseCount = count;
    return count;
  }

  Future<bool> clearAndRefetchData() async {
    try {
      final db = await _dbHelper.database;
      await db.transaction((txn) async {
        await txn.delete('Warehouse',
            where: 'BranchId = ?', whereArgs: [AppController.currentBranchId]);
      });

      await fetchWarehousesFromServer();
      return true;
    } catch (e) {
      return false;
    }
  }

  Future<List<ComboBoxDataModel>> getSectionsFormServer() async {
    try {
      sections.clear();

      var url = '/Warehouse/GetSections';
      var result = await Api.getOne(action: url);
      if (result != null && result.isSuccess && result.data != null) {
        for (var element in result.data) {
          sections
              .add(ComboBoxDataModel(id: element["ID"], name: element["Name"]));
        }

        notifyListeners();
        return sections;
      }
      notifyListeners();
      return sections;
    } catch (e) {
      print(e);
      return [];
    }
  }

  Future<void> getWarehousesBySectionIdFormServer(int id) async {
    try {
      warehousesBySection.clear();
      var url = '/Warehouse/StoreBySectionID?sectionID=$id';
      var result = await Api.getOne(action: url);
      if (result != null && result.isSuccess && result.data != null) {
        for (var element in result.data) {
          warehousesBySection.add(ComboBoxDataModel(
              id: int.tryParse(element["ID"]) ?? 0, name: element["Name"]));
        }

        notifyListeners();
      }
      notifyListeners();
    } catch (e) {
      print(e);
    }
  }
}
