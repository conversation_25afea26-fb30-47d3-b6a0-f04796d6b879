import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/inventory_operation_outgoing_conrtoller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20outgoing/widgets/inventory_operation_outgoing_base_info_widget.dart';
import 'package:inventory_application/screens/InventoryOperation/shared/inventory_operation_no_products_widget.dart';
import 'package:inventory_application/screens/InventoryOperation/shared/inventory_opertaion_bottom_bar_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:inventory_application/screens/shared/add_product_with_selected_attributes_widget.dart';
import 'package:inventory_application/screens/shared/product_attribute_selection_dialog_widget.dart';
import 'package:provider/provider.dart';

class InventoryOperationOutgoingScreen extends StatefulWidget {
  const InventoryOperationOutgoingScreen({super.key});

  @override
  State<InventoryOperationOutgoingScreen> createState() =>
      _InventoryOperationOutgoingScreenState();
}

class _InventoryOperationOutgoingScreenState
    extends State<InventoryOperationOutgoingScreen> {
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<InventoryOpertationOutgoingController>(context, listen: false)
          .getOutgoingNumber();
    });
  }

  @override
  Widget build(BuildContext context) {
    var products = Provider.of<InventoryOpertationOutgoingController>(context)
        .selectedOutgoingProduct;

    var provider = Provider.of<InventoryOpertationOutgoingController>(context,
        listen: false);
    return ApplicationLayout(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: [
              CommonHeader(
                icon: Icons.move_down,
                title: T("Inventory outgoing"),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 10,
                  ),
                  child: Column(
                    children: [
                      // const SizedBox(height: 50),
                      InventoryOperationOutgoingBaseInfoWidget(
                        onSelectWarehouse: () {
                          setState(() {});
                        },
                      ),
                      InvoiceSelectProductWidget(
                        selectedProducts: products,
                        canAdd: provider.inventoryOutgoing.fromStoreID != null,
                        oncannotAddProduct: () {
                          errorSnackBar(
                              message: "يرجى اختيار المستودع",
                              // ignore: use_build_context_synchronously
                              context: context);
                        },
                        onChange: () {
                          // Force UI refresh when products change
                          setState(() {});
                        },
                        onAddProduct: (ProductDTO product) {
                          provider.addProductToSelectedList(product);
                          // Force UI refresh
                          setState(() {});
                        },
                        onRemoveProduct: (int id) {
                          provider.deleteProductFromSelectedList(id);
                          // Force UI refresh
                          setState(() {});
                        },
                        onSearchByBarcode: (String barcode) async {
                          // Get the controller
                          final inventoryItemsTransController = Provider.of<
                              InventoryOpertationOutgoingController>(
                            context,
                            listen: false,
                          );

                          // Process the barcode
                          var result = await inventoryItemsTransController
                              .getItemByBarcode(
                            barcode: barcode,
                          );

                          // If result is a ProductDTO object rather than a boolean,
                          // it means we need to show the attribute selection dialog
                          if (result is ProductDTO) {
                            final selectedOptions =
                                await showDialog<Map<int, ItemAttributeOption>>(
                              context: context,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return AttributeSelectionDialog(
                                    product: result);
                              },
                            );

                            if (selectedOptions != null) {
                              addProductWithSelectedAttributes(
                                product: result,
                                selectedOptions: selectedOptions,
                                currentProducts:
                                    provider.selectedOutgoingProduct,
                                onUpdateExisting: (updatedProduct, index) {
                                  provider.selectedOutgoingProduct[index] =
                                      updatedProduct;
                                },
                                onAddNew: (newProduct) {
                                  provider.addProductToSelectedList(newProduct);
                                },
                                onAfterChange: () {
                                  provider.calculateInvoiceTotal();
                                  // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
                                  provider.notifyListeners();
                                },
                              );
                            }

                            return true;
                          }

                          // Force UI refresh immediately after barcode scan
                          setState(() {
                            // This will trigger a rebuild with the updated product list
                          });

                          // Add a small delay and refresh again to ensure UI updates
                          await Future.delayed(
                              const Duration(milliseconds: 100));
                          setState(() {});

                          // Show error if product not found
                          if (result == false) {
                            errorSnackBar(
                              message: T(
                                  "There is no product associated with the barcode"),
                              // ignore: use_build_context_synchronously
                              context: context,
                            );
                          }

                          return result;
                        },
                      ),

                      if (products.isNotEmpty) ...[
                        Container(
                          width: double.infinity,
                          margin: EdgeInsets.zero,
                          child: InvoiceProductListHeaderWidget(
                            backgroundColor: context.primaryColor,
                            textColor: Colors.white,
                          ),
                        ),
                        Consumer<InventoryOpertationOutgoingController>(
                          builder: (context,
                              inventoryOpertationOutgoingController, child) {
                            return ListView.separated(
                              itemCount: products.length,
                              shrinkWrap: true,
                              padding: EdgeInsets.zero,
                              separatorBuilder: (context, index) => Divider(
                                color: Colors.grey.withOpacity(0.2),
                                height: 1,
                                indent: 0,
                                endIndent: 0,
                              ),
                              physics: const NeverScrollableScrollPhysics(),
                              itemBuilder: (context, index) {
                                return InvoiceListItemsWidget(
                                  id: products[index].id ?? 0,
                                  barcode: products[index].barcode,
                                  virtualProductId:
                                      products[index].virtualProductId,
                                  selectedInvoiceProduct:
                                      inventoryOpertationOutgoingController
                                          .selectedOutgoingProduct,
                                  onChangeWarehouse: (int productId,
                                      int warehouseId, String warehouseName,
                                      [String? virtualProductId]) {
                                    errorSnackBar(
                                        message: "لا يمكن تغير المستودع هنا");
                                  },
                                  onDeleteProduct: (int productId,
                                      [String? virtualProductId]) {
                                    if (virtualProductId != null) {
                                      // If we have a virtual ID, use it when deleting
                                      provider.deleteProductFromSelectedList(
                                          productId,
                                          virtualProductId: virtualProductId);
                                    } else {
                                      // Otherwise use the regular product ID
                                      provider.deleteProductFromSelectedList(
                                          productId);
                                    }
                                    provider.calculateInvoiceTotal();
                                    setState(() {});
                                  },
                                  onUpdatePrice: (int productId, double price,
                                      [String? virtualProductId]) {
                                    if (virtualProductId != null) {
                                      provider.updateProductPrice(
                                          productId, price, virtualProductId);
                                    } else {
                                      provider.updateProductPrice(
                                          productId, price);
                                    }
                                    provider.calculateInvoiceTotal();
                                    setState(() {});
                                  },
                                  onUpdateQuantity:
                                      (int productId, double quantity,
                                          [String? virtualProductId]) {
                                    if (virtualProductId != null) {
                                      provider.updateProductQuantity(productId,
                                          quantity, virtualProductId);
                                    } else {
                                      provider.updateProductQuantity(
                                          productId, quantity);
                                    }
                                    provider.calculateInvoiceTotal();
                                    setState(() {});
                                  },
                                  onUpdateUnit:
                                      (int productId, ItemPriceDTO unit,
                                          [String? virtualProductId]) {
                                    provider.updateProductUnit(
                                        productId, unit, virtualProductId);
                                    provider.calculateInvoiceTotal();
                                    setState(() {});
                                  },
                                );
                              },
                            );
                          },
                        ),
                      ] else ...[
                        // Empty State
                        const InventoryOperationNoProductsWidget(),
                      ],

                      // Summary Section
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: InventoryOpertaionBottomBarWidget(
            model: provider.inventoryOutgoing,
            products: products,
            onclear: () {
              provider.selectedOutgoingProduct.clear();
              provider.calculateInvoiceTotal();
            },
            onSave: () async {
              if (provider.inventoryOutgoing.fromStoreID == null) {
                errorSnackBar(message: "يرجى اختيار المستودع المصدر");
                return;
              }

              pleaseWaitDialog(context: context, isShown: true);

              var result = await provider.saveInventoryOutgoing();
              if (result.isSuccess) {
                // ignore: use_build_context_synchronously
                pleaseWaitDialog(context: context, isShown: false);
                successSnackBar(message: T("The operation has been saved"));

                // Reset the invoice and set default values

                provider.selectedOutgoingProduct.clear();
                provider.inventoryOutgoing = InventoryOperationModel();
                // _setDefaultValues();
                setState(() {});
                return;
              }
              // ignore: use_build_context_synchronously
              pleaseWaitDialog(context: context, isShown: false);
              errorSnackBar(
                  message: result.message != null
                      ? result.message!.first.toString()
                      : T("Not saved"));
            }),
      ),
    );
  }

  String _getFormattedSelectedOptions(
      Map<int, ItemAttributeOption> selectedOptions) {
    if (selectedOptions.isEmpty) return '';

    // Sort by attribute ID (which should correspond to the attribute order)
    final sortedKeys = selectedOptions.keys.toList()..sort();

    // Join option names with '/'
    return sortedKeys
        .map((key) => selectedOptions[key]?.optionName ?? '')
        .join('/');
  }
}
