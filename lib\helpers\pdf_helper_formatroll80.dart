import 'dart:io';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
// ignore: depend_on_referenced_packages
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart' show rootBundle;

Future<File> createInvoicePdfroll80(
    InvoiceDto invoice, PdfPageFormat format) async {
  final pdf = pw.Document();
  final fontData = await rootBundle.load("assets/fonts/NeoSans-Regular.ttf");
  final arabicFont = pw.Font.ttf(fontData);

  pdf.addPage(
    pw.Page(
      pageFormat: format,
      build: (context) {
        var itemsList = invoice.salesItems ?? [];

        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Center(
              child: pw.Text(
                'فاتورة مبيعات',
                style: pw.TextStyle(font: arabicFont, fontSize: 15),
                textDirection: pw.TextDirection.rtl,
              ),
            ),
            pw.SizedBox(height: 30),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.start,
              children: [
                pw.Text(
                    "".myDateFormatter(invoice.invoiceDate, isShowTime: true),
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 7,
                      color: PdfColors.red,
                    ),
                    textDirection: pw.TextDirection.rtl),
              ],
            ),

            pw.SizedBox(height: 8),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.end,
              children: [
                pw.Text('${invoice.appReferanceCode} : NO.L ',
                    style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 9,
                    ),
                    textDirection: pw.TextDirection.rtl),
              ],
            ),

            pw.SizedBox(height: 8),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.end,
              children: [
                pw.Text("رقم الزبون: ${invoice.customerId}",
                    style: pw.TextStyle(font: arabicFont, fontSize: 9),
                    textDirection: pw.TextDirection.rtl),
              ],
            ),
            pw.SizedBox(height: 8),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.end,
              children: [
                pw.Text('اسم الزبون : ${invoice.custoemrName}',
                    style: pw.TextStyle(font: arabicFont, fontSize: 9),
                    textDirection: pw.TextDirection.rtl),
              ],
            ),

            pw.SizedBox(height: 15),
            pw.Table(
              border: pw.TableBorder.all(),
              columnWidths: {
                0: const pw.FixedColumnWidth(50),
                1: const pw.FixedColumnWidth(50),
                2: const pw.FixedColumnWidth(50),
                3: const pw.FixedColumnWidth(80),
                4: const pw.FixedColumnWidth(60),
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  children: [
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text('المجموع',
                          style: pw.TextStyle(font: arabicFont, fontSize: 6),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text('السعر',
                          style: pw.TextStyle(font: arabicFont, fontSize: 6),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text('الكمية',
                          style: pw.TextStyle(font: arabicFont, fontSize: 6),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    // pw.Container(
                    //   padding: const pw.EdgeInsets.symmetric(
                    //       horizontal: 3, vertical: 7),
                    //   child: pw.Text('الوحدة',
                    //       style: pw.TextStyle(font: arabicFont, fontSize: 9),
                    //       textAlign: pw.TextAlign.center,
                    //       textDirection: pw.TextDirection.rtl),
                    // ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text('الاسم',
                          style: pw.TextStyle(font: arabicFont, fontSize: 6),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text('الكود',
                          style: pw.TextStyle(font: arabicFont, fontSize: 6),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                  ],
                ),
              ],
            ),

            pw.Table(
              border: pw.TableBorder.all(),
              columnWidths: {
                0: const pw.FixedColumnWidth(50),
                1: const pw.FixedColumnWidth(50),
                2: const pw.FixedColumnWidth(50),
                3: const pw.FixedColumnWidth(80),
                4: const pw.FixedColumnWidth(60),
              },
              children: itemsList.map((e) {
                return pw.TableRow(
                  children: [
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text(
                        ((e.quantity ?? 0) * (e.price ?? 0)).toString(),
                        style: pw.TextStyle(font: arabicFont, fontSize: 6),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text(
                        (e.price ?? 0).toString(),
                        style: pw.TextStyle(font: arabicFont, fontSize: 6),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text(
                        e.quantity.toString(),
                        style: pw.TextStyle(font: arabicFont, fontSize: 6),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    // pw.Container(
                    //   padding: const pw.EdgeInsets.symmetric(
                    //       horizontal: 3, vertical: 7),
                    //   child: pw.Text(
                    //     e.uniteName.toString(),
                    //     style: pw.TextStyle(font: arabicFont, fontSize: 9),
                    //     textAlign: pw.TextAlign.center,
                    //     textDirection: pw.TextDirection.rtl,
                    //   ),
                    // ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text(
                        e.title ?? "",
                        style: pw.TextStyle(font: arabicFont, fontSize: 6),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.symmetric(
                          horizontal: 3, vertical: 7),
                      child: pw.Text(
                        e.code ?? "",
                        style: pw.TextStyle(font: arabicFont, fontSize: 6),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),

            pw.SizedBox(height: 20),
            pw.Container(
              width: 100,
              child: pw.Table(
                children: [
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      border:
                          pw.Border.all(width: 0, style: pw.BorderStyle.none),
                    ),
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 5),
                        child: pw.Text(invoice.total.toString(),
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                            )),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 5),
                        child: pw.Text('المجموع',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                            ),
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl),
                      ),
                    ],
                  ),
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      border:
                          pw.Border.all(width: 0, style: pw.BorderStyle.none),
                    ),
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 5),
                        child: pw.Text(invoice.paymentValue.toString(),
                            style: pw.TextStyle(
                              fontSize: 8,
                              font: arabicFont,
                            )),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 5),
                        child: pw.Text('دفعة نقدية',
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 8,
                            ),
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl),
                      ),
                    ],
                  ),
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      border:
                          pw.Border.all(width: 0, style: pw.BorderStyle.none),
                    ),
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 5),
                        child: pw.Text(invoice.discountValue.toString(),
                            style: pw.TextStyle(font: arabicFont, fontSize: 8)),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 5),
                        child: pw.Text('الخصم',
                            style: pw.TextStyle(
                                font: arabicFont,
                                fontWeight: pw.FontWeight.bold,
                                fontSize: 8),
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl),
                      ),
                    ],
                  ),
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      border:
                          pw.Border.all(width: 0, style: pw.BorderStyle.none),
                    ),
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 5),
                        child: pw.Text(invoice.totalAfterDiscount.toString(),
                            style: pw.TextStyle(font: arabicFont, fontSize: 8)),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.symmetric(vertical: 5),
                        child: pw.Text('صافي الفاتورة',
                            style: pw.TextStyle(
                                font: arabicFont,
                                fontWeight: pw.FontWeight.bold,
                                fontSize: 8),
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Add more content as needed
          ],
        );
      },
    ),
  );
  final output = await getTemporaryDirectory();
  final file = File("${output.path}/SalesInvoiceReport.pdf");
  await file.writeAsBytes(await pdf.save());

  return file;
}
