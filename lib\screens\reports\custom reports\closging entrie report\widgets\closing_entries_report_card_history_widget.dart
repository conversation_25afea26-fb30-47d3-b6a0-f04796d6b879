// Card History Dialog Widget
import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/accounting_report_helper_controller.dart';
import 'package:inventory_application/models/dto/reports/closing_entries_report_dto.dart';
import 'package:provider/provider.dart';

class CardHistoryDialog extends StatefulWidget {
  final String cardNumber;

  const CardHistoryDialog({
    Key? key,
    required this.cardNumber,
  }) : super(key: key);

  @override
  State<CardHistoryDialog> createState() => CardHistoryDialogState();
}

class CardHistoryDialogState extends State<CardHistoryDialog> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadCardHistory();
    });
  }

  Future<void> _loadCardHistory() async {
    final controller = context.read<AccountingReportHelperController>();
    await controller.getCardHistory(widget.cardNumber);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // Fixed Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.credit_card,
                    color: Colors.purple,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'تاريخ استخدام البطاقة: ${widget.cardNumber}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.purple,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),

            // Scrollable Content
            Expanded(
              child: Scrollbar(
                thumbVisibility: true,
                trackVisibility: true,
                thickness: 8.0,
                radius: const Radius.circular(4.0),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Consumer<AccountingReportHelperController>(
                    builder: (context, controller, child) {
                      if (controller.isLoadingCardHistory) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(50),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      final transactions = controller.cardHistory;
                      if (transactions.isEmpty) {
                        return Center(
                          child: Padding(
                            padding: const EdgeInsets.all(50),
                            child: Column(
                              children: [
                                Icon(
                                  Icons.inbox,
                                  size: 64,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  'لا توجد معاملات لهذه البطاقة',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Summary Section
                          _buildCardSummarySection(transactions, controller),
                          const SizedBox(height: 24),

                          // Transactions Section Header
                          const Row(
                            children: [
                              Icon(
                                Icons.list_alt,
                                color: Colors.purple,
                                size: 20,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'قائمة المعاملات',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.purple,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),

                          // Transactions List
                          ...transactions.map((transaction) =>
                              _buildCardHistoryTransactionTile(
                                  transaction, controller)),
                        ],
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardSummarySection(List<RechargeTransactionDTO> transactions,
      AccountingReportHelperController controller) {
    // Calculate totals
    double totalAmount = 0.0;
    double totalCash = 0.0;
    double totalCard = 0.0;
    double totalRecharge = 0.0;
    double totalGifts = 0.0;
    double totalFees = 0.0;
    int transactionCount = transactions.length;
    int cashTransactions = 0;
    int cardTransactions = 0;

    for (var transaction in transactions) {
      totalAmount += transaction.totalAmount ?? 0.0;
      if (transaction.paymentTypeId == 1) {
        totalCash += transaction.totalAmount ?? 0.0;
        cashTransactions++;
      } else {
        totalCard += transaction.totalAmount ?? 0.0;
        cardTransactions++;
      }
      totalRecharge += transaction.rechargeAmount ?? 0.0;
      totalGifts += transaction.giftAmount ?? 0.0;
      totalFees += transaction.totalFees ?? 0.0;
    }

    return Column(
      children: [
        // Main Summary Section
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.purple.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(
                    Icons.analytics,
                    color: Colors.purple,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Text(
                    'مجاميع الكارت',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // First Row
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryItem(
                      'عدد المعاملات',
                      transactionCount.toString(),
                      Icons.receipt_long,
                      Colors.blue,
                    ),
                  ),
                  Expanded(
                    child: _buildSummaryItem(
                      'إجمالي المبلغ',
                      controller.formatNumber(totalAmount),
                      Icons.monetization_on,
                      Colors.green,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Second Row
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryItem(
                      'إجمالي الشحن',
                      controller.formatNumber(totalRecharge),
                      Icons.account_balance_wallet,
                      Colors.orange,
                    ),
                  ),
                  Expanded(
                    child: _buildSummaryItem(
                      'إجمالي الرسوم',
                      controller.formatNumber(totalFees),
                      Icons.payment,
                      Colors.red,
                    ),
                  ),
                ],
              ),

              if (totalGifts > 0) ...[
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildSummaryItem(
                        'إجمالي الهدايا',
                        controller.formatNumber(totalGifts),
                        Icons.card_giftcard,
                        Colors.purple,
                      ),
                    ),
                    const Expanded(child: SizedBox()), // Empty space
                  ],
                ),
              ],
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Payment Methods Summary Section
        _buildPaymentMethodsSummary(
          controller,
          totalCash,
          totalCard,
          cashTransactions,
          cardTransactions,
        ),
      ],
    );
  }

  Widget _buildSummaryItem(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodsSummary(
    AccountingReportHelperController controller,
    double totalCash,
    double totalCard,
    int cashTransactions,
    int cardTransactions,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(
                Icons.payment,
                color: Colors.indigo,
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'تفاصيل طرق الدفع',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.indigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Payment methods cards
          Row(
            children: [
              // Cash Payment Card
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.green.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.payments,
                          color: Colors.green,
                          size: 24,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'الدفع النقدي',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        controller.formatNumber(totalCash),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.green,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$cashTransactions معاملة',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.green.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(width: 12),

              // Card Payment Card
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.credit_card,
                          color: Colors.blue,
                          size: 24,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'الدفع بطاقة',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        controller.formatNumber(totalCard),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '$cardTransactions معاملة',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.blue.shade700,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCardHistoryTransactionTile(RechargeTransactionDTO transaction,
      AccountingReportHelperController controller) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.purple.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.purple.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'رقم البطاقة: ${transaction.cardNumber ?? 'غير محدد'}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.purple,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'اسم العضو: ${transaction.memberName ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      'الشركة: ${transaction.companyName ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      'اسم الادمن: ${transaction.adminName ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'نوع الدفع: ${transaction.paymentTypeId == 1 ? 'نقدا' : 'بطاقة'}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    controller.formatNumber(transaction.totalAmount),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                      fontSize: 16,
                    ),
                  ),
                  if (transaction.totalFees != null &&
                      transaction.totalFees! > 0)
                    Text(
                      'رسوم: ${controller.formatNumber(transaction.totalFees)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[700],
                      ),
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(Icons.calendar_today,
                        size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      transaction.date?.split('T')[0] ?? 'غير محدد',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              if (transaction.rechargeAmount != null)
                Text(
                  'مبلغ الشحن: ${controller.formatNumber(transaction.rechargeAmount)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.green,
                  ),
                ),
              if (transaction.giftAmount != null &&
                  transaction.giftAmount! > 0) ...[
                const SizedBox(width: 8),
                Text(
                  'هدية: ${controller.formatNumber(transaction.giftAmount)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.purple,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // Clear card history when dialog is closed
    context.read<AccountingReportHelperController>().clearCardHistory();
    super.dispose();
  }
}
