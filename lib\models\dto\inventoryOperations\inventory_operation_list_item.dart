class InventoryOperationListDTO {
  String? transactionsType;
  int? customerID;
  String? fromDate;
  String? toDate;
  DataTableParameters? dataTableParameters;

  InventoryOperationListDTO(
      {this.transactionsType,
      this.customerID,
      this.fromDate,
      this.toDate,
      this.dataTableParameters});

  InventoryOperationListDTO.fromJson(Map<String, dynamic> json) {
    transactionsType = json['transactions_type'];
    customerID = json['Customer_ID'];
    fromDate = json['FromDate'];
    toDate = json['ToDate'];
    dataTableParameters = json['dataTableParameters'] != null
        ? new DataTableParameters.fromJson(json['dataTableParameters'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['transactions_type'] = this.transactionsType;
    data['Customer_ID'] = this.customerID;
    data['FromDate'] = this.fromDate;
    data['ToDate'] = this.toDate;
    if (this.dataTableParameters != null) {
      data['dataTableParameters'] = this.dataTableParameters!.toJson();
    }
    return data;
  }
}

class DataTableParameters {
  int? skip;
  int? take;
  String? columnName;
  String? dir;

  DataTableParameters({this.skip, this.take, this.columnName, this.dir});

  DataTableParameters.fromJson(Map<String, dynamic> json) {
    skip = json['Skip'];
    take = json['Take'];
    columnName = json['ColumnName'];
    dir = json['Dir'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Skip'] = this.skip;
    data['Take'] = this.take;
    data['ColumnName'] = this.columnName;
    data['Dir'] = this.dir;
    return data;
  }
}