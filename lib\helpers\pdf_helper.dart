import 'dart:io';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:flutter/services.dart' show rootBundle;

Future<File> createInvoicePdf(InvoiceDto invoice) async {
  final pdf = pw.Document();
  final fontData = await rootBundle.load("assets/fonts/NeoSans-Regular.ttf");
  final fontDataBold = await rootBundle.load("assets/fonts/NeoSans-Bold.ttf");
  final arabicFont = pw.Font.ttf(fontData);
  final arabicFontBold = pw.Font.ttf(fontDataBold);

  pdf.addPage(
    pw.Page(
      pageFormat: PdfPageFormat.a4,
      build: (context) {
        var itemsList = invoice.salesItems ?? [];

        return pw.Column(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Center(
              child: pw.Text(
                'فاتورة مبيعات', // Arabic text
                style: pw.TextStyle(font: arabicFont, fontSize: 24),
                textDirection: pw.TextDirection.rtl,
              ),
            ),

            pw.SizedBox(height: 10),
            pw.Center(
              child: pw.Text(
                  'رقم الفاتورة المحلية: ${invoice.appReferanceCode}',
                  style: pw.TextStyle(font: arabicFont, color: PdfColors.red),
                  textDirection: pw.TextDirection.rtl),
            ),
            pw.SizedBox(height: 10),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text(
                    "".myDateFormatter(
                      invoice.invoiceDate,
                    ),
                    style: pw.TextStyle(font: arabicFont),
                    textDirection: pw.TextDirection.rtl),
                pw.Text('اسم الزبون ${invoice.custoemrName}',
                    style: pw.TextStyle(font: arabicFont),
                    textDirection: pw.TextDirection.rtl),
              ],
            ),
            pw.SizedBox(height: 10),

            pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
              pw.Text("رقم الزبون: ${invoice.customerId}",
                  style: pw.TextStyle(font: arabicFont),
                  textDirection: pw.TextDirection.rtl),
            ]),
            pw.SizedBox(height: 20),
            pw.Table(
              border: pw.TableBorder.all(),
              columnWidths: {
                0: const pw.FixedColumnWidth(65),
                1: const pw.FixedColumnWidth(50),
                2: const pw.FixedColumnWidth(50),
                3: const pw.FixedColumnWidth(50),
                4: const pw.FlexColumnWidth(),
                5: const pw.FixedColumnWidth(70),
              },
              children: [
                pw.TableRow(
                  decoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  children: [
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text('المجموع',
                          style: pw.TextStyle(font: arabicFont),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text('السعر',
                          style: pw.TextStyle(font: arabicFont),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text('الكمية',
                          style: pw.TextStyle(font: arabicFont),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text('الوحدة',
                          style: pw.TextStyle(font: arabicFont),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text('الاسم',
                          style: pw.TextStyle(font: arabicFont),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text('الكود',
                          style: pw.TextStyle(
                            font: arabicFont,
                          ),
                          textAlign: pw.TextAlign.center,
                          textDirection: pw.TextDirection.rtl),
                    ),
                  ],
                ),
              ],
            ),

            pw.Table(
              border: pw.TableBorder.all(),
              columnWidths: {
                0: const pw.FixedColumnWidth(65),
                1: const pw.FixedColumnWidth(50),
                2: const pw.FixedColumnWidth(50),
                3: const pw.FixedColumnWidth(50),
                4: const pw.FlexColumnWidth(),
                5: const pw.FixedColumnWidth(70),
              },
              children: itemsList.map((e) {
                return pw.TableRow(
                  children: [
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text(
                        ((e.quantity ?? 0) * (e.price ?? 0)).toString(),
                        style: pw.TextStyle(font: arabicFont),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text(
                        (e.price ?? 0).toString(),
                        style: pw.TextStyle(font: arabicFont),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text(
                        e.quantity.toString(),
                        style: pw.TextStyle(font: arabicFont),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text(
                        e.uniteName.toString(),
                        style: pw.TextStyle(font: arabicFont),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text(
                        e.title ?? "",
                        style: pw.TextStyle(font: arabicFont),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                    pw.Container(
                      padding: const pw.EdgeInsets.all(10),
                      child: pw.Text(
                        e.code ?? "",
                        style: pw.TextStyle(font: arabicFont),
                        textAlign: pw.TextAlign.center,
                        textDirection: pw.TextDirection.rtl,
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),

            pw.SizedBox(height: 20),
            pw.Container(
              width: 180,
              child: pw.Table(
                border: pw.TableBorder.all(),
                children: [
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      border:
                          pw.Border.all(width: 0, style: pw.BorderStyle.none),
                    ),
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.all(10),
                        child: pw.Text(invoice.total.toString(),
                            style: pw.TextStyle(font: arabicFontBold)),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.all(10),
                        child: pw.Text('المجموع',
                            style: pw.TextStyle(
                                font: arabicFontBold,
                                fontWeight: pw.FontWeight.bold),
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl),
                      ),
                    ],
                  ),
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      border:
                          pw.Border.all(width: 0, style: pw.BorderStyle.none),
                    ),
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.all(10),
                        child: pw.Text(invoice.paymentValue.toString(),
                            style: pw.TextStyle(font: arabicFontBold)),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.all(10),
                        child: pw.Text('دفعة نقدية',
                            style: pw.TextStyle(
                                font: arabicFontBold,
                                fontWeight: pw.FontWeight.bold),
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl),
                      ),
                    ],
                  ),
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      border:
                          pw.Border.all(width: 0, style: pw.BorderStyle.none),
                    ),
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.all(10),
                        child: pw.Text(invoice.discountValue.toString(),
                            style: pw.TextStyle(font: arabicFontBold)),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.all(10),
                        child: pw.Text('الخصم',
                            style: pw.TextStyle(
                                font: arabicFontBold,
                                fontWeight: pw.FontWeight.bold),
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl),
                      ),
                    ],
                  ),
                  pw.TableRow(
                    decoration: pw.BoxDecoration(
                      border:
                          pw.Border.all(width: 0, style: pw.BorderStyle.none),
                    ),
                    children: [
                      pw.Container(
                        padding: const pw.EdgeInsets.all(10),
                        child: pw.Text(invoice.totalAfterDiscount.toString(),
                            style: pw.TextStyle(font: arabicFontBold)),
                      ),
                      pw.Container(
                        padding: const pw.EdgeInsets.all(10),
                        child: pw.Text('صافي الفاتورة',
                            style: pw.TextStyle(
                                font: arabicFontBold,
                                fontWeight: pw.FontWeight.bold),
                            textAlign: pw.TextAlign.center,
                            textDirection: pw.TextDirection.rtl),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            pw.SizedBox(height: 50),
            pw.Row(
              mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
              children: [
                pw.Text('Sales Manager  مدير المبيعات',
                    style: pw.TextStyle(font: arabicFont),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl),
                pw.Text('Accounting Manager  مدير الحسابات',
                    style: pw.TextStyle(font: arabicFont),
                    textAlign: pw.TextAlign.center,
                    textDirection: pw.TextDirection.rtl),
              ],
            ),
            // Add more content as needed
          ],
        );
      },
    ),
  );
  final output = await getTemporaryDirectory();
  final file = File("${output.path}/SalesInvoiceReport.pdf");
  await file.writeAsBytes(await pdf.save());

  return file;
}
