import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:vector_math/vector_math.dart' as vector;

import '../../../models/warehouse_planner/warehouse_layout.dart';
import '../../../models/warehouse_planner/editor_state.dart';

/// لوحة الرسم الرئيسية للمحرر ثنائي الأبعاد
class WarehouseEditorCanvas extends StatefulWidget {
  final WarehouseLayout layout;
  final EditorState editorState;
  final LayoutSettings settings;

  // Callbacks
  final Function(EditorState) onStateUpdate;
  final Function(vector.Vector2) onWallDrawStart;
  final Function(vector.Vector2) onWallDrawAdd;
  final VoidCallback onWallDrawFinish;
  final Function(String) onWallDelete;
  final Function(double) onEntranceAdd;
  final Function(vector.Vector2, String?) onShelfAdd;
  final Function(String, vector.Vector2) onShelfMove;
  final Function(String, double?) onShelfRotate;
  final Function(String?, ObjectType?) onObjectSelect;
  final Function(vector.Vector2, double?) onPreviewUpdate;
  final Function(String shelfId)? onShelfDoubleClick;

  const WarehouseEditorCanvas({
    Key? key,
    required this.layout,
    required this.editorState,
    required this.settings,
    required this.onStateUpdate,
    required this.onWallDrawStart,
    required this.onWallDrawAdd,
    required this.onWallDrawFinish,
    required this.onWallDelete,
    required this.onEntranceAdd,
    required this.onShelfAdd,
    required this.onShelfMove,
    required this.onShelfRotate,
    required this.onObjectSelect,
    required this.onPreviewUpdate,
    this.onShelfDoubleClick,
  }) : super(key: key);

  @override
  State<WarehouseEditorCanvas> createState() => _WarehouseEditorCanvasState();
}

class _WarehouseEditorCanvasState extends State<WarehouseEditorCanvas> {
  late EditorState _currentState;
  String? _draggedShelfId;
  vector.Vector2? _dragStartPosition;
  vector.Vector2? _lastPointerPosition;
  vector.Vector2? _dragOffset;
  bool _isDragging = false;
  Timer? _autoSaveTimer;

  @override
  void initState() {
    super.initState();
    _currentState = widget.editorState;
  }

  @override
  void didUpdateWidget(WarehouseEditorCanvas oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.editorState != oldWidget.editorState) {
      _currentState = widget.editorState;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveViewer(
      boundaryMargin: const EdgeInsets.all(100),
      minScale: 0.1,
      maxScale: 5.0,
      constrained: false,
      child: SizedBox(
        width: 2000, // مساحة عمل كبيرة
        height: 2000,
        child: Focus(
          autofocus: true,
          onKeyEvent: _handleKeyEvent,
          child: GestureDetector(
            onDoubleTap: _onDoubleTap,
            child: Listener(
              onPointerDown: _onPointerDown,
              onPointerMove: _onPointerMove,
              onPointerUp: _onPointerUp,
              child: MouseRegion(
                onHover: _onMouseHover,
                child: RepaintBoundary(
                  child: CustomPaint(
                    size: const Size(2000, 2000),
                    painter: WarehouseCanvasPainter(
                      layout: widget.layout,
                      editorState: _currentState,
                      settings: widget.settings,
                    ),
                    willChange: true,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _onPointerDown(PointerDownEvent event) {
    final worldPoint = _screenToWorld(event.localPosition);
    _lastPointerPosition = worldPoint;

    switch (_currentState.mode) {
      case EditMode.select:
        _handleSelectMode(worldPoint, event);
        break;
      case EditMode.drawWall:
        _handleWallDrawMode(worldPoint, event);
        break;
      case EditMode.deleteWall:
        _handleDeleteWallMode(worldPoint, event);
        break;
      case EditMode.addEntrance:
        _handleEntranceMode(worldPoint, event);
        break;
      case EditMode.placeShelf:
        _handleShelfPlaceMode(worldPoint, event);
        break;
      case EditMode.editShelf:
        _handleShelfEditMode(worldPoint, event);
        break;
    }
  }

  void _onPointerMove(PointerMoveEvent event) {
    final worldPoint = _screenToWorld(event.localPosition);

    // تحديث المعاينة حسب الوضع
    if (_currentState.mode == EditMode.placeShelf) {
      widget.onPreviewUpdate(worldPoint, null);
    }

    // سحب الخزانة المحسن
    if (_draggedShelfId != null &&
        _dragStartPosition != null &&
        _dragOffset != null) {
      final distance = (worldPoint - _dragStartPosition!).length;

      // بدء السحب عند الحركة الأولى (حد أدنى 3 سم)
      if (!_isDragging && distance > 3.0) {
        _isDragging = true;
        _showSuccessMessage('🔄 جاري التحريك...');
      }

      // إذا بدأ السحب، حرك الخزانة
      if (_isDragging) {
        // حساب الموضع الجديد مع تطبيق الإزاحة
        final newPosition = worldPoint - _dragOffset!;

        // تطبيق snap to grid
        final snappedPosition = _snapToGrid(newPosition);

        widget.onShelfMove(_draggedShelfId!, snappedPosition);
      }
    }
  }

  void _onPointerUp(PointerUpEvent event) {
    if (_draggedShelfId != null) {
      if (_isDragging) {
        _showSuccessMessage('✅ تم تحريك الخزانة بنجاح');
      }

      // إعادة تعيين متغيرات السحب
      _draggedShelfId = null;
      _dragStartPosition = null;
      _dragOffset = null;
      _isDragging = false;
    }
  }

  // دالة مساعدة لتطبيق snap to grid
  vector.Vector2 _snapToGrid(vector.Vector2 position) {
    if (!_currentState.snapToGrid) return position;

    final gridSize = _currentState.gridSize;
    return vector.Vector2(
      (position.x / gridSize).round() * gridSize,
      (position.y / gridSize).round() * gridSize,
    );
  }

  void _onMouseHover(PointerHoverEvent event) {
    final worldPoint = _screenToWorld(event.localPosition);

    // تحديث المعاينة للأوضاع التي تحتاج ذلك
    if (_currentState.mode == EditMode.placeShelf) {
      widget.onPreviewUpdate(worldPoint, null);
    }
  }

  void _handleSelectMode(vector.Vector2 worldPoint, PointerDownEvent event) {
    // البحث عن كائن تحت النقطة
    final hitObject = _hitTest(worldPoint);

    if (hitObject != null) {
      widget.onObjectSelect(hitObject['id'], hitObject['type']);

      // بدء السحب للخزائن
      if (hitObject['type'] == ObjectType.shelf) {
        _startDragging(hitObject['id'], worldPoint);
      }
    } else {
      widget.onObjectSelect(null, null);
    }
  }

  void _startDragging(String shelfId, vector.Vector2 worldPoint) {
    // العثور على الخزانة
    final shelf = widget.layout.shelves.firstWhere(
      (s) => s.id == shelfId,
      orElse: () => widget.layout.shelves.first,
    );

    _draggedShelfId = shelfId;
    _dragStartPosition = worldPoint;
    _isDragging = false;

    // حساب الإزاحة من النقطة المنقورة إلى مركز الخزانة
    _dragOffset = worldPoint - shelf.position;
  }

  void _handleWallDrawMode(vector.Vector2 worldPoint, PointerDownEvent event) {
    print('🖱️ رسم جدار - موضع النقر: ${worldPoint.x}, ${worldPoint.y}');

    if (event.buttons == kSecondaryButton) {
      // النقر بالزر الأيمن لإنهاء الرسم
      print('🖱️ زر أيمن - محاولة إنهاء الرسم');
      if (_currentState.wallDrawing.isDrawing) {
        widget.onWallDrawFinish();
      }
      return;
    }

    if (!_currentState.wallDrawing.isDrawing) {
      // بدء رسم جدار جديد
      print('🎯 بدء رسم جدار جديد');
      widget.onWallDrawStart(worldPoint);
      _showSuccessMessage(
          '🎯 بدء رسم جدار - انقر لإضافة نقاط، انقر مرتين أو زر أيمن للإنهاء');
    } else {
      // إضافة نقطة جديدة
      print('➕ إضافة نقطة جديدة للجدار');
      widget.onWallDrawAdd(worldPoint);

      // بدء مؤقت الحفظ التلقائي
      _startAutoSaveTimer();

      // تحقق من النقر المزدوج التلقائي (إذا كان لدينا نقطتين على الأقل)
      if (_currentState.wallDrawing.currentPoints.length >= 2) {
        _showSuccessMessage('➕ تم إضافة نقطة - انقر مرتين أو زر أيمن للإنهاء');
      } else {
        _showSuccessMessage('➕ أضف نقطة أخرى للجدار');
      }
    }
  }

  void _handleDeleteWallMode(
      vector.Vector2 worldPoint, PointerDownEvent event) {
    print('🗑️ وضع حذف جدار - موضع النقر: ${worldPoint.x}, ${worldPoint.y}');

    // البحث عن جدار تحت النقطة
    final hitObject = _hitTest(worldPoint);

    if (hitObject != null && hitObject['type'] == ObjectType.wall) {
      final wallId = hitObject['id'] as String;

      // تأكيد الحذف
      _showDeleteWallConfirmation(wallId);
    } else {
      _showErrorMessage('❌ لم يتم العثور على جدار في هذا الموقع');
    }
  }

  void _handleEntranceMode(vector.Vector2 worldPoint, PointerDownEvent event) {
    // البحث عن جدار قريب
    final wall = _findNearestWall(worldPoint, maxDistance: 30.0);
    if (wall != null) {
      final distanceOnWall = _getDistanceOnWall(wall, worldPoint);

      // تحديد الجدار أولاً في الـ Provider
      widget.onObjectSelect(wall.id, ObjectType.wall);

      // إضافة المدخل
      widget.onEntranceAdd(distanceOnWall);

      // عرض رسالة تأكيد
      _showSuccessMessage('تم إضافة المدخل بنجاح');
    } else {
      _showErrorMessage('انقر بالقرب من جدار لإضافة مدخل');
    }
  }

  void _handleShelfPlaceMode(
      vector.Vector2 worldPoint, PointerDownEvent event) {
    // وضع خزانة جديدة
    widget.onShelfAdd(
        worldPoint, _currentState.shelfPlacing.selectedTemplateId);
  }

  void _handleShelfEditMode(vector.Vector2 worldPoint, PointerDownEvent event) {
    // العثور على خزانة للتعديل
    final hitObject = _hitTest(worldPoint);
    if (hitObject != null && hitObject['type'] == ObjectType.shelf) {
      if (event.buttons == kSecondaryButton) {
        // النقر بالزر الأيمن للدوران
        widget.onShelfRotate(hitObject['id'], null);
      } else {
        widget.onObjectSelect(hitObject['id'], hitObject['type']);
      }
    }
  }

  // === أدوات التحويل والفحص ===

  vector.Vector2 _screenToWorld(Offset screenPoint) {
    // استخدام نظام التحويل من EditorState
    final canvasSize = Size(2000, 2000);
    return _currentState.screenToWorld(screenPoint, canvasSize);
  }

  Map<String, dynamic>? _hitTest(vector.Vector2 point) {
    // فحص الخزائن أولاً
    for (final shelf in widget.layout.shelves.reversed) {
      if (_isPointInShelf(point, shelf)) {
        return {'id': shelf.id, 'type': ObjectType.shelf};
      }
    }

    // فحص الجدران
    for (final wall in widget.layout.walls) {
      if (_isPointNearWall(point, wall)) {
        return {'id': wall.id, 'type': ObjectType.wall};
      }
    }

    // فحص المداخل
    for (final entrance in widget.layout.entrances) {
      if (_isPointInEntrance(point, entrance)) {
        return {'id': entrance.id, 'type': ObjectType.entrance};
      }
    }

    return null;
  }

  bool _isPointInShelf(vector.Vector2 point, Shelf shelf) {
    final corners = shelf.corners;
    return GeometryUtils.isPointInPolygon(point, corners);
  }

  bool _isPointNearWall(vector.Vector2 point, Wall wall,
      {double tolerance = 10.0}) {
    for (int i = 0; i < wall.points.length - 1; i++) {
      final distance = GeometryUtils.distancePointToLine(
          point, wall.points[i], wall.points[i + 1]);
      if (distance <= tolerance) {
        return true;
      }
    }
    return false;
  }

  bool _isPointInEntrance(vector.Vector2 point, Entrance entrance) {
    // العثور على الجدار
    final wall = widget.layout.walls.firstWhere((w) => w.id == entrance.wallId);

    // حساب موضع المدخل على الجدار
    final startPoint = wall.getPointAtDistance(entrance.startDistance);
    final endPoint = wall.getPointAtDistance(entrance.endDistance);

    if (startPoint == null || endPoint == null) return false;

    // فحص بسيط للمسافة
    final distanceToStart = (point - startPoint).length;
    final distanceToEnd = (point - endPoint).length;

    return distanceToStart <= 15.0 || distanceToEnd <= 15.0;
  }

  Wall? _findNearestWall(vector.Vector2 point, {double maxDistance = 50.0}) {
    Wall? nearestWall;
    double nearestDistance = maxDistance;

    for (final wall in widget.layout.walls) {
      for (int i = 0; i < wall.points.length - 1; i++) {
        final distance = GeometryUtils.distancePointToLine(
            point, wall.points[i], wall.points[i + 1]);

        if (distance < nearestDistance) {
          nearestDistance = distance;
          nearestWall = wall;
        }
      }
    }

    return nearestWall;
  }

  double _getDistanceOnWall(Wall wall, vector.Vector2 point) {
    double minDistance = double.infinity;
    double bestDistanceOnWall = 0;
    double currentDistance = 0;

    for (int i = 0; i < wall.points.length - 1; i++) {
      final segmentStart = wall.points[i];
      final segmentEnd = wall.points[i + 1];
      final segmentLength = (segmentEnd - segmentStart).length;

      // العثور على أقرب نقطة على هذا الجزء
      final segmentVector = segmentEnd - segmentStart;
      final pointVector = point - segmentStart;

      final t = (pointVector.dot(segmentVector) / segmentVector.length2)
          .clamp(0.0, 1.0);
      final closestPoint = segmentStart + segmentVector * t;

      final distance = (point - closestPoint).length;
      if (distance < minDistance) {
        minDistance = distance;
        bestDistanceOnWall = currentDistance + t * segmentLength;
      }

      currentDistance += segmentLength;
    }

    return bestDistanceOnWall;
  }

  void _showSuccessMessage(String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showErrorMessage(String message) {
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  void _showDeleteWallConfirmation(String wallId) {
    if (widget.onWallDelete != null) {
      widget.onWallDelete!(wallId);
      _showSuccessMessage('🗑️ تم حذف الجدار بنجاح!');
    }
  }

  // معالجة أحداث لوحة المفاتيح
  KeyEventResult _handleKeyEvent(FocusNode node, KeyEvent event) {
    if (event is! KeyDownEvent) return KeyEventResult.ignored;

    final selectedId = _currentState.selectedObjectId;
    final selectedType = _currentState.selectedObjectType;

    // إذا كانت خزانة محددة، حركها بالأسهم
    if (selectedId != null && selectedType == ObjectType.shelf) {
      final moveDistance =
          _currentState.snapToGrid ? _currentState.gridSize : 10.0;
      vector.Vector2? moveDirection;

      switch (event.logicalKey) {
        case LogicalKeyboardKey.arrowUp:
          moveDirection = vector.Vector2(0, -moveDistance);
          break;
        case LogicalKeyboardKey.arrowDown:
          moveDirection = vector.Vector2(0, moveDistance);
          break;
        case LogicalKeyboardKey.arrowLeft:
          moveDirection = vector.Vector2(-moveDistance, 0);
          break;
        case LogicalKeyboardKey.arrowRight:
          moveDirection = vector.Vector2(moveDistance, 0);
          break;
        case LogicalKeyboardKey.keyR:
          // دوران الخزانة بـ R
          widget.onShelfRotate(selectedId, null);
          _showSuccessMessage('🔄 تم تدوير الخزانة');
          return KeyEventResult.handled;
        case LogicalKeyboardKey.delete:
          // حذف الكائن المحدد
          _deleteSelectedObject();
          return KeyEventResult.handled;
      }

      if (moveDirection != null) {
        // الحصول على الخزانة الحالية
        final shelf = widget.layout.shelves.firstWhere(
          (s) => s.id == selectedId,
          orElse: () => widget.layout.shelves.first,
        );

        final newPosition = shelf.position + moveDirection;
        widget.onShelfMove(selectedId, newPosition);
        _showSuccessMessage('⬅️ تحريك الخزانة بالأسهم');
        return KeyEventResult.handled;
      }
    }

    return KeyEventResult.ignored;
  }

  void _deleteSelectedObject() {
    final selectedId = _currentState.selectedObjectId;
    final selectedType = _currentState.selectedObjectType;

    if (selectedId == null || selectedType == null) return;

    switch (selectedType) {
      case ObjectType.wall:
        widget.onWallDelete(selectedId);
        _showSuccessMessage('🗑️ تم حذف الجدار');
        break;
      case ObjectType.shelf:
        // TODO: إضافة callback لحذف الخزانة إذا لزم الأمر
        _showSuccessMessage('❌ حذف الخزانة غير متاح حالياً');
        break;
      default:
        break;
    }
  }

  void _startAutoSaveTimer() {
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer(const Duration(seconds: 3), () {
      if (_currentState.mode == EditMode.drawWall &&
          _currentState.wallDrawing.isDrawing) {
        if (_currentState.wallDrawing.currentPoints.length >= 2) {
          widget.onWallDrawFinish();
        }
      }
    });
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    super.dispose();
  }

  void _onDoubleTap() {
    // إذا كنا في وضع رسم الجدار، أنهِ الرسم
    if (_currentState.mode == EditMode.drawWall &&
        _currentState.wallDrawing.isDrawing) {
      if (_currentState.wallDrawing.currentPoints.length >= 2) {
        _autoSaveTimer?.cancel(); // إلغاء الحفظ التلقائي
        widget.onWallDrawFinish();

        return;
      }
    }

    if (widget.onShelfDoubleClick == null) return;

    // الحصول على آخر نقطة نقر
    final hitObject = _hitTest(_lastPointerPosition ?? vector.Vector2.zero());

    if (hitObject != null && hitObject['type'] == ObjectType.shelf) {
      widget.onShelfDoubleClick!(hitObject['id']);
    }
  }
}

/// الرسام المخصص للوحة المحرر
class WarehouseCanvasPainter extends CustomPainter {
  final WarehouseLayout layout;
  final EditorState editorState;
  final LayoutSettings settings;

  WarehouseCanvasPainter({
    required this.layout,
    required this.editorState,
    required this.settings,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // تحويل المنشأ لمنتصف اللوحة
    canvas.translate(size.width / 2, size.height / 2);

    // رسم الشبكة
    if (editorState.showGrid) {
      _drawGrid(canvas, size);
    }

    // رسم حدود المستودع
    _drawWarehouseBounds(canvas);

    // رسم الجدران
    _drawWalls(canvas);

    // رسم المداخل
    _drawEntrances(canvas);

    // رسم الخزائن
    _drawShelves(canvas);

    // رسم الخزانة المحددة مع تمييز
    _drawSelectedShelf(canvas);

    // رسم أدوات الرسم الحالية
    _drawCurrentTools(canvas);

    // رسم القياسات
    if (editorState.showMeasurements) {
      _drawMeasurements(canvas);
    }
  }

  void _drawGrid(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = settings.gridColor.withOpacity(settings.gridOpacity)
      ..strokeWidth = 0.5
      ..style = PaintingStyle.stroke;

    final gridSize = settings.gridSize;
    final halfWidth = size.width / 2;
    final halfHeight = size.height / 2;

    // خطوط عمودية
    for (double x = -halfWidth; x <= halfWidth; x += gridSize) {
      canvas.drawLine(
        Offset(x, -halfHeight),
        Offset(x, halfHeight),
        paint,
      );
    }

    // خطوط أفقية
    for (double y = -halfHeight; y <= halfHeight; y += gridSize) {
      canvas.drawLine(
        Offset(-halfWidth, y),
        Offset(halfWidth, y),
        paint,
      );
    }
  }

  void _drawWarehouseBounds(Canvas canvas) {
    final paint = Paint()
      ..color = const Color(0xFF34495E)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    final bounds = Rect.fromCenter(
      center: Offset.zero,
      width: layout.width,
      height: layout.height,
    );

    canvas.drawRect(bounds, paint);

    // تسمية الأبعاد
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );

    // العرض
    textPainter.text = TextSpan(
      text: '${layout.width.toInt()} سم',
      style: const TextStyle(
        color: Color(0xFF2C3E50),
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout();
    textPainter.paint(
        canvas, Offset(-textPainter.width / 2, layout.height / 2 + 10));

    // الطول
    canvas.save();
    canvas.rotate(-1.5708); // 90 درجة
    textPainter.text = TextSpan(
      text: '${layout.height.toInt()} سم',
      style: const TextStyle(
        color: Color(0xFF2C3E50),
        fontSize: 12,
        fontWeight: FontWeight.bold,
      ),
    );
    textPainter.layout();
    textPainter.paint(
        canvas, Offset(-textPainter.width / 2, layout.width / 2 + 10));
    canvas.restore();
  }

  void _drawWalls(Canvas canvas) {
    for (final wall in layout.walls) {
      _drawWall(canvas, wall);
    }
  }

  void _drawWall(Canvas canvas, Wall wall) {
    if (wall.points.length < 2) return;

    final isSelected = editorState.selectedObjectId == wall.id;

    final paint = Paint()
      ..color = isSelected ? settings.selectionColor : wall.color
      ..strokeWidth = wall.thickness
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final path = Path();
    path.moveTo(wall.points[0].x, -wall.points[0].y);

    for (int i = 1; i < wall.points.length; i++) {
      path.lineTo(wall.points[i].x, -wall.points[i].y);
    }

    canvas.drawPath(path, paint);

    // رسم نقاط التحكم إذا كان محدداً
    if (isSelected) {
      final pointPaint = Paint()
        ..color = settings.selectionColor
        ..style = PaintingStyle.fill;

      for (final point in wall.points) {
        canvas.drawCircle(
          Offset(point.x, -point.y),
          4,
          pointPaint,
        );
      }
    }
  }

  void _drawEntrances(Canvas canvas) {
    for (final entrance in layout.entrances) {
      _drawEntrance(canvas, entrance);
    }
  }

  void _drawEntrance(Canvas canvas, Entrance entrance) {
    // العثور على الجدار
    final wall = layout.walls.firstWhere((w) => w.id == entrance.wallId);

    final startPoint = wall.getPointAtDistance(entrance.startDistance);
    final endPoint = wall.getPointAtDistance(entrance.endDistance);

    if (startPoint == null || endPoint == null) return;

    final isSelected = editorState.selectedObjectId == entrance.id;

    final paint = Paint()
      ..color = isSelected ? settings.selectionColor : const Color(0xFFE67E22)
      ..strokeWidth = 8
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(startPoint.x, -startPoint.y),
      Offset(endPoint.x, -endPoint.y),
      paint,
    );

    // رمز المدخل
    final center = (startPoint + endPoint) * 0.5;
    final iconPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    canvas.drawCircle(Offset(center.x, -center.y), 6, iconPaint);
    canvas.drawCircle(
        Offset(center.x, -center.y),
        6,
        Paint()
          ..color = const Color(0xFFE67E22)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2);
  }

  void _drawShelves(Canvas canvas) {
    for (final shelf in layout.shelves) {
      // تخطي الخزانة المحددة - سنرسمها بشكل منفصل
      if (editorState.selectedObjectId == shelf.id &&
          editorState.selectedObjectType == ObjectType.shelf) {
        continue;
      }
      _drawShelf(canvas, shelf);
    }
  }

  void _drawSelectedShelf(Canvas canvas) {
    if (editorState.selectedObjectId == null ||
        editorState.selectedObjectType != ObjectType.shelf) {
      return;
    }

    final selectedShelf = layout.shelves.firstWhere(
      (s) => s.id == editorState.selectedObjectId,
      orElse: () => layout.shelves.first,
    );

    // رسم هالة حول الخزانة المحددة
    final glowPaint = Paint()
      ..color = Colors.blue.withOpacity(0.3)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    final handlePaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.fill;

    // حساب أركان الخزانة
    final corners = _getShelfCorners(selectedShelf);

    // رسم الهالة (أكبر قليلاً من الخزانة)
    final glowPath = Path();
    glowPath.addPolygon(
      corners.map((c) => Offset(c.x - 5, c.y - 5)).toList(),
      true,
    );
    canvas.drawPath(glowPath, glowPaint);

    // رسم الخزانة نفسها
    _drawShelf(canvas, selectedShelf);

    // رسم إطار التحديد
    final borderPath = Path();
    borderPath.addPolygon(
      corners.map((c) => Offset(c.x, c.y)).toList(),
      true,
    );
    canvas.drawPath(borderPath, borderPaint);

    // رسم مقابض التحريك في الأركان
    for (final corner in corners) {
      canvas.drawCircle(
        Offset(corner.x, corner.y),
        6.0,
        handlePaint,
      );
      canvas.drawCircle(
        Offset(corner.x, corner.y),
        6.0,
        Paint()
          ..color = Colors.white
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0,
      );
    }

    // رسم مقبض دوران في المنتصف العلوي
    final center = _getShelfCenter(selectedShelf);
    final rotationHandle =
        Offset(center.x, center.y - selectedShelf.depth / 2 - 20);

    // خط الاتصال
    canvas.drawLine(
      Offset(center.x, center.y - selectedShelf.depth / 2),
      rotationHandle,
      Paint()
        ..color = Colors.green
        ..strokeWidth = 2.0,
    );

    // مقبض الدوران
    canvas.drawCircle(
      rotationHandle,
      8.0,
      Paint()..color = Colors.green,
    );
    canvas.drawCircle(
      rotationHandle,
      8.0,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0,
    );
  }

  void _drawShelf(Canvas canvas, Shelf shelf) {
    final isSelected = editorState.selectedObjectId == shelf.id;

    final fillPaint = Paint()
      ..color = isSelected
          ? settings.selectionColor.withOpacity(0.8)
          : shelf.color.withOpacity(0.8)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color =
          isSelected ? settings.selectionColor : shelf.color.withOpacity(0.9)
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // رسم مستطيل الخزانة
    final corners = shelf.corners;
    final path = Path();
    path.moveTo(corners[0].x, -corners[0].y);
    for (int i = 1; i < corners.length; i++) {
      path.lineTo(corners[i].x, -corners[i].y);
    }
    path.close();

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, borderPaint);

    // رسم تفاصيل الخزانة
    _drawShelfDetails(canvas, shelf);

    // رسم مقابض التحكم إذا كانت محددة
    if (isSelected) {
      _drawShelfHandles(canvas, shelf);
    }
  }

  void _drawShelfDetails(Canvas canvas, Shelf shelf) {
    final center = shelf.position;

    // اسم الخزانة
    final textPainter = TextPainter(
      text: TextSpan(
        text: shelf.name,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        center.x - textPainter.width / 2,
        -center.y - textPainter.height / 2,
      ),
    );

    // رسم الأرفف الداخلية (خطوط رفيعة)
    final shelfPaint = Paint()
      ..color = Colors.white.withOpacity(0.5)
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    final corners = shelf.corners;
    final topLeft = corners[3];
    final topRight = corners[2];
    final bottomLeft = corners[0];

    final levelHeight = shelf.height / shelf.levels;
    final slotWidth = shelf.width / shelf.slotsPerLevel;

    // خطوط الأرفف الأفقية
    for (int level = 1; level < shelf.levels; level++) {
      final y = levelHeight * level;
      final startPoint = vector.Vector2(
        topLeft.x + (bottomLeft.x - topLeft.x) * (y / shelf.height),
        topLeft.y - y,
      );
      final endPoint = vector.Vector2(
        topRight.x + (corners[1].x - topRight.x) * (y / shelf.height),
        topRight.y - y,
      );

      canvas.drawLine(
        Offset(startPoint.x, -startPoint.y),
        Offset(endPoint.x, -endPoint.y),
        shelfPaint,
      );
    }

    // خطوط الخانات العمودية
    for (int slot = 1; slot < shelf.slotsPerLevel; slot++) {
      final x = slotWidth * slot;
      final startPoint = vector.Vector2(
        topLeft.x + (topRight.x - topLeft.x) * (x / shelf.width),
        topLeft.y,
      );
      final endPoint = vector.Vector2(
        bottomLeft.x + (corners[1].x - bottomLeft.x) * (x / shelf.width),
        bottomLeft.y,
      );

      canvas.drawLine(
        Offset(startPoint.x, -startPoint.y),
        Offset(endPoint.x, -endPoint.y),
        shelfPaint,
      );
    }
  }

  void _drawShelfHandles(Canvas canvas, Shelf shelf) {
    final corners = shelf.corners;
    final handlePaint = Paint()
      ..color = settings.selectionColor
      ..style = PaintingStyle.fill;

    // مقابض في الزوايا
    for (final corner in corners) {
      canvas.drawCircle(
        Offset(corner.x, -corner.y),
        6,
        handlePaint,
      );
    }

    // مقبض الدوران
    final center = shelf.position;
    canvas.drawCircle(
      Offset(center.x, -center.y + shelf.height / 2 + 20),
      8,
      Paint()
        ..color = const Color(0xFF9B59B6)
        ..style = PaintingStyle.fill,
    );
  }

  void _drawCurrentTools(Canvas canvas) {
    // رسم الجدار الحالي قيد الرسم
    if (editorState.wallDrawing.isDrawing) {
      _drawCurrentWall(canvas);
    }

    // رسم معاينة الخزانة
    if (editorState.mode == EditMode.placeShelf &&
        editorState.shelfPlacing.previewPosition != null) {
      _drawShelfPreview(canvas);
    }
  }

  void _drawCurrentWall(Canvas canvas) {
    final points = editorState.wallDrawing.currentPoints;
    if (points.length < 2) return;

    final paint = Paint()
      ..color = settings.previewColor.withOpacity(settings.previewOpacity)
      ..strokeWidth = editorState.wallDrawing.wallThickness
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final path = Path();
    path.moveTo(points[0].x, -points[0].y);

    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].x, -points[i].y);
    }

    canvas.drawPath(path, paint);
  }

  void _drawShelfPreview(Canvas canvas) {
    final templateId = editorState.shelfPlacing.selectedTemplateId;
    final position = editorState.shelfPlacing.previewPosition!;

    if (templateId == null) return;

    // العثور على القالب
    final template = ShelfTemplate.predefined.firstWhere(
      (t) => t.name == templateId,
      orElse: () => ShelfTemplate.predefined.first,
    );

    final paint = Paint()
      ..color = settings.previewColor.withOpacity(settings.previewOpacity)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = settings.previewColor
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    // رسم مستطيل الخزانة المعاين
    final rect = Rect.fromCenter(
      center: Offset(position.x, -position.y),
      width: template.width,
      height: template.depth,
    );

    canvas.drawRect(rect, paint);
    canvas.drawRect(rect, borderPaint);

    // نص المعاينة
    final textPainter = TextPainter(
      text: TextSpan(
        text: template.name,
        style: const TextStyle(
          color: Color(0xFF2C3E50),
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.rtl,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        position.x - textPainter.width / 2,
        -position.y - textPainter.height / 2,
      ),
    );
  }

  void _drawMeasurements(Canvas canvas) {
    // TODO: رسم القياسات والأبعاد
  }

  // دوال مساعدة
  List<vector.Vector2> _getShelfCorners(Shelf shelf) {
    final center = shelf.position;
    final halfWidth = shelf.width / 2;
    final halfDepth = shelf.depth / 2;

    // الأركان الأساسية قبل التدوير
    final corners = [
      vector.Vector2(-halfWidth, -halfDepth),
      vector.Vector2(halfWidth, -halfDepth),
      vector.Vector2(halfWidth, halfDepth),
      vector.Vector2(-halfWidth, halfDepth),
    ];

    // تطبيق التدوير
    final rotation = shelf.rotation * (3.14159 / 180); // تحويل إلى راديان
    final cos = math.cos(rotation);
    final sin = math.sin(rotation);

    return corners.map((corner) {
      final rotatedX = corner.x * cos - corner.y * sin;
      final rotatedY = corner.x * sin + corner.y * cos;
      return vector.Vector2(
        center.x + rotatedX,
        center.y + rotatedY,
      );
    }).toList();
  }

  vector.Vector2 _getShelfCenter(Shelf shelf) {
    return shelf.position;
  }

  @override
  bool shouldRepaint(covariant WarehouseCanvasPainter oldDelegate) {
    return oldDelegate.layout != layout ||
        oldDelegate.editorState != editorState ||
        oldDelegate.settings != settings;
  }
}
