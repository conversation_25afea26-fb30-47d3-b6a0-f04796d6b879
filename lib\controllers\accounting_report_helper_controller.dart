import 'package:flutter/material.dart';
import 'package:inventory_application/base/network/accounting_hepler_api.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/models/dto/reports/closing_entries_report_dto.dart';
import 'package:inventory_application/models/dto/reports/card_detail_report_dto.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';

class AccountingReportHelperController with ChangeNotifier {
  // Closing Entries Report Data
  ClosingEntriesReportDTO? _closingEntriesReport;

  // Loading states
  bool _isLoadingClosingEntries = false;
  bool _isLoadingAdmins = false;

  // Filter properties
  DateTime? _fromDate;
  DateTime? _toDate;
  int? _companyId;
  int? _adminId;
  String? _cardNumber;

  // Admin data
  List<ComboBoxDataModel> _admins = [];

  // Admin transactions data
  ClosingEntriesReportDTO _adminTransactions = ClosingEntriesReportDTO();
  bool _isLoadingAdminTransactions = false;

  // Card history data
  List<RechargeTransactionDTO> _cardHistory = [];
  bool _isLoadingCardHistory = false;

  // Card detail report data
  CardDetailReportDTO? _cardDetailReport;
  bool _isLoadingCardDetail = false;

  // Getters
  ClosingEntriesReportDTO? get closingEntriesReport => _closingEntriesReport;
  bool get isLoadingClosingEntries => _isLoadingClosingEntries;
  bool get isLoadingAdmins => _isLoadingAdmins;
  bool get isLoadingAdminTransactions => _isLoadingAdminTransactions;
  bool get isLoadingCardHistory => _isLoadingCardHistory;
  bool get isLoadingCardDetail => _isLoadingCardDetail;
  DateTime? get fromDate => _fromDate;
  DateTime? get toDate => _toDate;
  int? get companyId => _companyId;
  int? get adminId => _adminId;
  String? get cardNumber => _cardNumber;
  List<ComboBoxDataModel> get admins => _admins;
  ClosingEntriesReportDTO get adminTransactions => _adminTransactions;
  List<RechargeTransactionDTO> get cardHistory => _cardHistory;
  CardDetailReportDTO? get cardDetailReport => _cardDetailReport;

  // Initialize the controller
  AccountingReportHelperController() {
    // Set default filter to today
    _fromDate = DateTime.now();
    _toDate = DateTime.now();
    // Load admins on initialization
    getAllAdmins();
  }

  // Set filter properties
  void setDateRange(DateTime? from, DateTime? to) {
    _fromDate = from;
    _toDate = to;
    notifyListeners();
  }

  void setCompanyFilter(int? companyId) {
    _companyId = companyId;
    notifyListeners();
  }

  void setAdminFilter(int? adminId) {
    _adminId = adminId;
    notifyListeners();
  }

  void setCardNumberFilter(String? cardNumber) {
    _cardNumber = cardNumber;
    notifyListeners();
  }

  // Reset filters
  void resetFilters() {
    _fromDate = DateTime.now();
    _toDate = DateTime.now();
    _companyId = null;
    _adminId = null;
    _cardNumber = null;
    notifyListeners();
  }

  // Get all admins for filter dropdown
  Future<void> getAllAdmins() async {
    try {
      _isLoadingAdmins = true;
      notifyListeners();

      String action = 'api/Reports/GetAllAdmins';

      // Make API call
      var response = await AccountingHelperApi.getOne(action: action);

      if (response != null && response.isSuccess) {
        // Parse response to ComboBoxDataModel list
        if (response.data is List) {
          _admins = (response.data as List)
              .map((item) => ComboBoxDataModel.fromJson(item))
              .toList();
        }
        // Add "All Admins" option at the beginning
        _admins.insert(0, ComboBoxDataModel(id: 0, name: "كل المشرفين"));
        notifyListeners();
      } else {
        // Return mock data if API fails
        _admins = [];
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error getting admins: $e');
      // Return mock data on error
      _admins = [];
      notifyListeners();
    } finally {
      _isLoadingAdmins = false;
      notifyListeners();
    }
  }

  // Get selected admin name
  String getSelectedAdminName() {
    if (_adminId == null || _adminId == 0) return 'كل المشرفين';

    final selectedAdmin = _admins.firstWhere(
      (admin) => admin.id == _adminId,
      orElse: () => ComboBoxDataModel(id: 0, name: 'غير محدد'),
    );

    return selectedAdmin.name;
  }

  // Generate Closing Entries Report
  Future<ClosingEntriesReportDTO?> generateClosingEntriesReport({
    DateTime? fromDate,
    DateTime? toDate,
    int? companyId,
    int? adminId,
    String? cardNumber,
  }) async {
    try {
      _isLoadingClosingEntries = true;
      notifyListeners();

      // Update filters if provided
      if (fromDate != null) _fromDate = fromDate;
      if (toDate != null) _toDate = toDate;
      if (companyId != null) _companyId = companyId;
      if (adminId != null) _adminId = adminId;
      if (cardNumber != null) _cardNumber = cardNumber;

      // Prepare query parameters
      String action = 'api/Reports/ClosingEntries';
      Map<String, dynamic> queryParams = {};

      if (_fromDate != null) {
        queryParams['fromDate'] = _fromDate!.toIso8601String();
      }
      if (_toDate != null) {
        queryParams['toDate'] = _toDate!.toIso8601String();
      }
      if (_companyId != null) {
        queryParams['companyId'] = _companyId.toString();
      }
      if (adminId != null) {
        queryParams['adminId'] = adminId.toString();
      }
      if (_cardNumber != null && _cardNumber!.isNotEmpty) {
        queryParams['cardNumber'] = _cardNumber!;
      }

      // Build query string
      if (queryParams.isNotEmpty) {
        String queryString = queryParams.entries
            .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
            .join('&');
        action += '?$queryString';
      }

      // Make API call
      var response = await AccountingHelperApi.getOne(action: action);

      if (response != null && response.isSuccess) {
        _closingEntriesReport = ClosingEntriesReportDTO.fromJson(response.data);
        notifyListeners();
        return _closingEntriesReport;
      } else {
        // Return mock data structure if API fails

        notifyListeners();
        return _closingEntriesReport;
      }
    } catch (e) {
      debugPrint('Error generating closing entries report: $e');
      // Return mock data on error

      notifyListeners();
      return _closingEntriesReport;
    } finally {
      _isLoadingClosingEntries = false;
      notifyListeners();
    }
  }

  // Mock data for testing

  // Get financial summary
  FinancialSummaryDTO? getFinancialSummary() {
    return _closingEntriesReport?.financialSummary;
  }

  // Get detailed summaries
  DetailedSummariesDTO? getDetailedSummaries() {
    return _closingEntriesReport?.detailedSummaries;
  }

  // Get transaction details
  TransactionDetailsDTO? getTransactionDetails() {
    return _closingEntriesReport?.transactionDetails;
  }

  // Get company summaries
  List<CompanySummaryDTO>? getCompanySummaries() {
    return _closingEntriesReport?.detailedSummaries?.byCompany;
  }

  // Get admin summaries
  List<AdminSummaryDTO>? getAdminSummaries() {
    return _closingEntriesReport?.detailedSummaries?.byAdmin;
  }

  // Get recharge transactions
  List<RechargeTransactionDTO>? getRechargeTransactions() {
    return _closingEntriesReport?.transactionDetails?.recharges;
  }

  // Get sales transactions
  List<SalesTransactionDTO>? getSalesTransactions() {
    return _closingEntriesReport?.transactionDetails?.sales;
  }

  // Calculate total recharge amount
  double getTotalRechargeAmount() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalRechargeAmount ??
        0.0;
  }

  double getTotalRechargeAmountCard() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalRechargeAmountCard ??
        0.0;
  }

  double getTotalRechargeAmountCash() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalRechargeAmountCash ??
        0.0;
  }

  // Calculate total sales amount
  double getTotalSalesAmount() {
    return _closingEntriesReport
            ?.financialSummary?.salesSummary?.totalSalesAmount ??
        0.0;
  }

  double getTotalFeesAmount() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalFeesAmount ??
        0.0;
  }

  double getTotalFeesAmountCard() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalFeesAmountCard ??
        0.0;
  }

  double getTotalFeesAmountCash() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalFeesAmountCash ??
        0.0;
  }

  double getTotalParentFeeAmount() {
    var data =
        (getTotalRecive() - getTotalRechargeAmount() - getTotalFeesAmount()) ??
            0.0;
    _closingEntriesReport
        ?.financialSummary?.rechargeSummary?.totalParentFeeAmount = data;
    return data;
  }

  double getTotalParentFeeAmountCard() {
    var data = (getTotalReciveCard() -
            getTotalRechargeAmountCard() -
            getTotalFeesAmountCard()) ??
        0.0;
    _closingEntriesReport
        ?.financialSummary?.rechargeSummary?.totalParentFeeAmountCard = data;
    return data;
  }

  double getTotalParentFeeAmountCash() {
    var data = (getTotalReciveCash() -
            getTotalRechargeAmountCash() -
            getTotalFeesAmountCash()) ??
        0.0;
    _closingEntriesReport
        ?.financialSummary?.rechargeSummary?.totalParentFeeAmountCash = data;
    return data;
  }

  // Calculate net amount
  double getNetAmount() {
    return _closingEntriesReport?.financialSummary?.netAmount ?? 0.0;
  }

  // Get current balance
  double getCurrentBalance() {
    return _closingEntriesReport?.financialSummary?.totalCurrentBalance ?? 0.0;
  }

  double getGiftsAmount() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalGiftAmount ??
        0.0;
  }

  double getTotalRecive() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalAmount ??
        0.0;
  }

  double getTotalReciveCard() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalAmountCard ??
        0.0;
  }

  double getTotalReciveCash() {
    return _closingEntriesReport
            ?.financialSummary?.rechargeSummary?.totalAmountCash ??
        0.0;
  }

  // Get admin transactions by ID
  Future<ClosingEntriesReportDTO> getTransactionsByAdminId(
    int adminId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      _isLoadingAdminTransactions = true;
      notifyListeners();

      // Prepare query parameters
      String action = 'api/Reports/GetTransactionsByAdminId';
      Map<String, dynamic> queryParams = {
        'adminId': adminId.toString(),
      };

      if (fromDate != null) {
        queryParams['fromDate'] = fromDate.toIso8601String();
      }
      if (toDate != null) {
        queryParams['toDate'] = toDate.toIso8601String();
      }

      // Build query string
      String queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');
      action += '?$queryString';

      // Make API call
      var response = await AccountingHelperApi.getOne(action: action);

      if (response != null && response.isSuccess) {
        _adminTransactions = ClosingEntriesReportDTO.fromJson(response.data);

        notifyListeners();
        return _adminTransactions;
      } else {
        _adminTransactions = ClosingEntriesReportDTO();
        notifyListeners();
        return _adminTransactions;
      }
    } catch (e) {
      debugPrint('Error getting admin transactions: $e');
      _adminTransactions = ClosingEntriesReportDTO();
      notifyListeners();
      return _adminTransactions;
    } finally {
      _isLoadingAdminTransactions = false;
      notifyListeners();
    }
  }

  // Clear admin transactions
  void clearAdminTransactions() {
    _adminTransactions = ClosingEntriesReportDTO();
    notifyListeners();
  }

  // Get card history by card number
  Future<List<RechargeTransactionDTO>?> getCardHistory(
      String cardNumber) async {
    try {
      _isLoadingCardHistory = true;
      notifyListeners();

      // Prepare query parameters
      String action = 'api/Reports/GetCardHistory';
      Map<String, dynamic> queryParams = {
        'cardNumber': cardNumber,
      };

      // Build query string
      String queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');
      action += '?$queryString';

      // Make API call
      var response = await AccountingHelperApi.getOne(action: action);

      if (response != null && response.isSuccess) {
        if (response.data is List) {
          _cardHistory = (response.data as List)
              .map((item) => RechargeTransactionDTO.fromJson(item))
              .toList();
        } else {
          _cardHistory = [];
        }
        notifyListeners();
        return _cardHistory;
      } else {
        _cardHistory = [];
        notifyListeners();
        return _cardHistory;
      }
    } catch (e) {
      debugPrint('Error getting card history: $e');
      _cardHistory = [];
      notifyListeners();
      return _cardHistory;
    } finally {
      _isLoadingCardHistory = false;
      notifyListeners();
    }
  }

  // Clear card history
  void clearCardHistory() {
    _cardHistory = [];
    notifyListeners();
  }

  // Get card detail report
  Future<CardDetailReportDTO?> getCardDetailReport(String cardNumber) async {
    try {
      _isLoadingCardDetail = true;
      notifyListeners();

      // Prepare query parameters
      String action = 'api/Reports/GetCardDetailReport';
      Map<String, dynamic> queryParams = {
        'cardNumber': cardNumber,
      };

      // Build query string
      String queryString = queryParams.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');
      action += '?$queryString';

      // Make API call
      var response = await AccountingHelperApi.getOne(action: action);

      if (response != null && response.isSuccess) {
        _cardDetailReport = CardDetailReportDTO.fromJson(response.data);
        notifyListeners();
        return _cardDetailReport;
      } else {
        _cardDetailReport = null;
        notifyListeners();
        return null;
      }
    } catch (e) {
      debugPrint('Error getting card detail report: $e');
      _cardDetailReport = null;
      notifyListeners();
      return null;
    } finally {
      _isLoadingCardDetail = false;
      notifyListeners();
    }
  }

  // Clear card detail report
  void clearCardDetailReport() {
    _cardDetailReport = null;
    notifyListeners();
  }

  // Clear report data
  void clearReport() {
    _closingEntriesReport = null;
    notifyListeners();
  }

  // Format date for display
  String formatDate(DateTime? date) {
    if (date == null) return '';
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  // Format number with thousand separator
  String formatNumber(double? number) {
    if (number == null) return '0.00';
    final formatter = NumberFormat('#,##0.00', 'en_US');
    return formatter.format(number);
  }

  // Format number with currency symbol
  String formatCurrency(double? number) {
    if (number == null) return '0.00 د.أ';
    final formatter = NumberFormat('#,##0.00', 'en_US');
    return '${formatter.format(number)} د.أ';
  }

  // Format number without decimals
  String formatWholeNumber(double? number) {
    if (number == null) return '0';
    final formatter = NumberFormat('#,##0', 'en_US');
    return formatter.format(number);
  }

  // Format count/integer
  String formatCount(int? count) {
    if (count == null) return '0';
    final formatter = NumberFormat('#,##0', 'en_US');
    return formatter.format(count);
  }

  Future<bool> createCLosingEntryJournalForSkyLand(
      RechargeSummaryDTO model, DateTime date) async {
    try {
      if (model.totalAmount == 0 || model.totalAmount == null) {
        return false;
      }
      var url = 'Accounting/CreateCLosingEntryJournalForSkyLand';
      model.totalParentFeeAmount = getTotalParentFeeAmount();
      model.totalParentFeeAmountCard = getTotalParentFeeAmountCard();
      model.totalParentFeeAmountCash = getTotalParentFeeAmountCash();
      var body = model.toJson();

      body['Date'] = date.toIso8601String();
      var result = await Api.post(action: url, body: body);
      if (result != null && result.isSuccess) {
        return true;
      } else {
        errorSnackBar(message: result?.message?[0] ?? result?.errors?[0]);
        return false;
      }
    } catch (e) {
      print(e);
      return false;
    }
  }
}
