// class InventoryOperationListItem {
//   String? code;
//   String? entryDateFormated;
//   List<InventoryOperationItem>? inventoryOperationItems; // Update this with the proper model if known
//   int? fromStoreID;
//   int? toStoreID;
//   String? storeName;
//   String? toStoreName;
//   String? submit;
//   bool? shortagePosted;
//   bool? surplusPosted;
//   int? iD;
//   int? operationType;
//   String? entryDate;
//   int? year;
//   int? counter;
//   String? prefix;
//   String? notes;

//   int? branchID;
//   int? insertUserID;
//   int? updateUserID;
//   String? updateUserDate;
//   String? insertUserDate;
//   bool? isPosted;
//   int? sourceID;
//   String? sourceReference1;
//   String? sourceReference2;
//   String? aPPReferanceCode;
//   String? userDeviceID;
//   dynamic constantsBranchs;
//   dynamic constantsFromStore;
//   dynamic constantsToStore;
//   dynamic userDevice;

//   InventoryOperationListItem({
//     this.code,
//     this.entryDateFormated,
//     this.inventoryOperationItems,
//     this.fromStoreID,
//     this.toStoreID,
//     this.storeName,
//     this.toStoreName,
//     this.submit,
//     this.shortagePosted,
//     this.surplusPosted,
//     this.iD,
//     this.operationType,
//     this.entryDate,
//     this.year,
//     this.counter,
//     this.prefix,
//     this.notes,
//     this.branchID,
//     this.insertUserID,
//     this.updateUserID,
//     this.updateUserDate,
//     this.insertUserDate,
//     this.isPosted,
//     this.sourceID,
//     this.sourceReference1,
//     this.sourceReference2,
//     this.aPPReferanceCode,
//     this.userDeviceID,
//     this.constantsBranchs,
//     this.constantsFromStore,
//     this.constantsToStore,
//     this.userDevice,
//   });

//   InventoryOperationListItem.fromJson(Map<String, dynamic> json) {
//     code = json['Code'];
//     entryDateFormated = json['Entry_Date_Formated'];
//   inventoryOperationItems = (json['InventoryOperationItems'] as List?)
//     ?.map((e) => InventoryOperationItem.fromJson(e))
//     .toList();

//     fromStoreID = json['From_Store_ID'];
//     toStoreID = json['To_Store_ID'];
//     storeName = json['Store_Name'];
//     toStoreName = json['To_Store_Name'];
//     submit = json['Submit'];
//     shortagePosted = json['ShortagePosted'];
//     surplusPosted = json['SurplusPosted'];
//     iD = json['ID'];
//     operationType = json['Operation_Type'];
//     entryDate = json['Entry_Date'];
//     year = json['Year'];
//     counter = json['Counter'];
//     prefix = json['Prefix'];
//     notes = json['Notes'];
//     branchID = json['Branch_ID'];
//     insertUserID = json['Insert_User_ID'];
//     updateUserID = json['Update_User_ID'];
//     updateUserDate = json['Update_User_Date'];
//     insertUserDate = json['Insert_User_Date'];
//     isPosted = json['Is_Posted'];
//     sourceID = json['Source_ID'];
//     sourceReference1 = json['Source_Reference_1'];
//     sourceReference2 = json['Source_Reference_2'];
//     aPPReferanceCode = json['APP_Referance_Code'];
//     userDeviceID = json['User_Device_ID'];
//     constantsBranchs = json['Constants_Branchs'];
//     constantsFromStore = json['Constants_FromStore'];
//     constantsToStore = json['Constants_ToStore'];
//     userDevice = json['User_Device'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = {};
//     data['Code'] = code;
//     data['Entry_Date_Formated'] = entryDateFormated;
//    data['InventoryOperationItems'] = inventoryOperationItems;

//     data['From_Store_ID'] = fromStoreID;
//     data['To_Store_ID'] = toStoreID;
//     data['Store_Name'] = storeName;
//     data['To_Store_Name'] = toStoreName;
//     data['Submit'] = submit;
//     data['ShortagePosted'] = shortagePosted;
//     data['SurplusPosted'] = surplusPosted;
//     data['ID'] = iD;
//     data['Operation_Type'] = operationType;
//     data['Entry_Date'] = entryDate;
//     data['Year'] = year;
//     data['Counter'] = counter;
//     data['Prefix'] = prefix;
//     data['Notes'] = notes;
//     data['Branch_ID'] = branchID;
//     data['Insert_User_ID'] = insertUserID;
//     data['Update_User_ID'] = updateUserID;
//     data['Update_User_Date'] = updateUserDate;
//     data['Insert_User_Date'] = insertUserDate;
//     data['Is_Posted'] = isPosted;
//     data['Source_ID'] = sourceID;
//     data['Source_Reference_1'] = sourceReference1;
//     data['Source_Reference_2'] = sourceReference2;
//     data['APP_Referance_Code'] = aPPReferanceCode;
//     data['User_Device_ID'] = userDeviceID;
//     data['Constants_Branchs'] = constantsBranchs;
//     data['Constants_FromStore'] = constantsFromStore;
//     data['Constants_ToStore'] = constantsToStore;
//     data['User_Device'] = userDevice;
//     return data;
//   }
// }
// class InventoryOperationItem {
//   double? quantity;
//   int? unitId;
//   double? unitPrice;
//   String? itemName;
//   String? itemCode;
//   int? operationItemId;
//   int? operationId;
//   int? itemId;
//   int? storeId;

//   InventoryOperationItem({
//     this.quantity,
//     this.unitId,
//     this.unitPrice,
//     this.itemName,
//     this.itemCode,
//     this.operationItemId,
//     this.operationId,
//     this.itemId,
//     this.storeId,
//   });

//   InventoryOperationItem.fromJson(Map<String, dynamic> json) {
//     quantity = (json['Quantity'] as num?)?.toDouble();
//     unitId = json['Unit_ID'];
//     unitPrice = (json['Unit_Price'] as num?)?.toDouble();
//     itemName = json['Item_Name'];
//     itemCode = json['Item_Code']?.toString();
//     operationItemId = json['Operation_Item_ID'];
//     operationId = json['Operation_ID'];
//     itemId = json['Item_ID'];
//     storeId = json['Store_ID'];
//   }

//   Map<String, dynamic> toJson() {
//     return {
//       'Quantity': quantity,
//       'Unit_ID': unitId,
//       'Unit_Price': unitPrice,
//       'Item_Name': itemName,
//       'Item_Code': itemCode,
//       'Operation_Item_ID': operationItemId,
//       'Operation_ID': operationId,
//       'Item_ID': itemId,
//       'Store_ID': storeId,
//     };
//   }
// }
