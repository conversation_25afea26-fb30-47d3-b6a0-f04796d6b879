import 'package:inventory_application/base/constants/app/app_constants.dart';

extension DoubleFormatting on double {
//---------------------------------------------------------------
  double covertDoubleToMoney(double value) {
    var converted = value.toStringAsFixed(ApplicationConstants.Fixed_Number);
    return double.parse(converted);
  }

//---------------------------------------------------------------
  String covertDoubleToMoneyReturnString(double value) {
    var converted = value.toStringAsFixed(ApplicationConstants.Fixed_Number);
    return converted.isEmpty
        ? 0.toStringAsFixed(ApplicationConstants.Fixed_Number)
        : converted;
  }
//---------------------------------------------------------------
}
