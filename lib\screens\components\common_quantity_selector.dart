import 'dart:async';

import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/double_extension.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';

class QuantitySelector extends StatefulWidget {
  final double initialValue;
  final int? orderMinimumQuantity;
  final int? orderMaximumQuantity;
  final int? quantityStep;
  final int? stockQuantity;
  final String quantityUnit;
  final Function onChangeCount;
  final bool isShowCount;
  final int? refrenceId;
  final ProductDTO model;

  const QuantitySelector({
    super.key,
    required this.initialValue,
    this.orderMaximumQuantity,
    this.orderMinimumQuantity,
    this.quantityStep,
    this.stockQuantity,
    required this.quantityUnit,
    required this.onChangeCount,
    required this.isShowCount,
    this.refrenceId,
    required this.model,
  });

  @override
  // ignore: library_private_types_in_public_api
  _QuantitySelectorState createState() => _QuantitySelectorState();
}

class _QuantitySelectorState extends State<QuantitySelector> {
  late double _counter;
  Timer? timer;

  @override
  void initState() {
    super.initState();
    _counter = widget.initialValue;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final textProvider = widget.model;

    _counter = (textProvider.quantity
            ?.covertDoubleToMoney(textProvider.quantity ?? 0) ??
        0);
  }

  @override
  void didUpdateWidget(QuantitySelector oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update the counter when the model or initialValue changes
    if (widget.model != oldWidget.model ||
        widget.initialValue != oldWidget.initialValue ||
        widget.model.quantity != oldWidget.model.quantity) {
      _counter = (widget.model.quantity
              ?.covertDoubleToMoney(widget.model.quantity ?? 0) ??
          0);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            setState(() {
              if (_counter > (widget.orderMinimumQuantity ?? 0)) {
                _counter -= (widget.orderMinimumQuantity ?? 1);
              }
              widget.onChangeCount(_counter);
            });
          },
          child: Container(
            height: 30,
            width: 30,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: context.primaryColor),
            child: Icon(
              size: 18,
              Icons.remove,
              color: context.onBackground,
            ),
          ),
        ),
        SizedBox(width: context.width * 0.01),
        widget.isShowCount == true
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: () {
                      _showQuantityDialog();
                    },
                    child: Text(
                      _counter.toString(),
                      style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w700,
                          color: context.onSurface),
                    ),
                  ),
                ],
              )
            : const SizedBox.shrink(),
        SizedBox(width: context.width * 0.01),
        InkWell(
          onTap: () {
            setState(() {
              if ((_counter < (widget.stockQuantity ?? 0)) &&
                  (_counter < (widget.orderMaximumQuantity ?? 0))) {
                _counter += (widget.orderMinimumQuantity ?? 1);
              }
              widget.onChangeCount(_counter);
            });
          },
          child: Container(
            height: 30,
            width: 30,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(5),
                color: context.primaryColor),
            child: Icon(
              Icons.add,
              size: 18,
              color: context.onBackground,
            ),
          ),
        ),
      ],
    );
  }

  void _showQuantityDialog() {
    final numberField = TextEditingController();
    numberField.text = _counter.toString();
    double tempCounter = _counter; // Temporary counter to hold the value

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextFormField(
                    controller: numberField,
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    onChanged: (value) {
                      setState(() {
                        tempCounter = double.tryParse(value) ?? tempCounter;
                      });

                      // Start a new timer every time the value changes
                      if (timer != null) {
                        timer!.cancel();
                      }
                      timer = Timer(const Duration(milliseconds: 1200), () {
                        setState(() {
                          numberField.text =
                              '${round(int.parse(tempCounter.toString()))}';
                          tempCounter = double.parse(
                              round(int.parse(tempCounter.toString()))
                                  .toString());
                        });
                      });
                    },
                    decoration: InputDecoration(
                      hintText: 'Enter quantity',
                    ),
                  ),
                ],
              ),
              actions: <Widget>[
                TextButton(
                  child: Text('oK'),
                  onPressed: () {
                    bool isValid = false;
                    if (widget.quantityStep != null) {
                      isValid = tempCounter % widget.orderMinimumQuantity! == 0;
                    } else {
                      isValid = true;
                    }

                    if (!isValid) {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: Text('Warring'),
                            content: Text(
                              'The system will adjust the quantity appropriately based on the available increment amounts.',
                            ),
                            actions: <Widget>[
                              TextButton(
                                child: Text('oK'),
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                              ),
                            ],
                          );
                        },
                      );
                    } else {
                      setState(() {
                        _counter = tempCounter;
                      });
                      widget.onChangeCount(_counter);
                      Navigator.of(context).pop();
                    }
                  },
                ),
                TextButton(
                  child: Text('Cancel'),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      },
    );
  }

  int round(int n) {
    // Smaller multiple
    int a = (n ~/ widget.orderMinimumQuantity!) * widget.orderMinimumQuantity!;

    // Larger multiple
    int b = a + widget.orderMinimumQuantity!;

    // Return of closest of two
    return (n - a >= b - n) ? b : a;
  }
}
