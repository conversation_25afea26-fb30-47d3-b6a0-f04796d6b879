import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';

import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';

import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_response_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';

import 'package:inventory_application/models/dto/response.dart';

import 'package:inventory_application/models/model/inventory_operation_model.dart';

class InventoryOperationStocktakingController with ChangeNotifier {
  List<ProductDTO> selectedStocktakingProduct = [];
  String? returnTransfarCode;
  InventoryOperationModel inventorystocktaking = InventoryOperationModel();
  //---------------------------------------------------------------------------
  Future<String> getStockTakingNumber() async {
    try {
      var number =
          await InventoryOperationCounterGenerator.getNextCounterByType(
              InventoryOperationType.Stocktaking);

      notifyListeners();
      return number;
    } catch (e) {
      print(e);
      return "";
    }
  }

  //---------------------------------------------------------------------------
  Future<bool> repeatStocktakingInventory() async {
    try {
      inventorystocktaking.iD = 0;
      inventorystocktaking.aPPReferanceCode = await getStockTakingNumber();

      var result = await saveInventoryStocktaking(
        mode: inventorystocktaking,
        items: mapListProductDtoToInventoryOperationItem(
            selectedStocktakingProduct),
      );

      if (result.isSuccess) {
        inventorystocktaking = InventoryOperationModel();
        selectedStocktakingProduct.clear();
        notifyListeners();
      }

      return result.isSuccess;
    } catch (e) {
      print("Error in repeatStocktakingInventory: $e");
      return false;
    }
  }

  //---------------------------------------------------------- -----------------
  Future<ResponseResultModel> saveInventoryStocktaking({
    required InventoryOperationModel mode,
    required List<InventoryOperationItems> items,
  }) async {
    try {
      var url = '/InventoryOperation/Manage?transactions_type=Stocktaking';
      final db = InventoryOperationController();

      mode.inventoryOperationItems = items;

      mode.operationType = InventoryOperationType.Stocktaking.index;
      mode.entryDateFormated =
          DateFormat('dd/MM/yyyy', 'en').format(DateTime.now());

      if (mode.iD == null || (mode.iD ?? 0) == 0) {
        mode.entryDate = DateTime.now();
        mode.code = "*";
      }

      if (mode.aPPReferanceCode == null || mode.aPPReferanceCode == "") {
        mode.aPPReferanceCode = await getStockTakingNumber();
      } else {
        if (await InventoryOperationCounterGenerator.checkIfCounterUsed(
            InventoryOperationType.Stocktaking, mode.aPPReferanceCode ?? "")) {
          mode.aPPReferanceCode = await getStockTakingNumber();
        }
      }

      mode.iD ??= 0;

      if (await isThereNetworkConnection() == false) {
        var localId =
            await db.insertOrUpdateInventoryOperation(SqlLiteInvoiceModel(
          data: jsonEncode(mode.toJson()),
          status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
          id: null,
          localCode: mode.aPPReferanceCode,
          type: InventoryOperationType.Stocktaking.name,
        ).toJson());

        mode = InventoryOperationModel();
        items.clear();

        if (mode.iD == 0) {
          await InventoryOperationCounterGenerator.setCounterByTypeAuto(
            type: InventoryOperationType.Stocktaking,
          );
        }

        notifyListeners();
        return ResponseResultModel(
          isSuccess: true,
          data: InvoiceResponseDto(localId: localId),
        );
      }

      // ✅ عمل نسخة مفصولة للإيكوميرس (Deep Copy)
      List<InventoryOperationItems> modelItemsForEcommerce = [
        for (var item in items) item.copy() // تأكد من وجود الدالة copy()
      ];

      // ✅ دمج العناصر بحسب itemID
      final Map<int, InventoryOperationItems> mergedMap = {};
      for (var item in items) {
        if (item.itemID == null) continue;

        if (mergedMap.containsKey(item.itemID)) {
          final existing = mergedMap[item.itemID]!;

          existing.quantity = (existing.quantity ?? 0) + (item.quantity ?? 0);
          existing.balance = (existing.balance ?? 0) + (item.balance ?? 0);
        } else {
          mergedMap[item.itemID!] = item;
        }
      }

      mode.inventoryOperationItems = mergedMap.values.toList();

      var result = await Api.post(
        action: url,
        body: mode.toJson(),
      );

      if (result != null && result.isSuccess) {
        if (mode.iD == 0) {
          await InventoryOperationCounterGenerator
              .setInventoryOperationCounterInServer();
          await InventoryOperationCounterGenerator.setCounterByTypeAuto(
            type: InventoryOperationType.Stocktaking,
          );
        }

        var response = InvoiceResponseDto.fromJson(result.data);
        mode.iD = response.id;
        mode.code = response.code;

        var localId =
            await db.insertOrUpdateInventoryOperation(SqlLiteInvoiceModel(
          data: jsonEncode(mode.toJson()),
          status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
          id: null,
          localCode: mode.aPPReferanceCode,
          type: InventoryOperationType.Stocktaking.name,
        ).toJson());
        response.localId = localId;

        // ✅ إعادة العناصر المفصولة للإيكوميرس
        var modelForEcommerce = mode;
        modelForEcommerce.inventoryOperationItems = modelItemsForEcommerce;

        // await db.saveInventoryOperationToEcommerce(
        //     modelForEcommerce, TransactionTypes.StockTaking);

        await postAferSaveStocktaking(mode.code ?? "");

        mode = InventoryOperationModel();
        items.clear();
        notifyListeners();
        return ResponseResultModel(data: response, isSuccess: true);
      }

      return ResponseResultModel(
          isSuccess: false, message: [result?.message ?? "Unknown error"]);
    } catch (e) {
      print("Error in save Stocktaking: $e");
      return ResponseResultModel(isSuccess: false, message: [e.toString()]);
    }
  }

  Future<bool> postAferSaveStocktaking(String code) async {
    try {
      var mainController = InventoryOperationController();
      var data = await mainController.getInventoryBycode(
          type: "Stocktaking", code: code);
      data?.submit = "Post";

      mainController.editInventoryOperation(
          model: data ?? InventoryOperationModel(), type: "Stocktaking");
      // var suples = data?.inventoryOperationItems
      //     ?.where((item) => (item.balance ?? 0) < (item.quantity ?? 0))
      //     .toList();
      // var shortage = data?.inventoryOperationItems
      //     ?.where((item) => (item.balance ?? 0) > (item.quantity ?? 0))
      //     .toList();
      // if ((suples?.length ?? 0) > 0) {
      //   var modelTosend = data?.copyWith(inventoryOperationItems: suples);
      //   modelTosend?.operationType = InventoryOperationType.Surplus.index;
      //   // modelTosend?.submit = "Submit";

      //   mainController.editInventoryOperation(
      //       model: modelTosend ?? InventoryOperationModel(), type: "Surplus");
      // }

      // if ((shortage?.length ?? 0) > 0) {
      //   var modelTosend = data?.copyWith(inventoryOperationItems: shortage);
      //   modelTosend?.operationType = InventoryOperationType.Shortage.index;

      //   mainController.editInventoryOperation(
      //       model: modelTosend ?? InventoryOperationModel(), type: "Shortage");
      // }

      return true;
    } catch (e) {
      print("Error in postAferSaveStocktaking: $e");
      return false;
    }
  }
}
