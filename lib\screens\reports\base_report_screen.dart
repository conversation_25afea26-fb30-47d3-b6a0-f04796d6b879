import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/helpers/function.dart' as translate;
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/screens/reports/widgets/report_filter_dialog.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

abstract class BaseReportScreen extends StatefulWidget {
  const BaseReportScreen({super.key});
}

abstract class BaseReportScreenState<T extends BaseReportScreen>
    extends State<T> {
  bool _isLoading = false;

  // Abstract methods to be implemented by subclasses
  ReportType get reportType;
  Future<void> generateReport();
  Widget buildReportContent(ReportController reportController);
  String getReportTitle();
  IconData getReportIcon();
  Color getHeaderColor();
  Color getAccentColor();

  @override
  void initState() {
    super.initState();
    // Generate report on screen load
    WidgetsBinding.instance.addPostFrameCallback((_) {
      generateReport();
    });
  }

  // Show filter dialog
  Future<void> _showFilterDialog() async {
    final reportController =
        Provider.of<ReportController>(context, listen: false);

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => ReportFilterDialog(
        initialFilter: reportController.filter,
        selectedReportType: reportType,
      ),
    );

    if (result == true) {
      generateReport();
    }
  }

  // Helper method to get date range text
  String _getDateRangeText(ReportFilterDTO filter) {
    if (filter.fromDate == null || filter.toDate == null) {
      return translate.T('Date range not set');
    }

    final fromDate = DateFormat('yyyy-MM-dd').format(filter.fromDate!);
    final toDate = DateFormat('yyyy-MM-dd').format(filter.toDate!);

    return '$fromDate ${translate.T('to')} $toDate';
  }

  @override
  Widget build(BuildContext context) {
    final reportController = Provider.of<ReportController>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text(getReportTitle()),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_alt),
            onPressed: _showFilterDialog,
            tooltip: translate.T('Filter Report'),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: generateReport,
            tooltip: translate.T('Refresh Report'),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildReportView(reportController),
    );
  }

  // Build the report view
  Widget _buildReportView(ReportController reportController) {
    return Column(
      children: [
        // Enhanced report header with date range info
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [getHeaderColor(), getAccentColor()],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: getHeaderColor().withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      getReportIcon(),
                      size: 24,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          getReportTitle(),
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                _getDateRangeText(reportController.filter),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white.withOpacity(0.9),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: _showFilterDialog,
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.2),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.date_range,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      translate.T('Filter'),
                                      style: const TextStyle(
                                        fontSize: 12,
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),

        // Report content - takes remaining space
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.shade300,
                  blurRadius: 8,
                  offset: const Offset(0, -3),
                  spreadRadius: 1,
                ),
              ],
            ),
            margin: const EdgeInsets.only(top: 20),
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
              child: buildReportContent(reportController),
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to set loading state
  void setLoading(bool loading) {
    setState(() {
      _isLoading = loading;
    });
  }
}
