@echo off 
title ERP Application - Setup 
 
echo ================================================ 
echo   ERP Application - Installation 
echo ================================================ 
echo. 
echo This wizard will install the ERP Application on your computer. 
echo. 
pause 
 
echo Step 1: Installing Visual C++ Redistributable... 
echo This is required for the application to run properly. 
echo If you already have it installed, this step will be skipped. 
 
start /wait redist\vc_redist.x64.exe /passive /norestart 
echo Visual C++ Redistributable installation completed. 
echo. 
 
echo Step 2: Creating application directories... 
 
set INSTALL_DIR=%USERPROFILE%\Documents\ERP-Application 
set DATA_DIR=%USERPROFILE%\Documents\ERP-Data 
 
if not exist "%DATA_DIR%" mkdir "%DATA_DIR%" 
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%" 
if not exist "%USERPROFILE%\Documents\ERP-Logs" mkdir "%USERPROFILE%\Documents\ERP-Logs" 
echo Directories created successfully. 
echo. 
 
echo Step 3: Copying application files... 
 
xcopy /E /I /Y app\* "%INSTALL_DIR%" 
echo Application files copied successfully. 
echo. 
 
echo Step 4: Setting file permissions... 
echo Setting permissions on application files... 
icacls "%INSTALL_DIR%" /grant "%USERNAME%":(OI)(CI)F /T 
icacls "%DATA_DIR%" /grant "%USERNAME%":(OI)(CI)F /T 
echo Permissions set successfully. 
echo. 
 
echo Step 5: Creating launcher scripts... 
 
REM Create standard launcher 
echo @echo off > "%INSTALL_DIR%\run_app.bat" 
echo title ERP Application >> "%INSTALL_DIR%\run_app.bat" 
echo. >> "%INSTALL_DIR%\run_app.bat" 
echo if not exist "%USERPROFILE%\Documents\ERP-Data" mkdir "%USERPROFILE%\Documents\ERP-Data" >> "%INSTALL_DIR%\run_app.bat" 
echo. >> "%INSTALL_DIR%\run_app.bat" 
echo taskkill /F /IM inventory_application.exe 2>nul >> "%INSTALL_DIR%\run_app.bat" 
echo. >> "%INSTALL_DIR%\run_app.bat" 
echo cd /d "%~dp0" >> "%INSTALL_DIR%\run_app.bat" 
echo echo Starting application... >> "%INSTALL_DIR%\run_app.bat" 
echo start "" "%~dp0inventory_application.exe" --verbose >> "%INSTALL_DIR%\run_app.bat" 
echo. >> "%INSTALL_DIR%\run_app.bat" 
 
REM Create admin launcher 
echo @echo off > "%INSTALL_DIR%\run_as_admin.bat" 
echo title ERP Application - Admin Mode >> "%INSTALL_DIR%\run_as_admin.bat" 
echo. >> "%INSTALL_DIR%\run_as_admin.bat" 
echo echo Running ERP Application as Administrator... >> "%INSTALL_DIR%\run_as_admin.bat" 
echo echo. >> "%INSTALL_DIR%\run_as_admin.bat" 
echo echo Set UAC = CreateObject^("Shell.Application"^) > "%TEMP%\RunAsAdmin.vbs" >> "%INSTALL_DIR%\run_as_admin.bat" 
echo echo UAC.ShellExecute "%USERPROFILE%\Documents\ERP-Application\inventory_application.exe", "--verbose", "", "runas", 1 >> "%TEMP%\RunAsAdmin.vbs" >> "%INSTALL_DIR%\run_as_admin.bat" 
echo cscript //NoLogo "%TEMP%\RunAsAdmin.vbs" >> "%INSTALL_DIR%\run_as_admin.bat" 
echo del "%TEMP%\RunAsAdmin.vbs" >> "%INSTALL_DIR%\run_as_admin.bat" 
echo. >> "%INSTALL_DIR%\run_as_admin.bat" 
echo echo Application launched with administrative privileges. >> "%INSTALL_DIR%\run_as_admin.bat" 
echo pause >> "%INSTALL_DIR%\run_as_admin.bat" 
echo. >> "%INSTALL_DIR%\run_as_admin.bat" 
echo Launcher scripts created successfully. 
echo. 
 
echo Step 6: Creating desktop shortcuts... 
 
REM Create normal shortcut 
echo Set oWS = WScript.CreateObject^("WScript.Shell"^) > "%TEMP%\CreateShortcut.vbs" 
echo sLinkFile = "%USERPROFILE%\Desktop\ERP-Application.lnk" >> "%TEMP%\CreateShortcut.vbs" 
echo Set oLink = oWS.CreateShortcut^(sLinkFile^) >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.TargetPath = "%INSTALL_DIR%\run_app.bat" >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.IconLocation = "%INSTALL_DIR%\inventory_application.exe,0" >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.Description = "ERP Application" >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs" 
 
REM Create admin shortcut 
echo sLinkFile = "%USERPROFILE%\Desktop\ERP-Application (Admin).lnk" >> "%TEMP%\CreateShortcut.vbs" 
echo Set oLink = oWS.CreateShortcut^(sLinkFile^) >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.TargetPath = "%INSTALL_DIR%\run_as_admin.bat" >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.WorkingDirectory = "%INSTALL_DIR%" >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.IconLocation = "%INSTALL_DIR%\inventory_application.exe,0" >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.Description = "ERP Application (Administrator Mode)" >> "%TEMP%\CreateShortcut.vbs" 
echo oLink.Save >> "%TEMP%\CreateShortcut.vbs" 
cscript //NoLogo "%TEMP%\CreateShortcut.vbs" 
del "%TEMP%\CreateShortcut.vbs" 
echo Desktop shortcuts created successfully. 
echo. 
 
echo ================================================ 
echo Installation completed successfully! 
echo. 
echo You can now run the application using the desktop shortcuts: 
echo  - "ERP-Application" for normal mode 
echo  - "ERP-Application (Admin)" for administrator mode 
echo. 
echo If you encounter any issues, try running the application in administrator mode. 
echo ================================================ 
echo. 
pause 
