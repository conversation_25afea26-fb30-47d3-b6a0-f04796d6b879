import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:vector_math/vector_math.dart' as vector;
import '../../providers/warehouse_planner_provider.dart';
import '../../controllers/product_controller.dart';
import '../../models/dto/products/product_dto.dart';
import '../../models/warehouse_planner/warehouse_layout.dart';
import '../../models/warehouse_planner/editor_state.dart';
import 'widgets/warehouse_editor_canvas.dart';
import 'debug_warehouse_info.dart';

/// شاشة العرض للعامل - نسخة مبسطة من Admin بدون تعديل
class WorkerWarehouseDisplay extends StatefulWidget {
  const WorkerWarehouseDisplay({Key? key}) : super(key: key);

  @override
  State<WorkerWarehouseDisplay> createState() => _WorkerWarehouseDisplayState();
}

class _WorkerWarehouseDisplayState extends State<WorkerWarehouseDisplay> {
  final TextEditingController _searchController = TextEditingController();
  final ProductController _productController = ProductController();

  List<WarehouseLayout> _availableLayouts = [];
  WarehouseLayout? _selectedLayout;
  bool _isLoadingLayouts = true;

  List<ProductDTO> _searchResults = [];
  ProductDTO? _selectedProduct;
  List<ProductLocation> _productLocations = [];
  String? _highlightedShelfId;
  bool _isSearching = false;

  // إعدادات العرض (مثل Admin بدون تعديل)
  late EditorState _editorState;
  late LayoutSettings _settings;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
    _loadAvailableLayouts();
    _loadInitialProducts();
  }

  void _initializeSettings() {
    // إعدادات العرض الافتراضية (مثل Admin)
    _settings = LayoutSettings();

    // حالة المحرر للعرض فقط
    _editorState = EditorState(
      mode: EditMode.select, // دائماً في وضع Select
      selectedObjectId: null,
      selectedObjectType: null,
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialProducts() async {
    try {
      // تحميل المنتجات الأولية بدون بحث
      await _productController.getItems(resetAndRefresh: true);
      print('تم تحميل ${_productController.realProductList.length} منتج');
    } catch (e) {
      print('Error loading initial products: $e');
    }
  }

  // نفس التحميل من Admin
  Future<void> _loadAvailableLayouts() async {
    setState(() => _isLoadingLayouts = true);

    try {
      final provider =
          Provider.of<WarehousePlannerProvider>(context, listen: false);
      List<WarehouseLayout> savedLayouts = [];

      try {
        final savedFiles = await provider.getSavedWarehouses();
        for (final file in savedFiles) {
          try {
            final fileAsFile = File(file.path);
            if (!await fileAsFile.exists()) continue;

            final fileName = file.path.split('/').last;
            final layoutId = fileName.replaceAll('.json', '');
            final fileContent = await fileAsFile.readAsString();

            if (fileContent.isEmpty) continue;

            try {
              final jsonData = json.decode(fileContent);
              if (jsonData == null || !jsonData.containsKey('id')) continue;
            } catch (e) {
              continue;
            }

            await provider.loadWarehouse(layoutId);
            if (provider.currentLayout != null) {
              savedLayouts.add(provider.currentLayout!);
            }
          } catch (e) {
            print('Error loading layout from ${file.path}: $e');
            continue;
          }
        }
      } catch (e) {
        print('Error loading saved warehouses: $e');
      }

      if (savedLayouts.isEmpty) {
        final testLayout = _createTestLayout();
        savedLayouts = [testLayout];
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('لا توجد مخططات محفوظة، تم إنشاء مخطط تجريبي'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }

      setState(() {
        _availableLayouts = savedLayouts;
        _isLoadingLayouts = false;
      });

      if (savedLayouts.isNotEmpty) {
        _selectLayout(savedLayouts.first);
      }
    } catch (e) {
      print('Error in _loadAvailableLayouts: $e');
      setState(() => _isLoadingLayouts = false);
      final testLayout = _createTestLayout();
      setState(() {
        _availableLayouts = [testLayout];
        _isLoadingLayouts = false;
      });
      _selectLayout(testLayout);
    }
  }

  WarehouseLayout _createTestLayout() {
    final layout = WarehouseLayout(
      id: 'test_layout',
      name: 'مخطط تجريبي',
      description: 'مخطط تجريبي للعرض',
      width: 1000,
      height: 800,
      linkedWarehouseId: 1,
      linkedWarehouseName: 'المستودع الرئيسي',
    );

    // إضافة خزائن تجريبية مع منتجات
    _addTestShelvesWithProducts(layout);

    return layout;
  }

  void _addTestShelvesWithProducts(WarehouseLayout layout) {
    // خزانة 1: الأدوية العامة
    final shelf1 = Shelf(
      id: 'shelf_1',
      name: 'خزانة الأدوية العامة',
      position: vector.Vector2(200, 200),
      width: 150,
      depth: 80,
      height: 200,
      type: ShelfType.standard,
      levels: 4,
      slotsPerLevel: 3,
    );

    // إضافة منتجات في خزانة 1
    shelf1.bins = [
      // المستوى الأول
      Bin(
        id: 'bin_1_1',
        shelfId: shelf1.id,
        level: 0,
        slot: 0,
        position: vector.Vector2(0, 0),
        productId: 1, // باراسيتامول
        productName: 'باراسيتامول 500 مج',
        quantity: 50,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      Bin(
        id: 'bin_1_2',
        shelfId: shelf1.id,
        level: 0,
        slot: 1,
        position: vector.Vector2(1, 0),
        productId: 2, // ايبوبروفين
        productName: 'ايبوبروفين 200 مج',
        quantity: 30,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      Bin(
        id: 'bin_1_3',
        shelfId: shelf1.id,
        level: 0,
        slot: 2,
        position: vector.Vector2(2, 0),
        productId: 3, // أموكسيسيلين
        productName: 'أموكسيسيلين 250 مج',
        quantity: 25,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      // المستوى الثاني
      Bin(
        id: 'bin_1_4',
        shelfId: shelf1.id,
        level: 1,
        slot: 0,
        position: vector.Vector2(0, 1),
        productId: 1, // باراسيتامول مرة أخرى
        productName: 'باراسيتامول 500 مج',
        quantity: 75,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      Bin(
        id: 'bin_1_5',
        shelfId: shelf1.id,
        level: 1,
        slot: 1,
        position: vector.Vector2(1, 1),
        productId: 4, // ديكلوفيناك
        productName: 'ديكلوفيناك جل',
        quantity: 20,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      Bin(
        id: 'bin_1_6',
        shelfId: shelf1.id,
        level: 1,
        slot: 2,
        position: vector.Vector2(2, 1),
        productId: 5, // فيتامين د3
        productName: 'فيتامين د3',
        quantity: 60,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      // باقي الخانات فارغة
      ...List.generate(
          6,
          (index) => Bin(
                id: 'bin_1_empty_${index + 7}',
                shelfId: shelf1.id,
                level: (index ~/ 3) + 2,
                slot: index % 3,
                position: vector.Vector2(
                    (index % 3).toDouble(), ((index ~/ 3) + 2).toDouble()),
                quantity: 0,
                maxCapacity: 100,
                status: BinStatus.empty,
              )),
    ];

    // خزانة 2: أدوية القلب
    final shelf2 = Shelf(
      id: 'shelf_2',
      name: 'خزانة أدوية القلب',
      position: vector.Vector2(400, 200),
      width: 150,
      depth: 80,
      height: 200,
      type: ShelfType.controlled,
      levels: 3,
      slotsPerLevel: 4,
    );

    // إضافة منتجات في خزانة 2
    shelf2.bins = [
      Bin(
        id: 'bin_2_1',
        shelfId: shelf2.id,
        level: 0,
        slot: 0,
        position: vector.Vector2(0, 0),
        productId: 2, // ايبوبروفين مرة أخرى
        productName: 'ايبوبروفين 200 مج',
        quantity: 40,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      Bin(
        id: 'bin_2_2',
        shelfId: shelf2.id,
        level: 0,
        slot: 1,
        position: vector.Vector2(1, 0),
        productId: 6, // دواء جديد
        productName: 'أتورفاستاتين 20 مج',
        quantity: 35,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      // باقي الخانات فارغة
      ...List.generate(
          10,
          (index) => Bin(
                id: 'bin_2_empty_${index + 3}',
                shelfId: shelf2.id,
                level: (index + 2) ~/ 4,
                slot: (index + 2) % 4,
                position: vector.Vector2(((index + 2) % 4).toDouble(),
                    ((index + 2) ~/ 4).toDouble()),
                quantity: 0,
                maxCapacity: 100,
                status: BinStatus.empty,
              )),
    ];

    // خزانة 3: الفيتامينات
    final shelf3 = Shelf(
      id: 'shelf_3',
      name: 'خزانة الفيتامينات',
      position: vector.Vector2(600, 300),
      width: 120,
      depth: 60,
      height: 180,
      type: ShelfType.standard,
      levels: 3,
      slotsPerLevel: 2,
    );

    // إضافة منتجات في خزانة 3
    shelf3.bins = [
      Bin(
        id: 'bin_3_1',
        shelfId: shelf3.id,
        level: 0,
        slot: 0,
        position: vector.Vector2(0, 0),
        productId: 5, // فيتامين د3 مرة أخرى
        productName: 'فيتامين د3',
        quantity: 80,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      Bin(
        id: 'bin_3_2',
        shelfId: shelf3.id,
        level: 0,
        slot: 1,
        position: vector.Vector2(1, 0),
        productId: 7, // فيتامين ب12
        productName: 'فيتامين ب12',
        quantity: 45,
        maxCapacity: 100,
        status: BinStatus.normal,
      ),
      // باقي الخانات فارغة
      ...List.generate(
          4,
          (index) => Bin(
                id: 'bin_3_empty_${index + 3}',
                shelfId: shelf3.id,
                level: (index + 2) ~/ 2,
                slot: (index + 2) % 2,
                position: vector.Vector2(((index + 2) % 2).toDouble(),
                    ((index + 2) ~/ 2).toDouble()),
                quantity: 0,
                maxCapacity: 100,
                status: BinStatus.empty,
              )),
    ];

    // إضافة الخزائن للمخطط
    layout.shelves.addAll([shelf1, shelf2, shelf3]);

    print(
        'تم إنشاء مخطط تجريبي مع ${layout.shelves.length} خزائن تحتوي على منتجات');
  }

  void _selectLayout(WarehouseLayout layout) {
    setState(() {
      _selectedLayout = layout;
      _clearSearch();
    });
  }

  void _clearSearch() {
    setState(() {
      _searchResults.clear();
      _highlightedShelfId = null;
      _selectedProduct = null;
      _productLocations.clear();
    });
    _searchController.clear();
  }

  Future<void> _searchProducts(String query) async {
    if (query.trim().isEmpty) {
      _clearSearch();
      return;
    }

    setState(() => _isSearching = true);

    try {
      // البحث في قاعدة البيانات مع استخدام معطل البحث
      await _productController.getItems(
        resetAndRefresh: true,
        search: query,
      );

      // الحصول على النتائج من realProductList
      final searchResults = _productController.realProductList;

      setState(() {
        _searchResults = searchResults;
        _isSearching = false;
      });

      print('تم العثور على ${searchResults.length} منتج للبحث: $query');
    } catch (e) {
      print('Error searching products: $e');
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في البحث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _selectProduct(ProductDTO product) {
    if (_selectedLayout == null) return;

    final locations = <ProductLocation>[];

    // البحث في جميع الخزائن
    for (final shelf in _selectedLayout!.shelves) {
      for (final bin in shelf.bins) {
        if (bin.productId == product.id) {
          locations.add(ProductLocation(
            shelfId: shelf.id,
            shelfName: shelf.name,
            level: bin.level,
            slot: bin.slot,
            quantity: bin.quantity.toDouble(),
            binId: bin.id,
          ));
        }
      }
    }

    setState(() {
      _selectedProduct = product;
      _productLocations = locations;
      _highlightedShelfId =
          locations.isNotEmpty ? locations.first.shelfId : null;

      // تحديث حالة المحرر لإظهار الخزانة المحددة
      if (_highlightedShelfId != null) {
        _editorState = _editorState.copyWith(
          selectedObjectId: _highlightedShelfId,
          selectedObjectType: ObjectType.shelf,
        );
      }
    });

    if (locations.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('المنتج "${product.title}" غير موجود في هذا المخطط'),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 4),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              'تم العثور على "${product.title}" في ${locations.length} موقع'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  void _highlightLocation(ProductLocation location) {
    setState(() {
      _highlightedShelfId = location.shelfId;

      // تحديث حالة المحرر لإظهار الخزانة المحددة
      _editorState = _editorState.copyWith(
        selectedObjectId: location.shelfId,
        selectedObjectType: ObjectType.shelf,
      );
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'الخزانة: ${location.shelfName} | المستوى: ${location.level + 1} | الخانة: ${location.slot + 1}',
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // نفس اختيار الملفات من Admin
  Future<void> _selectLayoutFromFile() async {
    try {
      final provider =
          Provider.of<WarehousePlannerProvider>(context, listen: false);
      final saveDir = await provider.getSaveDirectory();
      final dir = Directory(saveDir);

      List<File> jsonFiles = [];
      if (await dir.exists()) {
        await for (final entity in dir.list()) {
          if (entity is File && entity.path.endsWith('.json')) {
            jsonFiles.add(entity);
          }
        }
      }

      if (jsonFiles.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('لا توجد ملفات مخططات في المجلد:\n$saveDir'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 4),
          ),
        );
        return;
      }

      _showFileSelectionDialog(jsonFiles);
    } catch (e) {
      print('Error selecting layout file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في البحث عن الملفات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showFileSelectionDialog(List<File> files) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر ملف المخطط'),
        content: Container(
          width: double.maxFinite,
          height: 400,
          child: ListView.builder(
            itemCount: files.length,
            itemBuilder: (context, index) {
              final file = files[index];
              final fileName =
                  file.path.split('/').last.replaceAll('.json', '');
              return Card(
                child: ListTile(
                  leading: const Icon(Icons.description, color: Colors.blue),
                  title: Text(fileName),
                  subtitle: FutureBuilder<String>(
                    future: _getFileInfo(file),
                    builder: (context, snapshot) {
                      if (snapshot.hasData) {
                        return Text(snapshot.data!);
                      }
                      return const Text('جاري التحميل...');
                    },
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    _loadLayoutFromFile(file);
                  },
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Future<String> _getFileInfo(File file) async {
    try {
      final stat = await file.stat();
      final size = (stat.size / 1024).round();
      final modified = stat.modified;
      return 'الحجم: ${size}KB | آخر تعديل: ${modified.day}/${modified.month}';
    } catch (e) {
      return 'خطأ في قراءة المعلومات';
    }
  }

  Future<void> _loadLayoutFromFile(File file) async {
    try {
      final content = await file.readAsString();
      final jsonData = json.decode(content);
      final layout = WarehouseLayout.fromJson(jsonData);

      setState(() {
        if (!_availableLayouts.any((l) => l.id == layout.id)) {
          _availableLayouts.add(layout);
        }
      });

      _selectLayout(layout);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تحميل المخطط: ${layout.name}'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      print('Error loading layout from file: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحميل الملف: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _openLayoutsFolder() async {
    try {
      final provider =
          Provider.of<WarehousePlannerProvider>(context, listen: false);
      final saveDir = await provider.getSaveDirectory();
      final dir = Directory(saveDir);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      if (Platform.isWindows) {
        await Process.run('explorer', [saveDir.replaceAll('/', '\\')]);
      } else if (Platform.isMacOS) {
        await Process.run('open', [saveDir]);
      } else if (Platform.isLinux) {
        await Process.run('xdg-open', [saveDir]);
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم فتح مجلد المخططات:\n$saveDir'),
          backgroundColor: Colors.blue,
          duration: const Duration(seconds: 4),
        ),
      );
    } catch (e) {
      print('Error opening layouts folder: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في فتح المجلد: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _onShelfDoubleClick(String shelfId) {
    if (_selectedLayout == null) return;

    final shelf = _selectedLayout!.shelves.cast<Shelf?>().firstWhere(
          (s) => s?.id == shelfId,
          orElse: () => null,
        );

    if (shelf != null) {
      _showShelfDetails(shelf);
    }
  }

  void _showShelfDetails(Shelf shelf) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الخزانة: ${shelf.name}'),
        content: Container(
          width: double.maxFinite,
          height: 400,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('المعرف: ${shelf.id}'),
              Text(
                  'الأبعاد: ${shelf.width.toInt()} × ${shelf.depth.toInt()} × ${shelf.height.toInt()} سم'),
              Text('المستويات: ${shelf.levels}'),
              Text('الخانات بكل مستوى: ${shelf.slotsPerLevel}'),
              const SizedBox(height: 16),
              const Text('محتويات الخزانة:',
                  style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount: shelf.bins.length,
                  itemBuilder: (context, index) {
                    final bin = shelf.bins[index];
                    if (bin.productId == null || bin.productId! <= 0) {
                      return Container(); // خانة فارغة
                    }

                    return Card(
                      child: ListTile(
                        title: Text(bin.productName ?? 'منتج غير معروف'),
                        subtitle: Text(
                            'المستوى: ${bin.level + 1} | الخانة: ${bin.slot + 1} | الكمية: ${bin.quantity}'),
                        trailing: Icon(
                          Icons.inventory,
                          color: bin.quantity > 5
                              ? Colors.green
                              : bin.quantity > 0
                                  ? Colors.orange
                                  : Colors.red,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _isLoadingLayouts
          ? const Center(child: CircularProgressIndicator())
          : _availableLayouts.isEmpty
              ? _buildEmptyState()
              : _selectedLayout == null
                  ? _buildSelectLayoutState()
                  : _buildMainContent(),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    final isPhone = MediaQuery.of(context).size.width <= 600;

    return AppBar(
      title: Text(
        'عرض المستودع - العامل',
        style: TextStyle(fontSize: isPhone ? 16 : 20),
      ),
      backgroundColor: Colors.blue.shade700,
      foregroundColor: Colors.white,
      actions: [
        if (!isPhone) ...[
          // عرض كامل للأجهزة اللوحية
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث',
            onPressed: _loadAvailableLayouts,
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.folder_open),
            tooltip: 'اختيار مخطط',
            onSelected: (value) {
              if (value == 'file') {
                _selectLayoutFromFile();
              } else if (value == 'folder') {
                _openLayoutsFolder();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'file',
                child: Row(
                  children: [
                    Icon(Icons.description),
                    SizedBox(width: 8),
                    Text('اختيار ملف مخطط'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'folder',
                child: Row(
                  children: [
                    Icon(Icons.folder),
                    SizedBox(width: 8),
                    Text('فتح مجلد المخططات'),
                  ],
                ),
              ),
            ],
          ),
          if (_availableLayouts.length > 1)
            PopupMenuButton<WarehouseLayout>(
              icon: const Icon(Icons.warehouse),
              tooltip: 'تغيير المستودع',
              onSelected: _selectLayout,
              itemBuilder: (context) => _availableLayouts
                  .map((layout) => PopupMenuItem<WarehouseLayout>(
                        value: layout,
                        child: Row(
                          children: [
                            Icon(
                              layout.id == _selectedLayout?.id
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_unchecked,
                              color: Colors.blue,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                layout.name,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ))
                  .toList(),
            ),
        ] else ...[
          // عرض مُجمع للهواتف
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'refresh':
                  _loadAvailableLayouts();
                  break;
                case 'file':
                  _selectLayoutFromFile();
                  break;
                case 'folder':
                  _openLayoutsFolder();
                  break;
                case 'debug':
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const DebugWarehouseInfo(),
                    ),
                  );
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem<String>(
                value: 'refresh',
                child: Row(
                  children: [
                    Icon(Icons.refresh),
                    SizedBox(width: 8),
                    Text('تحديث'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'file',
                child: Row(
                  children: [
                    Icon(Icons.description),
                    SizedBox(width: 8),
                    Text('اختيار ملف مخطط'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'folder',
                child: Row(
                  children: [
                    Icon(Icons.folder),
                    SizedBox(width: 8),
                    Text('فتح مجلد المخططات'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'debug',
                child: Row(
                  children: [
                    Icon(Icons.bug_report),
                    SizedBox(width: 8),
                    Text('تشخيص المشاكل'),
                  ],
                ),
              ),
            ],
          ),
          // قائمة المستودعات للهاتف
          if (_availableLayouts.length > 1)
            PopupMenuButton<WarehouseLayout>(
              icon: const Icon(Icons.warehouse),
              onSelected: _selectLayout,
              itemBuilder: (context) => _availableLayouts
                  .map((layout) => PopupMenuItem<WarehouseLayout>(
                        value: layout,
                        child: Row(
                          children: [
                            Icon(
                              layout.id == _selectedLayout?.id
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_unchecked,
                              color: Colors.blue,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                layout.name,
                                overflow: TextOverflow.ellipsis,
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ))
                  .toList(),
            ),
        ],
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.warehouse, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد مخططات متاحة',
            style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _selectLayoutFromFile,
            icon: const Icon(Icons.description),
            label: const Text('اختيار ملف مخطط'),
          ),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: _openLayoutsFolder,
            icon: const Icon(Icons.folder),
            label: const Text('فتح مجلد المخططات'),
          ),
          const SizedBox(height: 8),
          ElevatedButton.icon(
            onPressed: _loadAvailableLayouts,
            icon: const Icon(Icons.refresh),
            label: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectLayoutState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.select_all, size: 64, color: Colors.blue),
          const SizedBox(height: 16),
          const Text(
            'اختر مخططاً لعرضه',
            style: TextStyle(fontSize: 18),
          ),
          const SizedBox(height: 24),
          ...(_availableLayouts.map((layout) => Container(
                margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 32),
                child: ElevatedButton(
                  onPressed: () => _selectLayout(layout),
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 48),
                  ),
                  child: Text(layout.name),
                ),
              ))),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isTablet = constraints.maxWidth > 600;

        if (isTablet) {
          // عرض للجهاز اللوحي/سطح المكتب
          return Row(
            children: [
              // لوحة البحث اليسرى
              Container(
                width: 350,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border:
                      Border(right: BorderSide(color: Colors.grey.shade300)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 5,
                    ),
                  ],
                ),
                child: _buildSearchPanel(),
              ),
              // Canvas العرض الرئيسي
              Expanded(child: _buildCanvasArea()),
            ],
          );
        } else {
          // عرض للهاتف
          return Column(
            children: [
              // لوحة البحث في الأعلى
              Container(
                height: 280,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border:
                      Border(bottom: BorderSide(color: Colors.grey.shade300)),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 5,
                    ),
                  ],
                ),
                child: _buildSearchPanel(),
              ),
              // Canvas العرض الرئيسي
              Expanded(child: _buildCanvasArea()),
            ],
          );
        }
      },
    );
  }

  Widget _buildSearchPanel() {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isPhone = MediaQuery.of(context).size.width <= 600;

        return Column(
          children: [
            // شريط البحث
            Container(
              padding: EdgeInsets.all(isPhone ? 12 : 16),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'البحث عن المنتجات',
                    style: TextStyle(
                      fontSize: isPhone ? 14 : 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  SizedBox(height: isPhone ? 6 : 8),
                  TextField(
                    controller: _searchController,
                    style: TextStyle(fontSize: isPhone ? 14 : 16),
                    decoration: InputDecoration(
                      hintText: 'ادخل اسم المنتج أو الرمز...',
                      hintStyle: TextStyle(fontSize: isPhone ? 12 : 14),
                      prefixIcon: Icon(Icons.search, size: isPhone ? 20 : 24),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(Icons.clear, size: isPhone ? 20 : 24),
                              onPressed: _clearSearch,
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: isPhone ? 12 : 16,
                        vertical: isPhone ? 8 : 12,
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {});
                      if (value.trim().isNotEmpty) {
                        _searchProducts(value);
                      } else {
                        _clearSearch();
                      }
                    },
                  ),
                ],
              ),
            ),

            // نتائج البحث
            Expanded(child: _buildSearchResults()),
          ],
        );
      },
    );
  }

  Widget _buildSearchResults() {
    if (_isSearching) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_searchController.text.trim().isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'ابدأ بكتابة اسم المنتج للبحث',
              style: TextStyle(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 48, color: Colors.grey.shade400),
            const SizedBox(height: 16),
            Text(
              'لا توجد نتائج للبحث عن "${_searchController.text}"',
              style: TextStyle(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'تأكد من كتابة الاسم أو الرمز بشكل صحيح',
              style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // عداد النتائج
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
          ),
          child: Row(
            children: [
              Icon(Icons.search, size: 16, color: Colors.blue.shade600),
              const SizedBox(width: 8),
              Text(
                'عُثر على ${_searchResults.length} منتج',
                style: TextStyle(
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        // قائمة نتائج البحث
        Expanded(
          child: LayoutBuilder(
            builder: (context, constraints) {
              final isPhone = MediaQuery.of(context).size.width <= 600;

              return ListView.builder(
                itemCount: _searchResults.length,
                itemBuilder: (context, index) {
                  final product = _searchResults[index];
                  final isSelected = _selectedProduct?.id == product.id;

                  return Card(
                    margin: EdgeInsets.symmetric(
                      horizontal: isPhone ? 6 : 8,
                      vertical: isPhone ? 3 : 4,
                    ),
                    elevation: isSelected ? 4 : 1,
                    color: isSelected ? Colors.blue.shade50 : null,
                    child: ListTile(
                      dense: isPhone,
                      leading: CircleAvatar(
                        radius: isPhone ? 16 : 20,
                        backgroundColor: Colors.blue.shade100,
                        child: Text(
                          (product.title?.isNotEmpty ?? false)
                              ? product.title![0]
                              : '؟',
                          style: TextStyle(
                            color: Colors.blue.shade800,
                            fontSize: isPhone ? 12 : 14,
                          ),
                        ),
                      ),
                      title: Text(
                        product.title ?? 'منتج غير معروف',
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.bold : null,
                          fontSize: isPhone ? 13 : 15,
                        ),
                        maxLines: isPhone ? 1 : 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'الرمز: ${product.id}',
                            style: TextStyle(fontSize: isPhone ? 11 : 13),
                          ),
                          if (product.quantity != null)
                            Text(
                              'الكمية: ${product.quantity}',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                                fontSize: isPhone ? 10 : 12,
                              ),
                            ),
                        ],
                      ),
                      trailing: isSelected
                          ? Icon(
                              Icons.check_circle,
                              color: Colors.blue.shade600,
                              size: isPhone ? 20 : 24,
                            )
                          : Icon(
                              Icons.arrow_forward_ios,
                              size: isPhone ? 14 : 16,
                            ),
                      onTap: () => _selectProduct(product),
                    ),
                  );
                },
              );
            },
          ),
        ),

        // معلومات المنتج المحدد
        if (_selectedProduct != null) _buildSelectedProductInfo(),
      ],
    );
  }

  Widget _buildSelectedProductInfo() {
    if (_selectedProduct == null) return Container();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        border: Border(top: BorderSide(color: Colors.grey.shade200)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.inventory, color: Colors.green.shade700),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _selectedProduct!.title ?? 'منتج غير معروف',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade800,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          if (_productLocations.isNotEmpty) ...[
            Text(
              'المواقع الموجود بها (${_productLocations.length}):',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.green.shade700,
              ),
            ),
            const SizedBox(height: 8),
            ...(_productLocations.map((location) => Container(
                  margin: const EdgeInsets.only(bottom: 4),
                  child: InkWell(
                    onTap: () => _highlightLocation(location),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _highlightedShelfId == location.shelfId
                            ? Colors.blue.shade100
                            : Colors.white,
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 16,
                            color: _highlightedShelfId == location.shelfId
                                ? Colors.blue.shade700
                                : Colors.grey.shade600,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '${location.shelfName} - مستوى ${location.level + 1} - خانة ${location.slot + 1}',
                              style: TextStyle(
                                fontSize: 12,
                                color: _highlightedShelfId == location.shelfId
                                    ? Colors.blue.shade800
                                    : Colors.grey.shade700,
                              ),
                            ),
                          ),
                          Text(
                            '${location.quantity.toInt()}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: _highlightedShelfId == location.shelfId
                                  ? Colors.blue.shade700
                                  : Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ))),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.shade100,
                border: Border.all(color: Colors.orange.shade300),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, size: 16, color: Colors.orange.shade700),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'المنتج غير موجود في هذا المخطط',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCanvasArea() {
    if (_selectedLayout == null) {
      return const Center(child: Text('لا يوجد مخطط محدد'));
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: WarehouseEditorCanvas(
        layout: _selectedLayout!,
        editorState: _editorState,
        settings: _settings,
        // Callbacks فارغة للعرض فقط
        onStateUpdate: (state) {}, // بدون تحديث
        onWallDrawStart: (pos) {}, // بدون رسم
        onWallDrawAdd: (pos) {}, // بدون إضافة
        onWallDrawFinish: () {}, // بدون إنهاء
        onWallDelete: (id) {}, // بدون حذف
        onEntranceAdd: (pos) {}, // بدون إضافة مداخل
        onShelfAdd: (pos, type) {}, // بدون إضافة خزائن
        onShelfMove: (id, pos) {}, // بدون نقل
        onShelfRotate: (id, rot) {}, // بدون دوران
        onObjectSelect: (id, type) {
          // فقط تحديد للعرض
          setState(() {
            _editorState = _editorState.copyWith(
              selectedObjectId: id,
              selectedObjectType: type,
            );
            // إذا كانت الخزانة محددة، أظهر تفاصيلها
            if (type == ObjectType.shelf && id != null) {
              final shelf = _selectedLayout!.shelves.cast<Shelf?>().firstWhere(
                    (s) => s?.id == id,
                    orElse: () => null,
                  );
              if (shelf != null) {
                _showShelfDetails(shelf);
              }
            }
          });
        },
        onPreviewUpdate: (pos, rot) {}, // بدون معاينة
        onShelfDoubleClick: _onShelfDoubleClick, // عرض تفاصيل الخزانة
      ),
    );
  }
}

/// نموذج موقع المنتج
class ProductLocation {
  final String shelfId;
  final String shelfName;
  final int level;
  final int slot;
  final double quantity;
  final String binId;

  ProductLocation({
    required this.shelfId,
    required this.shelfName,
    required this.level,
    required this.slot,
    required this.quantity,
    required this.binId,
  });
}
