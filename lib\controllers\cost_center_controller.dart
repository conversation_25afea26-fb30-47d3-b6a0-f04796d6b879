import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper .dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/cost_center_model.dart';
import 'package:sqflite/sqflite.dart';

class CostCenterController with ChangeNotifier {
  List<CostCenterModel> costCenter = [];
  bool runningSyncization = false;
  int fetchedCostCount = 0;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  Future<void> getCostCenter() async {
    try {
      // return;
      // costCenter.clear();

      var fromLocalDatabase = await getCostCenterFromLocal();
      if (fromLocalDatabase.isNotEmpty) {
        costCenter.clear();
        costCenter.addAll(
          fromLocalDatabase.map((e) => CostCenterModel.fromJson(e)).toList(),
        );

        notifyListeners();
        return;
      } else {
        await fetchSuppliersFromServer();
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> fetchSuppliersFromServer() async {
    if (runningSyncization) return;
    try {
      // return;
      // var fromLocalDatabase = await getCostCenterFromLocal();

      // if (await isThereNetworkConnection() == false) {
      //   costCenter.clear();
      //   costCenter.addAll(
      //     fromLocalDatabase.map((e) => CostCenterModel.fromJson(e)).toList(),
      //   );
      //   notifyListeners();
      //   return;
      // }

      costCenter.clear();
      bool isStillThereSuppliers = true;
      fetchedCostCount = 0;
      while (isStillThereSuppliers) {
        runningSyncization = true;

        var requestBody = {
          "CostCenter_Id": "",
          "Customers_Id": "",
          "search": "",
          "dataTableParameters": {
            "Skip": fetchedCostCount,
            "Take": 200,
            "ColumnName": "Code",
            "Dir": "desc"
          }
        };

        var result = await Api.post(
            action: 'CostCenter/CostCenterList', body: requestBody);
        if (result != null && result.isSuccess) {
          for (var element in result.data) {
            var supplier = CostCenterModel.fromJson(element);
            insertCostCenterLocally(supplier);
            fetchedCostCount++;
            notifyListeners();
          }
          if (result.data.length < 200) {
            isStillThereSuppliers = false;
            runningSyncization = false;
          }
        } else {
          isStillThereSuppliers = false;
          runningSyncization = false;
        }
        // getCostCenter();
        notifyListeners();
      }
    } catch (e) {
      print(e);
    }
  }

  Future<bool> insertCostCenter(CostCenterModel supplier) async {
    try {
      var status = AppController.isThereConnection;

      if (status == true) {
        await syncCostCenterWithServer(supplier);
        return await insertCostCenterLocally(supplier);
      } else {
        return await insertCostCenterLocally(supplier);
      }
    } catch (e) {
      print("Error in inserting supplier: $e");
      return false;
    }
  }

  Future<bool> insertCostCenterLocally(CostCenterModel supplier) async {
    try {
      final Database db = await _dbHelper.database;
      var supplierJson = supplier.toJson();

      await db.insert(
        'CostCenter',
        supplierJson,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      costCenter.add(supplier);
      notifyListeners();
      return true;
    } catch (e) {
      print("Error in local supplier insertion: $e");
      return false;
    }
  }

  Future<bool> syncCostCenterWithServer(CostCenterModel supplier) async {
    try {
      var result = await Api.post(
        action: "Supplier/CreateSupplier",
        body: supplier.toJson(),
      );

      if (result != null && result.isSuccess) {
        supplier.iD = result.data['ID'];
        supplier.code = result.data['Code'];
        return true;
      }
      return false;
    } catch (e) {
      print("Error syncing supplier with server: $e");
      return false;
    }
  }

  Future<List<Map<String, dynamic>>> getCostCenterFromLocal() async {
    try {
      final Database db = await _dbHelper.database;
      // db.delete('CostCenter', where: null, whereArgs: null);
      return await db.query('CostCenter');
    } catch (e) {
      print("Error getting suppliers from local: $e");
      return [];
    }
  }

  Future<bool> updateCostCenter(CostCenterModel supplier) async {
    try {
      var status = AppController.isThereConnection;

      if (status == true) {
        var result = await Api.put(
          action: "Supplier/UpdateSupplier",
          body: supplier.toJson(),
        );

        if (result != null && result.isSuccess) {
          await updateCostCenterLocally(supplier);
          return true;
        }
        return false;
      } else {
        return await updateCostCenterLocally(supplier);
      }
    } catch (e) {
      print("Error updating supplier: $e");
      return false;
    }
  }

  Future<bool> updateCostCenterLocally(CostCenterModel supplier) async {
    try {
      final Database db = await _dbHelper.database;
      await db.update(
        'CostCenter',
        supplier.toJson(),
        where: 'ID = ?',
        whereArgs: [supplier.iD],
      );

      int index = costCenter.indexWhere((s) => s.iD == supplier.iD);
      if (index != -1) {
        costCenter[index] = supplier;
        notifyListeners();
      }
      return true;
    } catch (e) {
      print("Error updating supplier locally: $e");
      return false;
    }
  }

  Future<bool> deleteCostCenter(int supplierId) async {
    try {
      var status = AppController.isThereConnection;

      if (status == true) {
        var result =
            await Api.delete(action: "Supplier/DeleteSupplier/$supplierId");

        if (result != null && result.isSuccess) {
          await deleteCostCenterLocally(supplierId);
          return true;
        }
        return false;
      } else {
        return await deleteCostCenterLocally(supplierId);
      }
    } catch (e) {
      print("Error deleting supplier: $e");
      return false;
    }
  }

  Future<bool> deleteCostCenterLocally(int supplierId) async {
    try {
      final Database db = await _dbHelper.database;
      await db.delete(
        'CostCenter',
        where: 'ID = ?',
        whereArgs: [supplierId],
      );

      costCenter.removeWhere((supplier) => supplier.iD == supplierId);
      notifyListeners();
      return true;
    } catch (e) {
      print("Error deleting supplier locally: $e");
      return false;
    }
  }

  CostCenterModel? getCostCenterById(int id) {
    try {
      return costCenter.firstWhere((supplier) => supplier.iD == id);
    } catch (e) {
      return null;
    }
  }

  List<CostCenterModel> searchCostCenter(String searchTerm) {
    if (searchTerm.isEmpty) {
      return costCenter;
    }

    return costCenter.where((supplier) {
      return (supplier.name?.toLowerCase().contains(searchTerm.toLowerCase()) ??
              false) ||
          (supplier.code?.toLowerCase().contains(searchTerm.toLowerCase()) ??
              false);
    }).toList();
  }
}
