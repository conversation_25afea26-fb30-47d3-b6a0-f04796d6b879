import 'customer_sales_server_dto.dart';

class CustomerSalesServerReportDTO {
  final List<CustomerSalesServerDTO> customers;
  final DateTime? fromDate;
  final DateTime? toDate;
  final double totalSales;
  final double totalQuantity;
  final int totalCustomers;

  CustomerSalesServerReportDTO({
    required this.customers,
    this.fromDate,
    this.toDate,
    required this.totalSales,
    required this.totalQuantity,
    required this.totalCustomers,
  });

  factory CustomerSalesServerReportDTO.fromCustomersList(
    List<CustomerSalesServerDTO> customers, {
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    final totalSales = customers.fold<double>(
      0.0,
      (sum, customer) => sum + (customer.totalSales ?? 0.0),
    );

    final totalQuantity = customers.fold<double>(
      0.0,
      (sum, customer) => sum + (customer.totalQuantity ?? 0.0),
    );

    return CustomerSalesServerReportDTO(
      customers: customers,
      fromDate: fromDate,
      toDate: toDate,
      totalSales: totalSales,
      totalQuantity: totalQuantity,
      totalCustomers: customers.length,
    );
  }

  // Get top customers by sales amount
  List<CustomerSalesServerDTO> getTopCustomersBySales({int limit = 10}) {
    final sortedCustomers = List<CustomerSalesServerDTO>.from(customers);
    sortedCustomers
        .sort((a, b) => (b.totalSales ?? 0).compareTo(a.totalSales ?? 0));
    return sortedCustomers.take(limit).toList();
  }

  // Get top customers by quantity purchased
  List<CustomerSalesServerDTO> getTopCustomersByQuantity({int limit = 10}) {
    final sortedCustomers = List<CustomerSalesServerDTO>.from(customers);
    sortedCustomers
        .sort((a, b) => (b.totalQuantity ?? 0).compareTo(a.totalQuantity ?? 0));
    return sortedCustomers.take(limit).toList();
  }
}
