class SqlLiteInvoiceModel {
  int? id; // Nullable ID
  String? data; // Nullable data field
  String? status; // Nullable status field
  String? localCode; // Nullable status field
  int? inseatedUserId;
  String? inseatedUserName;
  String? type; // Nullable type field

  // Constructor
  SqlLiteInvoiceModel({
    this.id,
    this.data,
    this.status,
    this.localCode,
    this.inseatedUserId,
    this.inseatedUserName,
    this.type,
  });

  // Corrected method to convert from JSON to Invoice object
  SqlLiteInvoiceModel.fromJson(Map<String, dynamic> json) {
    id = json['ID'] as int?; // Assign directly to the instance's fields
    data = json['data'] as String?;
    status = json['status'] as String?;
    localCode = json['localCode'] as String?;
    inseatedUserId = json['inseatedUserId'] as int?;
    inseatedUserName = json['inseatedUserName'] as String?;
    type = json['type'] as String?;
  }

  // Method to convert Invoice object to JSON
  Map<String, dynamic> toJson() {
    return {
      'ID': id, // Convert fields to JSON format
      'data': data,
      'status': status,
      'localCode': localCode,
      'inseatedUserId': inseatedUserId,
      'inseatedUserName': inseatedUserName,
      'type': type,
    };
  }

  @override
  String toString() {
    return 'SqlLiteInvoiceModel(id: $id, data: $data, status: $status, localCode:$localCode)';
  }
}
