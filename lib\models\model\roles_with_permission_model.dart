class RolesWithPermission {
  int? roleID;
  String? roleName;
  List<Permissions>? permissions;

  RolesWithPermission({this.roleID, this.roleName, this.permissions});

  RolesWithPermission.fromJson(Map<String, dynamic> json) {
    roleID = json['RoleID'];
    roleName = json['RoleName'];
    if (json['Permissions'] != null) {
      permissions = <Permissions>[];
      json['Permissions'].forEach((v) {
        permissions!.add(new Permissions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['RoleID'] = this.roleID;
    data['RoleName'] = this.roleName;
    if (this.permissions != null) {
      data['Permissions'] = this.permissions!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Permissions {
  int? permissionID;
  String? permissionName;
  int? parentID;
  int? roleId;

  Permissions(
      {this.permissionID, this.permissionName, this.parentID, this.roleId});

  Permissions.fromJson(Map<String, dynamic> json) {
    permissionID = json['PermissionID'];
    permissionName = json['PermissionName'];
    parentID = json['Parent_ID'];
    roleId = json['roleId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['PermissionID'] = this.permissionID;
    data['PermissionName'] = this.permissionName;
    data['Parent_ID'] = this.parentID;
    data['roleId'] = this.roleId;
    return data;
  }
}
