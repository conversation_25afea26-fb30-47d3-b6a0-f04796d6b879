import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/purchase_invoice_model.dart';

extension PurchaseTypeExtension on PurchaseType {
  String get title {
    switch (this) {
      case PurchaseType.Order:
        return 'طلبيات شراء';
      case PurchaseType.Invoice:
        return 'فاتورة شراء';
      case PurchaseType.ReturnInvoice:
        return 'فاتورة مرتجعات شراء';
      case PurchaseType.ProformaInvoice:
        return 'فاتورة أولية';
      case PurchaseType.Order:
        return 'إذن استلام';
      default:
        return 'غير معروف';
    }
  }
}

enum PurchaseInvoiceSyncStatus { pending, syncing, synced }

class PurchaseInvoiceDto {
  int? id;
  int? localId;
  String? invoiceCode;
  String? appReferanceCode;
  int? supplierId;
  String? supplierName;
  String? supplierInvoiceNo;
  PurchaseType? invoiceType;
  String? invoiceTypeName;
  String? purchaseOrderId;
  String? quotationId;
  String? proformaInvoiceId;
  String? purchaseReceiptId;
  double? discountValue;
  double? paymentValue;
  double? discountPercentage;
  double? totalDiscount;
  double? totalAfterDiscount;
  double? total;
  double? productsTotalCount;
  int? warehouseId;
  String? warehouseName;
  String? note;
  String? connectedToInvoiceCode;
  String? connectedToInvoiceLocalCode;
  DateTime? invoiceDate;
  String? entryDateFormated;
  int? paymentMethodId;
  List<ProductDTO>? purchaseItems;
  int? currencyId;
  double? exchangeRate;
  int? costCenterId;
  String? costCenterName;
  String? purchaseType;
  String? transactionsType;
  String? itemsCategory;
  String? ddlItems;
  String? offerPeriod;
  String? offerEndingDate;
  String? paymentConditionId;
  String? shippingMethodId;
  String? shippingPeriod;
  double? vATPercent;

  PurchaseInvoiceDto({
    this.id,
    this.localId,
    this.invoiceCode,
    this.appReferanceCode,
    this.supplierId,
    this.costCenterName,
    this.supplierName,
    this.supplierInvoiceNo,
    this.invoiceType,
    this.invoiceTypeName,
    this.purchaseOrderId,
    this.quotationId,
    this.proformaInvoiceId,
    this.purchaseReceiptId,
    this.discountValue,
    this.paymentValue,
    this.discountPercentage,
    this.totalDiscount,
    this.totalAfterDiscount,
    this.total,
    this.productsTotalCount,
    this.entryDateFormated,
    this.warehouseId,
    this.warehouseName,
    this.note,
    this.connectedToInvoiceCode,
    this.connectedToInvoiceLocalCode,
    this.invoiceDate,
    this.paymentMethodId,
    this.purchaseItems,
    this.currencyId,
    this.exchangeRate,
    this.costCenterId,
    this.purchaseType,
    this.transactionsType,
    this.itemsCategory,
    this.ddlItems,
    this.offerPeriod,
    this.offerEndingDate,
    this.paymentConditionId,
    this.shippingMethodId,
    this.shippingPeriod,
    this.vATPercent,
  });

  PurchaseInvoiceDto.fromJson(Map<String, dynamic> json) {
    id = json['ID'];
    localId = json['local_Id'];
    invoiceCode = json['Code'];
    appReferanceCode = json['APP_Referance_Code'];
    supplierId = json['Supplier_ID'];
    costCenterName = json['Cost_Center_Name'];
    supplierName = json['Supplier_Name'];
    supplierInvoiceNo = json['Supplier_Invoice_No'];
    invoiceTypeName = json['Invoice_Type_Name'];
    purchaseOrderId = json['Purchase_Order_ID'];
    quotationId = json['Qutation_ID'];
    proformaInvoiceId = json['Proforma_Invoice_ID'];
    purchaseReceiptId = json['Purchase_Receipt_ID'];
    entryDateFormated = json['Entry_Date_Formated'];
    discountValue = json['Discount_Value'] != null
        ? double.parse(json['Discount_Value'].toString())
        : 0.0;
    paymentValue = json['Payment_Value'] != null
        ? double.parse(json['Payment_Value'].toString())
        : 0.0;
    discountPercentage = json['Discount_Percentage'] != null
        ? double.parse(json['Discount_Percentage'].toString())
        : 0.0;
    totalDiscount = json['Total_Discount'] != null
        ? double.parse(json['Total_Discount'].toString())
        : 0.0;
    totalAfterDiscount = json['Total_After_Discount'] != null
        ? double.parse(json['Total_After_Discount'].toString())
        : 0.0;
    total =
        json['total'] != null ? double.parse(json['total'].toString()) : 0.0;
    productsTotalCount = json['Products_Total_Count'] != null
        ? double.parse(json['Products_Total_Count'].toString())
        : 0.0;
    warehouseId = json['Warehouse_ID'];
    warehouseName = json['Warehouse_Name'];
    note = json['Notes'];
    connectedToInvoiceCode = json['Connected_To_Invoice_Code'];
    connectedToInvoiceLocalCode = json['Connected_To_Invoice_Local_Code'];
    invoiceDate = json['Invoice_Date'] != null
        ? DateTime.parse(json['Invoice_Date'])
        : null;
    paymentMethodId = json['Payment_Method_Id'];

    if (json['PurchaseItems'] != null) {
      purchaseItems = <ProductDTO>[];
      json['PurchaseItems'].forEach((v) {
        purchaseItems!.add(ProductDTO.fromJson(v));
      });
    }

    currencyId = json['Curreny_ID'];
    exchangeRate = json['Exchange_Rate'] != null
        ? double.parse(json['Exchange_Rate'].toString())
        : 1.0;
    costCenterId = json['Cost_Center_ID'];
    purchaseType = json['Purchase_Type'];
    transactionsType = json['transactions_type'];
    itemsCategory = json['ItemsCategory'];
    ddlItems = json['ddlItems'];
    offerPeriod = json['Offer_Period'];
    offerEndingDate = json['Offer_Ending_Date_Formated'];
    paymentConditionId = json['Payment_Condition_ID'];
    shippingMethodId = json['Shipping_Method_ID'];
    shippingPeriod = json['Shipping_Period'];
    vATPercent = json['VAT_Percent'] != null
        ? double.parse(json['VAT_Percent'].toString())
        : 0.0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = id;
    data['local_Id'] = localId;
    data['Code'] = invoiceCode;
    data['APP_Referance_Code'] = appReferanceCode;
    data['Supplier_ID'] = supplierId;
    data['Supplier_Name'] = supplierName;
    data['Supplier_Invoice_No'] = supplierInvoiceNo;
    data['Invoice_Type_Name'] = invoiceTypeName;
    data['Purchase_Order_ID'] = purchaseOrderId;
    data['Qutation_ID'] = quotationId;
    data['Proforma_Invoice_ID'] = proformaInvoiceId;
    data['Purchase_Receipt_ID'] = purchaseReceiptId;
    data['Discount_Value'] = discountValue;
    data['Payment_Value'] = paymentValue;
    data['Entry_Date_Formated'] = entryDateFormated;
    data['Discount_Percentage'] = discountPercentage;
    data['Total_Discount'] = totalDiscount;
    data['Total_After_Discount'] = totalAfterDiscount;
    data['total'] = total;
    data['Products_Total_Count'] = productsTotalCount;
    data['Warehouse_ID'] = warehouseId;
    data['Warehouse_Name'] = warehouseName;
    data['Notes'] = note;
    data['Connected_To_Invoice_Code'] = connectedToInvoiceCode;
    data['Connected_To_Invoice_Local_Code'] = connectedToInvoiceLocalCode;
    data['Invoice_Date'] = invoiceDate?.toIso8601String();
    data['Payment_Method_Id'] = paymentMethodId;

    if (purchaseItems != null) {
      data['PurchaseItems'] = purchaseItems!.map((v) => v.toJson()).toList();
    }

    data['Curreny_ID'] = currencyId;
    data['Exchange_Rate'] = exchangeRate;
    data['Cost_Center_ID'] = costCenterId;
    data['Cost_Center_Name'] = costCenterName;
    data['Purchase_Type'] = purchaseType;
    data['transactions_type'] = transactionsType;
    data['ItemsCategory'] = itemsCategory;
    data['ddlItems'] = ddlItems;
    data['Offer_Period'] = offerPeriod;
    data['Offer_Ending_Date_Formated'] = offerEndingDate;
    data['Payment_Condition_ID'] = paymentConditionId;
    data['Shipping_Method_ID'] = shippingMethodId;
    data['Shipping_Period'] = shippingPeriod;
    data['VAT_Percent'] = vATPercent;

    return data;
  }
}
