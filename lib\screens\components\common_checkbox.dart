import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class CommonCheckBox extends StatelessWidget {
  const CommonCheckBox(
      {super.key, required this.value, this.onChanged, required this.label});
  final bool value;
  final String label;
  final Function(bool? val)? onChanged;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onChanged != null) onChanged!(!value);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Checkbox(
            value: value,
            checkColor: context.backgroundColor,
            fillColor: MaterialStateProperty.all<Color>(context.primaryColor),
            onChanged: (p0) {
              if (onChanged != null) onChanged!(p0);
            },
          ),
          Text(label),
        ],
      ),
    );
  }
}
