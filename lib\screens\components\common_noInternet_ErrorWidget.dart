import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';

// ignore: must_be_immutable
class NoInternetErrorWidget extends StatelessWidget {
  const NoInternetErrorWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
            width: context.width / 4,
            child: Image.asset("assets/images/home/<USER>")),
        const SizedBox(
          height: 10,
        ),
        Text(
          T('There is no internet connection,\n please try again later'),
          style: TextStyle(color: context.errorColor),
        ),
      ],
    );
  }
}
