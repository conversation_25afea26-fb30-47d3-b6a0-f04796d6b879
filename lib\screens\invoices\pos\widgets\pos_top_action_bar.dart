import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/category_model.dart';
import 'package:inventory_application/screens/components/common_virtual_keyboard_widget.dart';
import 'dart:io' show Platform;
import 'package:virtual_keyboard_multi_language/virtual_keyboard_multi_language.dart';

class POSTopActionBar extends StatefulWidget {
  final TextEditingController searchController;
  final FocusNode? searchFocusNode;
  final Function(String) onSearchChanged;
  final List<CateogryModel> categories;
  final int? selectedCategory;
  final Function(CateogryModel?) onCategorySelected;

  const POSTopActionBar({
    super.key,
    required this.searchController,
    this.searchFocusNode,
    required this.onSearchChanged,
    required this.categories,
    this.selectedCategory,
    required this.onCategorySelected,
  });

  @override
  State<POSTopActionBar> createState() => _POSTopActionBarState();
}

class _POSTopActionBarState extends State<POSTopActionBar> {
  bool showKeyboard = false;
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void dispose() {
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _focusOnSearch() {
    final focusNode = widget.searchFocusNode ?? _searchFocusNode;
    focusNode.requestFocus();
  }

  @override
  Widget build(BuildContext context) {
    // Determine the hint text based on platform
    final String hintText = Platform.isWindows
        ? T('Search products... (F3)')
        : T('Search products...');

    // Create a focus node for the entire widget
    final FocusNode widgetFocusNode = FocusNode();

    return Focus(
        focusNode: widgetFocusNode,
        autofocus: true,
        onKeyEvent: (FocusNode node, KeyEvent event) {
          // Check if F3 key is pressed
          if (event is KeyDownEvent &&
              event.logicalKey == LogicalKeyboardKey.f3) {
            _focusOnSearch();
            return KeyEventResult.handled;
          }
          return KeyEventResult.ignored;
        },
        child: Container(
          padding: const EdgeInsets.all(5),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 5,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Search field + keyboard button
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: widget.searchController,
                      focusNode: widget.searchFocusNode ?? _searchFocusNode,
                      onChanged: widget.onSearchChanged,
                      // Add these properties to improve mobile keyboard behavior
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.search,
                      // Use system keyboard on mobile
                      onTap: () {
                        // On Windows, we might want to show our custom keyboard
                        if (Platform.isWindows) {
                          // Optional: show custom keyboard on Windows
                          // setState(() {
                          //   showKeyboard = true;
                          // });
                        }
                      },
                      decoration: InputDecoration(
                        contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 0),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: Colors.grey.shade300,
                          ),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: Colors.grey.shade300,
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: BorderSide(
                            color: context.newPrimaryColor,
                            width: 2,
                          ),
                        ),
                        hintText: hintText,
                        prefixIcon: Icon(
                          Icons.search,
                          color: Colors.grey.shade600,
                        ),
                        suffixIcon: widget.searchController.text.isNotEmpty
                            ? IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  color: Colors.grey.shade600,
                                  size: 10,
                                ),
                                onPressed: () {
                                  widget.searchController.clear();
                                  widget.onSearchChanged('');
                                },
                              )
                            : null,
                        filled: true,
                        fillColor: Colors.grey.shade50,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Show keyboard button only on Windows
                  if (Platform.isWindows)
                    IconButton(
                      icon: Icon(
                        showKeyboard ? Icons.keyboard_hide : Icons.keyboard,
                        color: context.newPrimaryColor,
                      ),
                      onPressed: () {
                        setState(() {
                          showKeyboard = !showKeyboard;

                          // If showing keyboard, focus on search field
                          if (showKeyboard) {
                            _focusOnSearch();
                          }
                        });
                      },
                      tooltip: showKeyboard
                          ? T('Hide Keyboard')
                          : T('Show Keyboard'),
                    ),
                ],
              ),

              const SizedBox(height: 10),

              // On-screen keyboard - only show on Windows
              if (showKeyboard && Platform.isWindows)
                CommonVirtualKeyboardWidget(
                  textEditingController: widget.searchController,
                  type: VirtualKeyboardType.Alphanumeric,
                  height: 300,
                  onKeyPress: (key) {
                    widget.onSearchChanged(widget.searchController.text);
                  },
                ),

              const SizedBox(height: 10),

              // Category chips
              SizedBox(
                height: 35,
                child: RawScrollbar(
                  thumbVisibility: true,
                  thickness: 6,
                  radius: const Radius.circular(3),
                  // thumbColor: context.newPrimaryColor.withOpacity(0.5),
                  child: ScrollConfiguration(
                    behavior: ScrollConfiguration.of(context).copyWith(
                      dragDevices: {
                        PointerDeviceKind.touch,
                        PointerDeviceKind.mouse,
                        PointerDeviceKind.trackpad,
                        PointerDeviceKind.stylus,
                      },
                      scrollbars: true,
                    ),
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Row(
                        children: [
                          // All Categories chip
                          Padding(
                            padding: const EdgeInsets.only(right: 8),
                            child: FilterChip(
                              label: Text(T('All Categories')),
                              selected: widget.selectedCategory == null,
                              onSelected: (_) =>
                                  widget.onCategorySelected(null),
                              backgroundColor: Colors.grey.shade100,
                              selectedColor:
                                  context.newPrimaryColor.withOpacity(0.2),
                              checkmarkColor: context.newPrimaryColor,
                              labelStyle: TextStyle(
                                color: widget.selectedCategory == null
                                    ? context.newPrimaryColor
                                    : Colors.grey.shade800,
                                fontWeight: widget.selectedCategory == null
                                    ? FontWeight.bold
                                    : FontWeight.normal,
                              ),
                            ),
                          ),
                          // Category chips
                          ...widget.categories.map((category) {
                            return Padding(
                              padding: const EdgeInsets.only(right: 8),
                              child: FilterChip(
                                label: Text(
                                  category.name ?? '',
                                  style: const TextStyle(fontSize: 12),
                                ),
                                selected:
                                    widget.selectedCategory == category.iD,
                                onSelected: (_) =>
                                    widget.onCategorySelected(category),
                                backgroundColor: Colors.grey.shade100,
                                selectedColor:
                                    context.newPrimaryColor.withOpacity(0.2),
                                checkmarkColor: context.newPrimaryColor,
                                labelStyle: TextStyle(
                                  color: widget.selectedCategory == category.iD
                                      ? context.newPrimaryColor
                                      : Colors.grey.shade800,
                                  fontWeight:
                                      widget.selectedCategory == category.iD
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                ),
                              ),
                            );
                          }).toList(),
                          // Add extra space at the end
                          const SizedBox(width: 16),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
