import 'dart:ui';
import 'package:vector_math/vector_math.dart' as vector;

/// حالة المحرر ثنائي الأبعاد
class EditorState {
  EditMode mode;
  double gridSize; // حجم الشبكة بـ cm
  bool snapToGrid; // المحاذاة للشبكة
  bool showGrid; // إظهار الشبكة
  bool showMeasurements; // إظهار القياسات

  // التحويل والعرض
  vector.Vector2 panOffset;
  double zoom;
  double minZoom;
  double maxZoom;

  // الكائن المحدد
  String? selectedObjectId;
  ObjectType? selectedObjectType;

  // أدوات الرسم الحالية
  WallDrawingState wallDrawing;
  EntranceAddingState entranceAdding;
  ShelfPlacingState shelfPlacing;

  EditorState({
    this.mode = EditMode.select,
    this.gridSize = 10.0,
    this.snapToGrid = true,
    this.showGrid = true,
    this.showMeasurements = true,
    vector.Vector2? panOffset,
    this.zoom = 1.0,
    this.minZoom = 0.1,
    this.maxZoom = 5.0,
    this.selectedObjectId,
    this.selectedObjectType,
    WallDrawingState? wallDrawing,
    EntranceAddingState? entranceAdding,
    ShelfPlacingState? shelfPlacing,
  })  : panOffset = panOffset ?? vector.Vector2.zero(),
        wallDrawing = wallDrawing ?? WallDrawingState(),
        entranceAdding = entranceAdding ?? EntranceAddingState(),
        shelfPlacing = shelfPlacing ?? ShelfPlacingState();

  EditorState copyWith({
    EditMode? mode,
    double? gridSize,
    bool? snapToGrid,
    bool? showGrid,
    bool? showMeasurements,
    vector.Vector2? panOffset,
    double? zoom,
    String? selectedObjectId,
    ObjectType? selectedObjectType,
    WallDrawingState? wallDrawing,
    EntranceAddingState? entranceAdding,
    ShelfPlacingState? shelfPlacing,
  }) {
    return EditorState(
      mode: mode ?? this.mode,
      gridSize: gridSize ?? this.gridSize,
      snapToGrid: snapToGrid ?? this.snapToGrid,
      showGrid: showGrid ?? this.showGrid,
      showMeasurements: showMeasurements ?? this.showMeasurements,
      panOffset: panOffset ?? this.panOffset,
      zoom: zoom ?? this.zoom,
      minZoom: minZoom,
      maxZoom: maxZoom,
      selectedObjectId: selectedObjectId ?? this.selectedObjectId,
      selectedObjectType: selectedObjectType ?? this.selectedObjectType,
      wallDrawing: wallDrawing ?? this.wallDrawing,
      entranceAdding: entranceAdding ?? this.entranceAdding,
      shelfPlacing: shelfPlacing ?? this.shelfPlacing,
    );
  }

  /// تحويل إحداثي الشاشة إلى إحداثي المستودع
  vector.Vector2 screenToWorld(Offset screenPoint, Size canvasSize) {
    final center = vector.Vector2(canvasSize.width / 2, canvasSize.height / 2);
    var worldPoint = vector.Vector2(screenPoint.dx, screenPoint.dy) - center;
    worldPoint.scale(1 / zoom);
    worldPoint -= panOffset;

    // تحويل Y لأن Canvas Y للأسفل لكن نريد Y للأعلى
    worldPoint.y = -worldPoint.y;

    return worldPoint;
  }

  /// تحويل إحداثي المستودع إلى إحداثي الشاشة
  Offset worldToScreen(vector.Vector2 worldPoint, Size canvasSize) {
    final center = vector.Vector2(canvasSize.width / 2, canvasSize.height / 2);
    var screenPoint = worldPoint.clone();

    // تحويل Y
    screenPoint.y = -screenPoint.y;

    screenPoint += panOffset;
    screenPoint.scale(zoom);
    screenPoint += center;

    return Offset(screenPoint.x, screenPoint.y);
  }

  /// محاذاة نقطة للشبكة
  vector.Vector2 snapToGridPoint(vector.Vector2 point) {
    if (!snapToGrid) return point;

    return vector.Vector2(
      (point.x / gridSize).round() * gridSize,
      (point.y / gridSize).round() * gridSize,
    );
  }
}

/// أوضاع المحرر
enum EditMode {
  select, // تحديد وتحريك
  drawWall, // رسم الجدران
  deleteWall, // مسح الجدران
  addEntrance, // إضافة مدخل
  placeShelf, // وضع خزانة
  editShelf, // تعديل خزانة
}

/// أنواع الكائنات
enum ObjectType {
  wall,
  entrance,
  shelf,
  bin,
}

/// حالة رسم الجدران
class WallDrawingState {
  List<vector.Vector2> currentPoints;
  bool isDrawing;
  double wallThickness;
  Color wallColor;

  WallDrawingState({
    List<vector.Vector2>? currentPoints,
    this.isDrawing = false,
    this.wallThickness = 20.0,
    this.wallColor = const Color(0xFF34495E),
  }) : currentPoints = currentPoints ?? [];

  WallDrawingState copyWith({
    List<vector.Vector2>? currentPoints,
    bool? isDrawing,
    double? wallThickness,
    Color? wallColor,
  }) {
    return WallDrawingState(
      currentPoints: currentPoints ?? this.currentPoints,
      isDrawing: isDrawing ?? this.isDrawing,
      wallThickness: wallThickness ?? this.wallThickness,
      wallColor: wallColor ?? this.wallColor,
    );
  }

  void startDrawing(vector.Vector2 point) {
    currentPoints.clear();
    currentPoints.add(point);
    isDrawing = true;
  }

  void addPoint(vector.Vector2 point) {
    if (isDrawing) {
      currentPoints.add(point);
    }
  }

  void finishDrawing() {
    isDrawing = false;
    currentPoints.clear();
  }

  void cancelDrawing() {
    isDrawing = false;
    currentPoints.clear();
  }
}

/// حالة إضافة المداخل
class EntranceAddingState {
  String? selectedWallId;
  double entranceWidth;
  vector.Vector2? previewPosition;

  EntranceAddingState({
    this.selectedWallId,
    this.entranceWidth = 100.0,
    this.previewPosition,
  });

  EntranceAddingState copyWith({
    String? selectedWallId,
    double? entranceWidth,
    vector.Vector2? previewPosition,
  }) {
    return EntranceAddingState(
      selectedWallId: selectedWallId ?? this.selectedWallId,
      entranceWidth: entranceWidth ?? this.entranceWidth,
      previewPosition: previewPosition ?? this.previewPosition,
    );
  }
}

/// حالة وضع الخزائن
class ShelfPlacingState {
  String? selectedTemplateId;
  vector.Vector2? previewPosition;
  double previewRotation;
  bool isDragging;

  ShelfPlacingState({
    this.selectedTemplateId,
    this.previewPosition,
    this.previewRotation = 0.0,
    this.isDragging = false,
  });

  ShelfPlacingState copyWith({
    String? selectedTemplateId,
    vector.Vector2? previewPosition,
    double? previewRotation,
    bool? isDragging,
  }) {
    return ShelfPlacingState(
      selectedTemplateId: selectedTemplateId ?? this.selectedTemplateId,
      previewPosition: previewPosition ?? this.previewPosition,
      previewRotation: previewRotation ?? this.previewRotation,
      isDragging: isDragging ?? this.isDragging,
    );
  }
}

/// إعدادات التخطيط
class LayoutSettings {
  // إعدادات الشبكة
  double gridSize;
  Color gridColor;
  double gridOpacity;

  // إعدادات الجدران
  double defaultWallThickness;
  Color defaultWallColor;
  double wallHeight;

  // إعدادات الخزائن
  Color selectionColor;
  Color previewColor;
  double previewOpacity;

  // إعدادات القياسات
  bool showDimensions;
  Color dimensionColor;
  double dimensionTextSize;

  LayoutSettings({
    this.gridSize = 10.0,
    this.gridColor = const Color(0xFFBDC3C7),
    this.gridOpacity = 0.5,
    this.defaultWallThickness = 20.0,
    this.defaultWallColor = const Color(0xFF34495E),
    this.wallHeight = 300.0,
    this.selectionColor = const Color(0xFF3498DB),
    this.previewColor = const Color(0xFF2ECC71),
    this.previewOpacity = 0.7,
    this.showDimensions = true,
    this.dimensionColor = const Color(0xFF2C3E50),
    this.dimensionTextSize = 12.0,
  });
}

/// أدوات مساعدة للهندسة
class GeometryUtils {
  /// فحص إذا كانت النقطة داخل مضلع
  static bool isPointInPolygon(
      vector.Vector2 point, List<vector.Vector2> polygon) {
    int intersections = 0;

    for (int i = 0; i < polygon.length; i++) {
      final j = (i + 1) % polygon.length;
      final vertex1 = polygon[i];
      final vertex2 = polygon[j];

      if (((vertex1.y > point.y) != (vertex2.y > point.y)) &&
          (point.x <
              (vertex2.x - vertex1.x) *
                      (point.y - vertex1.y) /
                      (vertex2.y - vertex1.y) +
                  vertex1.x)) {
        intersections++;
      }
    }

    return (intersections % 2) == 1;
  }

  /// حساب المسافة من نقطة إلى خط
  static double distancePointToLine(
      vector.Vector2 point, vector.Vector2 lineStart, vector.Vector2 lineEnd) {
    final lineLength = (lineEnd - lineStart).length;
    if (lineLength == 0) return (point - lineStart).length;

    final t = ((point - lineStart).dot(lineEnd - lineStart)) /
        (lineLength * lineLength);
    final projection = lineStart + (lineEnd - lineStart) * t.clamp(0.0, 1.0);

    return (point - projection).length;
  }

  /// فحص تقاطع خطين
  static vector.Vector2? lineIntersection(
    vector.Vector2 line1Start,
    vector.Vector2 line1End,
    vector.Vector2 line2Start,
    vector.Vector2 line2End,
  ) {
    final x1 = line1Start.x, y1 = line1Start.y;
    final x2 = line1End.x, y2 = line1End.y;
    final x3 = line2Start.x, y3 = line2Start.y;
    final x4 = line2End.x, y4 = line2End.y;

    final denominator = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
    if (denominator == 0) return null; // خطوط متوازية

    final t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denominator;
    final u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denominator;

    if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
      return vector.Vector2(
        x1 + t * (x2 - x1),
        y1 + t * (y2 - y1),
      );
    }

    return null;
  }

  /// فحص تداخل مستطيلين
  static bool rectanglesOverlap(
    vector.Vector2 rect1Center,
    double rect1Width,
    double rect1Height,
    vector.Vector2 rect2Center,
    double rect2Width,
    double rect2Height,
  ) {
    final rect1Left = rect1Center.x - rect1Width / 2;
    final rect1Right = rect1Center.x + rect1Width / 2;
    final rect1Top = rect1Center.y + rect1Height / 2;
    final rect1Bottom = rect1Center.y - rect1Height / 2;

    final rect2Left = rect2Center.x - rect2Width / 2;
    final rect2Right = rect2Center.x + rect2Width / 2;
    final rect2Top = rect2Center.y + rect2Height / 2;
    final rect2Bottom = rect2Center.y - rect2Height / 2;

    return !(rect1Right < rect2Left ||
        rect2Right < rect1Left ||
        rect1Top < rect2Bottom ||
        rect2Top < rect1Bottom);
  }
}
