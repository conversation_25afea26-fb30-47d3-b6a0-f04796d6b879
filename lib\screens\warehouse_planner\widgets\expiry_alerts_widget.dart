import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../providers/warehouse_planner_provider.dart';
import '../../../models/warehouse_planner/warehouse_layout.dart';
import '../../../models/warehouse_planner/editor_state.dart';

class ExpiryAlertsWidget extends StatelessWidget {
  const ExpiryAlertsWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<WarehousePlannerProvider>(
      builder: (context, provider, child) {
        final layout = provider.currentLayout;
        if (layout == null) {
          return const SizedBox.shrink();
        }

        final alerts = _generateExpiryAlerts(layout);

        if (alerts.isEmpty) {
          return _buildNoAlertsWidget();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAlertsHeader(alerts),
            const SizedBox(height: 12),
            Expanded(
              child: ListView.builder(
                itemCount: alerts.length,
                itemBuilder: (context, index) {
                  return _buildAlertCard(context, alerts[index], provider);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  List<ExpiryAlert> _generateExpiryAlerts(WarehouseLayout layout) {
    List<ExpiryAlert> alerts = [];
    final now = DateTime.now();

    for (final shelf in layout.shelves) {
      for (final bin in shelf.bins) {
        if (bin.productName != null && bin.expiryDate != null) {
          final daysUntilExpiry = bin.expiryDate!.difference(now).inDays;

          AlertSeverity severity;
          if (daysUntilExpiry <= 0) {
            severity = AlertSeverity.expired;
          } else if (daysUntilExpiry <= 7) {
            severity = AlertSeverity.critical;
          } else if (daysUntilExpiry <= 30) {
            severity = AlertSeverity.warning;
          } else {
            continue; // لا تحتاج تنبيه
          }

          alerts.add(ExpiryAlert(
            shelf: shelf,
            bin: bin,
            daysUntilExpiry: daysUntilExpiry,
            severity: severity,
          ));
        }
      }
    }

    // ترتيب حسب الأولوية (الأكثر خطورة أولاً)
    alerts.sort((a, b) {
      if (a.severity.index != b.severity.index) {
        return b.severity.index.compareTo(a.severity.index);
      }
      return a.daysUntilExpiry.compareTo(b.daysUntilExpiry);
    });

    return alerts;
  }

  Widget _buildAlertsHeader(List<ExpiryAlert> alerts) {
    final expiredCount =
        alerts.where((a) => a.severity == AlertSeverity.expired).length;
    final criticalCount =
        alerts.where((a) => a.severity == AlertSeverity.critical).length;
    final warningCount =
        alerts.where((a) => a.severity == AlertSeverity.warning).length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.red.withOpacity(0.1),
            Colors.orange.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.warning, color: Colors.red, size: 20),
              const SizedBox(width: 8),
              const Text(
                'تنبيهات انتهاء الصلاحية',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${alerts.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              if (expiredCount > 0) ...[
                _buildAlertBadge('منتهي', expiredCount, Colors.red),
                const SizedBox(width: 8),
              ],
              if (criticalCount > 0) ...[
                _buildAlertBadge('خطر', criticalCount, Colors.orange),
                const SizedBox(width: 8),
              ],
              if (warningCount > 0) ...[
                _buildAlertBadge('تحذير', warningCount, Colors.yellow.shade700),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAlertBadge(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        '$label ($count)',
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildNoAlertsWidget() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green,
            size: 48,
          ),
          const SizedBox(height: 16),
          const Text(
            'ممتاز! لا توجد أدوية منتهية الصلاحية',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.green,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          const Text(
            'جميع الأدوية في المستودع صالحة للاستخدام',
            style: TextStyle(
              color: Color(0xFF6C757D),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAlertCard(BuildContext context, ExpiryAlert alert,
      WarehousePlannerProvider provider) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
            color: _getSeverityColor(alert.severity).withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: _getSeverityColor(alert.severity).withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: _getSeverityColor(alert.severity).withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Icon(
              _getSeverityIcon(alert.severity),
              color: _getSeverityColor(alert.severity),
              size: 20,
            ),
          ),
        ),
        title: Text(
          alert.bin.productName!,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '📦 ${alert.shelf.name} • L${alert.bin.level + 1}-S${alert.bin.slot + 1}',
              style: const TextStyle(fontSize: 12),
            ),
            Row(
              children: [
                Icon(
                  Icons.inventory,
                  size: 12,
                  color: const Color(0xFF6C757D),
                ),
                const SizedBox(width: 4),
                Text(
                  '${alert.bin.quantity} ${alert.bin.unitName ?? 'وحدة'}',
                  style: const TextStyle(fontSize: 11),
                ),
                const SizedBox(width: 16),
                Icon(
                  Icons.schedule,
                  size: 12,
                  color: _getSeverityColor(alert.severity),
                ),
                const SizedBox(width: 4),
                Text(
                  _getExpiryText(alert.daysUntilExpiry),
                  style: TextStyle(
                    fontSize: 11,
                    color: _getSeverityColor(alert.severity),
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.location_on, color: Color(0xFF3498DB)),
              onPressed: () {
                provider.selectObject(alert.shelf.id, ObjectType.shelf);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                        'تم تحديد ${alert.bin.productName} في ${alert.shelf.name}'),
                    backgroundColor: Colors.blue,
                  ),
                );
              },
              tooltip: 'إظهار الموقع',
            ),
            PopupMenuButton<String>(
              onSelected: (value) =>
                  _handleAlertAction(context, value, alert, provider),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'move',
                  child: ListTile(
                    leading: Icon(Icons.move_to_inbox),
                    title: Text('نقل إلى مكان آخر'),
                    dense: true,
                  ),
                ),
                const PopupMenuItem(
                  value: 'remove',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: Colors.red),
                    title: Text('إزالة من المخزون'),
                    dense: true,
                  ),
                ),
                const PopupMenuItem(
                  value: 'extend',
                  child: ListTile(
                    leading: Icon(Icons.update),
                    title: Text('تحديث تاريخ الانتهاء'),
                    dense: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _handleAlertAction(BuildContext context, String action,
      ExpiryAlert alert, WarehousePlannerProvider provider) {
    switch (action) {
      case 'move':
        _showMoveDialog(context, alert, provider);
        break;
      case 'remove':
        _showRemoveDialog(context, alert, provider);
        break;
      case 'extend':
        _showExtendDialog(context, alert, provider);
        break;
    }
  }

  void _showMoveDialog(BuildContext context, ExpiryAlert alert,
      WarehousePlannerProvider provider) {
    // TODO: تنفيذ حوار نقل الدواء
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة النقل قيد التطوير')),
    );
  }

  void _showRemoveDialog(BuildContext context, ExpiryAlert alert,
      WarehousePlannerProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الإزالة'),
        content: Text('هل تريد إزالة ${alert.bin.productName} من المخزون؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              // إزالة المنتج من الخانة
              alert.bin.productId = null;
              alert.bin.productName = null;
              alert.bin.productCode = null;
              alert.bin.barcode = null;
              alert.bin.quantity = 0;
              alert.bin.expiryDate = null;
              alert.bin.unitPrice = null;
              alert.bin.unitName = null;
              alert.bin.status = BinStatus.empty;

              provider.updateEditorState(provider.editorState);
              Navigator.pop(context);

              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إزالة المنتج من المخزون'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إزالة', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showExtendDialog(BuildContext context, ExpiryAlert alert,
      WarehousePlannerProvider provider) {
    DateTime? newExpiryDate;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحديث تاريخ الانتهاء'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('المنتج: ${alert.bin.productName}'),
            const SizedBox(height: 16),
            Text(
                'التاريخ الحالي: ${alert.bin.expiryDate!.toString().substring(0, 10)}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () async {
                final date = await showDatePicker(
                  context: context,
                  initialDate:
                      alert.bin.expiryDate!.add(const Duration(days: 30)),
                  firstDate: DateTime.now(),
                  lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
                );
                if (date != null) {
                  newExpiryDate = date;
                }
              },
              child: const Text('اختر تاريخ جديد'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              if (newExpiryDate != null) {
                alert.bin.expiryDate = newExpiryDate;
                provider.updateEditorState(provider.editorState);
                Navigator.pop(context);

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم تحديث تاريخ الانتهاء'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }

  Color _getSeverityColor(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.expired:
        return Colors.red;
      case AlertSeverity.critical:
        return Colors.orange;
      case AlertSeverity.warning:
        return Colors.yellow.shade700;
    }
  }

  IconData _getSeverityIcon(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.expired:
        return Icons.dangerous;
      case AlertSeverity.critical:
        return Icons.warning;
      case AlertSeverity.warning:
        return Icons.info;
    }
  }

  String _getExpiryText(int daysUntilExpiry) {
    if (daysUntilExpiry <= 0) {
      return 'منتهي الصلاحية';
    } else if (daysUntilExpiry == 1) {
      return 'ينتهي غداً';
    } else {
      return 'ينتهي خلال $daysUntilExpiry يوم';
    }
  }
}

enum AlertSeverity { warning, critical, expired }

class ExpiryAlert {
  final Shelf shelf;
  final Bin bin;
  final int daysUntilExpiry;
  final AlertSeverity severity;

  ExpiryAlert({
    required this.shelf,
    required this.bin,
    required this.daysUntilExpiry,
    required this.severity,
  });
}
