import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/screens/components/common_checkbox.dart';

class InvoiceListSelectTypeWidget extends StatefulWidget {
  const InvoiceListSelectTypeWidget({super.key});

  @override
  State<InvoiceListSelectTypeWidget> createState() =>
      _InvoiceListSelectTypeWidgetState();
}

class _InvoiceListSelectTypeWidgetState
    extends State<InvoiceListSelectTypeWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
      decoration: BoxDecoration(
        color: context.onBackground,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            children: [
              CommonCheckBox(
                label: "فواتير المبيعات",
                value: true,
                onChanged: (val) {},
              ),
              CommonCheckBox(
                label: "فواتير المشتريات",
                value: true,
                onChanged: (val) {},
              ),
            ],
          ),
          Column(
            children: [
              CommonCheckBox(
                label: "مرتجعات المبيعات",
                value: true,
                onChanged: (val) {},
              ),
              CommonCheckBox(
                label: "مرتجعات المشتريات",
                value: false,
                onChanged: (val) {},
              ),
            ],
          ),
        ],
      ),
    );
  }
}
