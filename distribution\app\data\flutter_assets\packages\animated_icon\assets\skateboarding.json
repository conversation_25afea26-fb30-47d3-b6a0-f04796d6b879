{"v": "5.6.5", "fr": 24, "ip": 0, "op": 28, "w": 30, "h": 30, "nm": "skateboarding", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "lottie Outlines", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.5, -10.5, 0], "ix": 2}, "a": {"a": 0, "k": [2.75, 2.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 3, "s": [{"i": [[0, -1.38], [-1.38, 0], [0, 1.38], [1.383, 0]], "o": [[0, 1.38], [1.383, 0], [0, -1.38], [-1.38, 0]], "v": [[-2.5, 0], [-0.001, 2.5], [2.5, 0], [-0.001, -2.5]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, -1.38], [-1.38, 0], [0, 1.38], [1.383, 0]], "o": [[0, 1.38], [1.383, 0], [0, -1.38], [-1.38, 0]], "v": [[-4.25, -1], [-1.751, 1.5], [0.75, -1], [-1.751, -3.5]], "c": true}]}, {"t": 18, "s": [{"i": [[0, -1.38], [-1.38, 0], [0, 1.38], [1.383, 0]], "o": [[0, 1.38], [1.383, 0], [0, -1.38], [-1.38, 0]], "v": [[-2.5, 0], [-0.001, 2.5], [2.5, 0], [-0.001, -2.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.75, 2.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "lottie Outlines", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.5, 1, 0], "ix": 2}, "a": {"a": 0, "k": [4.75, 9.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 3, "s": [{"i": [[0, 0], [0, 0.663], [1.382, 0], [0.458, -0.554], [0, 0], [0, 0], [0, 0], [0, -0.746], [-0.424, -0.448], [0, 0], [0, 0], [0, 0], [0, 0], [-0.481, 0], [0, 0.552], [0, 0], [0.087, 0.151], [0, 0], [0, 0]], "o": [[0.421, -0.447], [0, -1.379], [-0.776, 0], [0, 0], [0, 0], [0, 0], [-0.518, 0.458], [0, 0.666], [0, 0], [0, 0], [0, 0], [0, 0], [0.095, 0.452], [0.551, 0], [0, 0], [0, -0.176], [0, 0], [0, 0], [0, 0]], "v": [[3.817, -4.792], [4.5, -6.501], [1.999, -9.001], [0.081, -8.085], [0.072, -8.092], [-3.647, -4.38], [-3.648, -4.367], [-4.5, -2.501], [-3.813, -0.786], [-3.813, -0.777], [-0.5, 2.646], [0.5, 8.352], [0.541, 8.202], [1.5, 9.001], [2.5, 8.001], [2.5, 2.001], [2.357, 1.505], [2.361, 1.49], [0.74, -1.704]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0.663], [1.382, 0], [0.458, -0.554], [0, 0], [0, 0], [0, 0], [0, -0.746], [-0.424, -0.448], [0, 0], [0, 0], [0, 0], [0, 0], [-0.481, 0], [0, 0.552], [0, 0], [0.087, 0.151], [0, 0], [0, 0]], "o": [[0.245, -0.692], [0, -1.379], [-0.776, 0], [0, 0], [0, 0], [0, 0], [-0.352, 0.648], [0, 0.666], [0, 0], [0, 0], [0, 0], [0, 0], [0.095, 0.452], [0.551, 0], [0, 0], [0, -0.176], [0, 0], [0, 0], [0, 0]], "v": [[3.458, -5.542], [3.906, -7.266], [1.499, -9.688], [-0.419, -8.772], [-0.428, -8.779], [-2.553, -3.943], [-2.585, -3.898], [-3.25, -2.001], [-2.563, -0.286], [-2.563, -0.277], [0.75, 3.145], [0.5, 8.352], [0.541, 8.202], [1.5, 9.001], [2.5, 8.001], [3.75, 2.501], [3.607, 2.005], [3.611, 1.99], [1.74, -1.486]], "c": true}]}, {"t": 18, "s": [{"i": [[0, 0], [0, 0.663], [1.382, 0], [0.458, -0.554], [0, 0], [0, 0], [0, 0], [0, -0.746], [-0.424, -0.448], [0, 0], [0, 0], [0, 0], [0, 0], [-0.481, 0], [0, 0.552], [0, 0], [0.087, 0.151], [0, 0], [0, 0]], "o": [[0.421, -0.447], [0, -1.379], [-0.776, 0], [0, 0], [0, 0], [0, 0], [-0.518, 0.458], [0, 0.666], [0, 0], [0, 0], [0, 0], [0, 0], [0.095, 0.452], [0.551, 0], [0, 0], [0, -0.176], [0, 0], [0, 0], [0, 0]], "v": [[3.817, -4.792], [4.5, -6.501], [1.999, -9.001], [0.081, -8.085], [0.072, -8.092], [-3.647, -4.38], [-3.648, -4.367], [-4.5, -2.501], [-3.813, -0.786], [-3.813, -0.777], [-0.5, 2.646], [0.5, 8.352], [0.541, 8.202], [1.5, 9.001], [2.5, 8.001], [2.5, 2.001], [2.357, 1.505], [2.361, 1.49], [0.74, -1.704]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.75, 9.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "lottie Outlines", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [3.082, -7.438, 0], "ix": 2}, "a": {"a": 0, "k": [10.332, 0.813, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[0, 0], [0.173, -0.13], [0.291, -0.175], [-0.281, -0.547], [-0.334, 0.231], [0, 0], [0, 0], [0.909, 0.355]], "o": [[-0.216, 0], [0, 0], [-0.291, 0.175], [0.484, 0.594], [0.462, -0.331], [0, 0], [2.202, 0.239], [0, 0]], "v": [[-0.801, -2.5], [-1.4, -2.3], [-5.4, 0.7], [-5.785, 2.125], [-4.201, 2.3], [-0.71, -0.318], [3.598, 0.393], [4.699, -2.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[0, 0], [0.178, -0.216], [0.228, -0.331], [-0.508, -0.484], [-0.244, 0.339], [0, 0], [0, 0], [0.909, 0.355]], "o": [[-0.484, 0.109], [0, 0], [-0.228, 0.331], [0.547, 0.422], [0.244, -0.339], [0, 0], [2.202, 0.239], [0, 0]], "v": [[-0.488, -0.938], [-1.322, -0.441], [-3.775, 3.169], [-3.801, 4.688], [-2.38, 4.503], [-0.21, 1.151], [3.88, 0.799], [4.699, -2.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[0, 0], [0.084, -0.403], [0.069, -0.794], [-0.813, -0.219], [-0.111, 0.403], [0, 0], [0, 0], [1.406, -0.094]], "o": [[-0.328, 0.313], [0, 0], [-0.025, 0.55], [0.688, 0.031], [0.267, -0.941], [0, 0], [2.202, 0.239], [0, 0]], "v": [[0.293, 1.594], [-0.15, 2.341], [-0.775, 6.012], [0.012, 7.219], [0.995, 6.253], [1.915, 3.026], [4.005, 1.174], [3.824, -1.938]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [-0.228, -0.309], [-0.619, -0.825], [-0.75, 0.281], [0.197, 0.368], [0, 0], [0, 0], [0.219, -1.125]], "o": [[-0.031, 0.563], [0, 0], [0.33, 0.441], [0.313, -0.281], [-0.639, -1.191], [0, 0], [0.132, -1.143], [0, 0]], "v": [[3.293, 3.906], [3.646, 4.966], [5.725, 8.106], [7.324, 8.438], [7.339, 7.253], [5.665, 3.932], [5.817, -0.326], [3.324, -0.656]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [-0.416, -0.184], [-1.525, -0.169], [-0.344, 0.531], [0.394, 0.138], [0, 0], [0, 0], [-0.469, -1.375]], "o": [[0.344, 0.469], [0, 0], [0.444, 0.113], [0.187, -0.469], [-1.17, -0.409], [0, 0], [-0.993, -1.268], [0, 0]], "v": [[5.762, 4.406], [6.678, 5.247], [10.475, 5.981], [11.7, 5.469], [11.089, 4.441], [7.509, 3.057], [5.38, -0.919], [2.793, 0.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-0.228, -0.309], [-0.619, -0.825], [-0.75, 0.281], [0.197, 0.368], [0, 0], [0, 0], [0.219, -1.125]], "o": [[-0.031, 0.563], [0, 0], [0.33, 0.441], [0.313, -0.281], [-0.639, -1.191], [0, 0], [0.132, -1.143], [0, 0]], "v": [[3.293, 3.906], [3.646, 4.966], [5.725, 8.106], [7.324, 8.438], [7.339, 7.253], [5.665, 3.932], [5.817, -0.326], [3.324, -0.656]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0, 0], [0.084, -0.403], [0.069, -0.794], [-0.813, -0.219], [-0.111, 0.403], [0, 0], [0, 0], [1.406, -0.094]], "o": [[-0.297, 0.094], [0, 0], [-0.025, 0.55], [0.688, 0.031], [0.267, -0.941], [0, 0], [2.202, 0.239], [0, 0]], "v": [[0.293, 1.594], [-0.197, 2.591], [-0.775, 6.012], [0.012, 7.219], [0.995, 6.253], [1.915, 3.026], [4.005, 1.174], [3.824, -1.938]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0, 0], [0.173, -0.13], [0.228, -0.331], [-0.508, -0.484], [-0.244, 0.339], [0, 0], [0, 0], [0.909, 0.355]], "o": [[-0.297, 0.094], [0, 0], [-0.228, 0.331], [0.547, 0.422], [0.244, -0.339], [0, 0], [2.202, 0.239], [0, 0]], "v": [[-0.488, -0.938], [-1.322, -0.441], [-3.775, 3.169], [-3.801, 4.688], [-2.38, 4.503], [-0.21, 1.151], [3.88, 0.799], [4.699, -2.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0, 0], [0.186, -0.176], [0.259, -0.253], [-0.395, -0.516], [-0.289, 0.285], [0, 0], [0, 0], [0.909, 0.355]], "o": [[-0.391, 0.047], [0, 0], [-0.259, 0.253], [0.516, 0.508], [0.353, -0.335], [0, 0], [2.202, 0.239], [0, 0]], "v": [[-0.644, -1.719], [-1.361, -1.37], [-4.588, 1.934], [-4.793, 3.406], [-3.29, 3.402], [-0.46, 0.416], [3.739, 0.596], [4.699, -2.5]], "c": true}]}, {"t": 16, "s": [{"i": [[0, 0], [0.173, -0.13], [0.291, -0.175], [-0.281, -0.547], [-0.334, 0.231], [0, 0], [0, 0], [0.909, 0.355]], "o": [[-0.216, 0], [0, 0], [-0.291, 0.175], [0.484, 0.594], [0.462, -0.331], [0, 0], [2.202, 0.239], [0, 0]], "v": [[-0.801, -2.5], [-1.4, -2.3], [-5.4, 0.7], [-5.785, 2.125], [-4.201, 2.3], [-0.71, -0.318], [3.598, 0.393], [4.699, -2.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [10.55, 0.625], "ix": 2}, "a": {"a": 0, "k": [4.5, -2.125], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "lottie Outlines", "parent": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 9.5, 0], "ix": 2}, "a": {"a": 0, "k": [13, 5.5, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8, -0.5], [6, 0.5], [-6, 0.5], [-8, -0.5]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 2, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [13, 5.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.553, 0], [0, -0.553], [-0.553, 0], [0, 0.552]], "o": [[-0.553, 0], [0, 0.552], [0.553, 0], [0, -0.553]], "v": [[0, -1], [-1, 0], [0, 1], [1, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [18, 9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.552, 0], [0, -0.553], [-0.552, 0], [0, 0.552]], "o": [[-0.552, 0], [0, 0.552], [0.552, 0], [0, -0.553]], "v": [[0, -1], [-1, 0], [0, 1], [1, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [8, 9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "lottie Outlines", "parent": 7, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [1.5, 1, 0], "ix": 2}, "a": {"a": 0, "k": [6.35, 10.85, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[2.262, 0], [0.777, -0.837], [0.022, -0.022], [0, 0], [0.027, -0.032], [0, -1.096], [-0.656, -0.75], [-0.044, -0.045], [0, 0], [0, 0], [-0.242, -0.231], [-0.861, 0], [0, 1.433], [0, 0], [0.161, 0.346], [0.025, 0.05], [0, 0], [0, 0], [0, 0], [-0.044, 0.058], [0, 0.984]], "o": [[-1.136, 0], [-0.024, 0.02], [0, 0], [-0.03, 0.03], [-0.789, 0.772], [0, 0.992], [0.037, 0.049], [0, 0], [0, 0], [0.061, 0.349], [0.679, 0.002], [1.433, 0], [0, 0], [0, -0.365], [-0.019, -0.052], [0, 0], [0, 0], [0, 0], [0.053, -0.052], [0.647, -0.748], [0, -2.261]], "v": [[1.999, -10.6], [-0.99, -9.288], [-1.058, -9.225], [-4.777, -5.514], [-4.863, -5.421], [-6.1, -2.5], [-5.085, 0.192], [-4.963, 0.334], [-1.991, 3.405], [-1.076, 8.627], [-0.601, 9.513], [1.563, 9.569], [4.1, 8.001], [4.1, 2], [3.855, 0.92], [3.788, 0.767], [2.691, -1.394], [4.886, -3.59], [4.953, -3.658], [5.098, -3.824], [6.1, -6.5]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[2.262, 0], [0.777, -0.837], [0.022, -0.022], [0, 0], [0.019, -0.037], [0, -1.096], [-0.656, -0.75], [-0.044, -0.045], [0, 0], [0, 0], [-0.242, -0.231], [-0.861, 0], [0, 1.433], [0, 0], [0.161, 0.346], [0.025, 0.05], [0, 0], [0, 0], [0, 0], [-0.044, 0.058], [0, 0.984]], "o": [[-1.136, 0], [-0.024, 0.02], [0, 0], [-0.03, 0.03], [-0.574, 1.139], [0, 0.992], [0.037, 0.049], [0, 0], [0, 0], [0.061, 0.349], [0.679, 0.002], [1.433, 0], [0, 0], [0, -0.365], [-0.019, -0.052], [0, 0], [0, 0], [0, 0], [0.053, -0.052], [0.647, -0.748], [0, -2.261]], "v": [[1.999, -10.6], [-0.99, -9.288], [-1.058, -9.225], [-3.808, -5.17], [-3.894, -5.077], [-5.131, -2.156], [-4.116, 0.536], [-3.994, 0.678], [-0.897, 3.155], [-1.076, 8.627], [-0.601, 9.513], [1.563, 9.569], [4.1, 8.001], [4.6, 2.594], [4.355, 1.514], [4.288, 1.361], [2.691, -1.394], [4.886, -3.59], [4.953, -3.658], [5.098, -3.824], [6.1, -6.5]], "c": true}]}, {"t": 18, "s": [{"i": [[2.262, 0], [0.777, -0.837], [0.022, -0.022], [0, 0], [0.027, -0.032], [0, -1.096], [-0.656, -0.75], [-0.044, -0.045], [0, 0], [0, 0], [-0.242, -0.231], [-0.861, 0], [0, 1.433], [0, 0], [0.161, 0.346], [0.025, 0.05], [0, 0], [0, 0], [0, 0], [-0.044, 0.058], [0, 0.984]], "o": [[-1.136, 0], [-0.024, 0.02], [0, 0], [-0.03, 0.03], [-0.789, 0.772], [0, 0.992], [0.037, 0.049], [0, 0], [0, 0], [0.061, 0.349], [0.679, 0.002], [1.433, 0], [0, 0], [0, -0.365], [-0.019, -0.052], [0, 0], [0, 0], [0, 0], [0.053, -0.052], [0.647, -0.748], [0, -2.261]], "v": [[1.999, -10.6], [-0.99, -9.288], [-1.058, -9.225], [-4.777, -5.514], [-4.863, -5.421], [-6.1, -2.5], [-5.085, 0.192], [-4.963, 0.334], [-1.991, 3.405], [-1.076, 8.627], [-0.601, 9.513], [1.563, 9.569], [4.1, 8.001], [4.1, 2], [3.855, 0.92], [3.788, 0.767], [2.691, -1.394], [4.886, -3.59], [4.953, -3.658], [5.098, -3.824], [6.1, -6.5]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [6.35, 10.85], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "lottie Outlines", "parent": 7, "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, -0.624, 0], "ix": 2}, "a": {"a": 0, "k": [11.25, 5.874, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[0.473, 0.176], [0, 0], [0, 0], [0, 0], [-2.218, -1.42], [0, 0], [-0.066, -0.023], [0, 0], [0, 0], [0, 0.552]], "o": [[-0.473, -0.176], [0, 0], [0, 0], [-1.42, -1.261], [0, 0], [0.06, 0.036], [0, 0], [0, 0], [0.625, 0.172], [0, -0.441]], "v": [[4.631, 1.675], [2.288, 0.895], [2.292, 0.891], [-1.676, -2.363], [-3.096, 0.253], [0.807, 2.485], [0.997, 2.573], [3.997, 3.573], [4.001, 3.561], [5.314, 2.624]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [{"i": [[0.449, 0.277], [0, 0], [0, 0], [0, 0], [-1.012, -1.816], [0, 0], [-0.066, -0.023], [0, 0], [0, 0], [0, 0.552]], "o": [[-0.566, -0.395], [0, 0], [0, 0], [-1.42, -1.261], [0, 0], [0.06, 0.036], [0, 0], [0, 0], [0.719, 0.391], [0, -0.441]], "v": [[2.693, 3.238], [0.273, 1.27], [0.277, 1.266], [-1.676, -2.363], [-4.049, -1.06], [-1.209, 2.86], [-1.018, 2.948], [1.904, 4.995], [1.939, 5.014], [3.376, 4.327]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[0.34, 0.387], [0, 0], [0, 0], [0, 0], [-1.012, -1.816], [0, 0], [-0.121, -0.136], [0, 0], [0, 0], [-0.25, 0.359]], "o": [[-0.316, -0.52], [0, 0], [0, 0], [-1.42, -1.261], [0, 0], [0.086, 0.217], [0, 0], [0, 0], [0.484, 0.579], [0.141, -0.328]], "v": [[0.849, 4.425], [-0.79, 1.786], [-0.926, 1.344], [-1.675, -2.363], [-4.049, -1.06], [-2.756, 2.501], [-2.409, 3.01], [-0.643, 5.635], [-0.608, 5.686], [1.001, 5.593]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 6, "s": [{"i": [[0.152, 0.668], [0, 0], [0, 0], [0, 0], [0.488, -2.066], [0, 0], [-0.121, -0.136], [0, 0], [0, 0], [-0.687, 0.094]], "o": [[-0.129, -0.551], [0, 0], [0, 0], [0.583, -1.982], [0, 0], [0.086, 0.217], [0, 0], [0, 0], [0.484, 0.579], [0.594, -0.219]], "v": [[-2.245, 5.05], [-3.071, 1.161], [-3.083, 1.188], [-1.676, -2.363], [-4.049, -2.903], [-5.193, 0.938], [-5.003, 1.698], [-3.987, 5.573], [-3.952, 5.655], [-2.718, 6.311]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[-0.535, 0.449], [0, 0], [0, 0], [0, 0], [1.613, -0.473], [0, 0], [0.191, -0.168], [0, 0], [0, 0], [-0.468, -0.312]], "o": [[0.309, -0.332], [0, 0], [0, 0], [2.115, -0.357], [0, 0], [-0.493, 0.186], [0, 0], [0, 0], [-0.641, 0.626], [0.719, 0.156]], "v": [[-10.401, 0.456], [-7.665, -1.777], [-7.645, -1.781], [-3.707, -2.363], [-3.83, -4.903], [-8.037, -3.718], [-8.94, -3.302], [-11.893, -0.833], [-12.014, -0.72], [-11.874, 0.968]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0.152, 0.668], [0, 0], [0, 0], [0, 0], [0.488, -2.066], [0, 0], [-0.121, -0.136], [0, 0], [0, 0], [-0.687, 0.094]], "o": [[-0.129, -0.551], [0, 0], [0, 0], [0.583, -1.982], [0, 0], [0.086, 0.217], [0, 0], [0, 0], [0.484, 0.579], [0.594, -0.219]], "v": [[-2.245, 5.05], [-3.071, 1.161], [-3.083, 1.188], [-1.676, -2.363], [-4.049, -2.903], [-5.193, 0.938], [-5.003, 1.698], [-3.987, 5.573], [-3.952, 5.655], [-2.718, 6.311]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0.34, 0.387], [0, 0], [0, 0], [0, 0], [-1.012, -1.816], [0, 0], [-0.121, -0.136], [0, 0], [0, 0], [-0.25, 0.359]], "o": [[-0.317, -0.52], [0, 0], [0, 0], [-1.42, -1.261], [0, 0], [0.086, 0.217], [0, 0], [0, 0], [0.484, 0.579], [0.141, -0.328]], "v": [[0.849, 4.425], [-0.79, 1.786], [-0.926, 1.344], [-1.675, -2.363], [-4.049, -1.06], [-2.756, 2.501], [-2.409, 3.01], [-0.643, 5.635], [-0.608, 5.686], [1.001, 5.593]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 15, "s": [{"i": [[0.449, 0.277], [0, 0], [0, 0], [0, 0], [-1.012, -1.816], [0, 0], [-0.066, -0.023], [0, 0], [0, 0], [0, 0.552]], "o": [[-0.567, -0.395], [0, 0], [0, 0], [-1.42, -1.261], [0, 0], [0.06, 0.036], [0, 0], [0, 0], [0.719, 0.391], [0, -0.441]], "v": [[2.693, 3.238], [0.273, 1.27], [0.277, 1.266], [-1.676, -2.363], [-4.049, -1.06], [-1.209, 2.86], [-1.018, 2.948], [1.904, 4.995], [1.939, 5.014], [3.376, 4.327]], "c": true}]}, {"t": 16, "s": [{"i": [[0.473, 0.176], [0, 0], [0, 0], [0, 0], [-2.218, -1.42], [0, 0], [-0.066, -0.023], [0, 0], [0, 0], [0, 0.552]], "o": [[-0.473, -0.176], [0, 0], [0, 0], [-1.42, -1.261], [0, 0], [0.06, 0.036], [0, 0], [0, 0], [0.625, 0.172], [0, -0.441]], "v": [[4.631, 1.675], [2.288, 0.895], [2.292, 0.891], [-1.676, -2.363], [-3.096, 0.253], [0.807, 2.485], [0.997, 2.573], [3.997, 3.573], [4.001, 3.561], [5.314, 2.624]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [16.936, 3.874], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 3, "s": [{"i": [[0, 0], [0, 0], [-0.016, -0.86], [-0.552, 0], [0, 0], [0, 0], [0, 0], [1.388, -1.644]], "o": [[0, 0], [0, 0], [0, 0.551], [0, 0], [0.5, -0.003], [0, 0], [1.868, -1.66], [0, 0]], "v": [[0.184, 1.574], [-4.276, 2.176], [-5.185, 3.173], [-4.185, 4.173], [0.674, 4.175], [1.612, 3.786], [5.692, 0.089], [3.602, -2.529]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 5, "s": [{"i": [[0, 0], [0, 0], [0, -0.531], [-0.552, 0], [0, 0], [0, 0], [0, 0], [1.388, -1.644]], "o": [[0, 0], [0, 0], [0, 0.551], [0, 0], [0.217, 0], [0, 0], [1.123, -1.885], [0, 0]], "v": [[2.028, 1.981], [-2.432, 2.583], [-3.341, 3.579], [-2.341, 4.579], [2.659, 4.579], [3.456, 4.192], [6.067, -0.224], [3.352, -1.935]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [0, 0], [-0.406, -0.665], [-0.594, 0.304], [0, 0], [0, 0], [0, 0], [0.244, -0.956]], "o": [[0, 0], [0, 0], [0.289, 0.398], [0, 0], [0.148, -0.477], [0, 0], [0.154, -1.76], [0, 0]], "v": [[3.434, 3.324], [1.896, 6.247], [1.737, 7.813], [3.409, 8.016], [5.573, 4.055], [5.737, 3.41], [6.536, -1.068], [3.352, -1.935]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [-0.359, -0.329], [-0.188, 1.124], [0, 0], [0, 0], [0, 0], [-0.35, -0.675]], "o": [[0, 0], [0, 0], [0.289, 0.398], [0, 0], [0.148, -0.477], [0, 0], [-1.158, -1.417], [0, 0]], "v": [[7.122, 2.856], [6.677, 7.091], [7.018, 7.969], [8.659, 7.11], [9.229, 3.118], [8.987, 2.129], [6.973, -1.287], [3.915, -0.841]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[0, 0], [0, 0], [-0.672, 0.296], [0.234, 0.421], [0, 0], [0, 0], [0, 0], [-0.35, -0.675]], "o": [[0, 0], [0, 0], [0.422, -0.157], [0, 0], [-0.164, -0.321], [0, 0], [-1.158, -1.417], [0, 0]], "v": [[9.809, 2.481], [12.177, 5.247], [13.487, 5.829], [13.878, 4.61], [11.495, 0.977], [10.909, 0.364], [6.442, -1.943], [4.79, 0.19]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [0, 0], [-0.922, 0.249], [0, 0.656], [0, 0], [0, 0], [0, 0], [-0.35, -0.675]], "o": [[0, 0], [0, 0], [0.516, -0.157], [0, 0], [-0.024, -0.399], [0, 0], [-1.158, -1.417], [0, 0]], "v": [[8.403, 4.043], [9.177, 8.466], [10.424, 9.579], [11.221, 8.204], [10.807, 3.196], [10.378, 2.114], [6.442, -1.943], [4.79, 0.19]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [-1.109, -0.188], [-0.156, 0.968], [0, 0], [0, 0], [0, 0], [-0.35, -0.675]], "o": [[0, 0], [0, 0], [0.703, -0.032], [0, 0], [-0.024, -0.774], [0, 0], [-1.158, -1.417], [0, 0]], "v": [[6.715, 5.856], [6.646, 11.747], [7.612, 13.11], [8.784, 11.86], [9.495, 6.008], [9.44, 5.02], [7.88, -0.505], [4.79, 0.19]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 14, "s": [{"i": [[0, 0], [0, 0], [-1.109, -0.188], [-0.438, 0.968], [0, 0], [0, 0], [0, 0], [-0.35, -0.675]], "o": [[0, 0], [0, 0], [0.703, -0.032], [0, 0], [-0.024, -0.774], [0, 0], [-1.158, -1.417], [0, 0]], "v": [[5.84, 5.887], [5.146, 11.591], [5.924, 13.173], [7.284, 11.985], [8.745, 6.165], [8.628, 4.645], [7.88, -0.505], [4.79, 0.19]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [-0.578, -0.719], [-0.594, 0.343], [0, 0], [0, 0], [0, 0], [-0.35, -0.675]], "o": [[0, 0], [0, 0], [0.609, 0.656], [0, 0], [0.726, -0.18], [0, 0], [-1.158, -1.417], [0, 0]], "v": [[4.153, 4.449], [0.021, 6.872], [-0.544, 8.423], [1.284, 8.579], [6.307, 5.883], [7.034, 4.739], [7.88, -0.505], [4.446, -0.654]], "c": true}]}, {"t": 18, "s": [{"i": [[0, 0], [0, 0], [-0.016, -0.86], [-0.552, 0], [0, 0], [0, 0], [0, 0], [1.388, -1.644]], "o": [[0, 0], [0, 0], [0, 0.551], [0, 0], [0.5, -0.003], [0, 0], [1.868, -1.66], [0, 0]], "v": [[0.184, 1.574], [-4.276, 2.176], [-5.185, 3.173], [-4.185, 4.173], [0.674, 4.175], [1.612, 3.786], [5.692, 0.089], [3.602, -2.529]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [6.622, 7.326], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 3, "nm": "Null 1", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [15, 15, 0], "to": [0.5, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [18, 15, 0], "to": [0, 0, 0], "ti": [0.5, 0, 0]}, {"t": 25, "s": [15, 15, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 28, "st": 0, "bm": 0}], "markers": []}