import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class StocktakingProductSelectionScreen extends StatefulWidget {
  const StocktakingProductSelectionScreen({
    super.key,
    required this.onAddProduct,
    required this.onRemoveProduct,
    required this.selectedProducts,
  });

  final Function(ProductDTO) onAddProduct;
  final Function(int, String?) onRemoveProduct;
  final List<ProductDTO> selectedProducts;

  @override
  _StocktakingProductSelectionScreenState createState() =>
      _StocktakingProductSelectionScreenState();
}

class _StocktakingProductSelectionScreenState
    extends State<StocktakingProductSelectionScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final TextEditingController _searchController = TextEditingController();

  int? _selectedCategoryId;
  String _searchText = '';

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadData();
  }

  @override
  void dispose() {
    animationController?.dispose();
    _refreshController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _loadData() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ProductController>(context, listen: false)
          .getItems(resetAndRefresh: true);
      Provider.of<CategoryController>(context, listen: false).fetchCategories();
    });
  }

  void _onRefresh() async {
    await Provider.of<ProductController>(context, listen: false)
        .getItems(resetAndRefresh: true);
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    await Provider.of<ProductController>(context, listen: false)
        .getItems(resetAndRefresh: false);
    _refreshController.loadComplete();
  }

  void _onSearchChanged(String value) {
    setState(() {
      _searchText = value.toLowerCase();
    });
    // Trigger new search with filter
    Provider.of<ProductController>(context, listen: false).getItems(
        resetAndRefresh: true, search: value, categoryId: _selectedCategoryId);
  }

  void _onCategorySelected(int? categoryId) {
    setState(() {
      _selectedCategoryId = categoryId;
    });
    // Trigger new search with filter
    Provider.of<ProductController>(context, listen: false).getItems(
        resetAndRefresh: true, search: _searchText, categoryId: categoryId);
  }

  bool _isProductSelected(ProductDTO product) {
    return widget.selectedProducts.any((selected) =>
        selected.id == product.id &&
        selected.virtualProductId == product.virtualProductId);
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 6,
                )
              ],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Column(
              children: [
                CommonHeader(
                  icon: Icons.inventory_2,
                  title: T("Select Products"),
                  actions: [
                    IconButton(
                      icon: const Icon(Icons.check),
                      onPressed: () => Navigator.pop(context),
                      tooltip: T("Done"),
                    ),
                  ],
                ),

                // Search bar
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextField(
                    controller: _searchController,
                    onChanged: _onSearchChanged,
                    decoration: InputDecoration(
                      hintText: T("Search products..."),
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchText.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                _onSearchChanged('');
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey.shade100,
                    ),
                  ),
                ),

                // Categories
                Consumer<CategoryController>(
                  builder: (context, categoryController, child) {
                    final categories = categoryController.categories;

                    if (categories.isEmpty) {
                      return const SizedBox.shrink();
                    }

                    return Container(
                      height: 50,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        itemCount: categories.length,
                        itemBuilder: (context, index) {
                          final category = categories[index];
                          return _buildCategoryChip(
                            category.name ?? '',
                            _selectedCategoryId == category.iD,
                            () => _onCategorySelected(
                                category.iD == 0 ? null : category.iD),
                          );
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          ),

          // Products Grid
          Expanded(
            child: Consumer<ProductController>(
              builder: (context, productController, child) {
                final products = productController.realProductList;

                if (productController.runningSyncization && products.isEmpty) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (products.isEmpty) {
                  return _buildEmptyState();
                }

                return SmartRefresher(
                  controller: _refreshController,
                  onRefresh: _onRefresh,
                  onLoading: _onLoading,
                  enablePullUp: true,
                  child: MasonryGridView.count(
                    crossAxisCount: 2,
                    mainAxisSpacing: 8,
                    crossAxisSpacing: 8,
                    padding: const EdgeInsets.all(16),
                    itemCount: products.length,
                    itemBuilder: (context, index) {
                      final product = products[index];
                      return _buildProductCard(product);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? context.newPrimaryColor : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? context.newPrimaryColor : Colors.grey.shade300,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : context.newTextColor,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildProductCard(ProductDTO product) {
    final isSelected = _isProductSelected(product);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: isSelected ? context.newPrimaryColor : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () => _handleProductTap(product),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product Image
            Container(
              height: 120,
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: product.thumbnail != null && product.thumbnail!.isNotEmpty
                  ? ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                      child: Image.network(
                        product.thumbnail!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) =>
                            _buildPlaceholderImage(),
                      ),
                    )
                  : _buildPlaceholderImage(),
            ),

            // Product Info
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    product.title ?? '',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 14,
                      color: context.newTextColor,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    T("Code: ${product.code ?? product.barcode ?? 'N/A'}"),
                    style: TextStyle(
                      fontSize: 12,
                      color: context.newTextColor.withOpacity(0.6),
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),

                  // Action Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => _handleProductTap(product),
                      icon: Icon(
                        isSelected ? Icons.remove_circle : Icons.add_circle,
                        size: 16,
                      ),
                      label: Text(
                        isSelected ? T("Remove") : T("Add"),
                        style: const TextStyle(fontSize: 12),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor:
                            isSelected ? Colors.red : context.newPrimaryColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 0,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey.shade100,
      child: Icon(
        Icons.inventory_2,
        size: 40,
        color: Colors.grey.shade400,
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 60,
            color: context.newPrimaryColor.withOpacity(0.4),
          ),
          const SizedBox(height: 16),
          Text(
            T("No products found"),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: context.newTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T("Try changing your search or category filter"),
            style: TextStyle(
              fontSize: 14,
              color: context.newTextColor.withOpacity(0.6),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _onRefresh,
            icon: const Icon(Icons.refresh_outlined),
            label: Text(T("Refresh")),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.newPrimaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleProductTap(ProductDTO product) {
    if (_isProductSelected(product)) {
      // Remove product
      widget.onRemoveProduct(product.id!, product.virtualProductId);
      successSnackBar(message: T("Product removed from stocktaking"));
    } else {
      // Check if product has attributes
      if (product.itemAttributes != null &&
          product.itemAttributes!.isNotEmpty &&
          product.hasSelectedAttributes != true) {
        _showAttributeSelectionDialog(product);
      } else {
        // Add product directly
        product.quantity = 1.0;
        widget.onAddProduct(product);
        successSnackBar(message: T("Product added to stocktaking"));
      }
    }
  }

  // Show attribute selection dialog for products with attributes
  Future<void> _showAttributeSelectionDialog(ProductDTO product) async {
    // Map to store selected options for each attribute
    Map<int, ItemAttributeOption> selectedOptions = {};

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text(
                T('Select Product Options'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: context.newPrimaryColor,
                ),
              ),
              content: SizedBox(
                width: MediaQuery.of(context).size.width * 0.8,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        product.title ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 16),

                      // Display attributes
                      ...product.itemAttributes!.map((attribute) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              attribute.attributeName ?? '',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: context.newTextColor,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: attribute.itemsAttributeOptions
                                      ?.map((option) {
                                    bool isSelected =
                                        selectedOptions[attribute.id!] ==
                                            option;
                                    return GestureDetector(
                                      onTap: () {
                                        setState(() {
                                          selectedOptions[attribute.id!] =
                                              option;
                                        });
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 8,
                                        ),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? context.newPrimaryColor
                                              : Colors.grey.shade200,
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          border: Border.all(
                                            color: isSelected
                                                ? context.newPrimaryColor
                                                : Colors.grey.shade300,
                                          ),
                                        ),
                                        child: Text(
                                          option.optionName ?? '',
                                          style: TextStyle(
                                            color: isSelected
                                                ? Colors.white
                                                : context.newTextColor,
                                            fontWeight: isSelected
                                                ? FontWeight.w600
                                                : FontWeight.normal,
                                          ),
                                        ),
                                      ),
                                    );
                                  }).toList() ??
                                  [],
                            ),
                            const SizedBox(height: 16),
                          ],
                        );
                      }).toList(),
                    ],
                  ),
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text(
                    T('Cancel'),
                    style: TextStyle(color: Colors.grey.shade600),
                  ),
                ),
                ElevatedButton(
                  onPressed: selectedOptions.length ==
                          product.itemAttributes!.length
                      ? () {
                          Navigator.of(context).pop();
                          _addProductWithAttributes(product, selectedOptions);
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.newPrimaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(T('Add to Stocktaking')),
                ),
              ],
            );
          },
        );
      },
    );
  }

  // Add product with selected attributes
  void _addProductWithAttributes(
      ProductDTO product, Map<int, ItemAttributeOption> selectedOptions) {
    // Generate a unique virtual product ID based on product ID and selected options
    String virtualProductId =
        "${product.id}_${selectedOptions.entries.map((e) => "${e.key}_${e.value.id}").join("_")}";

    // Create the selected attributes in the required format
    List<ItemAttribute> selectedAttributes =
        _createSelectedAttributesFormat(selectedOptions, product);

    // Create a copy of the product to avoid modifying the original
    final ProductDTO productCopy = ProductDTO(
      id: product.id,
      title: product.title,
      barcode: product.barcode,
      barcodeName: product.barcodeName,
      code: product.code,
      description: product.description,
      price: product.price,
      stock: product.stock,
      uniteId: product.uniteId,
      uniteName: product.uniteName,
      category: product.category,
      quantity: 1.0,
      warehouseId: product.warehouseId,
      warehouseName: product.warehouseName,
      thumbnail: product.thumbnail,
      attribute:
          selectedAttributes, // Store selected attributes in the required format
      hasSelectedAttributes: true,
      selectedOptionIds:
          selectedOptions.values.map((e) => e.optionId ?? 0).toList(),
      virtualProductId: virtualProductId,
    );

    // Get the formatted selected options (e.g., "S/Red")
    String optionsString = _getFormattedSelectedOptions(selectedOptions);

    // Modify the product title to include selected attributes
    if (optionsString.isNotEmpty) {
      productCopy.title = '${productCopy.title} - $optionsString';

      // Store selected options in the description field for reference
      String attributeDetails = selectedOptions.entries
          .map((entry) =>
              '${product.itemAttributes?.firstWhere((attr) => attr.id == entry.key).attributeName}: ${entry.value.optionName}')
          .join(', ');

      if (productCopy.description == null || productCopy.description!.isEmpty) {
        productCopy.description = attributeDetails;
      } else {
        productCopy.description =
            '${productCopy.description}\n[$attributeDetails]';
      }
    }

    // Add to stocktaking list
    widget.onAddProduct(productCopy);
    successSnackBar(message: T("Product with attributes added to stocktaking"));
  }

  // Create selected attributes in the required format
  List<ItemAttribute> _createSelectedAttributesFormat(
      Map<int, ItemAttributeOption> selectedOptions, ProductDTO product) {
    List<ItemAttribute> selectedAttributes = [];

    // Group by attribute ID and create the required format
    selectedOptions.forEach((attributeId, selectedOption) {
      // Find the original attribute to get its details
      ItemAttribute? originalAttribute = product.itemAttributes?.firstWhere(
        (attr) => attr.id == attributeId,
        orElse: () => ItemAttribute(),
      );

      // Create the attribute with only the selected option
      ItemAttribute selectedAttribute = ItemAttribute(
        id: attributeId,
        attributeTypeId: originalAttribute?.attributeTypeId,
        attributeName: originalAttribute?.attributeName ?? '',
        itemId: originalAttribute?.itemId,
        order: originalAttribute?.order,
        itemsAttributeOptions: [
          ItemAttributeOption(
            id: selectedOption.id, // Use the actual option ID
            attributeId: attributeId,
            optionId: selectedOption.optionId,
            optionName: selectedOption.optionName,
          )
        ],
      );

      selectedAttributes.add(selectedAttribute);
    });

    return selectedAttributes;
  }

  // Format the selected options for display (e.g., "S/Red")
  String _getFormattedSelectedOptions(
      Map<int, ItemAttributeOption> selectedOptions) {
    return selectedOptions.values
        .map((option) => option.optionName ?? '')
        .join('/');
  }
}
