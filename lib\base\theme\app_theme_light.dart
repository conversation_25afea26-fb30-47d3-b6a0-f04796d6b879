import 'package:flutter/material.dart';

import 'app_theme.dart';

class AppThemeLight extends AppTheme {
  static AppThemeLight? _instance;
  static AppThemeLight get instance {
    _instance ??= AppThemeLight._init();
    return _instance!;
  }

  AppThemeLight._init();

  @override
  ThemeData get theme => ThemeData.light().copyWith(
        colorScheme: _buildColorScheme,
      );

  ColorScheme get _buildColorScheme => const ColorScheme(
        brightness: Brightness.light,
        primary: Color(0xFF667EEA),
        onPrimary: Color(0xFF30C896),
        secondary: Color(0xFF764BA2),
        onSecondary: Color(0xffFBC11E),
        outline: Color(0xffE3D9FE),
        error: Color(0xFFd32f2f),
        onError: Color(0xffFBC11E),
        scrim: Colors.black,
        background: Colors.white,
        onBackground: Color.fromARGB(255, 245, 243, 243),
        surface: Colors.white,
        onSurface: Color(0xFF001928),
        surfaceTint: Color.fromARGB(255, 230, 229, 229),
      );
}

 // Custom colors for the app
