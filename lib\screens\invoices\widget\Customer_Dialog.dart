import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_text_field%20copy.dart';

Future<void> showCustomerDialog({
  required BuildContext context,
  required String title,
  required TextEditingController nameController,
  required TextEditingController phoneController,
  required TextEditingController addressController,
  required TextEditingController noteController,
  required VoidCallback? onSave,
  bool? isSynced,
  int? customerId,
  bool readOnly = false,
}) {
  return showDialog(
    context: context,
    builder: (BuildContext context) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Dialog(
          insetPadding: EdgeInsets.zero,
          child: Container(
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.all(12.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  title,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 25),
                TextForm<PERSON>ield(
                  controller: nameController,
                  readOnly: readOnly,
                  decoration: InputDecoration(
                    labelText: T("Customer Name"),
                    border: const OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 15),
                MyPhoneField(
                  textInputAction: TextInputAction.next,
                  hintText: T('0000 000 000'),
                  labelColor: Colors.grey,
                  onChanged: (val) {
                    phoneController.text = val.countryCode + val.number;
                  },
                  onCountryChanged: (val) {},
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: addressController,
                  readOnly: readOnly,
                  decoration: InputDecoration(
                    labelText: T("Address"),
                    border: const OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 15),
                TextFormField(
                  controller: noteController,
                  readOnly: readOnly,
                  decoration: InputDecoration(
                    labelText: T("Note"),
                    border: const OutlineInputBorder(),
                  ),
                ),
                const SizedBox(height: 15),
                if (!readOnly)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      TextButton(
                        onPressed: onSave,
                        child: Text(
                          T("Save"),
                          style: const TextStyle(
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      );
    },
  );
}
