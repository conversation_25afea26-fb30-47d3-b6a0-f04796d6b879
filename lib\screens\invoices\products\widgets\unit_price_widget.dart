
import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_collapse%20.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';

class UnitPriceWidget extends StatelessWidget {
  final String headerTitle;
  final List<ComboBoxDataModel> units; 
  final int? selectedUnitId;
  final String? selectedUnitName;
  final ValueChanged<int> onUnitSelected;
  final TextEditingController salesPrice1Controller;
  final TextEditingController salesPrice2Controller;
  final TextEditingController purchasePrice1Controller;
  final TextEditingController purchasePrice2Controller;

  const UnitPriceWidget({
    super.key,
    required this.headerTitle,
    required this.units, 
    this.selectedUnitId,
    this.selectedUnitName,
    required this.onUnitSelected,
    required this.salesPrice1Controller,
    required this.salesPrice2Controller,
    required this.purchasePrice1Controller,
    required this.purchasePrice2Controller,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.yellow,
        borderRadius: BorderRadius.circular(10),
      ),
      width: MediaQuery.of(context).size.width,
      child: CommonCollapseWidget(
        header: Text(headerTitle),
        body: Column(children: [
          Row(
            children: [
              Text(
                T("unit"),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          MyComboBox(
            caption: selectedUnitName ?? T("Select Unit"),
            height: 50,
            onSelect: (int id, String name) {
              onUnitSelected(id);
            },
            modalTitle: T("unit"),
            data: units, // <-- now works, same type
            isShowLabel: false,
            labelText: "",
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width / 2.3,
                child: Text(T("Sales Price 1"),
                    style: const TextStyle(fontSize: 14)),
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width / 2.3,
                child: Text(T("Sales Price 2"),
                    style: const TextStyle(fontSize: 14)),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width / 2.3,
                child: TextFormField(
                  controller: salesPrice1Controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: T('Sales Price 1'),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                  ),
                ),
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width / 2.3,
                child: TextFormField(
                  controller: salesPrice2Controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: T('Sales Price 2'),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width / 2.3,
                child: Text(T("Purchase Price 1"),
                    style: const TextStyle(fontSize: 14)),
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width / 2.3,
                child: Text(T("Purchase Price 2"),
                    style: const TextStyle(fontSize: 14)),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: MediaQuery.of(context).size.width / 2.3,
                child: TextFormField(
                  controller: purchasePrice1Controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: T('Purchase Price 1'),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                  ),
                ),
              ),
              SizedBox(
                width: MediaQuery.of(context).size.width / 2.3,
                child: TextFormField(
                  controller: purchasePrice2Controller,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: T('Purchase Price 2'),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 16),
                  ),
                ),
              ),
            ],
          ),
        ]),
        isDottedDecoration: false,
        headerColor: Colors.yellow,
      ),
    );
  }
}
