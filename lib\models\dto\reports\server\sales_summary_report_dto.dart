import 'user_sales_summary_dto.dart';
import 'device_sales_summary_dto.dart';

class SalesSummaryReportDTO {
  final List<UserSalesSummaryDTO> byUsers;
  final List<DeviceSalesSummaryDTO> byDevices;
  final DateTime? fromDate;
  final DateTime? toDate;

  SalesSummaryReportDTO({
    required this.byUsers,
    required this.byDevices,
    this.fromDate,
    this.toDate,
  });

  factory SalesSummaryReportDTO.fromJson(
    Map<String, dynamic> json, {
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    List<UserSalesSummaryDTO> usersList = [];
    List<DeviceSalesSummaryDTO> devicesList = [];

    // Parse users data
    if (json['ByUsers'] != null && json['ByUsers'] is List) {
      usersList = (json['ByUsers'] as List)
          .map((userJson) => UserSalesSummaryDTO.fromJson(userJson))
          .toList();
    }

    // Parse devices data
    if (json['ByDevices'] != null && json['ByDevices'] is List) {
      devicesList = (json['ByDevices'] as List)
          .map((deviceJson) => DeviceSalesSummaryDTO.fromJson(deviceJson))
          .toList();
    }

    return SalesSummaryReportDTO(
      byUsers: usersList,
      byDevices: devicesList,
      fromDate: fromDate,
      toDate: toDate,
    );
  }

  // Calculate total sales across all users
  double get totalSalesAllUsers {
    return byUsers.fold<double>(
      0.0,
      (sum, user) => sum + (user.totalSales ?? 0.0),
    );
  }

  // Calculate total returns across all users
  double get totalReturnsAllUsers {
    return byUsers.fold<double>(
      0.0,
      (sum, user) => sum + (user.totalReturns ?? 0.0),
    );
  }

  // Calculate total received across all users
  double get totalReceivedAllUsers {
    return byUsers.fold<double>(
      0.0,
      (sum, user) => sum + (user.totalReceived ?? 0.0),
    );
  }

  // Calculate total remaining across all users
  double get totalRemainingAllUsers {
    return byUsers.fold<double>(
      0.0,
      (sum, user) => sum + (user.totalRemaining ?? 0.0),
    );
  }

  // Calculate total sales across all devices
  double get totalSalesAllDevices {
    return byDevices.fold<double>(
      0.0,
      (sum, device) => sum + (device.totalSales ?? 0.0),
    );
  }

  // Get top performing users by net sales
  List<UserSalesSummaryDTO> getTopUsersBySales({int limit = 5}) {
    final sortedUsers = List<UserSalesSummaryDTO>.from(byUsers);
    sortedUsers.sort((a, b) => b.netSales.compareTo(a.netSales));
    return sortedUsers.take(limit).toList();
  }

  // Get top performing devices by net sales
  List<DeviceSalesSummaryDTO> getTopDevicesBySales({int limit = 5}) {
    final sortedDevices = List<DeviceSalesSummaryDTO>.from(byDevices);
    sortedDevices.sort((a, b) => b.netSales.compareTo(a.netSales));
    return sortedDevices.take(limit).toList();
  }
}
