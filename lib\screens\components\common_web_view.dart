import 'package:flutter/material.dart';

import 'package:webview_flutter/webview_flutter.dart';
import '../home/<USER>';

class CommonWebViewWidget extends StatefulWidget {
  const CommonWebViewWidget({
    super.key,
    required this.url,
  });

  final String url;

  @override
  State<CommonWebViewWidget> createState() => _CommonWebViewWidgetState();
}

class _CommonWebViewWidgetState extends State<CommonWebViewWidget> {
  WebViewController? controller;

  @override
  void initState() {
    print(widget.url);

    // var token =
    //     Provider.of<AuthController>(navigatorKey.currentContext!, listen: false)
    //         .getToken();

    // Map<String, String> headers = {
    //   'Authorization': 'bearer $token',
    // };
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..addJavaScriptChannel(
        'DD',
        onMessageReceived: (p0) {
          print(p0);
        },
      )
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {},
          onPageFinished: (String url) {},
          onWebResourceError: (WebResourceError error) {},
          // onNavigationRequest: (NavigationRequest request) {
          //   // if (request.url.startsWith(url)) {
          //   //   return NavigationDecision.prevent;
          //   // }
          //   // return NavigationDecision.navigate;
          // },
        ),
      )
      // ..loadRequest(Uri.parse(widget.url),
      //     method: LoadRequestMethod.get, headers: headers);
      ..loadRequest(
        Uri.parse(widget.url),
      );
    enableX();
    super.initState();
  }

  void enableX() async {
    await Future.delayed(const Duration(seconds: 5));
    controller?.runJavaScript(
        "function myGreeting(){ var s = {    'pageX':0,    'pageY':100,};  console.log('xxxxxxx'); Wheel(s);   } setTimeout(myGreeting, 5000);");
  }
  // void enableX() async {
  //   await Future.delayed(const Duration(seconds: 5));
  //   controller?.runJavaScript(
  //       "document.addEventListener('DOMContentLoaded', function() {  myGreeting(){ var s = {'pageX':0,    'pageY':100,};  console.log('xxxxxxx'); Wheel(s); });");
  // }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      // selectedBottomNavbarItem: BottomNavbarItems.none,
      child: WebViewWidget(
        controller: controller!,
      ),
    );
  }
}
