import 'package:flutter/foundation.dart';
import 'package:inventory_application/models/model/warehouse_3d_model.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/controllers/app_controller.dart';

class Warehouse3DIntegrationService {
  static final Warehouse3DIntegrationService _instance =
      Warehouse3DIntegrationService._internal();

  factory Warehouse3DIntegrationService() {
    return _instance;
  }

  Warehouse3DIntegrationService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// ربط المنتج الحالي بموقع ثلاثي الأبعاد
  Future<bool> linkProductToLocation({
    required int productId,
    required String productName,
    required String productCode,
    required int warehouseId,
    required int cabinetId,
    required int shelfId,
    required double quantity,
    double? xPosition,
    double? yPosition,
    double? zPosition,
    String? batchNumber,
    DateTime? expiryDate,
  }) async {
    try {
      final productLocation = ProductLocation3D(
        productId: productId,
        productName: productName,
        productCode: productCode,
        warehouseId: warehouseId,
        cabinetId: cabinetId,
        shelfId: shelfId,
        quantity: quantity,
        xPosition: xPosition ?? 0.0,
        yPosition: yPosition ?? 0.0,
        zPosition: zPosition ?? 0.0,
        batchNumber: batchNumber,
        expiryDate: expiryDate,
        lastUpdated: DateTime.now(),
      );

      final db = await _dbHelper.database;

      // التحقق من وجود المنتج في نفس الموقع
      final existingLocation = await db.query(
        'ProductLocation3D',
        where: 'productId = ? AND shelfId = ? AND BranchId = ?',
        whereArgs: [productId, shelfId, AppController.currentBranchId],
      );

      if (existingLocation.isNotEmpty) {
        // تحديث الكمية الموجودة
        await db.update(
          'ProductLocation3D',
          {
            'quantity': quantity,
            'lastUpdated': DateTime.now().toIso8601String(),
            'batchNumber': batchNumber,
            'expiryDate': expiryDate?.toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [existingLocation.first['id']],
        );
      } else {
        // إدراج موقع جديد
        await db.insert('ProductLocation3D', {
          'productId': productLocation.productId,
          'productName': productLocation.productName,
          'productCode': productLocation.productCode,
          'shelfId': productLocation.shelfId,
          'cabinetId': productLocation.cabinetId,
          'warehouseId': productLocation.warehouseId,
          'quantity': productLocation.quantity,
          'xPosition': productLocation.xPosition,
          'yPosition': productLocation.yPosition,
          'zPosition': productLocation.zPosition,
          'lastUpdated': productLocation.lastUpdated?.toIso8601String(),
          'batchNumber': productLocation.batchNumber,
          'expiryDate': productLocation.expiryDate?.toIso8601String(),
          'BranchId': AppController.currentBranchId,
        });
      }

      // تحديث حالة الرف
      await _updateShelfStatus(shelfId);

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error linking product to location: $e');
      }
      return false;
    }
  }

  /// تحديث كمية المنتج في موقع معين
  Future<bool> updateProductQuantity({
    required int productId,
    required int shelfId,
    required double newQuantity,
  }) async {
    try {
      final db = await _dbHelper.database;

      await db.update(
        'ProductLocation3D',
        {
          'quantity': newQuantity,
          'lastUpdated': DateTime.now().toIso8601String(),
        },
        where: 'productId = ? AND shelfId = ? AND BranchId = ?',
        whereArgs: [productId, shelfId, AppController.currentBranchId],
      );

      // تحديث حالة الرف
      await _updateShelfStatus(shelfId);

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating product quantity: $e');
      }
      return false;
    }
  }

  /// نقل منتج من موقع إلى آخر
  Future<bool> moveProduct({
    required int productId,
    required int fromShelfId,
    required int toShelfId,
    required int toCabinetId,
    required int toWarehouseId,
    required double quantity,
  }) async {
    try {
      final db = await _dbHelper.database;

      // جلب المنتج من الموقع الحالي
      final currentLocation = await db.query(
        'ProductLocation3D',
        where: 'productId = ? AND shelfId = ? AND BranchId = ?',
        whereArgs: [productId, fromShelfId, AppController.currentBranchId],
      );

      if (currentLocation.isEmpty) {
        return false;
      }

      final location = ProductLocation3D.fromJson(currentLocation.first);

      // تقليل الكمية من الموقع الحالي
      final remainingQuantity = (location.quantity ?? 0) - quantity;

      if (remainingQuantity < 0) {
        return false; // كمية غير كافية
      }

      if (remainingQuantity == 0) {
        // حذف الموقع إذا أصبحت الكمية صفر
        await db.delete(
          'ProductLocation3D',
          where: 'id = ?',
          whereArgs: [location.id],
        );
      } else {
        // تحديث الكمية المتبقية
        await db.update(
          'ProductLocation3D',
          {
            'quantity': remainingQuantity,
            'lastUpdated': DateTime.now().toIso8601String(),
          },
          where: 'id = ?',
          whereArgs: [location.id],
        );
      }

      // إضافة المنتج للموقع الجديد
      await linkProductToLocation(
        productId: productId,
        productName: location.productName ?? '',
        productCode: location.productCode ?? '',
        warehouseId: toWarehouseId,
        cabinetId: toCabinetId,
        shelfId: toShelfId,
        quantity: quantity,
        batchNumber: location.batchNumber,
        expiryDate: location.expiryDate,
      );

      // تحديث حالة الأرفف
      await _updateShelfStatus(fromShelfId);
      await _updateShelfStatus(toShelfId);

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error moving product: $e');
      }
      return false;
    }
  }

  /// ربط المنتجات الموجودة بمواقع ثلاثية الأبعاد
  Future<bool> syncExistingProducts() async {
    try {
      final db = await _dbHelper.database;

      // جلب جميع المنتجات مع مخزونها
      final products = await db.rawQuery('''
        SELECT p.*, i.Store_Name, i.Quantity_Balance
        FROM ProductModel p
        INNER JOIN Inventory i ON p.ID = i.Product_ID
        WHERE p.BranchId = ? AND i.Quantity_Balance > 0
      ''', [AppController.currentBranchId]);

      // جلب المستودعات والخزائن والأرفف المتاحة
      final warehouses = await db.query('Warehouse3D',
          where: 'BranchId = ?', whereArgs: [AppController.currentBranchId]);

      if (warehouses.isEmpty) {
        return false; // لا توجد مستودعات ثلاثية الأبعاد
      }

      for (final productData in products) {
        final productId = productData['ID'] as int;
        final productName = productData['Name'] as String?;
        final productCode = productData['Code'] as String?;
        final storeName = productData['Store_Name'] as String?;
        final quantity = productData['Quantity_Balance'] as double?;

        // البحث عن مستودع مناسب بناء على اسم المخزن
        final warehouse = _findMatchingWarehouse(warehouses, storeName);

        if (warehouse != null && quantity != null && quantity > 0) {
          // البحث عن خزانة وقف مناسبين
          final cabinetAndShelf =
              await _findAvailableCabinetAndShelf(warehouse['id'] as int);

          if (cabinetAndShelf != null) {
            await linkProductToLocation(
              productId: productId,
              productName: productName ?? 'منتج غير محدد',
              productCode: productCode ?? '',
              warehouseId: warehouse['id'] as int,
              cabinetId: cabinetAndShelf['cabinetId']!,
              shelfId: cabinetAndShelf['shelfId']!,
              quantity: quantity,
            );
          }
        }
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error syncing existing products: $e');
      }
      return false;
    }
  }

  /// البحث عن مستودع مناسب
  Map<String, dynamic>? _findMatchingWarehouse(
      List<Map<String, dynamic>> warehouses, String? storeName) {
    if (warehouses.isEmpty) return null;

    // إذا كان هناك اسم مخزن، حاول العثور على مستودع بنفس الاسم
    if (storeName != null) {
      for (final warehouse in warehouses) {
        final warehouseName = warehouse['name'] as String?;
        if (warehouseName != null &&
            warehouseName.toLowerCase().contains(storeName.toLowerCase())) {
          return warehouse;
        }
      }
    }

    // إذا لم يتم العثور على تطابق، استخدم أول مستودع
    return warehouses.first;
  }

  /// البحث عن خزانة ورف متاحين
  Future<Map<String, int>?> _findAvailableCabinetAndShelf(
      int warehouseId) async {
    try {
      final db = await _dbHelper.database;

      // البحث عن خزانة في المستودع
      final cabinets = await db.query(
        'Cabinet3D',
        where: 'warehouseId = ? AND BranchId = ?',
        whereArgs: [warehouseId, AppController.currentBranchId],
      );

      if (cabinets.isEmpty) return null;

      // البحث عن رف غير ممتلئ
      for (final cabinet in cabinets) {
        final cabinetId = cabinet['id'] as int;

        final shelves = await db.query(
          'Shelf3D',
          where:
              'cabinetId = ? AND BranchId = ? AND (currentOccupancy < maxCapacity OR currentOccupancy IS NULL)',
          whereArgs: [cabinetId, AppController.currentBranchId],
        );

        if (shelves.isNotEmpty) {
          return {
            'cabinetId': cabinetId,
            'shelfId': shelves.first['id'] as int,
          };
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding available cabinet and shelf: $e');
      }
      return null;
    }
  }

  /// تحديث حالة الرف
  Future<void> _updateShelfStatus(int shelfId) async {
    try {
      final db = await _dbHelper.database;

      // حساب عدد المنتجات في الرف
      final productCount = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM ProductLocation3D
        WHERE shelfId = ? AND BranchId = ?
      ''', [shelfId, AppController.currentBranchId]);

      final count = productCount.first['count'] as int;

      // جلب معلومات الرف
      final shelf = await db.query(
        'Shelf3D',
        where: 'id = ? AND BranchId = ?',
        whereArgs: [shelfId, AppController.currentBranchId],
      );

      if (shelf.isNotEmpty) {
        final maxCapacity = shelf.first['maxCapacity'] as int? ?? 100;

        ShelfStatus status;
        if (count == 0) {
          status = ShelfStatus.empty;
        } else if (count < maxCapacity) {
          status = ShelfStatus.partial;
        } else if (count == maxCapacity) {
          status = ShelfStatus.full;
        } else {
          status = ShelfStatus.overloaded;
        }

        // تحديث حالة الرف
        await db.update(
          'Shelf3D',
          {
            'currentOccupancy': count,
            'status': status.toString().split('.').last,
          },
          where: 'id = ? AND BranchId = ?',
          whereArgs: [shelfId, AppController.currentBranchId],
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating shelf status: $e');
      }
    }
  }

  /// جلب موقع منتج معين
  Future<ProductLocation3D?> getProductLocation(int productId) async {
    try {
      final db = await _dbHelper.database;

      final results = await db.query(
        'ProductLocation3D',
        where: 'productId = ? AND BranchId = ?',
        whereArgs: [productId, AppController.currentBranchId],
        limit: 1,
      );

      if (results.isNotEmpty) {
        return ProductLocation3D.fromJson(results.first);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting product location: $e');
      }
      return null;
    }
  }

  /// البحث عن منتجات قريبة من انتهاء الصلاحية
  Future<List<ProductLocation3D>> getExpiringProducts(
      {int daysBeforeExpiry = 30}) async {
    try {
      final db = await _dbHelper.database;
      final cutoffDate = DateTime.now().add(Duration(days: daysBeforeExpiry));

      final results = await db.query(
        'ProductLocation3D',
        where: 'expiryDate IS NOT NULL AND expiryDate <= ? AND BranchId = ?',
        whereArgs: [
          cutoffDate.toIso8601String(),
          AppController.currentBranchId
        ],
        orderBy: 'expiryDate ASC',
      );

      return results.map((map) => ProductLocation3D.fromJson(map)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting expiring products: $e');
      }
      return [];
    }
  }

  /// تقرير استخدام المستودع
  Future<Map<String, dynamic>> getWarehouseUtilizationReport(
      int warehouseId) async {
    try {
      final db = await _dbHelper.database;

      // إجمالي الخزائن والأرفف
      final totalCabinets = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM Cabinet3D
        WHERE warehouseId = ? AND BranchId = ?
      ''', [warehouseId, AppController.currentBranchId]);

      final totalShelves = await db.rawQuery('''
        SELECT COUNT(*) as count
        FROM Shelf3D s
        INNER JOIN Cabinet3D c ON s.cabinetId = c.id
        WHERE c.warehouseId = ? AND s.BranchId = ?
      ''', [warehouseId, AppController.currentBranchId]);

      // الأرفف المستخدمة
      final occupiedShelves = await db.rawQuery('''
        SELECT COUNT(DISTINCT s.id) as count
        FROM Shelf3D s
        INNER JOIN Cabinet3D c ON s.cabinetId = c.id
        INNER JOIN ProductLocation3D p ON s.id = p.shelfId
        WHERE c.warehouseId = ? AND s.BranchId = ?
      ''', [warehouseId, AppController.currentBranchId]);

      // إجمالي المنتجات
      final totalProducts = await db.rawQuery('''
        SELECT COUNT(*) as count, SUM(quantity) as totalQuantity
        FROM ProductLocation3D p
        INNER JOIN Shelf3D s ON p.shelfId = s.id
        INNER JOIN Cabinet3D c ON s.cabinetId = c.id
        WHERE c.warehouseId = ? AND p.BranchId = ?
      ''', [warehouseId, AppController.currentBranchId]);

      final cabinetCount = totalCabinets.first['count'] as int;
      final shelfCount = totalShelves.first['count'] as int;
      final occupiedShelfCount = occupiedShelves.first['count'] as int;
      final productCount = totalProducts.first['count'] as int;
      final totalQuantity =
          totalProducts.first['totalQuantity'] as double? ?? 0.0;

      return {
        'totalCabinets': cabinetCount,
        'totalShelves': shelfCount,
        'occupiedShelves': occupiedShelfCount,
        'utilizationRate':
            shelfCount > 0 ? (occupiedShelfCount / shelfCount * 100) : 0.0,
        'totalProducts': productCount,
        'totalQuantity': totalQuantity,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error generating utilization report: $e');
      }
      return {};
    }
  }
}
