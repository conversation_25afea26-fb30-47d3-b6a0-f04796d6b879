import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/accounting_report_helper_controller.dart';
import 'package:inventory_application/models/dto/reports/closing_entries_report_dto.dart';
import 'package:provider/provider.dart';

class AdminTransactionsDialog extends StatefulWidget {
  final AdminSummaryDTO admin;
  final DateTime? fromDate;
  final DateTime? toDate;

  const AdminTransactionsDialog({
    Key? key,
    required this.admin,
    this.fromDate,
    this.toDate,
  }) : super(key: key);

  @override
  State<AdminTransactionsDialog> createState() =>
      _AdminTransactionsDialogState();
}

class _AdminTransactionsDialogState extends State<AdminTransactionsDialog> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAdminTransactions();
    });
  }

  Future<void> _loadAdminTransactions() async {
    final controller = context.read<AccountingReportHelperController>();
    await controller.getTransactionsByAdminId(
      widget.admin.adminId!,
      fromDate: widget.fromDate,
      toDate: widget.toDate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Consumer<AccountingReportHelperController>(
          builder: (context, controller, child) {
        final transactions = controller.adminTransactions;
        if (controller.isLoadingAdminTransactions) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Container(
          width: MediaQuery.of(context).size.width * 0.9,
          height: MediaQuery.of(context).size.height * 0.8,
          child: Column(
            children: [
              // Fixed Header
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.person,
                      color: Colors.green,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'حركات الادمن: ${transactions.transactionDetails?.totalRechargeRecords ?? 'غير محدد'}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                          Text(
                            'الحساب: ${widget.admin.adminAccount ?? 'غير محدد'}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),

              // Scrollable Content
              Expanded(
                child: Scrollbar(
                  thumbVisibility: true,
                  trackVisibility: true,
                  thickness: 8.0,
                  radius: const Radius.circular(4.0),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Summary
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            // color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildSummaryCard(
                                      'اجمالي المبلغ',
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalAmount),
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalAmountCard),
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalAmountCash),
                                      Colors.orange,
                                      Icons.monetization_on,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: _buildSummaryCard(
                                      'إجمالي الشحن',
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalRechargeAmount),
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalRechargeAmountCard),
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalRechargeAmountCash),
                                      Colors.blue,
                                      Icons.account_balance_wallet,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 16),
                              Row(
                                children: [
                                  Expanded(
                                    child: _buildSummaryCard(
                                      'رسوم الكروت',
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalFeesAmount),
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalFeesAmountCard),
                                      controller.formatNumber(transactions
                                          .financialSummary
                                          ?.rechargeSummary
                                          ?.totalFeesAmountCash),
                                      Colors.green,
                                      Icons.shopping_cart,
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: _buildSummaryCard(
                                      'رسوم الاباء',
                                      (controller.formatNumber((transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalAmount ??
                                              0) -
                                          (transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalRechargeAmount ??
                                              0) -
                                          (transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalFeesAmount ??
                                              0))),
                                      controller.formatNumber((transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalAmountCard ??
                                              0) -
                                          (transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalRechargeAmountCard ??
                                              0) -
                                          (transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalFeesAmountCard ??
                                              0)),
                                      controller.formatNumber((transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalAmountCash ??
                                              0) -
                                          (transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalRechargeAmountCash ??
                                              0) -
                                          (transactions
                                                  .financialSummary
                                                  ?.rechargeSummary
                                                  ?.totalFeesAmountCash ??
                                              0)),
                                      Colors.purple,
                                      Icons.family_restroom,
                                    ),
                                  ),
                                  // const SizedBox(width: 16),
                                  // Expanded(
                                  //   child: _buildSummaryCard(
                                  //     'اجمالي الهدايا',
                                  //     controller.formatNumber(controller.getGiftsAmount()),
                                  //     Colors.purple,
                                  //     Icons.account_balance,
                                  //   ),
                                  // ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),

                        // Transactions Section Header
                        const Row(
                          children: [
                            Icon(
                              Icons.list_alt,
                              color: Colors.green,
                              size: 20,
                            ),
                            SizedBox(width: 8),
                            Text(
                              'قائمة المعاملات',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),

                        // Transactions List
                        if (transactions
                                .transactionDetails?.recharges?.isEmpty ??
                            true)
                          Center(
                            child: Padding(
                              padding: const EdgeInsets.all(50),
                              child: Column(
                                children: [
                                  Icon(
                                    Icons.inbox,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    'لا توجد معاملات لهذا المدير',
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        else
                          ...?transactions.transactionDetails?.recharges?.map(
                            (transaction) => _buildCardHistoryTransactionTile(
                              transaction,
                              context.read<AccountingReportHelperController>(),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildCardHistoryTransactionTile(RechargeTransactionDTO transaction,
      AccountingReportHelperController controller) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'رقم البطاقة: ${transaction.cardNumber ?? 'غير محدد'}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                        color: Colors.blue,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'اسم العضو: ${transaction.memberName ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      'الشركة: ${transaction.companyName ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 12),
                    ),
                    Text(
                      'اسم الادمن: ${transaction.adminName ?? 'غير محدد'}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    'نوع الدفع: ${transaction.paymentTypeId == 1 ? 'نقدا' : 'بطاقة'}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    controller.formatNumber(transaction.totalAmount),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                      fontSize: 16,
                    ),
                  ),
                  if (transaction.totalFees != null &&
                      transaction.totalFees! > 0)
                    Text(
                      'رسوم: ${controller.formatNumber(transaction.totalFees)}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange[700],
                      ),
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Row(
                  children: [
                    Icon(Icons.calendar_today,
                        size: 14, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      transaction.date?.split('T')[0] ?? 'غير محدد',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              if (transaction.rechargeAmount != null)
                Text(
                  'مبلغ الشحن: ${controller.formatNumber(transaction.rechargeAmount)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.green,
                  ),
                ),
              if (transaction.giftAmount != null &&
                  transaction.giftAmount! > 0) ...[
                const SizedBox(width: 8),
                Text(
                  'هدية: ${controller.formatNumber(transaction.giftAmount)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.blue,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, String valueCard,
      String valueCash, Color color, IconData icon) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // Gradient overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color.withOpacity(0.05),
                      Colors.transparent,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            ),
            // Main content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with icon and title
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: color.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: color.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          icon,
                          color: color,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          title,
                          style: TextStyle(
                            color: const Color(0xFF2D3748),
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Main value
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: color,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Payment methods breakdown
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        // Cards payment
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.blue.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.credit_card,
                                color: Colors.blue,
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'بطاقة:',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              valueCard,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.blue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // Cash payment
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.payments,
                                color: Colors.green,
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'نقداً:',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              valueCash,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // Clear admin transactions when dialog is closed
    context.read<AccountingReportHelperController>().clearAdminTransactions();
    super.dispose();
  }
}
