import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';

class WarehousesTab extends StatelessWidget {
  final ProductDTO data;

  const WarehousesTab({Key? key, required this.data}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: context.primaryColor),
                  ),
                  const Text(
                    "الكمية المتوفرة",
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              Divider(
                color: context.colors.primary,
                thickness: 1,
              ),
              data.warehouse != null && data.warehouse!.isNotEmpty
                  ? ListView.builder(
                      shrinkWrap: true,
                      itemCount: data.warehouse!.length,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        final warehouse = data.warehouse![index];
                        return Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  warehouse.name ?? "لا توجد بيانات",
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(fontSize: 16),
                                ),
                                Text(
                                  warehouse.quantity.toString(),
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ],
                            ),
                            Divider(
                              color: context.colors.primary,
                              thickness: 1,
                            ),
                          ],
                        );
                      },
                    )
                  : const SizedBox(),
            ],
          ),
        ),
      ],
    );
  }
}
