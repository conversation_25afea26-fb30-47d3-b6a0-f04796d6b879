import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/receipt_voucher_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/accounting/receipt_voucher/widgets/receipt_voucher_form_widget.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';

class ReceiptVoucherScreen extends StatefulWidget {
  const ReceiptVoucherScreen({super.key});

  @override
  State<ReceiptVoucherScreen> createState() => _ReceiptVoucherScreenState();
}

class _ReceiptVoucherScreenState extends State<ReceiptVoucherScreen> {
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<ReceiptVoucherController>(context, listen: false)
          .getReceiptVoucherNumber();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Scaffold(
        backgroundColor: const Color(0xFFF8F9FD),
        body: Consumer<ReceiptVoucherController>(
          builder: (context, provider, child) {
            return Column(
              children: [
                // Header Section
                CommonHeader(
                  icon: Icons.receipt_outlined,
                  title: T("Receipt Voucher"),
                  subtitle: T("Create new receipt voucher"),
                ),

                // Main Content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        // Voucher Number Card
                        _buildVoucherNumberCard(provider),

                        const SizedBox(height: 16),

                        // Form Section
                        Form(
                          key: _formKey,
                          child: ReceiptVoucherFormWidget(
                            onFieldChanged: (field, value) {
                              _handleFieldChange(field, value, provider);
                            },
                          ),
                        ),

                        const SizedBox(height: 24),

                        // Action Buttons
                        _buildActionButtons(provider),

                        const SizedBox(height: 20),
                      ],
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildVoucherNumberCard(ReceiptVoucherController provider) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.primaryColor,
            context.primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: context.primaryColor.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.receipt_long,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                T("Voucher Number"),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          FutureBuilder<String>(
            future: provider.getReceiptVoucherNumber(),
            builder: (context, snapshot) {
              return Text(
                snapshot.data ?? "...",
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 1.2,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(ReceiptVoucherController provider) {
    return Column(
      children: [
        // Save Button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: _isLoading ? null : () => _saveVoucher(provider),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.primaryColor,
              foregroundColor: Colors.white,
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.save_outlined, size: 20),
                      const SizedBox(width: 8),
                      Text(
                        T("Save Voucher"),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
          ),
        ),

        const SizedBox(height: 12),

        // Clear Button
        SizedBox(
          width: double.infinity,
          height: 48,
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => _clearForm(provider),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.grey[600],
              side: BorderSide(color: Colors.grey[300]!),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.clear_outlined, size: 18),
                const SizedBox(width: 8),
                Text(
                  T("Clear Form"),
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _handleFieldChange(
      String field, dynamic value, ReceiptVoucherController provider) {
    switch (field) {
      case 'amount':
        provider.updateReceiptVoucherField(voucharAmount: value);
        break;
      case 'note':
        provider.updateReceiptVoucherField(note: value);
        break;
      case 'debitAccount':
        provider.updateReceiptVoucherField(accountIdDebit: value);
        break;
      case 'creditAccount':
        provider.updateReceiptVoucherField(accountIdCredit: value);
        break;
      case 'currency':
        provider.updateReceiptVoucherField(currencyId: value);
        break;
      case 'exchangeRate':
        provider.updateReceiptVoucherField(exchangeRate: value);
        break;
      case 'date':
        provider.updateReceiptVoucherField(voucharDateFormated: value);
        break;
      case 'customer':
        provider.updateReceiptVoucherField(customerName: value);
        break;
      case 'invoiceNo':
        provider.updateReceiptVoucherField(invoiceNo: value);
        break;
      case 'paymentType':
        provider.updateReceiptVoucherField(paymentTypeId: value);
        break;
    }
  }

  Future<void> _saveVoucher(ReceiptVoucherController provider) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Validate voucher data
    if (!await provider.validateReceiptVoucher()) {
      setState(() {
        _isLoading = false;
      });
      errorSnackBar(
        message: T("Please fill all required fields"),
        context: context,
      );
      return;
    }

    try {
      final result = await provider.saveReceiptVoucher();

      if (result.isSuccess) {
        HapticFeedback.lightImpact();
        successSnackBar(
          message: T("Receipt voucher saved successfully"),
          context: context,
        );

        // Navigate back or clear form
        Navigator.of(context).pop();
      } else {
        errorSnackBar(
          message: result.message?.first ?? T("Failed to save voucher"),
          context: context,
        );
      }
    } catch (e) {
      errorSnackBar(
        message: T("An error occurred while saving"),
        context: context,
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _clearForm(ReceiptVoucherController provider) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Text(T("Clear Form")),
          content: Text(T("Are you sure you want to clear all data?")),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(T("Cancel")),
            ),
            ElevatedButton(
              onPressed: () {
                provider.clearReceiptVoucher();
                Navigator.of(context).pop();
                HapticFeedback.lightImpact();
                successSnackBar(
                  message: T("Form cleared"),
                  context: context,
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: Text(T("Clear")),
            ),
          ],
        );
      },
    );
  }
}
