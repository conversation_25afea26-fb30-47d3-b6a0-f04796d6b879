import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/controllers/warehouse_3d_controller.dart';
import 'package:inventory_application/models/model/warehouse_3d_model.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/warehouse_3d/warehouse_3d_designer_screen.dart';
import 'package:inventory_application/screens/warehouse_3d/warehouse_3d_search_screen.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/helpers/warehouse_3d_demo_data.dart';

class Warehouse3DManagerScreen extends StatefulWidget {
  const Warehouse3DManagerScreen({Key? key}) : super(key: key);

  @override
  State<Warehouse3DManagerScreen> createState() =>
      _Warehouse3DManagerScreenState();
}

class _Warehouse3DManagerScreenState extends State<Warehouse3DManagerScreen> {
  late Warehouse3DController _controller;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _controller = Provider.of<Warehouse3DController>(context, listen: false);
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    await _controller.fetchWarehouses3D();

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F7FA),
      appBar: AppBar(
        title: Text(
          T('إدارة المستودعات ثلاثية الأبعاد'),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: const Color(0xFF2C3E50),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: _openSearchScreen,
          ),
          IconButton(
            icon: const Icon(Icons.science, color: Colors.white),
            onPressed: _addDemoData,
            tooltip: T('إضافة بيانات تجريبية'),
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadData,
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewWarehouse,
        backgroundColor: const Color(0xFF3498DB),
        icon: const Icon(Icons.add, color: Colors.white),
        label: Text(
          T('مستودع جديد'),
          style: const TextStyle(color: Colors.white),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // بانر توضيحي
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF3498DB), Color(0xFF2980B9)],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.blue.withOpacity(0.3),
                        spreadRadius: 1,
                        blurRadius: 6,
                        offset: Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.white, size: 24),
                      SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🎯 مرحباً بك في نظام المستودعات ثلاثية الأبعاد!',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'ابدأ بإضافة بيانات تجريبية، ثم جرب البحث والزوم في العرض ثلاثي الأبعاد',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // المحتوى الأساسي
                Expanded(
                  child: Consumer<Warehouse3DController>(
                    builder: (context, controller, child) {
                      if (controller.warehouses3D.isEmpty) {
                        return _buildEmptyState();
                      }
                      return _buildWarehousesList(controller);
                    },
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warehouse,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            T('لا توجد مستودعات ثلاثية الأبعاد'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T('جرب إضافة بيانات تجريبية أولاً لاستكشاف المميزات'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          // زر البيانات التجريبية - أولوية أعلى
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Color(0xFF27AE60), Color(0xFF2ECC71)],
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withOpacity(0.3),
                  spreadRadius: 1,
                  blurRadius: 6,
                  offset: Offset(0, 3),
                ),
              ],
            ),
            child: ElevatedButton.icon(
              onPressed: _addDemoData,
              icon: const Icon(Icons.rocket_launch, color: Colors.white),
              label: Text(
                T('🚀 ابدأ مع البيانات التجريبية'),
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.transparent,
                shadowColor: Colors.transparent,
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              ),
            ),
          ),

          const SizedBox(height: 16),
          Text(
            'أو',
            style: TextStyle(color: Colors.grey[500], fontSize: 14),
          ),
          const SizedBox(height: 16),

          // زر الإنشاء الجديد
          OutlinedButton.icon(
            onPressed: _createNewWarehouse,
            icon: const Icon(Icons.add, color: Color(0xFF3498DB)),
            label: Text(
              T('إنشاء مستودع جديد فارغ'),
              style: TextStyle(color: Color(0xFF3498DB)),
            ),
            style: OutlinedButton.styleFrom(
              side: BorderSide(color: Color(0xFF3498DB)),
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWarehousesList(Warehouse3DController controller) {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: CustomScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          // إحصائيات سريعة
          SliverToBoxAdapter(
            child: _buildQuickStats(controller),
          ),

          // قائمة المستودعات
          SliverPadding(
            padding: const EdgeInsets.all(16),
            sliver: SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final warehouse = controller.warehouses3D[index];
                  return _buildWarehouseCard(warehouse);
                },
                childCount: controller.warehouses3D.length,
              ),
            ),
          ),

          // مساحة إضافية للـ FloatingActionButton
          const SliverToBoxAdapter(
            child: SizedBox(height: 80),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickStats(Warehouse3DController controller) {
    final totalCabinets =
        controller.warehouses3D.expand((w) => w.cabinets ?? []).length;

    final totalShelves = controller.warehouses3D
        .expand((w) => w.cabinets ?? [])
        .expand((c) => c.shelves ?? [])
        .length;

    final totalProducts = controller.warehouses3D
        .expand((w) => w.cabinets ?? [])
        .expand((c) => c.shelves ?? [])
        .expand((s) => s.products ?? [])
        .length;

    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF3498DB).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.analytics,
                    color: Color(0xFF3498DB),
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  T('إحصائيات سريعة'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    T('المستودعات'),
                    controller.warehouses3D.length.toString(),
                    Icons.warehouse,
                    const Color(0xFF9B59B6),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    T('الخزائن'),
                    totalCabinets.toString(),
                    Icons.inbox,
                    const Color(0xFF3498DB),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    T('الأرفف'),
                    totalShelves.toString(),
                    Icons.shelves,
                    const Color(0xFF27AE60),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    T('المنتجات'),
                    totalProducts.toString(),
                    Icons.inventory_2,
                    const Color(0xFFE67E22),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseCard(Warehouse3D warehouse) {
    final cabinetCount = warehouse.cabinets?.length ?? 0;
    final shelfCount =
        warehouse.cabinets?.expand((c) => c.shelves ?? []).length ?? 0;
    final productCount = warehouse.cabinets
            ?.expand((c) => c.shelves ?? [])
            .expand((s) => s.products ?? [])
            .length ??
        0;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _openWarehouseDesigner(warehouse),
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // رأس البطاقة
                Row(
                  children: [
                    Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.warehouse,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            warehouse.name ?? 'مستودع غير محدد',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF2C3E50),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            warehouse.description ?? 'لا يوجد وصف',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    PopupMenuButton<String>(
                      onSelected: (value) =>
                          _handleWarehouseAction(value, warehouse),
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              const Icon(Icons.edit, size: 16),
                              const SizedBox(width: 8),
                              Text(T('تعديل')),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'search',
                          child: Row(
                            children: [
                              const Icon(Icons.search, size: 16),
                              const SizedBox(width: 8),
                              Text(T('بحث')),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              const Icon(Icons.delete,
                                  size: 16, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(T('حذف'),
                                  style: const TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // الأبعاد
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.straighten,
                          size: 16, color: Color(0xFF6C7B7F)),
                      const SizedBox(width: 8),
                      Text(
                        '${warehouse.length?.toStringAsFixed(1) ?? "0"} × ${warehouse.width?.toStringAsFixed(1) ?? "0"} × ${warehouse.height?.toStringAsFixed(1) ?? "0"} م',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF6C7B7F),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // الإحصائيات
                Row(
                  children: [
                    Expanded(
                      child: _buildWarehouseStatItem(
                        T('خزائن'),
                        cabinetCount.toString(),
                        Icons.inbox,
                        const Color(0xFF3498DB),
                      ),
                    ),
                    Expanded(
                      child: _buildWarehouseStatItem(
                        T('أرفف'),
                        shelfCount.toString(),
                        Icons.shelves,
                        const Color(0xFF27AE60),
                      ),
                    ),
                    Expanded(
                      child: _buildWarehouseStatItem(
                        T('منتجات'),
                        productCount.toString(),
                        Icons.inventory_2,
                        const Color(0xFFE67E22),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // أزرار العمليات
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _openWarehouseDesigner(warehouse),
                        icon: const Icon(Icons.edit, size: 16),
                        label: Text(T('تصميم')),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF3498DB),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _searchInWarehouse(warehouse),
                        icon: const Icon(Icons.search, size: 16),
                        label: Text(T('بحث')),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWarehouseStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 16),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _createNewWarehouse() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const Warehouse3DDesignerScreen(),
      ),
    ).then((_) => _loadData());
  }

  void _openWarehouseDesigner(Warehouse3D warehouse) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Warehouse3DDesignerScreen(
          existingWarehouse: warehouse,
        ),
      ),
    ).then((_) => _loadData());
  }

  void _searchInWarehouse(Warehouse3D warehouse) {
    // تحديد المستودع المحدد
    _controller.selectWarehouse(warehouse.id.toString());

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const Warehouse3DSearchScreen(),
      ),
    );
  }

  void _openSearchScreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const Warehouse3DSearchScreen(),
      ),
    );
  }

  void _handleWarehouseAction(String action, Warehouse3D warehouse) {
    switch (action) {
      case 'edit':
        _openWarehouseDesigner(warehouse);
        break;
      case 'search':
        _searchInWarehouse(warehouse);
        break;
      case 'delete':
        _confirmDeleteWarehouse(warehouse);
        break;
    }
  }

  void _confirmDeleteWarehouse(Warehouse3D warehouse) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T('تأكيد الحذف')),
        content: Text(
          '${T("هل أنت متأكد من حذف المستودع")} "${warehouse.name}"؟\n${T("سيتم حذف جميع الخزائن والأرفف والمنتجات المرتبطة به.")}',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(T('إلغاء')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteWarehouse(warehouse);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(T('حذف'), style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteWarehouse(Warehouse3D warehouse) async {
    // TODO: تطبيق حذف المستودع
    // await _controller.deleteWarehouse(warehouse.id!);

    successSnackBar(
      message: T('تم حذف المستودع بنجاح'),
      context: context,
    );

    _loadData();
  }

  Future<void> _addDemoData() async {
    try {
      // إنشاء البيانات التجريبية
      final demoWarehouse = Warehouse3DDemoData.createDemoWarehouse();

      // حفظ المستودع الرئيسي
      final success = await _controller.createWarehouse3D(demoWarehouse);

      if (success && demoWarehouse.cabinets != null) {
        // حفظ الخزائن والأرفف
        for (final cabinet in demoWarehouse.cabinets!) {
          await _controller.createCabinet3D(cabinet);

          if (cabinet.shelves != null) {
            for (final shelf in cabinet.shelves!) {
              await _controller.createShelf3D(shelf);

              // حفظ المنتجات
              if (shelf.products != null) {
                for (final product in shelf.products!) {
                  await _controller.addProductLocation(product);
                }
              }
            }
          }
        }
      }

      await _loadData();

      successSnackBar(
        message: T('تم إضافة البيانات التجريبية بنجاح'),
        context: context,
      );

      // عرض نصائح الاستخدام
      _showUsageTips();
    } catch (e) {
      errorSnackBar(
        message: T('فشل في إضافة البيانات التجريبية'),
        context: context,
      );
    }
  }

  void _showUsageTips() {
    final tips = Warehouse3DDemoData.getUsageTips();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.lightbulb, color: Color(0xFFF39C12)),
            const SizedBox(width: 8),
            Text(T('نصائح للاستخدام')),
          ],
        ),
        content: SizedBox(
          width: 300,
          height: 400,
          child: ListView.builder(
            itemCount: tips.length,
            itemBuilder: (context, index) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Text(
                  tips[index],
                  style: const TextStyle(fontSize: 14),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(T('فهمت')),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _openSearchScreen();
            },
            child: Text(T('جرب البحث الآن')),
          ),
        ],
      ),
    );
  }
}
