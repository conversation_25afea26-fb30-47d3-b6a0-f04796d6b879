import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/screens/components/common_circle_button.dart';
import 'package:inventory_application/screens/components/common_header.dart';

import 'package:share_plus/share_plus.dart';
import '../home/<USER>';

class CommonPdfViewerScreen extends StatefulWidget {
  const CommonPdfViewerScreen({
    Key? key,
    this.headerImagePath,
    this.headerTitle,
    this.pdfAsBase64,
    this.path,
  }) : super(key: key);

  final String? headerImagePath;
  final String? headerTitle;
  final List<String>? pdfAsBase64;
  final String? path;

  @override
  // ignore: library_private_types_in_public_api
  _CommonPdfViewerScreenState createState() => _CommonPdfViewerScreenState();
}

class _CommonPdfViewerScreenState extends State<CommonPdfViewerScreen> {
  late int currentPage;
  late PageController pageController;

  @override
  void initState() {
    super.initState();
    currentPage = 0;
    pageController = PageController(initialPage: currentPage);
  }

  Uint8List decodeBase64(String base64String) {
    return Uint8List.fromList(base64.decode(base64String));
  }

  Future<void> shearFile(String fileName) async {
    try {
      await Share.shareXFiles([XFile(widget.path ?? "")], text: fileName);
    } catch (e) {
      print('Error downloading file: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      // selectedBottomNavbarItem: BottomNavbarItems.none,
      child: Stack(
        children: [
          Column(
            children: [
              widget.headerImagePath != null || widget.headerTitle != null
                  ? CommonHeader(
                      icon: Icons.abc,
                      title: widget.headerTitle ?? "",
                    )
                  : const SizedBox.shrink(),
              Expanded(
                child: Column(
                  children: [
                    Expanded(
                      child: PageView.builder(
                        controller: pageController,
                        itemCount: 1,
                        onPageChanged: (index) {
                          setState(() {
                            currentPage = index;
                          });
                        },
                        itemBuilder: (context, index) {
                          String pdfBase64String = "";
                          if (widget.pdfAsBase64 != null) {
                            pdfBase64String = widget.pdfAsBase64![index];

                            if (pdfBase64String == "Report not found") {
                              return const Center(
                                child: Text(
                                    "لم يتم تجهيز التقرير بعد يرجى الانتظار..."),
                              );
                            } else if (pdfBase64String == "") {
                              return const Center(
                                child: Text("لا يوجد ملف للعرض"),
                              );
                            }
                          } else {
                            return Center(
                              child: PDFView(
                                filePath: widget.path,
                                // pdfData: pdfData,
                                enableSwipe: true,
                                swipeHorizontal: true,
                                autoSpacing: true,
                                pageSnap: true,
                                preventLinkNavigation: true,
                                pageFling: true,
                                onRender: (_pages) {},
                                onError: (error) {},
                                onPageError: (page, error) {},
                                onViewCreated:
                                    (PDFViewController pdfViewController) {
                                  // You can use the pdfViewController to control the PDF view programmatically.
                                },
                              ),
                            );
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          Positioned(
            bottom: 30,
            left: context.width / 2.4,
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      for (int i = 0;
                          i < (widget.pdfAsBase64?.length ?? 0);
                          i++)
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: i == currentPage
                                  ? context.primaryColor
                                  : Colors.grey,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                Text(
                  'PDF ${currentPage + 1} of ${widget.pdfAsBase64?.length ?? 0}',
                  style: const TextStyle(
                    fontSize: 13,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 30,
            left: 10,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CommonCircleButton(
                  icon: Icon(
                    Icons.output_rounded,
                    color: context.surfaceColor,
                  ),
                  onClick: () {
                    Navigator.of(context).pop();
                  },
                ),
                const SizedBox(height: 10),
                CommonCircleButton(
                  icon: Icon(
                    Icons.share,
                    color: context.surfaceColor,
                  ),
                  onClick: () async {
                    await shearFile("PatientData.pdf");
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
