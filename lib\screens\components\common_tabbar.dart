import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class CommonTabBar extends StatefulWidget {
  const CommonTabBar(
      {super.key,
      required this.tabs,
      required this.contents,
      this.sliverAppBarWidget});
  final List<String> tabs;
  final List<String> contents;
  final Widget? sliverAppBarWidget;

  @override
  State<CommonTabBar> createState() => _CommonTabBarState();
}

class _CommonTabBarState extends State<CommonTabBar>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    _tabController = TabController(length: widget.tabs.length, vsync: this);
    _tabController.animation!.addListener(_tabListener);
    super.initState();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _tabListener() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return NestedScrollView(
      physics: const NeverScrollableScrollPhysics(),
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          widget.sliverAppBarWidget != null
              ? SliverAppBar(
                  backgroundColor: Colors.transparent,
                  automaticallyImplyLeading: false,
                  toolbarHeight: context.height / 2.9,
                  collapsedHeight: context.height / 2.9,
                  flexibleSpace: widget.sliverAppBarWidget,
                )
              : const SliverAppBar(
                  backgroundColor: Colors.transparent,
                  automaticallyImplyLeading: false,
                  toolbarHeight: 0,
                ),
          Directionality(
            textDirection: TextDirection.ltr,
            child: SliverPersistentHeader(
              delegate: MyTabsPar(
                TabBar(
                  controller: _tabController,
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  automaticIndicatorColorAdjustment: true,
                  unselectedLabelColor: Colors.black,
                  labelColor: Colors.black,
                  onTap: (value) {
                    setState(() {});
                  },
                  indicatorColor: Colors.transparent,
                  labelPadding: const EdgeInsets.symmetric(horizontal: 3),
                  indicatorPadding: const EdgeInsets.symmetric(horizontal: 8),
                  isScrollable: true,
                  tabs: List.generate(
                    widget.tabs.length,
                    (index) => Container(
                      width: 150,
                      height: 75,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 15,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        color: _tabController.index == index
                            ? context.onSecondary
                            : context.colors.scrim,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Tab(
                        child: Text(
                          widget.tabs[index],
                          style: const TextStyle(
                            fontSize: 18,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              floating: true,
              pinned: true,
            ),
          )
        ];
      },
      body: Directionality(
        textDirection: TextDirection.ltr,
        child: TabBarView(
          controller: _tabController,
          children: widget.contents.asMap().entries.map(
            (entry) {
              final dynamic content = entry.value;

              return ListView.builder(
                padding: const EdgeInsets.all(10),
                shrinkWrap: true,
                itemCount: 1,
                itemBuilder: (BuildContext context, int index) {
                  return TabBody(
                    body: content,
                    index: entry.key,
                  );
                },
              );
            },
          ).toList(),
        ),
      ),
    );
  }
}

class MyTabsPar extends SliverPersistentHeaderDelegate {
  MyTabsPar(this.tabBar);
  final TabBar tabBar;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Padding(
      padding: EdgeInsets.only(left: 10),
      child: tabBar,
    );
  }

  @override
  double get maxExtent => 100;

  @override
  double get minExtent => 100;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return false;
  }
}

class TabBody extends StatelessWidget {
  const TabBody({super.key, required this.index, required this.body});
  final int index;
  final String body;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      margin: const EdgeInsets.only(top: 25, bottom: 30),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Center(
            child: Text(
              '0${index + 1}',
              style: const TextStyle(
                fontSize: 25,
                height: 1,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(height: 20),
          Center(
            child: Text(
              body,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 16,
                height: 1,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
