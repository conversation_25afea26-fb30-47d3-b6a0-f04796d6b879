import 'dart:convert';

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/add_new_product_model.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/invoices/products/add_new_product_screen.dart';
import 'package:sqflite/sqflite.dart';
import 'dart:async';

class ProductController with ChangeNotifier {
  List<ProductDTO> productList = [];

  List<ProductDTO> realProductList = [];
  bool runningSyncization = false;
  int fetchedProductCount = 0;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  int _getCurrentPage() {
    var currentPage = ((realProductList.length) / _pageSize).ceil() + 1;
    if (realProductList.isEmpty) currentPage = 1;
    return currentPage;
  }

  final int _pageSize = 10;

  //---------------------------------------------------------------------------------
  Future<void> getItems({
    bool resetAndRefresh = false,
    int? categoryId,
    String? search,
  }) async {
    try {
      final db = await DatabaseHelper().database;
      if (resetAndRefresh) {
        realProductList.clear();
      }

      // Define skip and take for pagination
      int skip = realProductList.length;
      int take = _pageSize;

      // Build a query for the local database with filtering and pagination
      List<String> whereClauses = [];
      List<dynamic> whereArgs = [];

      if (!AppController.isSharedProducts) {
        whereClauses.add('BranchId = ?');
        whereArgs.add(AppController.currentBranchId);
      }

      if (categoryId != null && categoryId > 0) {
        whereClauses.add('Parent_ID = ?');
        whereArgs.add(categoryId);
      }
      if (search != null && search.isNotEmpty) {
        whereClauses.add('(Name LIKE ? OR Code LIKE ?)');
        whereArgs.add('%$search%');
        whereArgs.add('%$search%');
      }

      String? whereClause =
          whereClauses.isNotEmpty ? whereClauses.join(' AND ') : null;
      List<dynamic>? queryArgs = whereArgs.isNotEmpty ? whereArgs : null;

      // Query for unique products only (without duplicates)
      List<Map<String, dynamic>> products = await db.query(
        'ProductModel',
        where: whereClause,
        whereArgs: queryArgs,
        limit: take,
        offset: skip,
      );

      if (products.isNotEmpty) {
        // Local data is available, now fetch inventory and prices separately
        for (var productData in products) {
          var productModel = ProductModel.fromJson(productData);

          // Fetch Inventory data for this product
          List<Map<String, dynamic>> inventoryData = await db.query(
            'Inventory',
            where: 'Product_ID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch ItemPrice data for this product
          List<Map<String, dynamic>> priceData = await db.query(
            'ItemPrice',
            where: 'Product_ID = ?',
            whereArgs: [productModel.iD],
          );

          List<Map<String, dynamic>> barcodes = await db.query(
            'Barcodes',
            where: 'ItemID = ?',
            whereArgs: [productModel.iD],
          );

          List<Map<String, dynamic>> itemAttributesData = await db.query(
            'ItemAttributes',
            where: 'Item_ID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch and attach attribute options
          List<ItemAttribute> itemAttributes = [];
          for (var attributeData in itemAttributesData) {
            int? attributeId = attributeData['ID'];

            if (attributeId != null) {
              List<Map<String, dynamic>> optionsData = await db.query(
                'ItemsAttributeOptions',
                where: 'Attribute_ID = ?',
                whereArgs: [attributeId],
              );

              List<ItemAttributeOption> options = optionsData
                  .map((option) => ItemAttributeOption.fromJson(option))
                  .toList();

              ItemAttribute attribute = ItemAttribute.fromJson(attributeData);
              attribute.itemsAttributeOptions = options;

              itemAttributes.add(attribute);
            }
          }

          // Map and attach inventory and price details to the product model
          productModel.inventory =
              inventoryData.map((inv) => Inventory.fromJson(inv)).toList();
          productModel.itemPrice =
              priceData.map((price) => ItemPrice.fromJson(price)).toList();
          productModel.barcodes =
              barcodes.map((barcodes) => Barcodes.fromJson(barcodes)).toList();
          productModel.itemAttributes = itemAttributes;

          realProductList.add(mapProductModel(productModel));
        }
      } else {
        // Fetch from backend if no local data is available
        // await fetchProduct();
      }

      notifyListeners();
    } catch (e) {
      print("Error fetching items: $e");
    }
  }

  Future<ProductDTO?> getItemById(int productId) async {
    try {
      final db = await DatabaseHelper().database;
      final result = await db.query(
        'ProductModel',
        where: 'ID = ?',
        whereArgs: [productId],
      );

      if (result.isNotEmpty) {
        var productModel = ProductModel.fromJson(result.first);

        // Now fetch all related data for the product

        // Fetch Inventory data for this product
        List<Map<String, dynamic>> inventoryData = await db.query(
          'Inventory',
          where: 'Product_ID = ?',
          whereArgs: [productModel.iD],
        );
        // Fetch ItemPrice data for this product
        List<Map<String, dynamic>> priceData = await db.query(
          'ItemPrice',
          where: 'Product_ID = ?',
          whereArgs: [productModel.iD],
        );
        // Fetch all barcodes for this product
        List<Map<String, dynamic>> allBarcodes = await db.query(
          'Barcodes',
          where: 'ItemID = ?',
          whereArgs: [productModel.iD],
        );
        // Fetch attribute data for this product
        List<Map<String, dynamic>> itemAttributesData = await db.query(
          'ItemAttributes',
          where: 'Item_ID = ?',
          whereArgs: [productModel.iD],
        );
        // Fetch and attach attribute options
        List<ItemAttribute> itemAttributes = [];
        for (var attributeData in itemAttributesData) {
          int? attributeId = attributeData['ID'];

          if (attributeId != null) {
            List<Map<String, dynamic>> optionsData = await db.query(
              'ItemsAttributeOptions',
              where: 'Attribute_ID = ?',
              whereArgs: [attributeId],
            );
            List<ItemAttributeOption> options = optionsData
                .map((option) => ItemAttributeOption.fromJson(option))
                .toList();

            ItemAttribute attribute = ItemAttribute.fromJson(attributeData);
            attribute.itemsAttributeOptions = options;

            itemAttributes.add(attribute);
          }
        }

        // Map and attach all data to the product model
        productModel.inventory =
            inventoryData.map((inv) => Inventory.fromJson(inv)).toList();
        productModel.itemPrice =
            priceData.map((price) => ItemPrice.fromJson(price)).toList();
        productModel.barcodes =
            allBarcodes.map((b) => Barcodes.fromJson(b)).toList();
        productModel.itemAttributes = itemAttributes;

        // Convert to ProductDTO for use in the app
        ProductDTO productDTO = mapProductModel(productModel);

        return productDTO;
      }

      return null;
    } catch (e) {
      print("Error in getItemById: $e");
      return null;
    }
  }

  //-----------------------------------------------------
  static Future<ProductDTO?> getItemByBarcodeWithWriting(
      {String? barcode}) async {
    try {
      if (barcode == null || barcode.isEmpty) return null;

      // Play beep sound safely
      try {
        final player = AudioPlayer();
        await player.play(AssetSource('sounds/barcodeRead.m4a'));
        // Dispose the player after a short delay to free resources
        Future.delayed(const Duration(seconds: 1), () {
          try {
            player.dispose();
          } catch (e) {
            print('Error disposing audio player: $e');
          }
        });
      } catch (e) {
        print('Error playing barcode sound: $e');
        // Don't let audio errors prevent barcode processing
      }

      final db = await DatabaseHelper().database;
      final DatabaseHelper _db = DatabaseHelper();
      // Get all barcodes matching the scanned barcode
      List<Map<String, dynamic>> barcodeResults = await db.query(
        'Barcodes',
        where: "BarCode = ?",
        whereArgs: [barcode],
      );

      if (barcodeResults.isNotEmpty) {
        // Extract the first matched barcode
        var firstMatch = barcodeResults.first;
        int productId = firstMatch['ItemID'];
        String barcodeValue = firstMatch['BarCode'];
        String barcodeName = firstMatch['BarCodeName'] ?? '';
        bool isFinalBarcode = firstMatch['IsFinalBarcode'] == 1;
        String selectedAttributeAsString =
            firstMatch['SelectedAttributeAsString'] ?? '';

        // Fetch the corresponding product from ProductModel
        List<Map<String, dynamic>> products = await _db.query(
          'ProductModel',
          where: "ID = ?",
          whereArgs: [productId],
        );

        if (products.isNotEmpty) {
          var productModel = ProductModel.fromJson(products.first);

          // Now fetch all related data for the product

          // Fetch Inventory data for this product
          List<Map<String, dynamic>> inventoryData = await db.query(
            'Inventory',
            where: 'Product_ID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch ItemPrice data for this product
          List<Map<String, dynamic>> priceData = await db.query(
            'ItemPrice',
            where: 'Product_ID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch all barcodes for this product
          List<Map<String, dynamic>> allBarcodes = await db.query(
            'Barcodes',
            where: 'ItemID = ?',
            whereArgs: [productModel.iD],
          );

          // Fetch attribute data only if not a final barcode
          List<ItemAttribute> itemAttributes = [];
          if (!isFinalBarcode) {
            List<Map<String, dynamic>> itemAttributesData = await db.query(
              'ItemAttributes',
              where: 'Item_ID = ?',
              whereArgs: [productModel.iD],
            );

            // Fetch and attach attribute options
            for (var attributeData in itemAttributesData) {
              int? attributeId = attributeData['ID'];

              if (attributeId != null) {
                List<Map<String, dynamic>> optionsData = await db.query(
                  'ItemsAttributeOptions',
                  where: 'Attribute_ID = ?',
                  whereArgs: [attributeId],
                );

                List<ItemAttributeOption> options = optionsData
                    .map((option) => ItemAttributeOption.fromJson(option))
                    .toList();

                ItemAttribute attribute = ItemAttribute.fromJson(attributeData);
                attribute.itemsAttributeOptions = options;

                itemAttributes.add(attribute);
              }
            }
          }

          // Map and attach all data to the product model
          productModel.inventory =
              inventoryData.map((inv) => Inventory.fromJson(inv)).toList();
          productModel.itemPrice =
              priceData.map((price) => ItemPrice.fromJson(price)).toList();
          productModel.barcodes =
              allBarcodes.map((b) => Barcodes.fromJson(b)).toList();
          productModel.itemAttributes = itemAttributes;

          // Convert to ProductDTO for use in the app
          ProductDTO productDTO = mapProductModel(productModel);

          // Set the specific barcode that was scanned
          productDTO.barcode = barcodeValue;
          productDTO.barcodeName = barcodeName;
          if (selectedAttributeAsString.isNotEmpty) {
            final List<dynamic> decoded = jsonDecode(selectedAttributeAsString);
            productDTO.attribute = decoded
                .map((e) => ItemAttribute.fromJson(e as Map<String, dynamic>))
                .toList();
          }

          // If this is a final barcode, append the barcodeName (which contains attribute info)
          // to the product title
          if (isFinalBarcode && barcodeName.isNotEmpty) {
            productDTO.title = '${productDTO.title} - $barcodeName';

            // Set a flag to indicate this product already has attributes selected
            productDTO.hasSelectedAttributes = true;
          }

          return productDTO;
        }
      }

      return null;
    } catch (e) {
      print("Error in getItemByBarcode: $e");
      return null;
    }
  }

  //------------------------------------------------------------------------------
  Future<void> fetchProduct() async {
    // if (await checkIfProductDataExists()) {
    //   return;
    // }

    if (await isThereNetworkConnection() == false) {
      errorSnackBar(
          message: "يجب توفير اتصال بالانترنت لاول مرة للحصول على المنتجات");
      return;
    }
    productList.clear();
    bool isStillThereProducts = true;

    while (isStillThereProducts) {
      runningSyncization = true;
      var countByBrach = await getProductCountByBranch();
      String url = '/ItemsCategory/GetAllItems?skip=$countByBrach&take=500';
      var result = await Api.getOne(action: url);

      if (result != null && result.isSuccess) {
        for (var element in result.data) {
          var productModel = ProductModel.fromJson(element);

          String? localImagePath;
          if (productModel.mainImageUrl != null &&
              productModel.mainImageUrl!.isNotEmpty) {
            // توليد اسم ملف فريد مثلا من id المنتج
            String fileName = 'product_${productModel.iD}.jpg';

            localImagePath = await downloadAndSaveImage(
                productModel.mainImageUrl!, fileName);
          }

          // تحديث مسار الصورة المحلية في الموديل
          productModel.mainImageUrl = localImagePath;

          await insertProduct(productModel);
          // productList.add(productModel);
          fetchedProductCount++;
          notifyListeners();
        }

        //notifyListeners();
        if (result.data.length < 500) {
          isStillThereProducts = false;
          runningSyncization = false;
        }
      } else {
        isStillThereProducts = false;
        runningSyncization = false;
      }
    }
    notifyListeners();
    successSnackBar(message: "تمت مزامنة كافة المنتجات");
  }

//------------------------------------------------------------------------------
  Future<bool> addNewProductToServer({
    required addNewProduct newproduct,
  }) async {
    try {
      var response = await Api.post(
        action: '/ItemsCategory/Manage',
        body: newproduct,
      );

      if (response?.statusCode == 200 || response?.statusCode == 201) {
        successSnackBar(message: "تم إضافة المنتج بنجاح");
        return true;
      } else {
        errorSnackBar(message: "فشل في إضافة المنتج:");
        return false;
      }
    } catch (e) {
      errorSnackBar(message: "حدث خطأ: $e");
      return false;
    }
  }

  //------------------------------------------------------------------------------
  Future<void> fetchProductAfterCertineId() async {
    // if (await checkIfProductDataExists()) {
    //   return;
    // }

    if (await isThereNetworkConnection() == false) {
      errorSnackBar(
          message: "يجب توفير اتصال بالانترنت لاول مرة للحصول على المنتجات");
      return;
    }
    var maxId = await getMaxProductId();

    if (maxId == 0) {
      await fetchProduct();
      return;
    }
    bool isStillThereProducts = true;
    var skip = 0;
    while (isStillThereProducts) {
      runningSyncization = true;
      String url = '/ItemsCategory/GetAllItems?id=$maxId&skip=$skip&take=200';
      var result = await Api.getOne(action: url);

      if (result != null && result.isSuccess) {
        for (var element in result.data) {
          var productModel = ProductModel.fromJson(element);
          await insertProduct(productModel);
          fetchedProductCount++;
          skip++;
          notifyListeners();
        }

        //notifyListeners();
        if (result.data.length < 200) {
          isStillThereProducts = false;
          runningSyncization = false;
        }
      } else {
        isStillThereProducts = false;
        runningSyncization = false;
      }
    }
    notifyListeners();
    successSnackBar(message: "تمت مزامنة كافة المنتجات");
  }

  //------------------------------------------------------------------------------
  Future<void> insertProduct(ProductModel productModel) async {
    try {
      await _dbHelper.insert('ProductModel', {
        'ID': productModel.iD,
        'Name': productModel.name,
        'Main_Image_Url': productModel.mainImageUrl,
        'Code': productModel.code,
        'Parent_ID': productModel.parentID,
        'Parent_Name': productModel.parentName,
        'Level_Type': productModel.levelType,
        'isParent': productModel.isParent == true ? 1 : 0,
      });

      // Insert associated Inventory data into Inventory table
      if (productModel.inventory != null) {
        for (var inventory in productModel.inventory!) {
          await _dbHelper.insert('Inventory', {
            'ID': inventory.iD,
            'Store_Name': inventory.storeName,
            'Quantity_Balance': inventory.quantityBalance,
            'Product_ID': productModel.iD, // Foreign key to ProductModel
          });
        }
      }

      if (productModel.barcodes != null) {
        for (var barcode in productModel.barcodes!) {
          await _dbHelper.insert('Barcodes', {
            'BarCode': barcode.barCode,
            'BarCodeName': barcode.barCodeName,
            'IsFinalBarcode': barcode.isFinalBarcode == true ? 1 : 0,
            'SelectedAttributeAsString': barcode.selectedAttributeAsString,
            'ItemID': productModel.iD, // Foreign key to ProductModel
          });
        }
      }

      if (productModel.itemPrice != null) {
        for (var price in productModel.itemPrice!) {
          await _dbHelper.insert('ItemPrice', {
            'ID': price.iD,
            'Item_ID': price.itemID,
            'Unit_ID': price.unitID,
            'Unit_Name': price.unitName,
            'Is_Defult': price.isDefult == true ? 1 : 0,
            'Sales_Price': price.salesPrice,
            'Product_ID': productModel.iD, // Foreign key to ProductModel
          });
        }
      }
      if (productModel.itemAttributes != null) {
        for (var attribute in productModel.itemAttributes!) {
          // Insert the attribute record
          await _dbHelper.insert('ItemAttributes', {
            'ID': attribute.id,
            'Attribute_Type_Id': attribute.attributeTypeId,
            'Attribute_Name': attribute.attributeName,
            'Item_ID': productModel.iD,
          });

          // Insert each option into the ItemsAttributeOptions table
          if (attribute.itemsAttributeOptions != null) {
            for (var option in attribute.itemsAttributeOptions!) {
              await _dbHelper.insert('ItemsAttributeOptions', {
                'ID': option.id,
                'Attribute_ID': option.attributeId ?? attribute.id,
                'Option_ID': option.optionId,
                'Option_Name': option.optionName,
              });
            }
          }
        }
      }

      print(productModel.iD);
      //  print(productModel.code);
    } catch (e) {
      print(e);
    }
  }

  //------------------------------------------------------------------------------
  Future<int> getMaxProductId() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery(
        'SELECT MAX(id) as max_id FROM ProductModel WHERE BranchId = ${AppController.currentBranchId}');

    int maxId = Sqflite.firstIntValue(result) ?? 0;
    return maxId;
  }

  //------------------------------------------------------------------------------
  Future<int> getProductCount() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ProductModel WHERE BranchId = ${AppController.currentBranchId}');
    int count = Sqflite.firstIntValue(result) ?? 0;
    fetchedProductCount = count;
    return count;
  }

  Future<int> getProductCountByBranch() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM ProductModel WHERE BranchId = ${AppController.currentBranchId}');
    int count = Sqflite.firstIntValue(result) ?? 0;
    fetchedProductCount = count;
    return count;
  }

  //------------------------------------------------------------------------------
  Future<bool> clearAndRefetchData() async {
    try {
      _dbHelper.delete('Barcodes', where: null, whereArgs: null);
      _dbHelper.delete('Inventory', where: null, whereArgs: null);
      _dbHelper.delete('ItemPrice', where: null, whereArgs: null);
      _dbHelper.delete('ItemAttributes', where: null, whereArgs: null);
      _dbHelper.delete('ItemsAttributeOptions', where: null, whereArgs: null);
      _dbHelper.delete('ProductModel', where: null, whereArgs: null);

      fetchedProductCount = await getProductCount();
      notifyListeners();
      await fetchProduct();
      return true;
    } catch (e) {
      return false;
    }
  }
}
