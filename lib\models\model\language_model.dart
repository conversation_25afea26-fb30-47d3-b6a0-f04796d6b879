class LanguageModel {
  String? name;
  String? languageCulture;
  String? uniqueSeoCode;
  String? flagImageFileName;
  bool? rtl;
  bool? limitedToStores;
  bool? published;
  int? displayOrder;
  int? id;

  LanguageModel(
      {this.name,
      this.languageCulture,
      this.uniqueSeoCode,
      this.flagImageFileName,
      this.rtl,
      this.limitedToStores,
      this.published,
      this.displayOrder,
      this.id});

  LanguageModel.fromJson(Map<String, dynamic> json) {
    name = json['Name'];
    languageCulture = json['LanguageCulture'];
    uniqueSeoCode = json['UniqueSeoCode'];
    flagImageFileName = json['FlagImageFileName'];
    rtl = json['Rtl'];
    limitedToStores = json['LimitedToStores'];
    published = json['Published'];
    displayOrder = json['DisplayOrder'];
    id = json['Id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Name'] = name;
    data['LanguageCulture'] = languageCulture;
    data['UniqueSeoCode'] = uniqueSeoCode;
    data['FlagImageFileName'] = flagImageFileName;
    data['Rtl'] = rtl;
    data['LimitedToStores'] = limitedToStores;
    data['Published'] = published;
    data['DisplayOrder'] = displayOrder;
    data['Id'] = id;
    return data;
  }
}
