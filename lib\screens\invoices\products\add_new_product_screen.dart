import 'package:bottom_bar_matu/components/colors.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/unit_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/add_new_product_model.dart';
import 'package:inventory_application/screens/components/common_collapse%20.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/products/widgets/unit_price_widget.dart';
import 'package:provider/provider.dart';

class AddNewProductScreen extends StatefulWidget {
  const AddNewProductScreen({super.key});

  @override
  State<AddNewProductScreen> createState() => _AddNewProductScreenState();
}

class _AddNewProductScreenState extends State<AddNewProductScreen> {
  TextEditingController itemNumberController = TextEditingController();
  TextEditingController itemNameController = TextEditingController();
  TextEditingController salesPrice1Controller = TextEditingController();
  TextEditingController salesPrice2Controller = TextEditingController();
  TextEditingController purchasePrice1Controller = TextEditingController();
  TextEditingController purchasePrice2Controller = TextEditingController();

  // Controllers for unit 2
  TextEditingController salesPrice1Controller2 = TextEditingController();
  TextEditingController salesPrice2Controller2 = TextEditingController();
  TextEditingController purchasePrice1Controller2 = TextEditingController();
  TextEditingController purchasePrice2Controller2 = TextEditingController();

  // Controllers for unit 3
  TextEditingController salesPrice1Controller3 = TextEditingController();
  TextEditingController salesPrice2Controller3 = TextEditingController();
  TextEditingController purchasePrice1Controller3 = TextEditingController();
  TextEditingController purchasePrice2Controller3 = TextEditingController();

  String? selectedCategoryName;
  int? selectedCategoryId;
  int? selectedUnitId1;
  int? selectedUnitId2;
  int? selectedUnitId3;
  String? selectedUnitName1;
  String? selectedUnitName2;
  String? selectedUnitName3;
  @override
  Widget build(BuildContext context) {
    var categories = Provider.of<CategoryController>(context).categories;
    var units = Provider.of<UnitController>(context).units;

    return ApplicationLayout(
      child: SingleChildScrollView(
        child: Column(
          children: [
            const CommonHeader(
              icon: Icons.add,
              title: 'Add New Product',
              showBackButton: true,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              child: Column(children: [
                Row(
                  children: [
                    Text(
                      T("item"),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width / 2.2,
                      child: TextFormField(
                        decoration: InputDecoration(
                          hintText: T('Item number'),
                          hintStyle: const TextStyle(fontSize: 12),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 16),
                        ),
                        textInputAction: TextInputAction.search,
                        onFieldSubmitted: (value) {},
                        onChanged: (value) {
                          itemNumberController.text = value;
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width / 2.2,
                        child: TextFormField(
                          decoration: InputDecoration(
                            hintText: T('Item name'),
                            hintStyle: const TextStyle(fontSize: 12),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                          ),
                          textInputAction: TextInputAction.search,
                          onFieldSubmitted: (value) {},
                          onChanged: (value) {
                            itemNameController.text = value;
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    Text(
                      T("Category"),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                MyComboBox(
                  caption: selectedCategoryName ?? T("Select Category"),
                  height: 50,
                  onSelect: (int id, String name) {
                    categories.firstOrNull?.iD = id;
                    categories.firstOrNull?.name = name;

                    setState(() {
                      selectedCategoryName = name;
                      selectedCategoryId = id;
                    });
                  },
                  modalTitle: T("Categories"),
                  data: categories.map((cat) {
                    return ComboBoxDataModel(
                      id: cat.iD ?? 0,
                      name: cat.name ?? "",
                    );
                  }).toList(),
                  isShowLabel: false,
                  labelText: "",
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    Text(
                      T("unit"),
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                MyComboBox(

                  caption: selectedUnitName1 ?? T("Select Unit"),
                  height: 50,
                  onSelect: (int id, String name) {
                    units.firstOrNull?.id = id;
                    units.firstOrNull?.name = name;

                    setState(() {
                      selectedUnitName1 = name;
                      selectedUnitId1 = id;
                    });
                  },
                  modalTitle: T("unit"),
                  data: units.map((cat) {
                    return ComboBoxDataModel(
                      id: cat.id,
                      name: cat.name,
                    );
                  }).toList(),
                  isShowLabel: false,
                  labelText: "",
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    Text(
                      T("Prices"),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width / 2.2,
                        child: Text(
                          T("Sales Price 1"),
                          style: const TextStyle(
                            fontSize: 14,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width / 2.2,
                        child: Text(
                          T("Sales Price 2"),
                          style: const TextStyle(
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width / 2.2,
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: T('Sales Price 1'),
                          hintStyle: const TextStyle(fontSize: 12),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 16),
                        ),
                        textInputAction: TextInputAction.search,
                        onFieldSubmitted: (value) {},
                        onChanged: (value) {
                          salesPrice1Controller.text = value;
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width / 2.2,
                        child: TextFormField(
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: T('Sales Price 2'),
                            hintStyle: const TextStyle(fontSize: 12),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                          ),
                          textInputAction: TextInputAction.search,
                          onFieldSubmitted: (value) {},
                          onChanged: (value) {
                            salesPrice2Controller.text = value;
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 6),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(
                        width: MediaQuery.of(context).size.width / 2.2,
                        child: Text(
                          T("Purchase Price 1"),
                          style: const TextStyle(
                            fontSize: 14,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: MediaQuery.of(context).size.width / 2.2,
                        child: Text(
                          T("Purchase Price 2"),
                          style: const TextStyle(
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Row(
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width / 2.2,
                      child: TextFormField(
                        keyboardType: TextInputType.number,
                        decoration: InputDecoration(
                          hintText: T('Purchase Price 1'),
                          hintStyle: const TextStyle(fontSize: 12),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                              vertical: 12, horizontal: 16),
                        ),
                        textInputAction: TextInputAction.search,
                        onFieldSubmitted: (value) {},
                        onChanged: (value) {
                          purchasePrice1Controller.text = value;
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 5),
                      child: SizedBox(
                        width: MediaQuery.of(context).size.width / 2.2,
                        child: TextFormField(
                          keyboardType: TextInputType.number,
                          decoration: InputDecoration(
                            hintText: T('Purchase Price 2'),
                            hintStyle: const TextStyle(fontSize: 12),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                          ),
                          textInputAction: TextInputAction.search,
                          onFieldSubmitted: (value) {},
                          onChanged: (value) {
                            purchasePrice2Controller.text = value;
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 10,
                ),
                UnitPriceWidget(
                  headerTitle:"الوحدة الثانية",
                  units: units,
                  selectedUnitId: selectedUnitId2,
                  selectedUnitName: selectedUnitName2,
                  onUnitSelected: (id) {
                    setState(() {
                      selectedUnitId2 = id;
                      selectedUnitName2 = units.firstWhere(
                        (unit) => unit.id == id,
                        orElse: () => ComboBoxDataModel(id: 0, name: ""),
                      ).name;
                    });
                  },
                  salesPrice1Controller: salesPrice1Controller2,
                  salesPrice2Controller: salesPrice2Controller2,
                  purchasePrice1Controller: purchasePrice1Controller2,
                  purchasePrice2Controller: purchasePrice2Controller2,
                ), const SizedBox(
                  height: 10,
                ),
                UnitPriceWidget(
                  headerTitle: "الوحدة الثالثة",
                  units: units,
                  selectedUnitId: selectedUnitId3,
                  selectedUnitName: selectedUnitName3,
                  onUnitSelected: (id) {
                    setState(() {
                      selectedUnitId3 = id;
                      selectedUnitName3 = units.firstWhere(
                        (unit) => unit.id == id,
                        orElse: () => ComboBoxDataModel(id: 0, name: ""),
                      ).name;
                    });
                  },
                  salesPrice1Controller: salesPrice1Controller3,
                  salesPrice2Controller: salesPrice2Controller3,
                  purchasePrice1Controller: purchasePrice1Controller3,
                  purchasePrice2Controller: purchasePrice2Controller3,
                ),
                InkWell(
                  onTap: () async {
                    List<ItemsUnitsModel> itemsUnits = [];

                    if (selectedUnitId1 != null) {
                      itemsUnits.add(ItemsUnitsModel(
                        unitID: selectedUnitId1,
                        salesPrice1: int.tryParse(salesPrice1Controller.text),
                        salesPrice2: int.tryParse(salesPrice2Controller.text),
                        purchasePrice1:
                            int.tryParse(purchasePrice1Controller.text),
                        purchasePrice2:
                            int.tryParse(purchasePrice2Controller.text),
                        isMainUnit: true,
                      ));
                    }

                    if (selectedUnitId2 != null) {
                      itemsUnits.add(ItemsUnitsModel(
                        unitID: selectedUnitId2,
                        salesPrice1: int.tryParse(salesPrice1Controller2.text),
                        salesPrice2: int.tryParse(salesPrice2Controller2.text),
                        purchasePrice1:
                            int.tryParse(purchasePrice1Controller2.text),
                        purchasePrice2:
                            int.tryParse(purchasePrice2Controller2.text),
                        isMainUnit: false,
                      ));
                    }

                    if (selectedUnitId3 != null) {
                      itemsUnits.add(ItemsUnitsModel(
                        unitID: selectedUnitId3,
                        salesPrice1: int.tryParse(salesPrice1Controller3.text),
                        salesPrice2: int.tryParse(salesPrice2Controller3.text),
                        purchasePrice1:
                            int.tryParse(purchasePrice1Controller3.text),
                        purchasePrice2:
                            int.tryParse(purchasePrice2Controller3.text),
                        isMainUnit: false,
                      ));
                    }

                 
                    await Provider.of<ProductController>(context, listen: false)
                        .addNewProductToServer(
                      newproduct: addNewProduct(
                        categoryID: selectedCategoryId,
                        codingMode: 1,
                        code: itemNumberController.text,
                        salesPrice1:int.tryParse(salesPrice1Controller.text) ,
                        purchasePrice1: int.tryParse(purchasePrice1Controller.text),
                        name: itemNameController.text,
                 nameEn: "",
                 nameTr: "",
                 unitID: selectedUnitId1,
                 branchID: AppController.currentBranchId,
                        itemsUnitsModel: itemsUnits, 
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: context.newPrimaryColor,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    margin: const EdgeInsets.symmetric(vertical: 20),
                    width: MediaQuery.of(context).size.width,
                    child: Text(
                      textAlign: TextAlign.center,
                      T("Add Product"),
                      style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                )
              ]),
            ),
          ],
        ),
      ),
    );
  }
}
