class DeviceSalesSummaryDTO {
  final String? deviceId;
  final double? totalSales;
  final double? totalReturns;
  final double? totalDiscount;
  final double? totalReceived;
  final double? totalPaidToCustomer;
  final double? totalRemaining;

  DeviceSalesSummaryDTO({
    this.deviceId,
    this.totalSales,
    this.totalReturns,
    this.totalDiscount,
    this.totalReceived,
    this.totalPaidToCustomer,
    this.totalRemaining,
  });

  factory DeviceSalesSummaryDTO.fromJson(Map<String, dynamic> json) {
    return DeviceSalesSummaryDTO(
      deviceId: json['DeviceId'],
      totalSales: json['TotalSales']?.toDouble(),
      totalReturns: json['TotalReturns']?.toDouble(),
      totalDiscount: json['TotalDiscount']?.toDouble(),
      totalReceived: json['TotalReceived']?.toDouble(),
      totalPaidToCustomer: json['TotalPaidToCustomer']?.toDouble(),
      totalRemaining: json['TotalRemaining']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'DeviceId': deviceId,
      'TotalSales': totalSales,
      'TotalReturns': totalReturns,
      'TotalDiscount': totalDiscount,
      'TotalReceived': totalReceived,
      'TotalPaidToCustomer': totalPaidToCustomer,
      'TotalRemaining': totalRemaining,
    };
  }

  // Net sales (sales - returns)
  double get netSales => (totalSales ?? 0.0) - (totalReturns ?? 0.0);

  // Net total after discount
  double get netTotal => netSales - (totalDiscount ?? 0.0);
}
