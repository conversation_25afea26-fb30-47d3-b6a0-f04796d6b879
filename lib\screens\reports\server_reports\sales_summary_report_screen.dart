import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/server/sales_summary_report_dto.dart';
import 'package:inventory_application/models/dto/reports/server/user_sales_summary_dto.dart';
import 'package:inventory_application/models/dto/reports/server/device_sales_summary_dto.dart';
import 'package:inventory_application/services/server_reports_service.dart';
import 'package:inventory_application/services/report_printer_service.dart';
import 'package:inventory_application/screens/reports/widgets/report_print_helper.dart';

// Define DateRangeType enum locally
enum DateRangeType {
  daily,
  monthly,
  yearly,
  custom,
}

class SalesSummaryReportScreen extends StatefulWidget {
  const SalesSummaryReportScreen({
    Key? key,
  }) : super(key: key);

  @override
  State<SalesSummaryReportScreen> createState() =>
      _SalesSummaryReportScreenState();
}

class _SalesSummaryReportScreenState extends State<SalesSummaryReportScreen>
    with TickerProviderStateMixin {
  SalesSummaryReportDTO? _reportData;
  bool _isLoading = false;
  DateTime? _fromDate;
  DateTime? _toDate;
  DateRangeType? _selectedDateRangeType;
  int _selectedTabIndex = 0;

  // Controllers
  late AnimationController _animationController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Services
  final ServerReportsService _reportsService = ServerReportsService();

  // Date range types with Arabic names
  final Map<DateRangeType, String> _dateRangeOptions = {
    DateRangeType.daily: 'يومي',
    DateRangeType.monthly: 'شهري',
    DateRangeType.yearly: 'سنوي',
    DateRangeType.custom: 'تخصيص التاريخ',
  };

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _slideController.forward();
  }

  Future<void> _loadReport() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      print('Loading sales summary report with filters:');
      print('From Date: $_fromDate');
      print('To Date: $_toDate');

      final data = await _reportsService.getSalesSummaryByUsersAndDevices(
        fromDate: _fromDate,
        toDate: _toDate,
      );

      if (data != null) {
        setState(() {
          _reportData = data;
        });
        _animationController.forward();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('فشل في تحميل بيانات التقرير'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error loading report: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _applyDateRangeType(DateRangeType type) {
    final now = DateTime.now();

    switch (type) {
      case DateRangeType.daily:
        _fromDate = DateTime(now.year, now.month, now.day);
        _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case DateRangeType.monthly:
        _fromDate = DateTime(now.year, now.month, 1);
        _toDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case DateRangeType.yearly:
        _fromDate = DateTime(now.year, 1, 1);
        _toDate = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      case DateRangeType.custom:
        // Don't automatically set dates for custom
        break;
    }

    setState(() {
      _selectedDateRangeType = type;
    });
  }

  String _getDateRangeTypeDisplayName(DateRangeType type) {
    return _dateRangeOptions[type] ?? type.toString();
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: (_fromDate != null && _toDate != null)
          ? DateTimeRange(start: _fromDate!, end: _toDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
        _selectedDateRangeType = DateRangeType.custom;
      });
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  // وظيفة طباعة التقرير
  Future<void> _printReport() async {
    if (_reportData != null) {
      try {
        await ReportPrinterService.printSalesSummaryReport(
          _reportData!,
          context,
          reportTitle: 'تقرير ملخص المبيعات',
          fromDate: _fromDate,
          toDate: _toDate,
        );
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في الطباعة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text('ملخص المبيعات'),
        backgroundColor: const Color(0xFF6366F1),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // زر الطباعة في الـ AppBar
          if (_reportData != null && !_isLoading)
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'print') {
                  _printReport();
                }
              },
              itemBuilder: (context) => [
                ReportPrintHelper.buildPrintMenuItem(),
              ],
            ),
        ],
      ),
      floatingActionButton: _reportData != null && !_isLoading
          ? ReportPrintHelper.buildFloatingPrintButton(
              context: context,
              hasData: _reportData != null,
              onPressed: _printReport,
            )
          : null,
      body: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            children: [
              // Filters section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 0,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الفلاتر',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF374151),
                      ),
                    ),
                    const SizedBox(height: 16),
                    _buildDateFilters(),
                    const SizedBox(height: 16),
                    _buildActionButtons(),
                  ],
                ),
              ),

              // Loading indicator
              if (_isLoading)
                Container(
                  padding: const EdgeInsets.all(40),
                  child: const Center(
                    child: CircularProgressIndicator(
                      color: Color(0xFF6366F1),
                    ),
                  ),
                ),

              // Summary cards (if data is loaded)
              if (_reportData != null && !_isLoading) _buildSummaryCards(),

              // Tabs (if data is loaded)
              if (_reportData != null && !_isLoading) _buildTabsSection(),

              // Content area
              if (_reportData != null && !_isLoading) _buildContentSection(),

              // Empty state
              if (_reportData == null && !_isLoading) _buildEmptyState(),

              // زر الطباعة داخل المحتوى
              if (_reportData != null && !_isLoading)
                ReportPrintHelper.buildSalesSummaryPrintButton(
                  context: context,
                  reportData: _reportData,
                  reportTitle: 'تقرير ملخص المبيعات',
                  fromDate: _fromDate,
                  toDate: _toDate,
                ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: InkWell(
              onTap: () => setState(() => _selectedTabIndex = 0),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                bottomLeft: Radius.circular(12),
              ),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  color: _selectedTabIndex == 0
                      ? const Color(0xFF6366F1)
                      : Colors.transparent,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.people,
                      color: _selectedTabIndex == 0
                          ? Colors.white
                          : const Color(0xFF6366F1),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'حسب المستخدمين',
                      style: TextStyle(
                        color: _selectedTabIndex == 0
                            ? Colors.white
                            : const Color(0xFF6366F1),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: InkWell(
              onTap: () => setState(() => _selectedTabIndex = 1),
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  color: _selectedTabIndex == 1
                      ? const Color(0xFF6366F1)
                      : Colors.transparent,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.devices,
                      color: _selectedTabIndex == 1
                          ? Colors.white
                          : const Color(0xFF6366F1),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'حسب الأجهزة',
                      style: TextStyle(
                        color: _selectedTabIndex == 1
                            ? Colors.white
                            : const Color(0xFF6366F1),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContentSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: _selectedTabIndex == 0 ? _buildUsersList() : _buildDevicesList(),
    );
  }

  Widget _buildDateFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Date Range Type
        Text(
          'نطاق التاريخ',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF6B7280),
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<DateRangeType>(
              value: _selectedDateRangeType,
              isExpanded: true,
              hint: Text('اختر نطاق التاريخ'),
              onChanged: (DateRangeType? value) {
                if (value != null) {
                  _applyDateRangeType(value);
                }
              },
              items: _dateRangeOptions.entries
                  .map((entry) => DropdownMenuItem<DateRangeType>(
                        value: entry.key,
                        child: Text(entry.value),
                      ))
                  .toList(),
            ),
          ),
        ),

        // Custom date selection (if custom is selected)
        if (_selectedDateRangeType == DateRangeType.custom) ...[
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildDateDisplayCard(
                  label: 'من تاريخ',
                  date: _fromDate,
                  onTap: _selectDateRange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildDateDisplayCard(
                  label: 'إلى تاريخ',
                  date: _toDate,
                  onTap: _selectDateRange,
                ),
              ),
            ],
          ),
        ],

        // Display selected date range info
        if (_fromDate != null && _toDate != null) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: const Color(0xFF6366F1).withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: const Color(0xFF6366F1).withOpacity(0.3),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.date_range,
                  size: 16,
                  color: Color(0xFF6366F1),
                ),
                const SizedBox(width: 8),
                Text(
                  '${DateFormat('yyyy-MM-dd').format(_fromDate!)} إلى ${DateFormat('yyyy-MM-dd').format(_toDate!)}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF6366F1),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (_selectedDateRangeType != null) ...[
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: const Color(0xFF6366F1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _getDateRangeTypeDisplayName(_selectedDateRangeType!),
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDateDisplayCard({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF6B7280),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  date != null
                      ? DateFormat('yyyy-MM-dd').format(date)
                      : 'اختر التاريخ',
                  style: TextStyle(
                    fontSize: 14,
                    color: date != null ? Colors.black87 : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _loadReport,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6366F1),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text('إنشاء التقرير'),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton(
          onPressed: () {
            setState(() {
              _selectedDateRangeType = null;
              _fromDate = null;
              _toDate = null;
              _reportData = null;
            });
            _animationController.reset();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey.shade200,
            foregroundColor: Colors.grey.shade700,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text('مسح'),
        ),
      ],
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // First row - Users summary
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي المبيعات (المستخدمين)',
                  value: NumberFormat('#,##0.00')
                      .format(_reportData!.totalSalesAllUsers),
                  icon: Icons.attach_money,
                  color: Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: 'إجمالي المستلم',
                  value: NumberFormat('#,##0.00')
                      .format(_reportData!.totalReceivedAllUsers),
                  icon: Icons.payments,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          // Second row - Additional metrics
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  title: 'عدد المستخدمين',
                  value: _reportData!.byUsers.length.toString(),
                  icon: Icons.people,
                  color: Colors.purple,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildSummaryCard(
                  title: 'عدد الأجهزة',
                  value: _reportData!.byDevices.length.toString(),
                  icon: Icons.devices,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF6B7280),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.analytics_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد بيانات مبيعات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اختر نطاق تاريخ وقم بإنشاء التقرير',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersList() {
    final users = _reportData?.byUsers ?? [];

    if (users.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        child: Center(
          child: Text(
            'لا توجد بيانات مبيعات للمستخدمين',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: users.asMap().entries.map((entry) {
            final index = entry.key;
            final user = entry.value;
            return Padding(
              padding:
                  EdgeInsets.only(bottom: index < users.length - 1 ? 16 : 0),
              child: _buildUserCard(user, index),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildDevicesList() {
    final devices = _reportData?.byDevices ?? [];

    if (devices.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(40),
        child: Center(
          child: Text(
            'لا توجد بيانات مبيعات للأجهزة',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: devices.asMap().entries.map((entry) {
            final index = entry.key;
            final device = entry.value;
            return Padding(
              padding:
                  EdgeInsets.only(bottom: index < devices.length - 1 ? 16 : 0),
              child: _buildDeviceCard(device, index),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildUserCard(UserSalesSummaryDTO user, int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.person,
                    color: Colors.blue,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.userName ?? 'مستخدم غير معروف',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF374151),
                        ),
                      ),
                      Text(
                        'معرف المستخدم: ${user.userId ?? 'غير متوفر'}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Financial metrics grid
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    label: 'المبيعات',
                    value:
                        NumberFormat('#,##0.00').format(user.totalSales ?? 0),
                    color: Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    label: 'المرتجعات',
                    value:
                        NumberFormat('#,##0.00').format(user.totalReturns ?? 0),
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    label: 'المستلم',
                    value: NumberFormat('#,##0.00')
                        .format(user.totalReceived ?? 0),
                    color: Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    label: 'المتبقي',
                    value: NumberFormat('#,##0.00')
                        .format(user.totalRemaining ?? 0),
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Net sales highlight
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.purple.withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'صافي المبيعات',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF374151),
                    ),
                  ),
                  Text(
                    NumberFormat('#,##0.00').format(user.netSales),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceCard(DeviceSalesSummaryDTO device, int index) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.08),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Device header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.teal.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.devices,
                    color: Colors.teal,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الجهاز: ${device.deviceId ?? 'غير معروف'}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF374151),
                        ),
                      ),
                      Text(
                        'ملخص الجهاز',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Financial metrics grid
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    label: 'المبيعات',
                    value:
                        NumberFormat('#,##0.00').format(device.totalSales ?? 0),
                    color: Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    label: 'المرتجعات',
                    value: NumberFormat('#,##0.00')
                        .format(device.totalReturns ?? 0),
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    label: 'المستلم',
                    value: NumberFormat('#,##0.00')
                        .format(device.totalReceived ?? 0),
                    color: Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    label: 'المتبقي',
                    value: NumberFormat('#,##0.00')
                        .format(device.totalRemaining ?? 0),
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Net sales highlight
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.teal.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.teal.withOpacity(0.3),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'صافي المبيعات',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF374151),
                    ),
                  ),
                  Text(
                    NumberFormat('#,##0.00').format(device.netSales),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.teal,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricItem({
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
