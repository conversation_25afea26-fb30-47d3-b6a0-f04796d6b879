import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/controllers/accounting_report_helper_controller.dart';
import 'package:inventory_application/controllers/device_user_controller.dart';
import 'package:inventory_application/models/dto/reports/sales_total_report_dto.dart';
import 'package:inventory_application/models/dto/reports/closing_entries_report_dto.dart';
import 'package:inventory_application/services/server_reports_service.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/services/report_printer_service.dart';
import 'package:inventory_application/screens/reports/widgets/report_print_helper.dart';

class ComprehensiveReportScreen extends StatefulWidget {
  const ComprehensiveReportScreen({Key? key}) : super(key: key);

  @override
  State<ComprehensiveReportScreen> createState() =>
      _ComprehensiveReportScreenState();
}

class _ComprehensiveReportScreenState extends State<ComprehensiveReportScreen>
    with TickerProviderStateMixin {
  // Animation Controllers
  late AnimationController _animationController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Data from both sources
  SalesTotalReportDTO? _salesTotalData;
  ClosingEntriesReportDTO? _closingEntriesData;

  // Loading states
  bool _isLoadingSalesTotal = false;
  bool _isLoadingClosingEntries = false;

  // Filter parameters
  DateTime? _fromDate;
  DateTime? _toDate;

  // Services
  final ServerReportsService _reportsService = ServerReportsService();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeDefaults();
    _loadBothReports();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));
  }

  void _initializeDefaults() {
    _fromDate = DateTime.now().subtract(const Duration(days: 7));
    _toDate = DateTime.now();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _loadBothReports() async {
    await Future.wait([
      _loadSalesTotalReport(),
      _loadClosingEntriesReport(),
    ]);

    if (_salesTotalData != null || _closingEntriesData != null) {
      _animationController.forward();
      _slideController.forward();
    }
  }

  Future<void> _loadSalesTotalReport() async {
    setState(() {
      _isLoadingSalesTotal = true;
    });

    try {
      final deviceController = context.read<DeviceUserController>();
      String? deviceId = deviceController.selectedDevice?.id;
      int? userId = deviceController.selectedUser?.id;

      final report = await _reportsService.getSalesTotalWithPaidAmount(
        deviceId: deviceId,
        userId: userId,
        fromDate: _fromDate,
        toDate: _toDate,
      );

      setState(() {
        _salesTotalData = report;
        _isLoadingSalesTotal = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingSalesTotal = false;
      });
      errorSnackBar(message: 'خطأ في تحميل تقرير المبيعات: ${e.toString()}');
    }
  }

  Future<void> _loadClosingEntriesReport() async {
    setState(() {
      _isLoadingClosingEntries = true;
    });

    try {
      final controller = context.read<AccountingReportHelperController>();
      final report = await controller.generateClosingEntriesReport(
        fromDate: _fromDate,
        toDate: _toDate,
      );

      setState(() {
        _closingEntriesData = report;
        _isLoadingClosingEntries = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingClosingEntries = false;
      });
      errorSnackBar(message: 'خطأ في تحميل تقرير القيود: ${e.toString()}');
    }
  }

  Future<void> _showDateFilterDialog() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _fromDate != null && _toDate != null
          ? DateTimeRange(start: _fromDate!, end: _toDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
      });
      _loadBothReports();
    }
  }

  Future<void> _printComprehensiveReport() async {
    if (_salesTotalData == null && _closingEntriesData == null) {
      errorSnackBar(message: 'لا توجد بيانات للطباعة');
      return;
    }

    try {
      final reportMap = <String, String>{};

      // Add sales data if available
      if (_salesTotalData != null) {
        reportMap.addAll({
          '=== بيانات المبيعات (السيرفر) ===': '',
          'إجمالي المبيعات': _salesTotalData!.totalSales.toStringAsFixed(2),
          'إجمالي المرتجعات': _salesTotalData!.totalReturns.toStringAsFixed(2),
          'صافي المبيعات': _salesTotalData!.netSales.toStringAsFixed(2),
          'إجمالي الخصم': _salesTotalData!.totalDiscount.toStringAsFixed(2),
          'إجمالي المستلم': _salesTotalData!.totalReceive.toStringAsFixed(2),
          'إجمالي المدفوع': _salesTotalData!.totalPaid.toStringAsFixed(2),
          'إجمالي المتبقي': _salesTotalData!.totalRemaining.toStringAsFixed(2),
        });
      }

      // Add closing entries data if available
      if (_closingEntriesData != null) {
        final controller = context.read<AccountingReportHelperController>();
        reportMap.addAll({
          '=== بيانات قيود الإقفال ===': '',
          'إجمالي الشحن':
              controller.getTotalRechargeAmount().toStringAsFixed(2),
          'إجمالي الرسوم': controller.getTotalFeesAmount().toStringAsFixed(2),
          'صافي المبلغ المستلم': controller.getNetAmount().toStringAsFixed(2),
          'إجمالي الهدايا': controller.getGiftsAmount().toStringAsFixed(2),
          'الرصيد الحالي': controller.getCurrentBalance().toStringAsFixed(2),
        });
      }

      await ReportPrinterService.printGenericReport(
        reportMap,
        'التقرير الشامل المجمع',
        context,
        fromDate: _fromDate,
        toDate: _toDate,
      );
    } catch (e) {
      errorSnackBar(message: 'خطأ في الطباعة: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          T('التقرير الشامل المجمع'),
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: const Color(0xFF8B5CF6),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: _showDateFilterDialog,
            tooltip: 'تحديد الفترة الزمنية',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadBothReports,
            tooltip: 'تحديث البيانات',
          ),
          if ((_salesTotalData != null || _closingEntriesData != null) &&
              !_isLoadingSalesTotal &&
              !_isLoadingClosingEntries)
            PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'print') {
                  _printComprehensiveReport();
                }
              },
              itemBuilder: (context) => [
                ReportPrintHelper.buildPrintMenuItem(),
              ],
            ),
        ],
      ),
      floatingActionButton: (_salesTotalData != null ||
                  _closingEntriesData != null) &&
              !_isLoadingSalesTotal &&
              !_isLoadingClosingEntries
          ? ReportPrintHelper.buildFloatingPrintButton(
              context: context,
              hasData: _salesTotalData != null || _closingEntriesData != null,
              onPressed: _printComprehensiveReport,
            )
          : null,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoadingSalesTotal || _isLoadingClosingEntries) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('جاري تحميل البيانات من المصدرين...'),
          ],
        ),
      );
    }

    if (_salesTotalData == null && _closingEntriesData == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              size: 64,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            Text(
              T('لا توجد بيانات للعرض'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              T('تأكد من توفر البيانات في الفترة المحددة'),
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadBothReports,
              icon: const Icon(Icons.refresh),
              label: Text(T('إعادة المحاولة')),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF8B5CF6),
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeaderCard(),
              const SizedBox(height: 24),
              _buildCombinedSummarySection(),
              const SizedBox(height: 24),
              if (_salesTotalData != null) ...[
                _buildSalesReportSection(),
                const SizedBox(height: 24),
              ],
              if (_closingEntriesData != null) ...[
                _buildClosingEntriesSection(),
                const SizedBox(height: 24),
              ],
              _buildGrandTotalSection(),
              const SizedBox(height: 60), // Space for FAB
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF8B5CF6),
            Color(0xFF6366F1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: const Color(0xFF8B5CF6).withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      T('📊 التقرير الشامل المجمع'),
                      style: const TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      T('دمج بيانات المبيعات وقيود الإقفال من مصدرين مختلفين'),
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withOpacity(0.9),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.15),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: const Icon(
                  Icons.merge_type,
                  size: 32,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.date_range,
                  color: Colors.white.withOpacity(0.8),
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  _fromDate != null && _toDate != null
                      ? '${dateFormat.format(_fromDate!)} - ${dateFormat.format(_toDate!)}'
                      : T('جميع الفترات'),
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                GestureDetector(
                  onTap: _showDateFilterDialog,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      T('تغيير'),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCombinedSummarySection() {
    return Card(
      elevation: 6,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade50,
              Colors.purple.shade50,
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: Colors.blue.shade700,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  T('الملخص المجمع من المصدرين'),
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildStatusCard(
                    title: T('مصدر المبيعات'),
                    status: _salesTotalData != null ? 'متصل' : 'غير متاح',
                    color: _salesTotalData != null ? Colors.green : Colors.red,
                    icon: _salesTotalData != null
                        ? Icons.check_circle
                        : Icons.error,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatusCard(
                    title: T('مصدر قيود الإقفال'),
                    status: _closingEntriesData != null ? 'متصل' : 'غير متاح',
                    color:
                        _closingEntriesData != null ? Colors.green : Colors.red,
                    icon: _closingEntriesData != null
                        ? Icons.check_circle
                        : Icons.error,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard({
    required String title,
    required String status,
    required Color color,
    required IconData icon,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            status,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSalesReportSection() {
    if (_salesTotalData == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: Colors.green.shade700,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  T('📈 بيانات المبيعات (السيرفر)'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMetricsGrid([
              _MetricData(
                  'إجمالي المبيعات', _salesTotalData!.totalSales, Colors.blue),
              _MetricData('إجمالي المرتجعات', _salesTotalData!.totalReturns,
                  Colors.red),
              _MetricData(
                  'صافي المبيعات', _salesTotalData!.netSales, Colors.green),
              _MetricData('إجمالي الخصم', _salesTotalData!.totalDiscount,
                  Colors.orange),
              _MetricData('إجمالي المستلم', _salesTotalData!.totalReceive,
                  Colors.purple),
              _MetricData(
                  'إجمالي المدفوع', _salesTotalData!.totalPaid, Colors.teal),
              _MetricData(
                  'الإجمالي الصافي', _salesTotalData!.netTotal, Colors.indigo),
              _MetricData('إجمالي المتبقي', _salesTotalData!.totalRemaining,
                  Colors.brown),
            ]),
          ],
        ),
      ),
    );
  }

  Widget _buildClosingEntriesSection() {
    if (_closingEntriesData == null) return const SizedBox.shrink();

    final controller = context.read<AccountingReportHelperController>();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance,
                  color: Colors.blue.shade700,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  T('💰 بيانات قيود الإقفال'),
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildMetricsGrid([
              _MetricData('إجمالي الشحن', controller.getTotalRechargeAmount(),
                  Colors.blue),
              _MetricData('إجمالي الرسوم', controller.getTotalFeesAmount(),
                  Colors.green),
              _MetricData('صافي المبلغ المستلم', controller.getNetAmount(),
                  Colors.orange),
              _MetricData(
                  'إجمالي الهدايا', controller.getGiftsAmount(), Colors.purple),
              _MetricData('الرصيد الحالي', controller.getCurrentBalance(),
                  Colors.indigo),
            ]),

            // عرض ملخصات إضافية
            const SizedBox(height: 20),
            _buildAdditionalSummaries(controller),
          ],
        ),
      ),
    );
  }

  Widget _buildAdditionalSummaries(
      AccountingReportHelperController controller) {
    final companySummaries = controller.getCompanySummaries();
    final adminSummaries = controller.getAdminSummaries();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('📋 ملخصات تفصيلية'),
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),

        // Company summaries
        if (companySummaries != null && companySummaries.isNotEmpty) ...[
          Text(
            T('حسب الشركة:'),
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          ...companySummaries.take(3).map((company) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            company.companyName ?? 'غير محدد',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                              'معاملات: ${company.transactionCount} | أعضاء: ${company.memberCount}'),
                        ],
                      ),
                    ),
                    Text(
                      controller.formatNumber(company.totalRecharge),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
              )),
          const SizedBox(height: 12),
        ],

        // Admin summaries
        if (adminSummaries != null && adminSummaries.isNotEmpty) ...[
          Text(
            T('حسب المدير:'),
            style: const TextStyle(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),
          ...adminSummaries.take(3).map((admin) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            admin.adminName ?? 'غير محدد',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                              'حساب: ${admin.adminAccount ?? ''} | معاملات: ${admin.transactionCount}'),
                        ],
                      ),
                    ),
                    Text(
                      controller.formatNumber(admin.totalAmount),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ],
    );
  }

  Widget _buildGrandTotalSection() {
    // Calculate combined totals
    double salesTotal = _salesTotalData?.netTotal ?? 0.0;
    double closingTotal = _closingEntriesData != null
        ? context.read<AccountingReportHelperController>().getNetAmount()
        : 0.0;
    double grandTotal = salesTotal + closingTotal;

    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.purple.shade700,
              Colors.indigo.shade700,
            ],
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.calculate,
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  T('🧮 الإجمالي الكبير المجمع'),
                  style: const TextStyle(
                    fontSize: 22,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.white.withOpacity(0.3)),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        T('إجمالي المبيعات (السيرفر):'),
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        NumberFormat('#,##0.00').format(salesTotal),
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        T('إجمالي قيود الإقفال:'),
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        NumberFormat('#,##0.00').format(closingTotal),
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Container(
                    height: 1,
                    color: Colors.white.withOpacity(0.5),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        T('الإجمالي الكبير:'),
                        style: const TextStyle(
                          fontSize: 20,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        NumberFormat('#,##0.00').format(grandTotal),
                        style: const TextStyle(
                          fontSize: 24,
                          color: Colors.white,
                          fontWeight: FontWeight.w900,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          T('مصادر البيانات'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${_salesTotalData != null ? 1 : 0} + ${_closingEntriesData != null ? 1 : 0} = ${(_salesTotalData != null ? 1 : 0) + (_closingEntriesData != null ? 1 : 0)}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        Text(
                          T('تاريخ التحديث'),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          DateFormat('HH:mm').format(DateTime.now()),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricsGrid(List<_MetricData> metrics) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 2.5,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: metrics.length,
      itemBuilder: (context, index) {
        final metric = metrics[index];
        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: metric.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: metric.color.withOpacity(0.3)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                metric.title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                  color: metric.color.darken(20),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 4),
              Text(
                NumberFormat('#,##0.00').format(metric.value),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: metric.color,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },
    );
  }
}

class _MetricData {
  final String title;
  final double value;
  final Color color;

  _MetricData(this.title, this.value, this.color);
}

extension ColorExtension on Color {
  Color darken(int percent) {
    var f = 1 - percent / 100;
    return Color.fromARGB(
      alpha,
      (red * f).round(),
      (green * f).round(),
      (blue * f).round(),
    );
  }
}
