import 'package:flutter/foundation.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';

void addProductWithSelectedAttributes({
  required ProductDTO product,
  required Map<int, ItemAttributeOption> selectedOptions,
  required List<ProductDTO> currentProducts,
  required void Function(ProductDTO updatedProduct, int index) onUpdateExisting,
  required void Function(ProductDTO newProduct) onAddNew,
  required VoidCallback onAfterChange,
}) {
  String optionsHash = selectedOptions.entries
      .map((e) => '${e.value.optionId}-${e.key}')
      .join('_');
  String virtualProductId = '${product.id}_$optionsHash';

  int existingIndex = currentProducts
      .indexWhere((element) => element.virtualProductId == virtualProductId);

  if (existingIndex >= 0) {
    var existingProduct = currentProducts[existingIndex];
    double newQuantity =
        (existingProduct.quantity ?? 1) + (product.quantity ?? 1);

    final updatedProduct = ProductDTO.from(existingProduct)
      ..quantity = newQuantity
      ..total = (existingProduct.price ?? 0) * newQuantity;

    onUpdateExisting(updatedProduct, existingIndex);
    onAfterChange();
    return;
  }

  // Create the selected attributes in the required format
  List<ItemAttribute> selectedAttributes =
      createSelectedAttributesFormat(selectedOptions, product);

  final ProductDTO productCopy = ProductDTO(
    id: product.id,
    title: product.title,
    barcode: product.barcode,
    barcodeName: product.barcodeName,
    code: product.code,
    description: product.description,
    price: product.price,
    stock: product.stock,
    uniteId: product.uniteId,
    uniteName: product.uniteName,
    category: product.category,
    quantity: product.quantity ?? 1.0,
    warehouseId: product.warehouseId,
    warehouseName: product.warehouseName,
    thumbnail: product.thumbnail,
    attribute:
        selectedAttributes, // Store selected attributes in the required format
    hasSelectedAttributes: true,
    virtualProductId: virtualProductId,
    selectedOptionIds:
        selectedOptions.values.map((e) => e.optionId ?? 0).toList(),
  );

  String optionsString = getFormattedSelectedOptions(selectedOptions);

  if (optionsString.isNotEmpty) {
    productCopy.title = '${productCopy.title} - $optionsString';

    String attributeDetails = selectedOptions.entries
        .map((entry) =>
            '${product.itemAttributes?.firstWhere((attr) => attr.id == entry.key).attributeName}: ${entry.value.optionName}')
        .join(', ');

    if (productCopy.description == null || productCopy.description!.isEmpty) {
      productCopy.description = attributeDetails;
    } else {
      productCopy.description =
          '${productCopy.description}\n[$attributeDetails]';
    }
  }

  onAddNew(productCopy);
  onAfterChange();
}

String getFormattedSelectedOptions(
    Map<int, ItemAttributeOption> selectedOptions) {
  if (selectedOptions.isEmpty) return '';

  final sortedKeys = selectedOptions.keys.toList()..sort();

  return sortedKeys
      .map((key) => selectedOptions[key]?.optionName ?? '')
      .join('/');
}

// Create selected attributes in the required format
List<ItemAttribute> createSelectedAttributesFormat(
    Map<int, ItemAttributeOption> selectedOptions, ProductDTO product) {
  List<ItemAttribute> selectedAttributes = [];

  // Group by attribute ID and create the required format
  selectedOptions.forEach((attributeId, selectedOption) {
    // Find the original attribute to get its details
    ItemAttribute? originalAttribute = product.itemAttributes?.firstWhere(
      (attr) => attr.id == attributeId,
      orElse: () => ItemAttribute(),
    );

    // Create the attribute with only the selected option
    ItemAttribute selectedAttribute = ItemAttribute(
      id: attributeId,
      attributeTypeId: originalAttribute?.attributeTypeId,
      attributeName: originalAttribute?.attributeName ?? '',
      itemId: originalAttribute?.itemId,
      order: originalAttribute?.order,
      itemsAttributeOptions: [
        ItemAttributeOption(
          id: selectedOption.id, // Use the actual option ID
          attributeId: attributeId,
          optionId: selectedOption.optionId,
          optionName: selectedOption.optionName,
        )
      ],
    );

    selectedAttributes.add(selectedAttribute);
  });

  return selectedAttributes;
}
