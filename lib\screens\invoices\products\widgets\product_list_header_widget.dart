import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';

class ProductListHeaderWidget extends StatefulWidget {
  const ProductListHeaderWidget({super.key});

  @override
  State<ProductListHeaderWidget> createState() =>
      _ProductListHeaderWidgetState();
}

class _ProductListHeaderWidgetState extends State<ProductListHeaderWidget> {
  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 30,
      child: SizedBox(
        width: context.width - 20,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(
                  Icons.list,
                  size: 30,
                  color: context.colors.secondary,
                ),
                SizedBox(
                  height: 50,
                  width: context.width - 60,
                  child: CommonTextField(
                    label: T("Enter Item Name"),
                  ),
                ),
              ],
            ),
            const Si<PERSON><PERSON><PERSON>(height: 15),
            SizedBox(
              width: context.width,
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: 5,
                itemBuilder: (context, index) {
                  return Container(
                    alignment: Alignment.center,
                    margin: const EdgeInsets.symmetric(
                      horizontal: 5,
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border:
                          Border.all(width: 1, color: context.colors.onPrimary),
                    ),
                    child: const Text(
                      "catogries1",
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  );
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
