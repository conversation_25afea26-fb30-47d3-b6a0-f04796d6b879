import 'dart:ui';
import 'dart:math' as Math;
import 'package:vector_math/vector_math.dart' as vector;

/// نموذج التخطيط الرئيسي للمستودع
class WarehouseLayout {
  String id;
  String name;
  String description;
  double width; // العرض بـ cm
  double height; // الطول بـ cm
  double wallHeight; // ارتفاع الجدران بـ cm

  // ربط بالمستودع الفعلي في النظام
  int? linkedWarehouseId; // معرف المستودع في النظام
  String? linkedWarehouseName; // اسم المستودع

  List<Wall> walls;
  List<Entrance> entrances;
  List<Shelf> shelves;

  DateTime createdAt;
  DateTime updatedAt;

  WarehouseLayout({
    required this.id,
    required this.name,
    this.description = '',
    required this.width,
    required this.height,
    this.wallHeight = 300.0,
    this.linkedWarehouseId,
    this.linkedWarehouseName,
    List<Wall>? walls,
    List<Entrance>? entrances,
    List<Shelf>? shelves,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : walls = walls ?? [],
        entrances = entrances ?? [],
        shelves = shelves ?? [],
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'width': width,
      'height': height,
      'wallHeight': wallHeight,
      'linkedWarehouseId': linkedWarehouseId,
      'linkedWarehouseName': linkedWarehouseName,
      'walls': walls.map((w) => w.toJson()).toList(),
      'entrances': entrances.map((e) => e.toJson()).toList(),
      'shelves': shelves.map((s) => s.toJson()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// تحويل من JSON
  factory WarehouseLayout.fromJson(Map<String, dynamic> json) {
    return WarehouseLayout(
      id: json['id'],
      name: json['name'],
      description: json['description'] ?? '',
      width: json['width'].toDouble(),
      height: json['height'].toDouble(),
      wallHeight: json['wallHeight']?.toDouble() ?? 300.0,
      linkedWarehouseId: json['linkedWarehouseId'],
      linkedWarehouseName: json['linkedWarehouseName'],
      walls: (json['walls'] as List?)?.map((w) => Wall.fromJson(w)).toList(),
      entrances: (json['entrances'] as List?)
          ?.map((e) => Entrance.fromJson(e))
          .toList(),
      shelves:
          (json['shelves'] as List?)?.map((s) => Shelf.fromJson(s)).toList(),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// نسخة محدثة
  WarehouseLayout copyWith({
    String? name,
    String? description,
    double? width,
    double? height,
    double? wallHeight,
    List<Wall>? walls,
    List<Entrance>? entrances,
    List<Shelf>? shelves,
  }) {
    return WarehouseLayout(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      width: width ?? this.width,
      height: height ?? this.height,
      wallHeight: wallHeight ?? this.wallHeight,
      walls: walls ?? this.walls,
      entrances: entrances ?? this.entrances,
      shelves: shelves ?? this.shelves,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }
}

/// نموذج الجدار
class Wall {
  String id;
  List<vector.Vector2> points; // نقاط الخط المتعدد
  double thickness; // سماكة الجدار بـ cm
  Color color;

  Wall({
    required this.id,
    required this.points,
    this.thickness = 20.0,
    this.color = const Color(0xFF34495E),
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'points': points.map((p) => {'x': p.x, 'y': p.y}).toList(),
      'thickness': thickness,
      'color': color.value,
    };
  }

  factory Wall.fromJson(Map<String, dynamic> json) {
    return Wall(
      id: json['id'],
      points: (json['points'] as List)
          .map((p) => vector.Vector2(p['x'].toDouble(), p['y'].toDouble()))
          .toList(),
      thickness: json['thickness']?.toDouble() ?? 20.0,
      color: Color(json['color'] ?? 0xFF34495E),
    );
  }

  /// طول الجدار الإجمالي
  double get totalLength {
    double length = 0;
    for (int i = 0; i < points.length - 1; i++) {
      length += (points[i + 1] - points[i]).length;
    }
    return length;
  }

  /// الحصول على نقطة في موضع محدد على الجدار
  vector.Vector2? getPointAtDistance(double distance) {
    double currentDistance = 0;

    for (int i = 0; i < points.length - 1; i++) {
      final segmentStart = points[i];
      final segmentEnd = points[i + 1];
      final segmentLength = (segmentEnd - segmentStart).length;

      if (currentDistance + segmentLength >= distance) {
        final ratio = (distance - currentDistance) / segmentLength;
        return segmentStart + (segmentEnd - segmentStart) * ratio;
      }

      currentDistance += segmentLength;
    }

    return null;
  }
}

/// نموذج المدخل/الفتحة
class Entrance {
  String id;
  String wallId; // معرف الجدار
  double startDistance; // المسافة من بداية الجدار
  double width; // عرض الفتحة
  String name;
  EntranceType type;

  Entrance({
    required this.id,
    required this.wallId,
    required this.startDistance,
    required this.width,
    this.name = '',
    this.type = EntranceType.door,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wallId': wallId,
      'startDistance': startDistance,
      'width': width,
      'name': name,
      'type': type.toString(),
    };
  }

  factory Entrance.fromJson(Map<String, dynamic> json) {
    return Entrance(
      id: json['id'],
      wallId: json['wallId'],
      startDistance: json['startDistance'].toDouble(),
      width: json['width'].toDouble(),
      name: json['name'] ?? '',
      type: EntranceType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => EntranceType.door,
      ),
    );
  }

  double get endDistance => startDistance + width;
}

enum EntranceType {
  door, // باب
  window, // نافذة
  opening, // فتحة
}

/// نموذج الخزانة/الرف
class Shelf {
  String id;
  String name;
  vector.Vector2 position; // الموضع في المستودع (cm)
  double width; // العرض (cm)
  double depth; // العمق (cm)
  double height; // الارتفاع (cm)
  double rotation; // الدوران بالدرجات (0, 90, 180, 270)

  ShelfType type;
  Color color;

  // تفاصيل داخلية
  int levels; // عدد المستويات الرأسية
  int slotsPerLevel; // عدد الخانات في كل مستوى
  List<Bin> bins; // الخانات (levels × slotsPerLevel)

  Shelf({
    required this.id,
    required this.name,
    required this.position,
    required this.width,
    required this.depth,
    required this.height,
    this.rotation = 0.0,
    required this.type,
    this.color = const Color(0xFF3498DB),
    this.levels = 5,
    this.slotsPerLevel = 4,
    List<Bin>? bins,
  }) : bins = bins ?? [] {
    _generateBins();
  }

  /// توليد الخانات أوتوماتيكياً
  void _generateBins() {
    bins.clear();
    for (int level = 0; level < levels; level++) {
      for (int slot = 0; slot < slotsPerLevel; slot++) {
        bins.add(Bin(
          id: '${id}_L${level}_S$slot',
          shelfId: id,
          level: level,
          slot: slot,
          position: vector.Vector2(
            slot * (width / slotsPerLevel),
            level * (height / levels),
          ),
        ));
      }
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'position': {'x': position.x, 'y': position.y},
      'width': width,
      'depth': depth,
      'height': height,
      'rotation': rotation,
      'type': type.toString(),
      'color': color.value,
      'levels': levels,
      'slotsPerLevel': slotsPerLevel,
      'bins': bins.map((b) => b.toJson()).toList(),
    };
  }

  factory Shelf.fromJson(Map<String, dynamic> json) {
    final shelf = Shelf(
      id: json['id'],
      name: json['name'],
      position: vector.Vector2(
        json['position']['x'].toDouble(),
        json['position']['y'].toDouble(),
      ),
      width: json['width'].toDouble(),
      depth: json['depth'].toDouble(),
      height: json['height'].toDouble(),
      rotation: json['rotation']?.toDouble() ?? 0.0,
      type: ShelfType.values.firstWhere(
        (t) => t.toString() == json['type'],
        orElse: () => ShelfType.standard,
      ),
      color: Color(json['color'] ?? 0xFF3498DB),
      levels: json['levels'] ?? 5,
      slotsPerLevel: json['slotsPerLevel'] ?? 4,
    );

    // تحميل الخانات إذا كانت موجودة
    if (json['bins'] != null) {
      shelf.bins.clear();
      shelf.bins.addAll(
        (json['bins'] as List).map((b) => Bin.fromJson(b)),
      );
    }

    return shelf;
  }

  /// الحصول على الزوايا الأربع للخزانة (مع الدوران)
  List<vector.Vector2> get corners {
    final halfWidth = width / 2;
    final halfDepth = depth / 2;

    // الزوايا قبل الدوران
    final localCorners = [
      vector.Vector2(-halfWidth, -halfDepth),
      vector.Vector2(halfWidth, -halfDepth),
      vector.Vector2(halfWidth, halfDepth),
      vector.Vector2(-halfWidth, halfDepth),
    ];

    // تطبيق الدوران
    final radians = rotation * (3.14159 / 180);
    final cos = Math.cos(radians);
    final sin = Math.sin(radians);

    return localCorners.map((corner) {
      final rotatedX = corner.x * cos - corner.y * sin;
      final rotatedY = corner.x * sin + corner.y * cos;
      return vector.Vector2(position.x + rotatedX, position.y + rotatedY);
    }).toList();
  }

  /// فحص التصادم مع خزانة أخرى
  bool collidesWith(Shelf other) {
    // فحص AABB بسيط أولاً
    final thisBounds = _getAABB();
    final otherBounds = other._getAABB();

    return thisBounds.overlaps(otherBounds);
  }

  Rect _getAABB() {
    final corners = this.corners;
    double minX = corners[0].x;
    double maxX = corners[0].x;
    double minY = corners[0].y;
    double maxY = corners[0].y;

    for (final corner in corners) {
      minX = Math.min(minX, corner.x);
      maxX = Math.max(maxX, corner.x);
      minY = Math.min(minY, corner.y);
      maxY = Math.max(maxY, corner.y);
    }

    return Rect.fromLTRB(minX, minY, maxX, maxY);
  }
}

enum ShelfType {
  standard, // خزانة عادية
  refrigerated, // خزانة مبردة
  controlled, // خزانة محكمة الحرارة
  hazardous, // خزانة مواد خطرة
  narcotics, // خزانة مخدرات
}

/// نموذج الخانة داخل الخزانة
class Bin {
  String id;
  String shelfId;
  int level; // المستوى (0 = الأسفل)
  int slot; // رقم الخانة في المستوى
  vector.Vector2 position; // الموضع النسبي داخل الخزانة

  // معلومات المنتج المخزن
  int? productId; // معرف المنتج في النظام
  String? productName;
  String? productCode;
  String? barcode;
  int quantity;
  int maxCapacity; // السعة القصوى للخانة
  DateTime? expiryDate;
  double? unitPrice;
  String? unitName;
  BinStatus status;

  Bin({
    required this.id,
    required this.shelfId,
    required this.level,
    required this.slot,
    required this.position,
    this.productId,
    this.productName,
    this.productCode,
    this.barcode,
    this.quantity = 0,
    this.maxCapacity = 100,
    this.expiryDate,
    this.unitPrice,
    this.unitName,
    this.status = BinStatus.empty,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'shelfId': shelfId,
      'level': level,
      'slot': slot,
      'position': {'x': position.x, 'y': position.y},
      'productId': productId,
      'productName': productName,
      'productCode': productCode,
      'barcode': barcode,
      'quantity': quantity,
      'maxCapacity': maxCapacity,
      'expiryDate': expiryDate?.toIso8601String(),
      'unitPrice': unitPrice,
      'unitName': unitName,
      'status': status.toString(),
    };
  }

  factory Bin.fromJson(Map<String, dynamic> json) {
    return Bin(
      id: json['id'],
      shelfId: json['shelfId'],
      level: json['level'],
      slot: json['slot'],
      position: vector.Vector2(
        json['position']['x'].toDouble(),
        json['position']['y'].toDouble(),
      ),
      productId: json['productId'],
      productName: json['productName'],
      productCode: json['productCode'],
      barcode: json['barcode'],
      quantity: json['quantity'] ?? 0,
      maxCapacity: json['maxCapacity'] ?? 100,
      expiryDate: json['expiryDate'] != null
          ? DateTime.parse(json['expiryDate'])
          : null,
      unitPrice: json['unitPrice']?.toDouble(),
      unitName: json['unitName'],
      status: BinStatus.values.firstWhere(
        (s) => s.toString() == json['status'],
        orElse: () => BinStatus.empty,
      ),
    );
  }

  /// لون الخانة حسب الحالة
  Color get statusColor {
    switch (status) {
      case BinStatus.empty:
        return const Color(0xFFECF0F1);
      case BinStatus.normal:
        return const Color(0xFF27AE60);
      case BinStatus.lowStock:
        return const Color(0xFFF39C12);
      case BinStatus.expiringSoon:
        return const Color(0xFFE67E22);
      case BinStatus.expired:
        return const Color(0xFFE74C3C);
    }
  }

  /// فحص إذا كانت الخانة قريبة الانتهاء
  bool get isExpiringSoon {
    if (expiryDate == null) return false;
    final daysUntilExpiry = expiryDate!.difference(DateTime.now()).inDays;
    return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
  }

  /// فحص إذا كانت الخانة منتهية الصلاحية
  bool get isExpired {
    if (expiryDate == null) return false;
    return expiryDate!.isBefore(DateTime.now());
  }
}

enum BinStatus {
  empty, // فارغة
  normal, // طبيعية
  lowStock, // مخزون قليل
  expiringSoon, // قريبة الانتهاء
  expired, // منتهية الصلاحية
}

/// أنواع الخزائن المعرفة مسبقاً
class ShelfTemplate {
  final String name;
  final double width;
  final double depth;
  final double height;
  final ShelfType type;
  final Color color;
  final int levels;
  final int slotsPerLevel;

  const ShelfTemplate({
    required this.name,
    required this.width,
    required this.depth,
    required this.height,
    required this.type,
    required this.color,
    required this.levels,
    required this.slotsPerLevel,
  });

  static const List<ShelfTemplate> predefined = [
    ShelfTemplate(
      name: 'خزانة صغيرة',
      width: 100,
      depth: 40,
      height: 200,
      type: ShelfType.standard,
      color: Color(0xFF3498DB),
      levels: 4,
      slotsPerLevel: 3,
    ),
    ShelfTemplate(
      name: 'خزانة متوسطة',
      width: 150,
      depth: 50,
      height: 250,
      type: ShelfType.standard,
      color: Color(0xFF2ECC71),
      levels: 5,
      slotsPerLevel: 4,
    ),
    ShelfTemplate(
      name: 'خزانة كبيرة',
      width: 200,
      depth: 60,
      height: 300,
      type: ShelfType.standard,
      color: Color(0xFF9B59B6),
      levels: 6,
      slotsPerLevel: 5,
    ),
    ShelfTemplate(
      name: 'خزانة مبردة',
      width: 120,
      depth: 80,
      height: 200,
      type: ShelfType.refrigerated,
      color: Color(0xFF3498DB),
      levels: 3,
      slotsPerLevel: 3,
    ),
    ShelfTemplate(
      name: 'خزانة مخدرات',
      width: 80,
      depth: 40,
      height: 150,
      type: ShelfType.narcotics,
      color: Color(0xFFE74C3C),
      levels: 3,
      slotsPerLevel: 2,
    ),
  ];
}
