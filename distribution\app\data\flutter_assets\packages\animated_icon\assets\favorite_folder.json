{"v": "5.6.5", "fr": 24, "ip": 0, "op": 28, "w": 30, "h": 30, "nm": "favorite-folder", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "lottie Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15, 15, 0], "ix": 2}, "a": {"a": 0, "k": [15, 15, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 3, "s": [{"i": [[1.261, -1.003], [0.291, 0.231], [0, 2.238], [-1.534, 0], [-0.506, -0.68], [-0.914, 0], [0, -1.534]], "o": [[-0.291, 0.231], [-1.261, -1.003], [0, -1.534], [0.913, 0], [0.506, -0.68], [1.534, 0], [0, 2.238]], "v": [[0.493, 5.816], [-0.493, 5.816], [-5, -0.223], [-2.222, -3.001], [0, -1.874], [2.222, -3.001], [5, -0.223]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[1.749, -1.391], [0.403, 0.321], [0, 3.104], [-2.127, 0], [-0.702, -0.943], [-1.268, 0], [0, -2.127]], "o": [[-0.404, 0.32], [-1.749, -1.391], [0, -2.127], [1.266, 0], [0.702, -0.943], [2.127, 0], [0, 3.104]], "v": [[0.684, 7.477], [-0.684, 7.477], [-6.934, -0.898], [-3.081, -4.75], [0, -3.189], [3.081, -4.75], [6.934, -0.898]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 11, "s": [{"i": [[1.261, -1.003], [0.291, 0.231], [0, 2.238], [-1.534, 0], [-0.506, -0.68], [-0.914, 0], [0, -1.534]], "o": [[-0.291, 0.231], [-1.261, -1.003], [0, -1.534], [0.913, 0], [0.506, -0.68], [1.534, 0], [0, 2.238]], "v": [[0.493, 5.816], [-0.493, 5.816], [-5, -0.223], [-2.222, -3.001], [0, -1.874], [2.222, -3.001], [5, -0.223]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [{"i": [[1.261, -1.003], [0.291, 0.231], [0, 2.238], [-1.534, 0], [-0.506, -0.68], [-0.914, 0], [0, -1.534]], "o": [[-0.291, 0.231], [-1.261, -1.003], [0, -1.534], [0.913, 0], [0.506, -0.68], [1.534, 0], [0, 2.238]], "v": [[0.493, 5.816], [-0.493, 5.816], [-5, -0.223], [-2.222, -3.001], [0, -1.874], [2.222, -3.001], [5, -0.223]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[1.025, -0.815], [0.236, 0.188], [0, 1.818], [-1.246, 0], [-0.411, -0.552], [-0.743, 0], [0, -1.246]], "o": [[-0.236, 0.188], [-1.025, -0.815], [0, -1.246], [0.742, 0], [0.411, -0.552], [1.246, 0], [0, 1.818]], "v": [[0.401, 5.011], [-0.401, 5.011], [-4.062, 0.105], [-1.805, -2.152], [0, -1.237], [1.805, -2.152], [4.062, 0.105]], "c": true}]}, {"t": 25, "s": [{"i": [[1.261, -1.003], [0.291, 0.231], [0, 2.238], [-1.534, 0], [-0.506, -0.68], [-0.914, 0], [0, -1.534]], "o": [[-0.291, 0.231], [-1.261, -1.003], [0, -1.534], [0.913, 0], [0.506, -0.68], [1.534, 0], [0, 2.238]], "v": [[0.493, 5.816], [-0.493, 5.816], [-5, -0.223], [-2.222, -3.001], [0, -1.874], [2.222, -3.001], [5, -0.223]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 3, "s": [{"i": [[1.105, 0], [0, 0], [0, 0], [0.703, 0], [0, 0], [0, -1.105], [0, 0], [-1.105, 0], [0, 0], [0, 1.104], [0, 0]], "o": [[0, 0], [0, 0], [-0.361, -0.602], [0, 0], [-1.105, 0], [0, 0], [0, 1.104], [0, 0], [1.105, 0], [0, 0], [0, -1.105]], "v": [[11, -9.001], [-3.8, -9.001], [-4.417, -10.029], [-6.132, -11.001], [-11, -11.001], [-13, -9.001], [-13, 9.001], [-11, 11.001], [11, 11.001], [13, 9.001], [13, -7.001]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0.552, 0.108], [1.425, -0.094], [0, 0], [0.703, 0], [0, 0], [0, -1.105], [-1.906, -7.376], [-1.043, -0.274], [-6.469, 1.624], [-0.25, 0.968], [0.125, 0.875]], "o": [[-5.281, -1.031], [-0.263, -0.469], [-0.361, -0.602], [0, 0], [-1.105, 0], [0, 0], [0.332, 1.283], [5, 1.312], [1.415, -0.355], [1.626, -5.592], [-0.125, -0.875]], "v": [[11, -9.001], [-3.988, -9.376], [-4.417, -10.029], [-6.132, -11.001], [-11, -11.001], [-13, -9.001], [-13, 9.001], [-11, 11.001], [11, 11.001], [13, 9.001], [13, -7.001]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[1.105, 0], [0, 0], [0, 0], [0.703, 0], [0, 0], [0, -1.105], [0, 0], [-1.105, 0], [0, 0], [0, 1.104], [0, 0]], "o": [[0, 0], [0, 0], [-0.361, -0.602], [0, 0], [-1.105, 0], [0, 0], [0, 1.104], [0, 0], [1.105, 0], [0, 0], [0, -1.105]], "v": [[11, -9.001], [-3.8, -9.001], [-4.417, -10.029], [-6.132, -11.001], [-11, -11.001], [-13, -9.001], [-13, 9.001], [-11, 11.001], [11, 11.001], [13, 9.001], [13, -7.001]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [{"i": [[1.105, 0], [0, 0], [0, 0], [0.703, 0], [0, 0], [0, -1.105], [0, 0], [-1.105, 0], [0, 0], [0, 1.104], [0, 0]], "o": [[0, 0], [0, 0], [-0.361, -0.602], [0, 0], [-1.105, 0], [0, 0], [0, 1.104], [0, 0], [1.105, 0], [0, 0], [0, -1.105]], "v": [[11, -9.001], [-3.8, -9.001], [-4.417, -10.029], [-6.132, -11.001], [-11, -11.001], [-13, -9.001], [-13, 9.001], [-11, 11.001], [11, 11.001], [13, 9.001], [13, -7.001]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[1.359, -0.359], [2.206, 0.688], [0, 0], [0.703, 0], [0, 0], [0, -1.105], [1.256, -7.156], [-1.61, 0.405], [-7.188, -1.152], [0.328, 1.28], [-0.264, 1.587]], "o": [[-5.687, 1.503], [-0.263, -0.469], [-0.361, -0.602], [0, 0], [-1.105, 0], [0, 0], [-0.266, 1.515], [6.453, -1.626], [1.75, 0.28], [-1.723, -6.727], [0.234, -1.406]], "v": [[11, -9.001], [-3.988, -9.376], [-4.417, -10.029], [-6.132, -11.001], [-11, -11.001], [-13, -9.001], [-13, 9.001], [-10.438, 10.907], [10.469, 10.969], [12.906, 8.813], [13, -7.001]], "c": true}]}, {"t": 25, "s": [{"i": [[1.105, 0], [0, 0], [0, 0], [0.703, 0], [0, 0], [0, -1.105], [0, 0], [-1.105, 0], [0, 0], [0, 1.104], [0, 0]], "o": [[0, 0], [0, 0], [-0.361, -0.602], [0, 0], [-1.105, 0], [0, 0], [0, 1.104], [0, 0], [1.105, 0], [0, 0], [0, -1.105]], "v": [[11, -9.001], [-3.8, -9.001], [-4.417, -10.029], [-6.132, -11.001], [-11, -11.001], [-13, -9.001], [-13, 9.001], [-11, 11.001], [11, 11.001], [13, 9.001], [13, -7.001]], "c": true}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15, 14], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}], "markers": []}