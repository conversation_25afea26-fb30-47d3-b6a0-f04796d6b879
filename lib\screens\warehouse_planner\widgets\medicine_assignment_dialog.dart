import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../providers/warehouse_planner_provider.dart';
import '../../../models/warehouse_planner/warehouse_layout.dart';
import '../../../controllers/product_controller.dart';
import '../../../models/dto/products/product_dto.dart';

class MedicineAssignmentDialog extends StatefulWidget {
  final Shelf shelf;

  const MedicineAssignmentDialog({
    Key? key,
    required this.shelf,
  }) : super(key: key);

  @override
  State<MedicineAssignmentDialog> createState() =>
      _MedicineAssignmentDialogState();
}

class _MedicineAssignmentDialogState extends State<MedicineAssignmentDialog> {
  List<ProductDTO> _availableProducts = [];
  List<ProductDTO> _filteredProducts = [];
  final TextEditingController _searchController = TextEditingController();
  bool _isLoading = true;
  late ProductController _productController;

  @override
  void initState() {
    super.initState();
    _productController = ProductController();
    _loadProducts();
    _searchController.addListener(_filterProducts);
  }

  Future<void> _loadProducts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // تحميل أول مجموعة من المنتجات
      await _loadInitialProducts();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading products: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المنتجات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterProducts() {
    final query = _searchController.text.trim();

    if (query.isEmpty) {
      // إذا كان البحث فارغ، عرض أول 20 منتج
      _loadInitialProducts();
    } else {
      // البحث مباشرة في قاعدة البيانات
      _searchInDatabase(query);
    }
  }

  Future<void> _loadInitialProducts() async {
    try {
      await _productController.getItems(resetAndRefresh: true);
      setState(() {
        _availableProducts = _productController.realProductList;
        _filteredProducts = List.from(_availableProducts);
      });
    } catch (e) {
      print('Error loading initial products: $e');
    }
  }

  Future<void> _searchInDatabase(String query) async {
    try {
      setState(() {
        _isLoading = true;
      });

      // البحث باستخدام ProductController مع معاملات البحث
      await _productController.getItems(
        resetAndRefresh: true,
        search: query,
      );

      setState(() {
        _availableProducts = _productController.realProductList;
        _filteredProducts = List.from(_availableProducts);
        _isLoading = false;
      });
    } catch (e) {
      print('Error searching products: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadMoreProducts() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // تحميل المزيد من المنتجات (الصفحة التالية)
      await _productController.getItems(resetAndRefresh: false);

      setState(() {
        _availableProducts = _productController.realProductList;
        _filteredProducts = List.from(_availableProducts);
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading more products: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المزيد: $e'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    }
  }

  void _assignProductToBin(ProductDTO product, Bin bin) {
    // التحقق من وجود منتج مسبق في الخانة
    if (bin.productName != null) {
      _showReplaceConfirmationDialog(product, bin);
      return;
    }

    // التحقق من المخزون
    final stock = product.stock ?? 0;
    if (stock <= 0) {
      _showStockWarningDialog(product, bin);
      return;
    }

    _showAssignmentDialog(product, bin);
  }

  void _showReplaceConfirmationDialog(ProductDTO product, Bin bin) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('⚠️ استبدال المنتج'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('الخانة تحتوي على: ${bin.productName}'),
            const SizedBox(height: 8),
            Text('هل تريد استبدالها بـ: ${product.title}؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showAssignmentDialog(product, bin);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('استبدال', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showStockWarningDialog(ProductDTO product, Bin bin) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('⚠️ تحذير مخزون'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('المنتج "${product.title}" غير متوفر في المخزون.'),
            const SizedBox(height: 8),
            const Text('هل تريد إضافته رغم ذلك؟'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showAssignmentDialog(product, bin);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إضافة رغم ذلك',
                style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showAssignmentDialog(ProductDTO product, Bin bin) {
    showDialog(
      context: context,
      builder: (context) => _AssignProductDialog(
        product: product,
        bin: bin,
        shelf: widget.shelf,
        onConfirm: (quantity, expiryDate) {
          setState(() {
            // تحديث معلومات الخانة
            bin.productId = product.id;
            bin.productName = product.title;
            bin.productCode = product.code;
            bin.barcode = product.barcode;
            bin.quantity = quantity;
            bin.expiryDate = expiryDate;
            bin.unitName = product.uniteName;
            bin.unitPrice = product.price;
            bin.status = quantity > 0 ? BinStatus.normal : BinStatus.empty;
          });

          // إشعار Provider بالتحديث
          final provider =
              Provider.of<WarehousePlannerProvider>(context, listen: false);
          // تحديث المستودع لإشعار المستمعين
          provider.updateEditorState(provider.editorState);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تعيين ${product.title} للخانة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان ومعلومات الخزانة
            Row(
              children: [
                Icon(Icons.medical_services, color: const Color(0xFF3498DB)),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'توزيع الأدوية - ${widget.shelf.name}',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${widget.shelf.levels} مستويات × ${widget.shelf.slotsPerLevel} خانات = ${widget.shelf.bins.length} خانة إجمالي',
                        style: const TextStyle(
                          color: Color(0xFF6C757D),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const Divider(),

            // بحث وفلترة
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: TextField(
                    controller: _searchController,
                    decoration: const InputDecoration(
                      labelText: 'بحث عن دواء',
                      hintText: 'اسم الدواء، الكود، أو الباركود',
                      prefixIcon: Icon(Icons.search),
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
              ],
            ),
            const SizedBox(height: 16),

            Expanded(
              child: Row(
                children: [
                  // قائمة الأدوية المتاحة
                  Expanded(
                    flex: 2,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFE9ECEF)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: const BoxDecoration(
                              color: Color(0xFFF8F9FA),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8),
                                topRight: Radius.circular(8),
                              ),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.medication, size: 20),
                                const SizedBox(width: 8),
                                const Text(
                                  'الأدوية المتاحة',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                const Spacer(),
                                Text(
                                  _searchController.text.isNotEmpty
                                      ? '${_filteredProducts.length} نتيجة بحث'
                                      : '${_filteredProducts.length} دواء',
                                  style: const TextStyle(
                                    color: Color(0xFF6C757D),
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: _isLoading
                                ? const Center(
                                    child: CircularProgressIndicator())
                                : Column(
                                    children: [
                                      Expanded(
                                        child: ListView.builder(
                                          padding: const EdgeInsets.all(8),
                                          itemCount: _filteredProducts.length,
                                          itemBuilder: (context, index) {
                                            final product =
                                                _filteredProducts[index];
                                            final stock = product.stock ?? 0;

                                            return Container(
                                              margin: const EdgeInsets.only(
                                                  bottom: 8),
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                    color: const Color(
                                                        0xFFE9ECEF)),
                                                borderRadius:
                                                    BorderRadius.circular(8),
                                                color: Colors.white,
                                              ),
                                              child: ListTile(
                                                dense: true,
                                                leading: CircleAvatar(
                                                  backgroundColor:
                                                      const Color(0xFF3498DB),
                                                  child: Text(
                                                    product.code
                                                            ?.substring(0, 2) ??
                                                        'XX',
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 12,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                ),
                                                title: Text(
                                                  product.title ?? 'غير محدد',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    fontSize: 14,
                                                  ),
                                                ),
                                                subtitle: Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text(
                                                      'الكود: ${product.code}',
                                                      style: const TextStyle(
                                                          fontSize: 12),
                                                    ),
                                                    Container(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 6,
                                                          vertical: 2),
                                                      decoration: BoxDecoration(
                                                        color: stock > 0
                                                            ? Colors.green[50]
                                                            : Colors.red[50],
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(12),
                                                        border: Border.all(
                                                          color: stock > 0
                                                              ? Colors
                                                                  .green[300]!
                                                              : Colors
                                                                  .red[300]!,
                                                          width: 1,
                                                        ),
                                                      ),
                                                      child: Text(
                                                        stock > 0
                                                            ? 'متوفر: ${stock.toInt()} ${product.uniteName ?? 'وحدة'}'
                                                            : 'غير متوفر',
                                                        style: TextStyle(
                                                          fontSize: 11,
                                                          color: stock > 0
                                                              ? Colors
                                                                  .green[700]
                                                              : Colors.red[700],
                                                          fontWeight:
                                                              FontWeight.w600,
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                                trailing: Text(
                                                  '${product.price?.toStringAsFixed(2) ?? '0.00'} ر.س',
                                                  style: const TextStyle(
                                                    fontWeight: FontWeight.bold,
                                                    color: Color(0xFF3498DB),
                                                  ),
                                                ),
                                                onTap: () =>
                                                    _showBinSelector(product),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                      // زر تحميل المزيد أو مؤشر البحث
                                      if (_filteredProducts.isNotEmpty &&
                                          _searchController.text.isEmpty)
                                        Container(
                                          padding: const EdgeInsets.all(8),
                                          child: ElevatedButton.icon(
                                            onPressed: _isLoading
                                                ? null
                                                : _loadMoreProducts,
                                            icon: _isLoading
                                                ? const SizedBox(
                                                    width: 16,
                                                    height: 16,
                                                    child:
                                                        CircularProgressIndicator(
                                                            strokeWidth: 2,
                                                            color:
                                                                Colors.white))
                                                : const Icon(Icons.refresh,
                                                    size: 16),
                                            label: Text(_isLoading
                                                ? 'جاري التحميل...'
                                                : 'تحميل المزيد'),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  const Color(0xFF3498DB),
                                              foregroundColor: Colors.white,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      horizontal: 16,
                                                      vertical: 8),
                                            ),
                                          ),
                                        ),
                                      // رسالة إرشادية للبحث
                                      if (_filteredProducts.isEmpty &&
                                          !_isLoading &&
                                          _searchController.text.isNotEmpty)
                                        Container(
                                          padding: const EdgeInsets.all(16),
                                          child: Column(
                                            children: [
                                              Icon(Icons.search_off,
                                                  size: 48,
                                                  color: Colors.grey[400]),
                                              const SizedBox(height: 8),
                                              Text(
                                                'لا توجد نتائج لـ "${_searchController.text}"',
                                                style: TextStyle(
                                                    color: Colors.grey[600],
                                                    fontSize: 14),
                                                textAlign: TextAlign.center,
                                              ),
                                              const SizedBox(height: 4),
                                              Text(
                                                'جرب كلمات بحث مختلفة أو تحقق من الإملاء',
                                                style: TextStyle(
                                                    color: Colors.grey[500],
                                                    fontSize: 12),
                                                textAlign: TextAlign.center,
                                              ),
                                            ],
                                          ),
                                        ),
                                    ],
                                  ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // خريطة الخزانة
                  Expanded(
                    flex: 3,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFE9ECEF)),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12),
                            decoration: const BoxDecoration(
                              color: Color(0xFFF8F9FA),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8),
                                topRight: Radius.circular(8),
                              ),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.grid_view, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'خريطة الخزانة - ${widget.shelf.name}',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                ),
                              ],
                            ),
                          ),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: _buildShelfGrid(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShelfGrid() {
    return Column(
      children: [
        // مفتاح الألوان
        Container(
          padding: const EdgeInsets.all(8),
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            color: const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildLegendItem('فارغ', Colors.grey[300]!),
              _buildLegendItem('مشغول', Colors.green[300]!),
              _buildLegendItem('منتهي الصلاحية', Colors.red[300]!),
              _buildLegendItem('قريب الانتهاء', Colors.orange[300]!),
            ],
          ),
        ),

        // الشبكة
        Expanded(
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: widget.shelf.slotsPerLevel,
              crossAxisSpacing: 4,
              mainAxisSpacing: 4,
              childAspectRatio: 1,
            ),
            itemCount: widget.shelf.bins.length,
            itemBuilder: (context, index) {
              final bin = widget.shelf.bins[index];
              final level = widget.shelf.levels -
                  1 -
                  (index ~/ widget.shelf.slotsPerLevel);
              final slot = index % widget.shelf.slotsPerLevel;

              return GestureDetector(
                onTap: () => _showBinDetails(bin),
                child: Container(
                  decoration: BoxDecoration(
                    color: bin.statusColor,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey[400]!),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'L${level + 1}-S${slot + 1}',
                        style: const TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (bin.productName != null)
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.all(2),
                            child: Text(
                              bin.productName!,
                              style: const TextStyle(fontSize: 8),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      if (bin.quantity > 0)
                        Text(
                          '${bin.quantity}',
                          style: const TextStyle(
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 10),
        ),
      ],
    );
  }

  void _showBinSelector(ProductDTO product) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('اختر الخانة لـ ${product.title ?? 'المنتج'}'),
        content: SizedBox(
          width: 300,
          height: 200,
          child: GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: widget.shelf.slotsPerLevel,
              crossAxisSpacing: 4,
              mainAxisSpacing: 4,
            ),
            itemCount: widget.shelf.bins.length,
            itemBuilder: (context, index) {
              final bin = widget.shelf.bins[index];
              final level = widget.shelf.levels -
                  1 -
                  (index ~/ widget.shelf.slotsPerLevel);
              final slot = index % widget.shelf.slotsPerLevel;
              final isEmpty = bin.status == BinStatus.empty;

              return GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  _assignProductToBin(product, bin);
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: isEmpty ? Colors.green[100] : Colors.blue[100],
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: isEmpty ? Colors.green : Colors.blue,
                      width: 2,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'L${level + 1}-S${slot + 1}',
                        style: TextStyle(
                          fontSize: 9,
                          fontWeight: FontWeight.bold,
                          color: isEmpty ? Colors.green[700] : Colors.blue[700],
                        ),
                      ),
                      if (!isEmpty && bin.productName != null)
                        Expanded(
                          child: Text(
                            bin.productName!,
                            style: TextStyle(
                              fontSize: 7,
                              color: Colors.blue[600],
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      Text(
                        isEmpty ? 'فارغ' : 'مشغول',
                        style: TextStyle(
                          fontSize: 8,
                          color: isEmpty ? Colors.green[600] : Colors.blue[600],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  void _showBinDetails(Bin bin) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تفاصيل الخانة L${bin.level + 1}-S${bin.slot + 1}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (bin.productName != null) ...[
              Text('المنتج: ${bin.productName}'),
              Text('الكود: ${bin.productCode}'),
              Text('الكمية: ${bin.quantity} ${bin.unitName ?? 'وحدة'}'),
              if (bin.expiryDate != null)
                Text(
                    'تاريخ الانتهاء: ${bin.expiryDate!.toString().substring(0, 10)}'),
              if (bin.unitPrice != null)
                Text('السعر: ${bin.unitPrice!.toStringAsFixed(2)} ر.س'),
            ] else
              const Text('خانة فارغة'),
          ],
        ),
        actions: [
          if (bin.productName != null)
            TextButton(
              onPressed: () {
                setState(() {
                  bin.productId = null;
                  bin.productName = null;
                  bin.productCode = null;
                  bin.barcode = null;
                  bin.quantity = 0;
                  bin.expiryDate = null;
                  bin.unitPrice = null;
                  bin.unitName = null;
                  bin.status = BinStatus.empty;
                });
                Navigator.pop(context);
                final provider = Provider.of<WarehousePlannerProvider>(context,
                    listen: false);
                provider.updateEditorState(provider.editorState);
              },
              child: const Text('إفراغ الخانة'),
            ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}

class _AssignProductDialog extends StatefulWidget {
  final ProductDTO product;
  final Bin bin;
  final Shelf shelf;
  final Function(int quantity, DateTime? expiryDate) onConfirm;

  const _AssignProductDialog({
    required this.product,
    required this.bin,
    required this.shelf,
    required this.onConfirm,
  });

  @override
  State<_AssignProductDialog> createState() => __AssignProductDialogState();
}

class __AssignProductDialogState extends State<_AssignProductDialog> {
  final _quantityController = TextEditingController(text: '1');
  DateTime? _selectedExpiryDate;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('تعيين ${widget.product.title ?? 'المنتج'}'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('الخانة: L${widget.bin.level + 1}-S${widget.bin.slot + 1}'),
          const SizedBox(height: 16),
          TextField(
            controller: _quantityController,
            keyboardType: TextInputType.number,
            decoration: InputDecoration(
              labelText: 'الكمية',
              hintText: 'أدخل الكمية',
              suffixText: widget.product.uniteName ?? 'وحدة',
              border: const OutlineInputBorder(),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Text(
                  _selectedExpiryDate != null
                      ? 'تاريخ الانتهاء: ${_selectedExpiryDate!.toString().substring(0, 10)}'
                      : 'لم يتم تحديد تاريخ الانتهاء',
                ),
              ),
              TextButton(
                onPressed: () async {
                  final date = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now().add(const Duration(days: 365)),
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365 * 5)),
                  );
                  if (date != null) {
                    setState(() {
                      _selectedExpiryDate = date;
                    });
                  }
                },
                child: const Text('اختر التاريخ'),
              ),
            ],
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: () {
            final quantity = int.tryParse(_quantityController.text) ?? 0;
            if (quantity <= 0) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('يرجى إدخال كمية صحيحة')),
              );
              return;
            }

            Navigator.pop(context);
            widget.onConfirm(quantity, _selectedExpiryDate);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF3498DB),
            foregroundColor: Colors.white,
          ),
          child: const Text('تأكيد'),
        ),
      ],
    );
  }

  @override
  void dispose() {
    _quantityController.dispose();
    super.dispose();
  }
}
