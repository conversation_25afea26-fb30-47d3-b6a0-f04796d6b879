import 'package:flutter/material.dart';

import 'app_theme.dart';

class AppThemeDark extends AppTheme {
  static AppThemeDark? _instance;
  static AppThemeDark get instance {
    _instance ??= AppThemeDark._init();
    return _instance!;
  }

  AppThemeDark._init();

  @override
  ThemeData get theme => ThemeData.dark().copyWith(
        colorScheme: _buildColorScheme,
      );

  ColorScheme get _buildColorScheme => ColorScheme(
        brightness: Brightness.dark,
        primary: Colors.blue.shade200,
        onPrimary: Colors.black, // Text color on primary
        secondary: Colors.purple, // Secondary color
        onSecondary: Colors.white,
        error: Colors.red.shade300,
        onError: Colors.black,
        background: Colors.grey.shade900,
        onBackground: Colors.white,
        surface: Colors.grey.shade800, // Background of cards, dialogs, etc.
        onSurface: Colors.white, // Text on surface
      );
}
