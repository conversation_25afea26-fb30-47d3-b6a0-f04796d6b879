import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math.dart' as vector;
import 'package:path_provider/path_provider.dart';

import '../models/warehouse_planner/warehouse_layout.dart';
import '../models/warehouse_planner/editor_state.dart';

/// مقدم حالة تخطيط المستودع
class WarehousePlannerProvider extends ChangeNotifier {
  WarehouseLayout? _currentLayout;
  EditorState _editorState = EditorState();
  LayoutSettings _settings = LayoutSettings();

  // قوائم للبحث والفلترة
  List<Bin> _searchResults = [];
  String _searchQuery = '';
  List<BinStatus> _activeFilters = [];

  // Getters
  WarehouseLayout? get currentLayout => _currentLayout;
  EditorState get editorState => _editorState;
  LayoutSettings get settings => _settings;
  List<Bin> get searchResults => _searchResults;
  String get searchQuery => _searchQuery;
  List<BinStatus> get activeFilters => _activeFilters;

  bool get hasLayout => _currentLayout != null;

  /// إنشاء مستودع جديد
  void createNewWarehouse({
    required String name,
    String? description,
    double width = 2000.0,
    double height = 1500.0,
    int? linkedWarehouseId,
    String? linkedWarehouseName,
  }) {
    _currentLayout = WarehouseLayout(
      id: 'warehouse_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description ?? '',
      width: width,
      height: height,
      linkedWarehouseId: linkedWarehouseId,
      linkedWarehouseName: linkedWarehouseName,
    );

    _resetEditorState();
    notifyListeners();
  }

  /// تحميل مستودع من ملف
  Future<bool> loadWarehouse(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        debugPrint('الملف غير موجود: $filePath');
        return false;
      }

      final jsonString = await file.readAsString();
      final json = jsonDecode(jsonString);

      _currentLayout = WarehouseLayout.fromJson(json);
      _resetEditorState();
      debugPrint('تم تحميل المستودع: ${_currentLayout!.name}');
      notifyListeners();

      return true;
    } catch (e) {
      debugPrint('خطأ في تحميل المستودع: $e');
      return false;
    }
  }

  /// الحصول على قائمة الملفات المحفوظة
  Future<List<FileSystemEntity>> getSavedWarehouses() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final warehousesDir = Directory('${directory.path}/WarehousePlanner');

      if (!await warehousesDir.exists()) {
        return [];
      }

      final files = await warehousesDir
          .list()
          .where((entity) => entity.path.endsWith('.json'))
          .toList();

      return files;
    } catch (e) {
      debugPrint('خطأ في قراءة الملفات: $e');
      return [];
    }
  }

  /// الحصول على مسار مجلد الحفظ
  Future<String> getSaveDirectory() async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/WarehousePlanner';
  }

  /// حفظ المستودع الحالي
  Future<bool> saveWarehouse([String? filePath]) async {
    if (_currentLayout == null) return false;

    try {
      filePath ??= await _getDefaultSavePath();
      final file = File(filePath);

      _currentLayout!.updatedAt = DateTime.now();
      final jsonString = jsonEncode(_currentLayout!.toJson());

      await file.writeAsString(jsonString);
      return true;
    } catch (e) {
      debugPrint('خطأ في حفظ المستودع: $e');
      return false;
    }
  }

  Future<String> _getDefaultSavePath() async {
    final directory = await getApplicationDocumentsDirectory();
    final warehousesDir = Directory('${directory.path}/WarehousePlanner');
    if (!await warehousesDir.exists()) {
      await warehousesDir.create(recursive: true);
    }

    // اسم ملف أوضح وأبسط
    final cleanName = _currentLayout!.name.replaceAll(RegExp(r'[^\w\s-]'), '');
    final fileName =
        '${cleanName}_${DateTime.now().toString().substring(0, 19).replaceAll(':', '-')}.json';
    final fullPath = '${warehousesDir.path}/$fileName';

    debugPrint('حفظ الملف في: $fullPath');
    return fullPath;
  }

  /// تحديث حالة المحرر
  void updateEditorState(EditorState newState) {
    _editorState = newState;
    notifyListeners();
  }

  /// تغيير وضع المحرر
  void setEditMode(EditMode mode) {
    _editorState = _editorState.copyWith(mode: mode);

    // إعادة تعيين حالات الأدوات
    if (mode != EditMode.drawWall) {
      _editorState.wallDrawing.cancelDrawing();
    }
    if (mode != EditMode.addEntrance) {
      _editorState = _editorState.copyWith(
        entranceAdding: _editorState.entranceAdding.copyWith(
          selectedWallId: null,
          previewPosition: null,
        ),
      );
    }

    notifyListeners();
  }

  /// تحديد كائن
  void selectObject(String? objectId, ObjectType? objectType) {
    _editorState = _editorState.copyWith(
      selectedObjectId: objectId,
      selectedObjectType: objectType,
    );
    notifyListeners();
  }

  /// === إدارة الجدران ===

  /// بدء رسم جدار جديد
  void startDrawingWall(vector.Vector2 startPoint) {
    final snappedPoint = _editorState.snapToGridPoint(startPoint);
    _editorState.wallDrawing.startDrawing(snappedPoint);
    notifyListeners();
  }

  /// بدء رسم جدار جديد
  void startWallDrawing(vector.Vector2 startPoint) {
    final snappedPoint = _editorState.snapToGridPoint(startPoint);
    _editorState.wallDrawing.startDrawing(snappedPoint);
    print('🎯 بدء رسم جدار في: ${snappedPoint.x}, ${snappedPoint.y}');
    notifyListeners();
  }

  /// إضافة نقطة للجدار الحالي
  void addWallPoint(vector.Vector2 point) {
    final snappedPoint = _editorState.snapToGridPoint(point);
    _editorState.wallDrawing.addPoint(snappedPoint);
    print(
        '➕ إضافة نقطة: ${snappedPoint.x}, ${snappedPoint.y} - إجمالي النقاط: ${_editorState.wallDrawing.currentPoints.length}');
    notifyListeners();
  }

  /// إنهاء رسم الجدار القديم
  void finishDrawingWall() {
    print(
        '🏁 محاولة إنهاء رسم الجدار - عدد النقاط: ${_editorState.wallDrawing.currentPoints.length}');

    if (_editorState.wallDrawing.isDrawing &&
        _editorState.wallDrawing.currentPoints.length >= 2) {
      final wall = Wall(
        id: 'wall_${DateTime.now().millisecondsSinceEpoch}',
        points: List.from(_editorState.wallDrawing.currentPoints),
        thickness: 20.0,
      );

      _currentLayout!.walls.add(wall);
      print(
          '✅ تم إضافة جدار جديد! عدد الجدران: ${_currentLayout!.walls.length}');
      _editorState.wallDrawing.finishDrawing();
      notifyListeners();
    } else {
      print('❌ لا يمكن إنهاء الجدار - نقاط غير كافية أو لا يوجد رسم نشط');
    }
  }

  /// === إدارة المداخل ===

  /// تحديد جدار لإضافة مدخل
  void selectWallForEntrance(String wallId) {
    _editorState = _editorState.copyWith(
      entranceAdding: _editorState.entranceAdding.copyWith(
        selectedWallId: wallId,
      ),
    );
    notifyListeners();
  }

  /// إضافة مدخل
  void addEntrance(double distanceOnWall) {
    if (_currentLayout == null) return;

    // العثور على الجدار المحدد
    String? wallId = _editorState.selectedObjectId;
    if (wallId == null || _editorState.selectedObjectType != ObjectType.wall) {
      debugPrint('لا يوجد جدار محدد لإضافة المدخل');
      return;
    }

    final wall = _currentLayout!.walls.firstWhere(
      (w) => w.id == wallId,
      orElse: () => _currentLayout!.walls.first,
    );

    // فحص عدم تداخل المدخل مع مداخل أخرى
    final entranceWidth = _editorState.entranceAdding.entranceWidth;
    final startDistance = distanceOnWall - entranceWidth / 2;
    final endDistance = distanceOnWall + entranceWidth / 2;

    final existingEntrances =
        _currentLayout!.entrances.where((e) => e.wallId == wall.id).toList();

    for (final existing in existingEntrances) {
      if (!(endDistance <= existing.startDistance ||
          startDistance >= existing.endDistance)) {
        // تداخل مع مدخل موجود
        return;
      }
    }

    // فحص أن المدخل يقع ضمن طول الجدار
    if (startDistance < 0 || endDistance > wall.totalLength) {
      return;
    }

    final entrance = Entrance(
      id: 'entrance_${DateTime.now().millisecondsSinceEpoch}',
      wallId: wall.id,
      startDistance: startDistance,
      width: entranceWidth,
      name: 'مدخل ${_currentLayout!.entrances.length + 1}',
    );

    _currentLayout!.entrances.add(entrance);
    notifyListeners();
  }

  /// حذف مدخل
  void deleteEntrance(String entranceId) {
    if (_currentLayout == null) return;

    _currentLayout!.entrances.removeWhere((e) => e.id == entranceId);

    if (_editorState.selectedObjectId == entranceId) {
      selectObject(null, null);
    }

    notifyListeners();
  }

  /// === إدارة الخزائن ===

  /// تحديد قالب خزانة للوضع
  void selectShelfTemplate(String templateId) {
    _editorState = _editorState.copyWith(
      shelfPlacing: _editorState.shelfPlacing.copyWith(
        selectedTemplateId: templateId,
      ),
    );
    notifyListeners();
  }

  /// تحديث معاينة موضع الخزانة
  void updateShelfPreview(vector.Vector2 position, [double? rotation]) {
    final snappedPosition = _editorState.snapToGridPoint(position);
    _editorState = _editorState.copyWith(
      shelfPlacing: _editorState.shelfPlacing.copyWith(
        previewPosition: snappedPosition,
        previewRotation: rotation ?? _editorState.shelfPlacing.previewRotation,
      ),
    );
    notifyListeners();
  }

  /// إضافة خزانة
  void addShelf(vector.Vector2 position, [String? templateId]) {
    if (_currentLayout == null) return;

    templateId ??= _editorState.shelfPlacing.selectedTemplateId;
    if (templateId == null) return;

    // البحث عن القالب
    final template = ShelfTemplate.predefined.firstWhere(
      (t) => t.name == templateId,
      orElse: () => ShelfTemplate.predefined.first,
    );

    final snappedPosition = _editorState.snapToGridPoint(position);

    final shelf = Shelf(
      id: 'shelf_${DateTime.now().millisecondsSinceEpoch}',
      name: template.name,
      position: snappedPosition,
      width: template.width,
      depth: template.depth,
      height: template.height,
      rotation: _editorState.shelfPlacing.previewRotation,
      type: template.type,
      color: template.color,
      levels: template.levels,
      slotsPerLevel: template.slotsPerLevel,
    );

    // فحص التصادم
    if (_checkShelfCollision(shelf)) {
      return; // لا يمكن وضع الخزانة هنا
    }

    _currentLayout!.shelves.add(shelf);
    notifyListeners();
  }

  /// فحص تصادم الخزانة
  bool _checkShelfCollision(Shelf newShelf) {
    if (_currentLayout == null) return false;

    // فحص التصادم مع الخزائن الأخرى
    for (final existingShelf in _currentLayout!.shelves) {
      if (newShelf.collidesWith(existingShelf)) {
        return true;
      }
    }

    // فحص التصادم مع الجدران (اختياري - يمكن تطبيقه لاحقاً)

    return false;
  }

  /// تحريك خزانة
  void moveShelf(String shelfId, vector.Vector2 newPosition) {
    if (_currentLayout == null) return;

    final shelfIndex =
        _currentLayout!.shelves.indexWhere((s) => s.id == shelfId);
    if (shelfIndex == -1) return;

    final shelf = _currentLayout!.shelves[shelfIndex];
    final snappedPosition = _editorState.snapToGridPoint(newPosition);

    // إنشاء خزانة جديدة بالموضع الجديد للفحص
    final tempShelf = Shelf(
      id: shelf.id,
      name: shelf.name,
      position: snappedPosition,
      width: shelf.width,
      depth: shelf.depth,
      height: shelf.height,
      rotation: shelf.rotation,
      type: shelf.type,
      color: shelf.color,
      levels: shelf.levels,
      slotsPerLevel: shelf.slotsPerLevel,
    );

    // فحص التصادم (مع استثناء الخزانة الحالية)
    final otherShelves = _currentLayout!.shelves.where((s) => s.id != shelfId);
    for (final other in otherShelves) {
      if (tempShelf.collidesWith(other)) {
        return; // تصادم!
      }
    }

    // تحديث موضع الخزانة
    final updatedShelf = Shelf(
      id: shelf.id,
      name: shelf.name,
      position: snappedPosition,
      width: shelf.width,
      depth: shelf.depth,
      height: shelf.height,
      rotation: shelf.rotation,
      type: shelf.type,
      color: shelf.color,
      levels: shelf.levels,
      slotsPerLevel: shelf.slotsPerLevel,
      bins: shelf.bins,
    );

    _currentLayout!.shelves[shelfIndex] = updatedShelf;
    notifyListeners();
  }

  /// دوران خزانة
  void rotateShelf(String shelfId, [double? newRotation]) {
    if (_currentLayout == null) return;

    final shelfIndex =
        _currentLayout!.shelves.indexWhere((s) => s.id == shelfId);
    if (shelfIndex == -1) return;

    final shelf = _currentLayout!.shelves[shelfIndex];
    final rotation = newRotation ?? (shelf.rotation + 90) % 360;

    final updatedShelf = Shelf(
      id: shelf.id,
      name: shelf.name,
      position: shelf.position,
      width: shelf.width,
      depth: shelf.depth,
      height: shelf.height,
      rotation: rotation,
      type: shelf.type,
      color: shelf.color,
      levels: shelf.levels,
      slotsPerLevel: shelf.slotsPerLevel,
      bins: shelf.bins,
    );

    _currentLayout!.shelves[shelfIndex] = updatedShelf;
    notifyListeners();
  }

  /// تحديث خصائص الخزانة
  void updateShelfProperties(
    String shelfId, {
    String? name,
    int? levels,
    int? slotsPerLevel,
    double? width,
    double? depth,
    double? height,
  }) {
    if (_currentLayout == null) return;

    final shelfIndex =
        _currentLayout!.shelves.indexWhere((s) => s.id == shelfId);
    if (shelfIndex == -1) return;

    final shelf = _currentLayout!.shelves[shelfIndex];

    // إذا تغير عدد المستويات أو الخانات، أعد إنشاء الخانات
    List<Bin> newBins = shelf.bins;
    if (levels != null && levels != shelf.levels ||
        slotsPerLevel != null && slotsPerLevel != shelf.slotsPerLevel) {
      final newLevels = levels ?? shelf.levels;
      final newSlots = slotsPerLevel ?? shelf.slotsPerLevel;

      newBins = [];
      for (int level = 0; level < newLevels; level++) {
        for (int slot = 0; slot < newSlots; slot++) {
          newBins.add(Bin(
            id: '${shelfId}_${level}_${slot}',
            shelfId: shelfId,
            level: level,
            slot: slot,
            position: vector.Vector2(slot * 20.0, level * 20.0),
          ));
        }
      }
    }

    final updatedShelf = Shelf(
      id: shelf.id,
      name: name ?? shelf.name,
      type: shelf.type,
      position: shelf.position,
      rotation: shelf.rotation,
      width: width ?? shelf.width,
      depth: depth ?? shelf.depth,
      height: height ?? shelf.height,
      levels: levels ?? shelf.levels,
      slotsPerLevel: slotsPerLevel ?? shelf.slotsPerLevel,
      color: shelf.color,
      bins: newBins,
    );

    _currentLayout!.shelves[shelfIndex] = updatedShelf;
    print(
        '📦 تم تحديث خصائص الخزانة: $shelfId - مستويات: ${levels ?? shelf.levels}, خانات: ${slotsPerLevel ?? shelf.slotsPerLevel}');
    notifyListeners();
  }

  /// حذف خزانة
  void deleteShelf(String shelfId) {
    if (_currentLayout == null) return;

    _currentLayout!.shelves.removeWhere((s) => s.id == shelfId);

    if (_editorState.selectedObjectId == shelfId) {
      selectObject(null, null);
    }

    notifyListeners();
  }

  /// === إدارة الخانات والأدوية ===

  /// تحديث محتوى خانة
  void updateBin(
    String shelfId,
    String binId, {
    int? productId,
    String? productName,
    String? barcode,
    int? quantity,
    DateTime? expiryDate,
  }) {
    if (_currentLayout == null) return;

    final shelfIndex =
        _currentLayout!.shelves.indexWhere((s) => s.id == shelfId);
    if (shelfIndex == -1) return;

    final shelf = _currentLayout!.shelves[shelfIndex];
    final binIndex = shelf.bins.indexWhere((b) => b.id == binId);
    if (binIndex == -1) return;

    final bin = shelf.bins[binIndex];
    final updatedBin = Bin(
      id: bin.id,
      shelfId: bin.shelfId,
      level: bin.level,
      slot: bin.slot,
      position: bin.position,
      productId: productId ?? bin.productId,
      productName: productName ?? bin.productName,
      barcode: barcode ?? bin.barcode,
      quantity: quantity ?? bin.quantity,
      expiryDate: expiryDate ?? bin.expiryDate,
      status: _calculateBinStatus(
        quantity ?? bin.quantity,
        expiryDate ?? bin.expiryDate,
      ),
    );

    shelf.bins[binIndex] = updatedBin;
    notifyListeners();
  }

  BinStatus _calculateBinStatus(int quantity, DateTime? expiryDate) {
    if (quantity == 0) return BinStatus.empty;

    if (expiryDate != null) {
      final now = DateTime.now();
      if (expiryDate.isBefore(now)) {
        return BinStatus.expired;
      }

      final daysUntilExpiry = expiryDate.difference(now).inDays;
      if (daysUntilExpiry <= 30) {
        return BinStatus.expiringSoon;
      }
    }

    // فحص المخزون القليل (يمكن تخصيصه)
    if (quantity < 10) {
      return BinStatus.lowStock;
    }

    return BinStatus.normal;
  }

  /// === البحث والفلترة ===

  /// البحث عن دواء
  void searchMedicine(String query) {
    _searchQuery = query;
    _updateSearchResults();
  }

  /// تطبيق فلاتر
  void applyFilters(List<BinStatus> filters) {
    _activeFilters = filters;
    _updateSearchResults();
  }

  void _updateSearchResults() {
    _searchResults.clear();

    if (_currentLayout == null) {
      notifyListeners();
      return;
    }

    for (final shelf in _currentLayout!.shelves) {
      for (final bin in shelf.bins) {
        bool matches = true;

        // فلترة حسب البحث
        if (_searchQuery.isNotEmpty) {
          final query = _searchQuery.toLowerCase();
          final productName = bin.productName?.toLowerCase() ?? '';
          final barcode = bin.barcode?.toLowerCase() ?? '';

          if (!productName.contains(query) && !barcode.contains(query)) {
            matches = false;
          }
        }

        // فلترة حسب الحالة
        if (_activeFilters.isNotEmpty) {
          if (!_activeFilters.contains(bin.status)) {
            matches = false;
          }
        }

        if (matches) {
          _searchResults.add(bin);
        }
      }
    }

    notifyListeners();
  }

  /// تنظيف البحث
  void clearSearch() {
    _searchQuery = '';
    _activeFilters.clear();
    _searchResults.clear();
    notifyListeners();
  }

  /// === أدوات مساعدة ===

  void _resetEditorState() {
    _editorState = EditorState();
    _clearSearch();
  }

  void _clearSearch() {
    _searchQuery = '';
    _activeFilters.clear();
    _searchResults.clear();
  }

  /// الحصول على خزانة بالمعرف
  Shelf? getShelfById(String shelfId) {
    return _currentLayout?.shelves.firstWhere((s) => s.id == shelfId);
  }

  /// الحصول على جدار بالمعرف
  Wall? getWallById(String wallId) {
    return _currentLayout?.walls.firstWhere((w) => w.id == wallId);
  }

  /// إحصائيات المستودع
  Map<String, dynamic> getWarehouseStats() {
    if (_currentLayout == null) {
      return {
        'totalShelves': 0,
        'totalBins': 0,
        'occupiedBins': 0,
        'expiredItems': 0,
        'expiringSoon': 0,
        'lowStockItems': 0,
      };
    }

    int totalBins = 0;
    int occupiedBins = 0;
    int expiredItems = 0;
    int expiringSoon = 0;
    int lowStockItems = 0;

    for (final shelf in _currentLayout!.shelves) {
      totalBins += shelf.bins.length;

      for (final bin in shelf.bins) {
        if (bin.status != BinStatus.empty) occupiedBins++;
        if (bin.status == BinStatus.expired) expiredItems++;
        if (bin.status == BinStatus.expiringSoon) expiringSoon++;
        if (bin.status == BinStatus.lowStock) lowStockItems++;
      }
    }

    return {
      'totalShelves': _currentLayout!.shelves.length,
      'totalBins': totalBins,
      'occupiedBins': occupiedBins,
      'expiredItems': expiredItems,
      'expiringSoon': expiringSoon,
      'lowStockItems': lowStockItems,
    };
  }

  /// حذف جدار محدد
  void deleteWall(String wallId) {
    if (_currentLayout == null) return;

    final wallIndex = _currentLayout!.walls.indexWhere((w) => w.id == wallId);
    if (wallIndex == -1) return;

    // إزالة المداخل المرتبطة بهذا الجدار أولاً
    _currentLayout!.entrances
        .removeWhere((entrance) => entrance.wallId == wallId);

    // إزالة الجدار
    _currentLayout!.walls.removeAt(wallIndex);

    // إلغاء التحديد إذا كان هذا الجدار محدد
    if (_editorState.selectedObjectId == wallId) {
      updateEditorState(_editorState.copyWith(
        selectedObjectId: null,
        selectedObjectType: null,
      ));
    }

    notifyListeners();
  }

  /// تحديث خصائص جدار
  void updateWallProperties(
    String wallId, {
    double? thickness,
    Color? color,
  }) {
    if (_currentLayout == null) return;

    final wallIndex = _currentLayout!.walls.indexWhere((w) => w.id == wallId);
    if (wallIndex == -1) return;

    final wall = _currentLayout!.walls[wallIndex];

    _currentLayout!.walls[wallIndex] = Wall(
      id: wall.id,
      points: wall.points,
      thickness: thickness ?? wall.thickness,
      color: color ?? wall.color,
    );

    notifyListeners();
  }
}
