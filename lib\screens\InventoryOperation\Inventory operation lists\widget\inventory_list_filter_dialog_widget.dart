import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:provider/provider.dart';

class InventoryListFilterDialogWidget extends StatefulWidget {
  InventoryListFilterDialogWidget({super.key, required this.onSearch});



  final Function({DateTime? fromDate, DateTime? toDate}) onSearch;
  @override
  State<InventoryListFilterDialogWidget> createState() =>
      _InventoryListFilterDialogWidgetState();
}

class _InventoryListFilterDialogWidgetState
    extends State<InventoryListFilterDialogWidget> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
 DateTime? _fromDate;
  DateTime? _toDate;
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
   var controller = Provider.of<InventoryOperationController>(context);
var filterData = controller.filterModel;


    return FadeTransition(
      opacity: _fadeAnimation,
      child: Dialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 10),
        backgroundColor: Colors.transparent,
        elevation: 0,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            constraints: BoxConstraints(
              maxWidth: context.width,
              maxHeight: context.height * 0.9,
            ),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.15),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildHeader(context),
                Flexible(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                      _buildDateRangeSection(),
                         const SizedBox(height: 30),
                      _buildActionButtons(context, filterData),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
Widget _buildDateRangeSection() {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        T("Date Range"),
        style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
      ),
      const SizedBox(height: 10),
      Row(
        children: [
          Expanded(
            child: _buildDatePicker(
              label: T("From Date"),
              selectedDate: _fromDate,
              onTap: () async {
                DateTime? picked = await showDatePicker(
                  context: context,
                  initialDate: _fromDate ?? DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                );
                if (picked != null) {
                  setState(() {
                    _fromDate = picked;
                  });
                }
              },
            ),
          ),
          const SizedBox(width: 10),
         
        ],
      ),
    ],
  );
}

Widget _buildDatePicker({
  required String label,
  required DateTime? selectedDate,
  required VoidCallback onTap,
}) {
  return GestureDetector(
    onTap: onTap,
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 14),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Icon(Icons.date_range, color: context.newPrimaryColor, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              selectedDate != null
                  ? "${selectedDate.year}-${selectedDate.month.toString().padLeft(2, '0')}-${selectedDate.day.toString().padLeft(2, '0')}"
                  : label,
              style: TextStyle(
                color: selectedDate != null
                    ? context.newTextColor
                    : Colors.grey.shade600,
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.newPrimaryColor,
            context.newPrimaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.filter_list,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  T("Filter Inventories"),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  T("Refine your search results"),
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white,
              size: 24,
            ),
          ),
        ],
      ),
    );
  }

  
  Widget _buildInvoiceCodeSection(BuildContext context, dynamic filterData) {
    return _buildSection(
      title: T("Invoice Number"),
      icon: Icons.numbers,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: CommonTextField(
          initialValue: filterData.invoiceCode,
          floatingLabelBehavior: FloatingLabelBehavior.never,
          label: T('Enter invoice number'),
          onChanged: (value) {
            filterData.invoiceCode = value;
          },
        ),
      ),
    );
  }

  Widget _buildCustomerSection(BuildContext context, dynamic filterData) {
    return _buildSection(
      title: T("Customer"),
      icon: Icons.person,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
        ),
        child: MyComboBox(
          selectedValue: filterData.customerId,
          caption: filterData.customerName ?? T('Select Customer'),
          height: 55,
          width: double.infinity,
          onSelect: (int id, String name) {
            filterData.customerId = id;
            filterData.customerName = name;
            setState(() {});
          },
          modalTitle: T('Select Customer'),
          data: Provider.of<CustomerController>(context, listen: false)
              .customers
              .map(
                (e) => ComboBoxDataModel(id: e.iD ?? 0, name: e.name ?? ""),
              )
              .toList(),
          isShowLabel: false,
          labelText: "",
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: context.newPrimaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: context.newPrimaryColor,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: context.newTextColor,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        child,
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context, dynamic filterData) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _clearFilters(filterData),
            icon: const Icon(Icons.clear_all),
            label: Text(T("Clear All")),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: Colors.grey.shade400),
              foregroundColor: Colors.grey.shade700,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: ElevatedButton.icon(
         onPressed: () {
  // Apply the new filter values
  final controller = Provider.of<InventoryOperationController>(context, listen: false);
  controller.filterModel.fromDate = _fromDate.toString();
  controller.filterModel.toDate = _toDate.toString();

  // Refresh with new filters
  controller.getInventories(resetAndRefresh: true);

  Navigator.of(context).pop();
  widget.onSearch(fromDate: _fromDate, toDate: _toDate);
},


            icon: const Icon(Icons.search),
            label: Text(T("Apply Filters")),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.newPrimaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _clearFilters(dynamic filterData) {
    setState(() {
_fromDate = null;
      _toDate = null;
      filterData.invoiceType = null;
      filterData.invoiceTypeName = null;
      filterData.invoiceCode = null;
      filterData.customerId = null;
      filterData.customerName = null;
    });
  }
}
