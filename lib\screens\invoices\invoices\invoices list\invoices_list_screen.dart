import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/invoices%20list/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/invoices%20list/widgets/invoice_lsit_filter_dialog_widget.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart' as ptr;

class InvoicesListScreenScreen extends StatefulWidget {
  const InvoicesListScreenScreen({super.key});

  @override
  State<InvoicesListScreenScreen> createState() =>
      _InvoicesListScreenScreenState();
}

class _InvoicesListScreenScreenState extends State<InvoicesListScreenScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final ptr.RefreshController _refreshController =
      ptr.RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();
  late List<ComboBoxDataModel> _filterTypes;
  late ComboBoxDataModel _selectedFilter;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _filterTypes = [
      ComboBoxDataModel(id: 3, name: T("Sales Invoices")),
      ComboBoxDataModel(id: 4, name: T("Return Invoices")),
      ComboBoxDataModel(id: 0, name: T("Sales Orders")),
    ];
    _selectedFilter = _filterTypes[0];
    _onRefresh();
  }

  //==================================================//
  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  //==================================================//
  void _onRefresh() async {
    await Provider.of<InvoiceController>(context, listen: false)
        .getInvoices(resetAndRefresh: true);
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  //==================================================//
  void _onLoading() async {
    await Provider.of<InvoiceController>(context, listen: false).getInvoices();
    // if failed,use loadFailed(),if no data return,use LoadNodata()
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    var data = Provider.of<InvoiceController>(context).invoices;
    var status = AppController.isThereConnection;

    return ApplicationLayout(
      child: Column(
        children: [
          // HeaderText(

          CommonHeader(
            icon: Icons.receipt_long,
            title: T("All invoices"),
          ),
          // Search button header
          _buildSearchHeader(context),

          Container(
            height: 45,
            margin: const EdgeInsets.symmetric(vertical: 12),
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _filterTypes.length,
              separatorBuilder: (_, __) => const SizedBox(width: 8),
              itemBuilder: (context, index) {
                final type = _filterTypes[index];
                final isSelected = _selectedFilter.id == type.id;

                return _buildFilterButton(
                  label: type.name,
                  icon: Icons.filter_alt_outlined,
                  isSelected: isSelected,
                  onTap: () {
                    setState(() {
                      _selectedFilter = type;
                    });

                    Provider.of<InvoiceController>(context, listen: false)
                        .getInvoices(
                      resetAndRefresh: true,
                      invoiceType: type.id, // 👈 Make sure this is passed
                    );
                  },
                );
              },
            ),
          ),

          // Invoice list
          Expanded(
            child: status == true
                ? Padding(
                    padding: const EdgeInsets.all(10),
                    child: _buildSmartRefresherWithMouseSupport(data),
                  )
                : Container(
                    margin: const EdgeInsets.only(top: 50),
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.wifi_off,
                          size: 80,
                          color: context.newTextColor.withOpacity(0.3),
                        ),
                        const SizedBox(height: 20),
                        Text(
                          T("No Internet Connection"),
                          style: TextStyle(
                            color: context.newTextColor,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 10),
                        Text(
                          T("Please check your connection and try again"),
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: context.newTextColor.withOpacity(0.7),
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 30),
                        ElevatedButton.icon(
                          onPressed: () {
                            _onRefresh();
                          },
                          icon: const Icon(Icons.refresh),
                          label: Text(T("Retry")),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.newPrimaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 25, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmartRefresherWithMouseSupport(List<dynamic> data) {
    // For desktop platforms (Windows, macOS, Linux), use SmartRefresher with enhanced scrolling
    if (kIsWeb ||
        defaultTargetPlatform == TargetPlatform.windows ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.linux) {
      return Scrollbar(
        controller: _scrollController,
        thumbVisibility: true,
        trackVisibility: true,
        child: ptr.SmartRefresher(
          enablePullDown: true,
          enablePullUp: true,
          physics: const BouncingScrollPhysics(), // This enables mouse wheel
          header: ptr.WaterDropHeader(
            waterDropColor: context.newPrimaryColor,
          ),
          footer: ptr.CustomFooter(
            builder: (context, mode) {
              Widget body;
              if (mode == ptr.LoadStatus.loading) {
                body = const CupertinoActivityIndicator();
              } else if (mode == ptr.LoadStatus.idle) {
                body = Text(
                  T("Pull up to load more"),
                  style: TextStyle(color: context.newTextColor),
                );
              } else if (mode == ptr.LoadStatus.failed) {
                body = Text(
                  T("Load Failed! Click to retry!"),
                  style: TextStyle(color: context.newTextColor),
                );
              } else if (mode == ptr.LoadStatus.canLoading) {
                body = Text(
                  T("Release to load more"),
                  style: TextStyle(color: context.newTextColor),
                );
              } else {
                body = Text(
                  T("No more invoices"),
                  style: TextStyle(color: context.newTextColor),
                );
              }
              return SizedBox(
                height: 55.0,
                child: Center(child: body),
              );
            },
          ),
          controller: _refreshController,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          child: data.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.receipt_long,
                        size: 80,
                        color: context.newTextColor.withOpacity(0.3),
                      ),
                      const SizedBox(height: 15),
                      Text(
                        T("No invoices found"),
                        style: TextStyle(
                          color: context.newTextColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 10),
                      Text(
                        T("Try adjusting your search parameters"),
                        style: TextStyle(
                          color: context.newTextColor.withOpacity(0.7),
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  controller: _scrollController,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: data.length + 1, // +1 for Load More button
                  itemBuilder: (context, index) {
                    // If it's the last item, show Load More button
                    if (index == data.length) {
                      return Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        child: ElevatedButton.icon(
                          onPressed: _onLoading,
                          icon: const Icon(Icons.refresh, size: 20),
                          label: Text(T("Load More")),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: context.newPrimaryColor,
                            foregroundColor: Colors.white,
                            elevation: 2,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                        ),
                      );
                    }

                    // Regular invoice item
                    return InvoiceListItemWidget(
                      onDelete: () {
                        _onRefresh();
                      },
                      model: data[index],
                    );
                  },
                ),
        ),
      );
    }

    // For mobile platforms, use SmartRefresher as usual
    return Scrollbar(
      controller: _scrollController,
      thumbVisibility: true,
      trackVisibility: true,
      child: ptr.SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        physics: const BouncingScrollPhysics(),
        header: ptr.WaterDropHeader(
          waterDropColor: context.newPrimaryColor,
        ),
        footer: ptr.CustomFooter(
          builder: (context, mode) {
            Widget body;
            if (mode == ptr.LoadStatus.loading) {
              body = const CupertinoActivityIndicator();
            } else if (mode == ptr.LoadStatus.idle) {
              body = Text(
                T("Pull up to load more"),
                style: TextStyle(color: context.newTextColor),
              );
            } else if (mode == ptr.LoadStatus.failed) {
              body = Text(
                T("Load Failed! Click to retry!"),
                style: TextStyle(color: context.newTextColor),
              );
            } else if (mode == ptr.LoadStatus.canLoading) {
              body = Text(
                T("Release to load more"),
                style: TextStyle(color: context.newTextColor),
              );
            } else {
              body = Text(
                T("No more invoices"),
                style: TextStyle(color: context.newTextColor),
              );
            }
            return SizedBox(
              height: 55.0,
              child: Center(child: body),
            );
          },
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: data.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.receipt_long,
                      size: 80,
                      color: context.newTextColor.withOpacity(0.3),
                    ),
                    const SizedBox(height: 15),
                    Text(
                      T("No invoices found"),
                      style: TextStyle(
                        color: context.newTextColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      T("Try adjusting your search parameters"),
                      style: TextStyle(
                        color: context.newTextColor.withOpacity(0.7),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              )
            : ListView.builder(
                shrinkWrap: true,
                itemCount: data.length,
                itemBuilder: (context, index) {
                  return InvoiceListItemWidget(
                    onDelete: () {
                      _onRefresh();
                    },
                    model: data[index],
                  );
                },
              ),
      ),
    );
  }

  Widget _buildSearchHeader(BuildContext context) {
    return Container(
      width: context.width - 20,
      margin: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: context.newPrimaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              Icons.filter_list,
              color: context.newPrimaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  T("Filter & Search"),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: context.newTextColor,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  T("Find invoices by date, type, customer or number"),
                  style: TextStyle(
                    fontSize: 12,
                    color: context.newTextColor.withOpacity(0.6),
                  ),
                ),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: context.newPrimaryColor,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) =>
                        InvoiceListFilterDialogWidget(
                      onSearch: () {
                        _onRefresh();
                        setState(() {});
                      },
                    ),
                  );
                },
                borderRadius: BorderRadius.circular(10),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.search,
                        color: Colors.white,
                        size: 18,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        T("Filter"),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? context.newPrimaryColor
              : context.newPrimaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? context.newPrimaryColor
                : context.newPrimaryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : context.newPrimaryColor,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : context.newPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
