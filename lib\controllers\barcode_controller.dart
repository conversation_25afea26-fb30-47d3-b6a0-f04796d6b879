import 'dart:io';

import 'package:flutter/material.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:shared_preferences/shared_preferences.dart';

class BarcodeController extends ChangeNotifier {
  String? _selectedDevice;
  bool _isExternalBarcodeReaderEnabled = false;
  List<String> _availableDevices = [];
  bool _isScanning = false;

  bool get isExternalBarcodeReaderEnabled => _isExternalBarcodeReaderEnabled;
  String? get selectedDevice => _selectedDevice;
  List<String> get availableDevices => _availableDevices;
  bool get isScanning => _isScanning;

  // Initialize controller by loading saved settings
  Future<void> initialize() async {
    try {
      await loadSettings();
    } catch (e) {}
  }

  // Load settings from SharedPreferences
  Future<void> loadSettings() async {
    final prefs = await SharedPreferences.getInstance();
    _selectedDevice = prefs.getString('selectedBarcodeDevice');
    _isExternalBarcodeReaderEnabled =
        prefs.getBool('isBarcodeReaderEnabled') ?? false;
    notifyListeners();
  }

  // Save settings to SharedPreferences
  Future<void> saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('selectedBarcodeDevice', _selectedDevice ?? '');
      await prefs.setBool(
          'isBarcodeReaderEnabled', _isExternalBarcodeReaderEnabled);
      successSnackBar(message: 'Barcode settings saved successfully');
    } catch (e) {
      errorSnackBar(message: 'Failed to save barcode settings');
    }
  }

  // Check if an external barcode reader should be used
  bool shouldUseExternalReader() {
    return _isExternalBarcodeReaderEnabled &&
        _selectedDevice != null &&
        _selectedDevice!.isNotEmpty;
  }

  // Set the external barcode reader enabled status
  void setExternalBarcodeReaderEnabled(bool enabled) {
    _isExternalBarcodeReaderEnabled = enabled;
    notifyListeners();
  }

  // Set the selected device
  void setSelectedDevice(String? device) {
    _selectedDevice = device;
    notifyListeners();
  }

  // Scan for available barcode reader devices
  Future<void> scanForDevices() async {
    _isScanning = true;
    _availableDevices = [];
    notifyListeners();

    try {
      // Mock device detection for now
      // In a real implementation, this would use platform-specific code to detect devices
      await Future.delayed(const Duration(seconds: 1));

      // For Windows, we would use a platform-specific method to list HID or Serial devices
      if (Platform.isWindows) {
        _availableDevices = [
          'USB Barcode Scanner (COM3)',
          'HID Barcode Reader',
          'Generic USB Device',
        ];
      } else {
        // For mobile, we just use the built-in camera
        _availableDevices = ['Built-in Camera'];
      }
    } catch (e) {
      errorSnackBar(message: 'Error scanning for barcode devices');
    } finally {
      _isScanning = false;
      notifyListeners();
    }
  }

  // Check if the selected device is available in the current device list
  bool isSelectedDeviceAvailable() {
    return _selectedDevice != null &&
        _availableDevices.contains(_selectedDevice);
  }

  // Get a human-readable description of the current barcode reader setup
  String getActiveReaderDescription() {
    if (!_isExternalBarcodeReaderEnabled) {
      return 'Using camera for barcode scanning';
    } else if (_selectedDevice == null || _selectedDevice!.isEmpty) {
      return 'No barcode reader selected';
    } else {
      return 'Using $_selectedDevice';
    }
  }
}
