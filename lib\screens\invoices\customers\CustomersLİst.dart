import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/customer_model.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/widget/Customer_Dialog.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class CustomerslistWidget extends StatefulWidget {
  const CustomerslistWidget({super.key});

  @override
  State<CustomerslistWidget> createState() => _CustomerslistWidgetState();
}

class _CustomerslistWidgetState extends State<CustomerslistWidget>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  // Create a new RefreshController for each widget instance
  RefreshController? _refreshController;
  final TextEditingController nameController = TextEditingController();
  final TextEditingController addressController = TextEditingController();
  final TextEditingController noteController = TextEditingController();
  final TextEditingController phoneController = TextEditingController();

  @override
  void dispose() {
    nameController.dispose();
    addressController.dispose();
    noteController.dispose();
    phoneController.dispose();
    animationController?.dispose();
    // Properly dispose the RefreshController
    _refreshController?.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    // Initialize the RefreshController in initState
    _refreshController = RefreshController(initialRefresh: false);

    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    // Fetch data after widget is built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onRefresh();
    });
  }

  //==================================================//
  void _onRefresh() async {
    if (!mounted) return;

    try {
      await Provider.of<CustomerController>(context, listen: false)
          .fetchCustomersFromServer();
    } catch (e) {
      // Handle error silently
      debugPrint('Error refreshing customers: $e');
    } finally {
      // Check if widget is still mounted and controller is not disposed
      if (mounted &&
          _refreshController != null &&
          !(_refreshController!.isRefresh)) {
        _refreshController!.refreshCompleted();
      }
    }
  }

  //==================================================//
  void _onLoading() async {
    if (!mounted) return;

    try {
      await Provider.of<CustomerController>(context, listen: false)
          .fetchCustomersFromServer();
      if (mounted) setState(() {});
    } catch (e) {
      // Handle error silently
      debugPrint('Error loading more customers: $e');
    } finally {
      // Check if widget is still mounted and controller is not disposed
      if (mounted &&
          _refreshController != null &&
          !(_refreshController!.isLoading)) {
        _refreshController!.loadComplete();
      }
    }
  }

  void _showAddCustomerDialog() {
    // Clear the controllers first
    nameController.clear();
    phoneController.clear();
    addressController.clear();
    noteController.clear();

    showCustomerDialog(
      context: context,
      title: T("Add new Customer"),
      nameController: nameController,
      phoneController: phoneController,
      addressController: addressController,
      noteController: noteController,
      onSave: () async {
        String name = nameController.text;
        String phone = phoneController.text;
        String address = addressController.text;
        String note = noteController.text;

        var customerData = CustomerModel(
          name: name,
          nameEn: name,
          nameTr: name,
          nameLocalized: name,
          phone: phone,
          code: "*",
          iD: 0,
          accountingName: null,
          accountingNumber: null,
          customerTypeName: null,
          notes: note,
          address: address,
        );

        bool isSaved = await Provider.of<CustomerController>(
          context,
          listen: false,
        ).insertCustomer(customerData);

        if (isSaved) {
          successSnackBar(message: T('Customer saved!'));
          // ignore: use_build_context_synchronously
          Navigator.of(context).pop();
        } else {
          errorSnackBar(message: T('Failed to save customer.'));
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Ensure the RefreshController exists

    var data = Provider.of<CustomerController>(context).customers;
    return ApplicationLayout(
      child: Column(
        children: [
          // Modern header
          CommonHeader(
            icon: Icons.people,
            title: T("Customers"),
          ),

          // Action buttons
          _buildActionButtons(context),

          // Customer list
          Expanded(
            child: _refreshController == null
                ? const Center(child: CircularProgressIndicator())
                : SmartRefresher(
                    enablePullDown: true,
                    enablePullUp: true,
                    header: const WaterDropHeader(
                      waterDropColor: Colors.blue,
                    ),
                    footer: CustomFooter(
                      builder: (context, mode) {
                        Widget body;
                        if (mode == LoadStatus.loading) {
                          body = const CupertinoActivityIndicator();
                        } else if (mode == LoadStatus.idle) {
                          body = Text(T("Pull up to load"));
                        } else if (mode == LoadStatus.failed) {
                          body = Text(T("Load Failed! Click retry!"));
                        } else if (mode == LoadStatus.canLoading) {
                          body = Text(T("Release to load more"));
                        } else {
                          body = Text(T("No more data"));
                        }
                        return SizedBox(
                          height: 55.0,
                          child: Center(child: body),
                        );
                      },
                    ),
                    controller: _refreshController!,
                    onRefresh: _onRefresh,
                    onLoading: _onLoading,
                    child: data.isEmpty
                        ? _buildEmptyState()
                        : ListView.builder(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            itemCount: data.length,
                            itemBuilder: (BuildContext context, int index) {
                              final customer = data[index];
                              return _buildCustomerCard(
                                  context, customer, index);
                            },
                          ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Row(
        children: [
          // Sync button
          Expanded(
            child: _buildActionButton(
              context,
              icon: Icons.sync,
              label: T("مزامنة العملاء"),
              color: context.newPrimaryColor,
              onTap: () async {
                await Provider.of<CustomerController>(context, listen: false)
                    .syncCustomersWithServer();
              },
            ),
          ),
          const SizedBox(width: 12),
          // Add customer button
          Expanded(
            child: _buildActionButton(
              context,
              icon: Icons.person_add,
              label: T("Add new Customer"),
              color: context.colors.secondary,
              onTap: _showAddCustomerDialog,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: color,
      elevation: 2,
      shadowColor: color.withOpacity(0.4),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: Colors.white,
                size: 18,
              ),
              const SizedBox(width: 8),
              Flexible(
                child: Text(
                  label,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerCard(
      BuildContext context, CustomerModel customer, int index) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Material(
        elevation: 2,
        shadowColor: Colors.grey.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
        color: Colors.white,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            nameController.text = customer.name ?? "";
            phoneController.text = customer.phone ?? "";
            addressController.text = customer.address ?? "";
            noteController.text = customer.notes ?? "";

            showCustomerDialog(
              context: context,
              title: T("Customer Details"),
              nameController: nameController,
              phoneController: phoneController,
              addressController: addressController,
              noteController: noteController,
              onSave: null,
              readOnly: true,
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Customer icon with circle background
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: context.newPrimaryColor.withOpacity(0.15),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      Icons.person,
                      color: context.newPrimaryColor,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Customer info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        customer.name ?? T("No Name"),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (customer.phone != null && customer.phone!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Row(
                            children: [
                              Icon(
                                Icons.phone,
                                size: 14,
                                color: Colors.grey[600],
                              ),
                              const SizedBox(width: 4),
                              Text(
                                customer.phone!,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),

                // Action buttons
                Row(
                  children: [
                    // View button
                    _buildIconButton(
                      icon: Icons.remove_red_eye,
                      color: Colors.blue.shade700,
                      onPressed: () {
                        nameController.text = customer.name ?? "";
                        phoneController.text = customer.phone ?? "";
                        addressController.text = customer.address ?? "";
                        noteController.text = customer.notes ?? "";

                        showCustomerDialog(
                          context: context,
                          title: T("Customer Details"),
                          nameController: nameController,
                          phoneController: phoneController,
                          addressController: addressController,
                          noteController: noteController,
                          onSave: null,
                          readOnly: true,
                        );
                      },
                    ),

                    // Edit button
                    _buildIconButton(
                      icon: Icons.edit,
                      color: context.colors.secondary,
                      onPressed: () {
                        if (AppController.isThereConnection == false) {
                          errorSnackBar(
                              message: T("يجب توفر الانترنت للتعديل"));
                          return;
                        }

                        nameController.text = customer.name ?? "";
                        phoneController.text = customer.phone ?? "";
                        addressController.text = customer.address ?? "";
                        noteController.text = customer.notes ?? "";

                        showCustomerDialog(
                          customerId: customer.iD,
                          context: context,
                          title: T("Edit Customer info"),
                          nameController: nameController,
                          phoneController: phoneController,
                          addressController: addressController,
                          noteController: noteController,
                          isSynced: customer.iD != null && customer.iD != 0,
                          onSave: () async {
                            String name = nameController.text;
                            String phone = phoneController.text;
                            String address = addressController.text;
                            String note = noteController.text;
                            int id = customer.iD ?? 0;

                            var customerData = CustomerModel(
                                name: name,
                                nameEn: name,
                                nameTr: name,
                                nameLocalized: name,
                                phone: phone,
                                code: customer.code ?? "",
                                iD: id,
                                accountingName: null,
                                accountingNumber: null,
                                customerTypeName: null,
                                notes: note,
                                address: address);

                            bool isUpdated =
                                await Provider.of<CustomerController>(
                              context,
                              listen: false,
                            ).updateCustomer(id, customerData);

                            if (isUpdated) {
                              successSnackBar(
                                  message: T('Customer updated successfully!'));
                            } else {
                              errorSnackBar(
                                  message: T('Failed to update customer.'));
                            }
                          },
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIconButton({
    required IconData icon,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(50),
        onTap: onPressed,
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Icon(
            icon,
            color: color,
            size: 22,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            T("No customers found"),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T("Add a new customer to get started"),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _showAddCustomerDialog,
            icon: const Icon(Icons.add),
            label: Text(T("Add Customer")),
            style: ElevatedButton.styleFrom(
              backgroundColor: context.newPrimaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
