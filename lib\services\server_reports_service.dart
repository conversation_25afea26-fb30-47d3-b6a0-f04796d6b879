import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/models/dto/reports/sales_total_report_dto.dart';
import 'package:inventory_application/models/dto/reports/server/product_sales_server_dto.dart';
import 'package:inventory_application/models/dto/reports/server/customer_sales_server_dto.dart';
import 'package:inventory_application/models/dto/reports/server/sales_summary_report_dto.dart';
import 'package:inventory_application/models/dto/reports/inventory_attributes_report_dto.dart';
import 'package:inventory_application/models/dto/reports/product_transaction_details_dto.dart';
import 'package:inventory_application/models/dto/device_dto.dart';
import 'package:inventory_application/models/dto/user_dto.dart';
import 'package:inventory_application/controllers/app_controller.dart';

class ServerReportsService {
  static final ServerReportsService _instance =
      ServerReportsService._internal();
  factory ServerReportsService() => _instance;
  ServerReportsService._internal();

  /// Get Sales Total with Paid Amount Report
  Future<SalesTotalReportDTO?> getSalesTotalWithPaidAmount({
    String? deviceId,
    int? userId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // Build request body
      Map<String, dynamic> requestBody = {};
      if (AuthController.getIsMainAdmin()) {
        if (deviceId != null && deviceId.isNotEmpty) {
          requestBody['deviceId'] = deviceId;
        }

        if (userId != null) {
          requestBody['userid'] = userId;
        }
      } else {
        requestBody['deviceId'] = AppController.deviceId;
        requestBody['userid'] = AuthController.getUserId();
      }

      if (fromDate != null) {
        requestBody['fromDate'] = fromDate.toIso8601String();
      }

      if (toDate != null) {
        requestBody['toDate'] = toDate.toIso8601String();
      }

      print('Fetching sales total report with body: $requestBody');

      var response = await Api.post(
        action: 'Reports/GetSalesTotalWithPaidAmount',
        body: requestBody,
      );

      if (response != null && response.isSuccess && response.data != null) {
        return SalesTotalReportDTO.fromJson(response.data);
      } else {
        print('Error in sales total report: ${response?.message}');
        return null;
      }
    } catch (e) {
      print('Exception in getSalesTotalWithPaidAmount: $e');
      return null;
    }
  }

  /// Get current device ID from AppController
  String? getCurrentDeviceId() {
    try {
      return AppController.deviceId;
    } catch (e) {
      print('Error getting device ID: $e');
      return null;
    }
  }

  /// Get all devices
  Future<List<DeviceDTO>?> getDevices() async {
    try {
      print('Fetching devices from server');

      var response = await Api.getOne(action: 'Reports/GetDevices');

      if (response != null && response.isSuccess && response.data != null) {
        List<DeviceDTO> devices = [];
        if (response.data is List) {
          for (var deviceJson in response.data) {
            devices.add(DeviceDTO.fromJson(deviceJson));
          }
        }
        return devices;
      } else {
        print('Error getting devices: ${response?.message}');
        return null;
      }
    } catch (e) {
      print('Exception in getDevices: $e');
      return null;
    }
  }

  /// Get users by device ID
  Future<List<UserDTO>?> getUsersByDeviceId(String deviceId) async {
    try {
      print('Fetching users for device: $deviceId');

      var response = await Api.post(
        action: 'Reports/GetUsersByDeviceId',
        body: {'id': deviceId},
      );

      if (response != null && response.isSuccess && response.data != null) {
        List<UserDTO> users = [];
        if (response.data is List) {
          for (var userJson in response.data) {
            users.add(UserDTO.fromJson(userJson));
          }
        }
        return users;
      } else {
        print('Error getting users: ${response?.message}');
        return null;
      }
    } catch (e) {
      print('Exception in getUsersByDeviceId: $e');
      return null;
    }
  }

  /// Get Product Sales Report
  Future<List<ProductSalesServerDTO>?> getProductSalesReport({
    String? deviceId,
    int? userId,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // Build request body
      Map<String, dynamic> requestBody = {};

      if (AuthController.getIsMainAdmin()) {
        if (deviceId != null && deviceId.isNotEmpty) {
          requestBody['deviceId'] = deviceId;
        }

        if (userId != null) {
          requestBody['userid'] = userId;
        }
      } else {
        requestBody['deviceId'] = AppController.deviceId;
        requestBody['userid'] = AuthController.getUserId();
      }

      if (fromDate != null) {
        requestBody['fromDate'] = fromDate.toIso8601String();
      }

      if (toDate != null) {
        requestBody['toDate'] = toDate.toIso8601String();
      }

      print('Fetching product sales report with body: $requestBody');

      var response = await Api.post(
        action: 'Reports/GetProductSalesReport',
        body: requestBody,
      );

      if (response != null && response.isSuccess && response.data != null) {
        List<ProductSalesServerDTO> products = [];
        if (response.data is List) {
          for (var productJson in response.data) {
            products.add(ProductSalesServerDTO.fromJson(productJson));
          }
        }
        return products;
      } else {
        print('Error getting product sales report: ${response?.message}');
        return null;
      }
    } catch (e) {
      print('Exception in getProductSalesReport: $e');
      return null;
    }
  }

  /// Get Customer Sales Report
  Future<List<CustomerSalesServerDTO>?> getCustomerSalesReport({
    DateTime? fromDate,
    DateTime? toDate,
    int? customerId,
  }) async {
    try {
      // Build request body
      Map<String, dynamic> requestBody = {};

      if (fromDate != null) {
        requestBody['fromDate'] = fromDate.toIso8601String();
      }

      if (toDate != null) {
        requestBody['toDate'] = toDate.toIso8601String();
      }

      if (customerId != null) {
        requestBody['customerId'] = customerId;
      }

      print('Fetching customer sales report with body: $requestBody');

      var response = await Api.post(
        action: 'Reports/GetCustomerSalesReport',
        body: requestBody,
      );

      if (response != null && response.isSuccess && response.data != null) {
        List<CustomerSalesServerDTO> customers = [];
        if (response.data is List) {
          for (var customerJson in response.data) {
            customers.add(CustomerSalesServerDTO.fromJson(customerJson));
          }
        }
        return customers;
      } else {
        print('Error getting customer sales report: ${response?.message}');
        return null;
      }
    } catch (e) {
      print('Exception in getCustomerSalesReport: $e');
      return null;
    }
  }

  /// Get Sales Summary by Users and Devices Report
  Future<SalesSummaryReportDTO?> getSalesSummaryByUsersAndDevices({
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      // Build request body
      Map<String, dynamic> requestBody = {};

      if (fromDate != null) {
        requestBody['fromDate'] = fromDate.toIso8601String();
      }

      if (toDate != null) {
        requestBody['toDate'] = toDate.toIso8601String();
      }

      print('Fetching sales summary report with body: $requestBody');

      var response = await Api.post(
        action: 'Reports/GetSalesSummaryByUsersAndDevices',
        body: requestBody,
      );

      if (response != null && response.isSuccess && response.data != null) {
        return SalesSummaryReportDTO.fromJson(
          response.data,
          fromDate: fromDate,
          toDate: toDate,
        );
      } else {
        print('Error getting sales summary report: ${response?.message}');
        return null;
      }
    } catch (e) {
      print('Exception in getSalesSummaryByUsersAndDevices: $e');
      return null;
    }
  }

  /// Get Inventory Attributes Report
  Future<List<InventoryAttributesReportDTO>?> getInventoryAttributesReport({
    required int itemId,
    DateTime? fromDate,
    DateTime? toDate,
    int? storeId,
    int? branchId,
  }) async {
    try {
      print('Fetching inventory attributes report for item: $itemId');

      // Build query parameters
      List<String> queryParams = ['ItemId=$itemId'];

      if (fromDate != null) {
        queryParams.add('fromDate=${fromDate.toIso8601String()}');
      }

      if (toDate != null) {
        queryParams.add('toDate=${toDate.toIso8601String()}');
      }

      if (storeId != null) {
        queryParams.add('storeId=$storeId');
      }

      if (branchId != null) {
        queryParams.add('branchId=$branchId');
      }

      final queryString = queryParams.join('&');

      var response = await Api.getOne(
        action: 'Reports/GetInventoryAttributesReport?$queryString',
      );

      if (response != null && response.isSuccess && response.data != null) {
        List<InventoryAttributesReportDTO> attributes = [];
        if (response.data is List) {
          for (var attributeJson in response.data) {
            attributes
                .add(InventoryAttributesReportDTO.fromJson(attributeJson));
          }
        }
        return attributes;
      } else {
        print(
            'Error getting inventory attributes report: ${response?.message}');
        return null;
      }
    } catch (e) {
      print('Exception in getInventoryAttributesReport: $e');
      return null;
    }
  }

  Future<ProductTransactionReportDTO?> getProductTransactionDetails({
    required int itemId,
    DateTime? fromDate,
    DateTime? toDate,
    int? storeId,
    int? branchId,
  }) async {
    try {
      // Build query parameters
      List<String> queryParams = ['ItemId=$itemId'];

      if (fromDate != null) {
        queryParams.add('fromDate=${fromDate.toIso8601String()}');
      }
      if (toDate != null) {
        queryParams.add('toDate=${toDate.toIso8601String()}');
      }
      if (storeId != null) {
        queryParams.add('storeId=$storeId');
      }
      if (branchId != null) {
        queryParams.add('branchId=$branchId');
      }

      String queryString = queryParams.join('&');

      var response = await Api.getOne(
        action: 'Reports/GetInventoryTransactionsDetails?$queryString',
      );

      if (response != null && response.data != null) {
        return ProductTransactionReportDTO.fromJson(response.data);
      }

      return null;
    } catch (e) {
      print('Error fetching product transaction details: $e');
      return null;
    }
  }
}
