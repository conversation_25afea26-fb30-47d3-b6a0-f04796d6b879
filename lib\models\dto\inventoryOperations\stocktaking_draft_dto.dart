import 'package:inventory_application/models/dto/products/product_dto.dart';

class StocktakingDraftDTO {
  final String? stocktakingId;
  final int? warehouseId;
  final String? warehouseName;
  final DateTime? startDate;
  final List<ProductDTO>? items;
  final String? status;
  final DateTime? lastModified;

  StocktakingDraftDTO({
    this.stocktakingId,
    this.warehouseId,
    this.warehouseName,
    this.startDate,
    this.items,
    this.status,
    this.lastModified,
  });

  factory StocktakingDraftDTO.fromJson(Map<String, dynamic> json) {
    return StocktakingDraftDTO(
      stocktakingId: json['stocktakingId'],
      warehouseId: json['warehouseId'],
      warehouseName: json['warehouseName'],
      startDate: json['startDate'],
      items: (json['items'] as List?)
          ?.map((item) => ProductDTO.fromJson(item))
          .toList(),
      status: json['status'],
      lastModified: json['lastModified'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stocktakingId': stocktakingId,
      'warehouseId': warehouseId,
      'warehouseName': warehouseName,
      'startDate': startDate?.toIso8601String(),
      'items': items?.map((e) => e.toJson()).toList(),
      'status': status,
      'lastModified': lastModified?.toIso8601String(),
    };
  }
}
