import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:sqflite/sqflite.dart';

class BranchController with ChangeNotifier {
  List<ComboBoxDataModel> branches = [];
  int fetchedBranchCount = 0;
  bool runningSyncization = false;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  Future<void> fetchBranches() async {
    try {
      var fromLocalDatabase = await getBranches();
      if (fromLocalDatabase.isNotEmpty) {
        branches.clear();
        for (var element in fromLocalDatabase) {
          branches
              .add(ComboBoxDataModel(id: element["Id"], name: element["Name"]));
        }
        notifyListeners();
        return;
      }

      branches.clear();
      var url = '/Security/GetAllBranch';
      var result = await Api.getOne(action: url);
      if (result != null && result.isSuccess && result.data != null) {
        List<Map<String, dynamic>> branchesToInsert = [];
        for (var element in result.data) {
          branchesToInsert.add({
            'Id': element["Id"],
            'Name': element["Name"],
            'Code': element["Code"],
            'Address': element["Address"],
            'Phone': element["Phone"],
            'IsActive': element["IsActive"] ?? 1,
            'CreatedDate': element["CreatedDate"],
            'ModifiedDate': element["ModifiedDate"]
          });
          branches
              .add(ComboBoxDataModel(id: element["Id"], name: element["Name"]));
        }
        // Use bulk insert for better performance
        await _dbHelper.bulkInsertWithoutBranchId('Branches', branchesToInsert);
        notifyListeners();
      }
    } catch (e) {
      print('Error fetching branches: $e');
    }
  }

  Future<void> fetchBranchesFromServer() async {
    try {
      bool isStillThereBranches = true;
      fetchedBranchCount = 0;
      branches.clear();
      while (isStillThereBranches) {
        runningSyncization = true;
        var url = '/Security/GetAllBranch?skip=$fetchedBranchCount&take=200';
        var result = await Api.getOne(action: url);
        if (result != null && result.isSuccess && result.data != null) {
          List<Map<String, dynamic>> branchesToInsert = [];
          for (var element in result.data) {
            branchesToInsert.add({
              'Id': element["Id"],
              'Name': element["Name"],
              'Code': element["Code"],
              'Address': element["Address"],
              'Phone': element["Phone"],
              'IsActive': element["IsActive"] ?? 1,
              'CreatedDate': element["CreatedDate"],
              'ModifiedDate': element["ModifiedDate"]
            });
            branches.add(
                ComboBoxDataModel(id: element["Id"], name: element["Name"]));
            fetchedBranchCount++;
          }
          // Use bulk insert for better performance
          await _dbHelper.bulkInsertWithoutBranchId(
              'Branches', branchesToInsert);
          notifyListeners();
        }

        if (result?.data?.length ?? 0 < 200) {
          isStillThereBranches = false;
          runningSyncization = false;
        }
      }
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print('Error fetching branches from server: $e');
      }
    }
  }

  Future<int> setBranch(Map<String, dynamic> branchData) async {
    final db = await _dbHelper.database;
    return await db.insert(
      'Branches',
      branchData,
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<List<Map<String, dynamic>>> getBranches() async {
    final db = await _dbHelper.database;
    return await db.query('Branches', orderBy: 'Name ASC');
  }

  Future<Map<String, dynamic>?> getBranchById(int id) async {
    final db = await _dbHelper.database;
    final result = await db.query(
      'Branches',
      where: 'Id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? result.first : null;
  }

  Future<int> getBranchCount() async {
    final db = await _dbHelper.database;
    var result = await db.rawQuery('SELECT COUNT(*) as count FROM Branches');
    int count = Sqflite.firstIntValue(result) ?? 0;
    fetchedBranchCount = count;
    return count;
  }

  Future<bool> clearAndRefetchData() async {
    try {
      final db = await _dbHelper.database;
      await db.transaction((txn) async {
        await txn.delete('Branches');
      });

      await fetchBranchesFromServer();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing and refetching branches: $e');
      }
      return false;
    }
  }
}
