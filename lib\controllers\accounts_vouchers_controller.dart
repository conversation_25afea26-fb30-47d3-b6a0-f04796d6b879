import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/vouchar_model.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';

class AccountsVouchersController with ChangeNotifier {
  List<AccountsVoucharsModel> vouchers = [];
  AccountsVoucharsModel? voucherDetails;
  List<SqlLiteInvoiceModel> localVouchers = [];
  final DatabaseHelper _dbHelper = DatabaseHelper();

  //---------------------------------------------------------------------------
  // Function to insert or update a voucher
  Future<int> insertOrUpdateAccountsVoucher(
      Map<String, dynamic> voucher) async {
    var idToReturn = 0;
    var checkIfExist = await checkIfAccountsVoucherExists(voucher["localCode"]);

    if (checkIfExist == false) {
      idToReturn = await _dbHelper.insert('AccountsVouchers', voucher);
    } else {
      idToReturn = await updateVoucher(
          voucher["localCode"], json.decode(voucher["data"]));
    }

    return idToReturn;
  }

  //------------------------------------------------------------------------------
  // Function to get pending vouchers
  Future<List<AccountsVoucherDtoWithLiteId>> getPendingVouchers() async {
    var result = await _dbHelper.query('AccountsVouchers',
        where: 'status = ?',
        whereArgs: [getInvoiceSyncStatus(InvoiceSyncStatus.pending)]);

    List<AccountsVoucherDtoWithLiteId> vouchers = [];
    for (var element in result) {
      var voucherModel = SqlLiteInvoiceModel.fromJson(element);

      if (voucherModel.data != null) {
        try {
          var parsedData = jsonDecode(voucherModel.data!);
          var voucherData = AccountsVoucharsModel.fromJson(parsedData);

          vouchers.add(AccountsVoucherDtoWithLiteId(
              data: voucherData, id: voucherModel.id));
        } catch (e) {
          print("Error parsing voucher data: $e");
        }
      }
    }

    return vouchers;
  }

  //------------------------------------------------------------------------------
  Future<bool> syncVouchersWithServer() async {
    try {
      var vouchers = await getPendingVouchers();
      if (vouchers.isEmpty) {
        return false;
      }

      var url = '/AccountsVouchers/Manage';
      for (var voucher in vouchers) {
        voucher.data?.code = "*";

        var result = await Api.post(
          action: url,
          body: voucher.data?.toJson(),
        );

        if (result != null && result.isSuccess) {
          await updateVoucherSyncStatus(
              getInvoiceSyncStatus(InvoiceSyncStatus.synced), voucher.id ?? 0);
          await updateVoucherServerId(
              voucher.id ?? 0, result.data["ID"], result.data["Code"]);
          voucher.data?.code = result.data["Code"];
        }
      }

      notifyListeners();
      return true;
    } catch (e) {
      print("Error syncing vouchers: $e");
      return false;
    }
  }

  //------------------------------------------------------------------------------
  // Function to update voucher sync status
  Future<int> updateVoucherSyncStatus(String status, int id) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      'AccountsVouchers',
      {'status': status},
      where: 'ID = ?',
      whereArgs: [id],
    );
  }

  //------------------------------------------------------------------------------
  Future<int> updateVoucherServerId(
      int localId, int serverId, String code) async {
    final db = await DatabaseHelper().database;

    var result = await db.query(
      'AccountsVouchers',
      columns: ['data'],
      where: 'ID = ?',
      whereArgs: [localId],
    );

    if (result.isNotEmpty) {
      Map<String, dynamic> data = jsonDecode(result.first['data'] as String);
      data['ID'] = serverId;
      data['Code'] = code;
      String updatedDataJson = jsonEncode(data);

      return await db.update(
        'AccountsVouchers',
        {'data': updatedDataJson},
        where: 'ID = ?',
        whereArgs: [localId],
      );
    }
    return 0;
  }

  //------------------------------------------------------------------------------
  Future<List<SqlLiteInvoiceModel>> getVouchersFromLocalStorage() async {
    final List<Map<String, dynamic>> result =
        await _dbHelper.query('AccountsVouchers');
    return result.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
  }

  //--------------------------------------------------------
  Future<AccountsVoucharsModel> getVoucherById(int id) async {
    final db = await DatabaseHelper().database;
    var voucher = await db.query(
      'AccountsVouchers',
      where: 'ID = ?',
      whereArgs: [id],
    );

    var mappedItem =
        voucher.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
    var result = mappedItem
        .map((e) => AccountsVoucherDtoWithLiteId(
              id: e.id,
              status: e.status,
              data: AccountsVoucharsModel.fromJson(json.decode(e.data ?? "")),
            ))
        .toList();

    if (result.isNotEmpty) {
      return result[0].data ?? AccountsVoucharsModel();
    }

    return AccountsVoucharsModel();
  }

  //--------------------------------------------------------
  Future<List<AccountsVoucherDtoWithLiteId>> fetchLocalVouchers() async {
    try {
      localVouchers = await getVouchersFromLocalStorage();

      var data = localVouchers
          .map((e) => AccountsVoucherDtoWithLiteId(
                id: e.id,
                status: e.status,
                data: AccountsVoucharsModel.fromJson(json.decode(e.data ?? "")),
              ))
          .toList();

      return data;
    } catch (e) {
      print(e);
      return [];
    }
  }

  //------------------------------------------------------------------------------
  Future<bool> checkIfAccountsVoucherExists(String voucherLocalCode) async {
    final db = await DatabaseHelper().database;

    var result = await db.query(
      'AccountsVouchers',
      columns: ['data'],
      where: 'localCode = ?',
      whereArgs: [voucherLocalCode],
    );

    return result.isNotEmpty;
  }

  //------------------------------------------------------------------------------
  Future<AccountsVoucharsModel?> getAccountsVoucherByCode(
      {required String voucherLocalCode}) async {
    final db = await DatabaseHelper().database;
    var voucher = await db.query(
      'AccountsVouchers',
      where: 'localCode = ?',
      whereArgs: [voucherLocalCode],
    );

    var mappedItem =
        voucher.map((json) => SqlLiteInvoiceModel.fromJson(json)).toList();
    var result = mappedItem
        .map((e) => AccountsVoucherDtoWithLiteId(
              id: e.id,
              status: e.status,
              data: AccountsVoucharsModel.fromJson(json.decode(e.data ?? "")),
            ))
        .toList();

    if (result.isNotEmpty) {
      result[0].data?.code = result[0].data?.code;
      return result[0].data;
    }

    return null;
  }

  //------------------------------------------------------------------------------
  Future<int> updateVoucher(
      String voucherLocalCode, Map<String, dynamic> voucher) async {
    final db = await DatabaseHelper().database;

    var result = await db.query(
      'AccountsVouchers',
      columns: ['data'],
      where: 'localCode = ?',
      whereArgs: [voucherLocalCode],
    );

    if (result.isNotEmpty) {
      String updatedDataJson = jsonEncode(voucher);

      return await db.update(
        'AccountsVouchers',
        {'data': updatedDataJson},
        where: 'localCode = ?',
        whereArgs: [voucherLocalCode],
      );
    }
    return 0;
  }
}

// Helper classes
class AccountsVoucherDtoWithLiteId {
  int? id;
  String? status;
  AccountsVoucharsModel? data;

  AccountsVoucherDtoWithLiteId({
    this.id,
    this.status,
    this.data,
  });
}
