import 'package:easy_localization/easy_localization.dart';

extension StringLocalization on String {
  String get locale => this.tr();
}

//---------------------------------------------------------------
extension StringFormatting on String {
  //--------------------------------------------------
  String myDateFormatter(DateTime? date,
      {bool isShowDay = false,
      bool isShowTime = true,
      bool hideDateIfSameDay = false,
      bool isEnglish = false}) {
    if (date != null) {
      var str = '';
      str = DateFormat('yyyy/MM/dd', 'en').format(date);
      str += isShowDay
          ? DateFormat('  EEEE', isEnglish ? 'en' : 'ar').format(date)
          : '';
      if (isShowTime) {
        str += DateFormat('  hh:mm', 'en').format(date) +
            DateFormat(' a', isEnglish ? 'en' : 'ar').format(date);
      }

      if (hideDateIfSameDay) {
        var now = DateTime.now();
        if (date.day + date.month != now.day + now.month) {
          str = DateFormat('yyyy/MM/dd', 'en').format(date);
        } else {
          str = isShowDay
              ? DateFormat('  EEEE', isEnglish ? 'en' : 'ar').format(date)
              : '';
          if (isShowTime) {
            str += DateFormat('  hh:mm', 'en').format(date) +
                DateFormat(' a', isEnglish ? 'en' : 'ar').format(date);
          }
        }
      }

      return str;
    }
    return '';
  }

  //-------------------------------------------------------------------
  String formatTimeString(String timeString) {
    // Parse the input time string
    List<String> timeParts = timeString.split(':');
    int hours = int.parse(timeParts[0]);
    int minutes = int.parse(timeParts[1]);

    // Determine whether it's morning or evening
    String period = hours < 12 ? 'صباحاً' : 'مساءً';

    // Format the time as hh:mm
    String formattedTime =
        '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}';

    return '$formattedTime $period';
  }

//---------------------------------------------------------------
  String getArabicMonthName(DateTime date) {
    final List<String> arabicMonthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'إبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر',
    ];

    return arabicMonthNames[date.month - 1];
  }

//---------------------------------------------------------------
  String getDayName({DateTime? date, bool isEnglish = false}) {
    var str = DateFormat('  EEEE', isEnglish ? 'en' : 'ar')
        .format(date ?? DateTime.now());

    return str;
  }

//---------------------------------------------------------------
  String differenceInHour(DateTime date) {
    DateTime endDate = DateTime.now();
    Duration difference = date.difference(endDate);

    int hoursDifference = difference.inHours.abs();
    return hoursDifference.toString();
  }
}
//---------------------------------------------------------------


