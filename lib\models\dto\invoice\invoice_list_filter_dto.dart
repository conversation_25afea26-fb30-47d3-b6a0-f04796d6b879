import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';

class InvoiceListFilterDTO {
  DateTime? fromDate;
  DateTime? toDate;
  String? invoiceCode;
  int? customerId;
  String? customerName;
  SalesType invoiceType;
  String? invoiceTypeName;
  String? invoiceTypeNameForRequest;
  DataTableParameters? dataTableParameters;

  InvoiceListFilterDTO({
    this.fromDate,
    this.toDate,
    this.invoiceCode,
    this.customerId,
    this.customerName,
    required this.invoiceType,
    this.invoiceTypeName,
    this.invoiceTypeNameForRequest,
    this.dataTableParameters,
  });

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['FromDate'] = fromDate?.toIso8601String();
    data['ToDate'] = toDate?.toIso8601String();
    data['Sales_Code'] = invoiceCode;
    data['Customer_ID'] = customerId;
    // data['invoiceType'] = invoiceType.index;
    data['transactions_type'] = invoiceTypeNameForRequest;
    if (dataTableParameters != null) {
      data['dataTableParameters'] = dataTableParameters!.toJson();
    }
    return data;
  }
}
