import 'package:flutter/material.dart';
import 'package:inventory_application/services/excel_export_service.dart';

/// ويدجت زر تصدير Excel
class ExcelExportButton extends StatelessWidget {
  final String reportTitle;
  final List<Map<String, dynamic>> data;
  final List<String> headers;
  final String? fileName;
  final DateTime? fromDate;
  final DateTime? toDate;
  final IconData? icon;
  final String? buttonText;
  final Color? backgroundColor;
  final Color? textColor;

  const ExcelExportButton({
    Key? key,
    required this.reportTitle,
    required this.data,
    required this.headers,
    this.fileName,
    this.fromDate,
    this.toDate,
    this.icon,
    this.buttonText,
    this.backgroundColor,
    this.textColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: data.isEmpty ? null : () => _exportToExcel(context),
      icon: Icon(
        icon ?? Icons.table_chart,
        color: textColor ?? Colors.white,
      ),
      label: Text(
        buttonText ?? 'تصدير Excel',
        style: TextStyle(
          color: textColor ?? Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor ?? Colors.green,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Future<void> _exportToExcel(BuildContext context) async {
    await ExcelExportService.exportGenericReport(
      context: context,
      reportTitle: reportTitle,
      data: data,
      headers: headers,
      fileName: fileName,
      fromDate: fromDate,
      toDate: toDate,
    );
  }
}

/// ويدجت زر تصدير تقرير المبيعات
class SalesExcelExportButton extends StatelessWidget {
  final List<Map<String, dynamic>> salesData;
  final String reportTitle;
  final DateTime? fromDate;
  final DateTime? toDate;

  const SalesExcelExportButton({
    Key? key,
    required this.salesData,
    required this.reportTitle,
    this.fromDate,
    this.toDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: salesData.isEmpty ? null : () => _exportSalesReport(context),
      icon: const Icon(Icons.receipt_long, color: Colors.white),
      label: const Text(
        'تصدير تقرير المبيعات',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Future<void> _exportSalesReport(BuildContext context) async {
    await ExcelExportService.exportSalesReport(
      context: context,
      salesData: salesData,
      reportTitle: reportTitle,
      fromDate: fromDate,
      toDate: toDate,
    );
  }
}

/// ويدجت زر تصدير تقرير المخزون
class InventoryExcelExportButton extends StatelessWidget {
  final List<Map<String, dynamic>> inventoryData;
  final String reportTitle;

  const InventoryExcelExportButton({
    Key? key,
    required this.inventoryData,
    required this.reportTitle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed:
          inventoryData.isEmpty ? null : () => _exportInventoryReport(context),
      icon: const Icon(Icons.inventory, color: Colors.white),
      label: const Text(
        'تصدير تقرير المخزون',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.orange,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  Future<void> _exportInventoryReport(BuildContext context) async {
    await ExcelExportService.exportInventoryReport(
      context: context,
      inventoryData: inventoryData,
      reportTitle: reportTitle,
    );
  }
}
