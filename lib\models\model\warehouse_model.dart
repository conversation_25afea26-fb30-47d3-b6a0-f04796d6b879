class WarehouseModel {
  final int? id;
  final String? name;
  final String? code;
  final String? address;
  final bool? isActive;

  WarehouseModel({
    this.id,
    this.name,
    this.code,
    this.address,
    this.isActive,
  });

  factory WarehouseModel.fromJson(Map<String, dynamic> json) {
    return WarehouseModel(
      id: json['id'],
      name: json['name'],
      code: json['code'],
      address: json['address'],
      isActive: json['isActive'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'address': address,
      'isActive': isActive,
    };
  }
}
