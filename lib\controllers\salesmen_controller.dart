import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/models/dto/response.dart';
import 'package:inventory_application/models/model/salemen_model.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:sqflite/sqflite.dart';

class SalesmenController with ChangeNotifier {
  List<ComboBoxDataModel> salesmen = [];
  int fetchedSalesmenCount = 0;
  bool runningSyncization = false;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  Future<void> fetchSalesmen() async {
    try {
      var fromlocalDatabase = await getSalesmen();
      if (fromlocalDatabase.isNotEmpty) {
        salesmen.clear();
        for (var element in fromlocalDatabase) {
          salesmen.add(ComboBoxDataModel.fromJson(element));
        }
        notifyListeners();
        return;
      }

      salesmen.clear();
      var url = '/Salesmen/SalesmenList';
      var requestModel = SalesmenRequestDTO(
        mobile: "",
        salesman_ID: "",
        supplier_Type_ID: "",
        dataTableParameters: DataTableParameters(
          columnName: "Code",
          dir: "desc",
          skip: fetchedSalesmenCount,
          take: 200,
        ),
      );
      var result = await Api.post(action: url, body: requestModel.toJson());
      if (result != null) {
        if (result.isSuccess) {
          for (var element in result.data) {
            setSalesmen(element["ID"], element["Name"], element["Code"]);
            salesmen.add(ComboBoxDataModel(
                id: element["ID"],
                name: element["Name"],
                code: element["Code"]));
          }
        }
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------
  Future<void> fetchSalesmenFromServer() async {
    try {
      bool isStillThereWarehouses = true;
      fetchedSalesmenCount = 0;
      salesmen.clear();
      while (isStillThereWarehouses) {
        runningSyncization = true;
        var url = '/Salesmen/SalesmenList';
        var requestModel = SalesmenRequestDTO(
          mobile: "",
          salesman_ID: "",
          supplier_Type_ID: "",
          dataTableParameters: DataTableParameters(
            columnName: "Code",
            dir: "desc",
            skip: fetchedSalesmenCount,
            take: 200,
          ),
        );
        var result = await Api.post(action: url, body: requestModel.toJson());
        if (result != null && result.isSuccess) {
          for (var element in result.data) {
            await setSalesmen(element["ID"], element["Name"], element["Code"]);
            salesmen.add(ComboBoxDataModel(
                id: element["ID"],
                name: element["Name"],
                code: element["Code"]));
            fetchedSalesmenCount++;
            notifyListeners();
          }
          if (result.data.length < 200) {
            isStillThereWarehouses = false;
            runningSyncization = false;
          }
        } else {
          isStillThereWarehouses = false;
          runningSyncization = false;
        }
      }
      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
    }
  }

  //--------------------------------------------
  Future<int> setSalesmen(int id, String name, String code) async {
    // Use insert with conflict resolution to replace if id exists
    final result = await _dbHelper.insert(
      'Salesmen',
      {'id': id, 'name': name, "code": code},
    );
    return result;
  }

  //--------------------------------------------
  Future<List<Map<String, dynamic>>> getSalesmen() async {
    var result = await _dbHelper.query('Salesmen');
    return result;
  }

  //--------------------------------------------
  Future<int> getSalesmenCount() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM Salesmen WHERE BranchId = ${AppController.currentBranchId}');
    int count = Sqflite.firstIntValue(result) ?? 0;
    return count;
  }

  //--------------------------------------------
  //--------------------------------------------------------------------------------
  Future<bool> clearAndRefetchData() async {
    try {
      _dbHelper.delete('Salesmen', where: null, whereArgs: null);

      await fetchSalesmenFromServer();
      return true;
    } catch (e) {
      return false;
    }
  }
  //--------------------------------------------
}
