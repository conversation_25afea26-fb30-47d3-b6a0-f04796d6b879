class ResponseResultModel {
  int statusCode = 0;
  bool isSuccess = false;
  List<dynamic>? errors;
  List<dynamic>? message;
  dynamic data;

  ResponseResultModel({
    this.data,
    this.errors,
    this.message,
    required this.isSuccess,
  });

  ResponseResultModel.fromJson(Map<dynamic, dynamic> json)
      : statusCode = json['StatusCode'],
        isSuccess = json['IsSuccess'],
        errors = json['Errors'],
        message = json['Message'],
        data = json['Data'];
}

//-----------------------
class EcommerceResponseResultModel {
  int statusCode = 0;
  bool isSuccess = false;
  String? errors;

  dynamic data;

  EcommerceResponseResultModel({
    this.data,
    this.errors,
    required this.isSuccess,
  });

  EcommerceResponseResultModel.fromJson(Map<dynamic, dynamic> json)
      : statusCode = json['statusCode'],
        isSuccess = json['isSuccess'],
        errors = json['errors'],
        data = json['data'];
}

//-----------------------
class DataTableParameters {
  int? skip;
  int? take;
  String? columnName;
  String? dir; //desc asc

  DataTableParameters({this.skip, this.take, this.columnName, this.dir}) {
    // take = 10;
  }

  DataTableParameters.fromJson(Map<String, dynamic> json) {
    skip = json['Skip'];
    take = json['Take'];
    columnName = json['ColumnName'];
    dir = json['Dir'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Skip'] = skip;
    data['Take'] = take;
    data['ColumnName'] = columnName;
    data['Dir'] = dir;
    return data;
  }
}
