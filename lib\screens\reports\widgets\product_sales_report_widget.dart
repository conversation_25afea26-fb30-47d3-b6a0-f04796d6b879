import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/product_sales_report_dto.dart';
import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:intl/intl.dart';

class ProductSalesReportWidget extends StatefulWidget {
  final ProductSalesReportDTO report;

  const ProductSalesReportWidget({
    Key? key,
    required this.report,
  }) : super(key: key);

  @override
  State<ProductSalesReportWidget> createState() =>
      _ProductSalesReportWidgetState();
}

class _ProductSalesReportWidgetState extends State<ProductSalesReportWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int touchedIndex = -1;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab bar at the top (fixed)
        TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: T('Products')),
            Tab(text: T('Details')),
          ],
          labelColor: context.newPrimaryColor,
          unselectedLabelColor: Colors.grey,
          indicatorColor: context.newPrimaryColor,
        ),

        // Content area (scrollable)
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Products tab
              _buildScrollableTab(_buildProductsContent()),

              // Details tab
              _buildScrollableTab(_buildDetailsContent()),
            ],
          ),
        ),
      ],
    );
  }

  // Helper method to create a scrollable tab
  Widget _buildScrollableTab(Widget content) {
    return ListView(
      children: [
        // Report header at the top of each tab
        _buildReportHeader(),

        // Tab content
        content,
      ],
    );
  }

  // Build report header with key metrics
  Widget _buildReportHeader() {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final fromDate = dateFormat.format(widget.report.fromDate);
    final toDate = dateFormat.format(widget.report.toDate);
    final dateRange =
        fromDate == toDate ? fromDate : '$fromDate ${T('to')} $toDate';

    return Container(
      padding: const EdgeInsets.all(16),
      color: context.newPrimaryColor.withOpacity(0.1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            T('Product Sales Report'),
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: context.newPrimaryColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            dateRange,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),

          // Key metrics in a responsive layout
          LayoutBuilder(
            builder: (context, constraints) {
              // Check if we're on a small screen (mobile)
              final isSmallScreen = constraints.maxWidth < 600;

              return isSmallScreen
                  ? Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Total Sales'),
                                value:
                                    widget.report.totalSales.toStringAsFixed(2),
                                icon: Icons.attach_money,
                                color: Colors.green,
                              ),
                            ),
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Total Quantity'),
                                value: widget.report.totalQuantity.toString(),
                                icon: Icons.shopping_cart,
                                color: Colors.blue,
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Products'),
                                value: widget.report.productSummaries.length
                                    .toString(),
                                icon: Icons.inventory_2,
                                color: Colors.purple,
                              ),
                            ),
                            Expanded(
                              child: _buildMetricCard(
                                title: T('Invoices'),
                                value: widget.report.invoices.length.toString(),
                                icon: Icons.receipt_long,
                                color: Colors.orange,
                              ),
                            ),
                          ],
                        ),
                      ],
                    )
                  : Row(
                      children: [
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Total Sales'),
                            value: widget.report.totalSales.toStringAsFixed(2),
                            icon: Icons.attach_money,
                            color: Colors.green,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Total Quantity'),
                            value: widget.report.totalQuantity.toString(),
                            icon: Icons.shopping_cart,
                            color: Colors.blue,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Products'),
                            value: widget.report.productSummaries.length
                                .toString(),
                            icon: Icons.inventory_2,
                            color: Colors.purple,
                          ),
                        ),
                        Expanded(
                          child: _buildMetricCard(
                            title: T('Invoices'),
                            value: widget.report.invoices.length.toString(),
                            icon: Icons.receipt_long,
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    );
            },
          ),
        ],
      ),
    );
  }

  // Build a metric card for the header
  Widget _buildMetricCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build the products tab content
  Widget _buildProductsContent() {
    final topProducts = widget.report.getTopSellingProducts();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Top products chart
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildTopProductsChart(topProducts),
            ),
          ),

          // Products table
          _buildProductsTable(widget.report.productSummaries),

          // Add some bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build the details tab content
  Widget _buildDetailsContent() {
    final salesByDate = widget.report.calculateSalesByDate();

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Sales by date chart
          Card(
            elevation: 2,
            margin: const EdgeInsets.only(bottom: 24),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildSalesByDateChart(salesByDate),
            ),
          ),

          // Invoices list
          Text(
            T('Invoices'),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          if (widget.report.invoices.isEmpty)
            Center(
              child: Text(
                T('No invoices found for the selected period'),
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: widget.report.invoices.length,
              itemBuilder: (context, index) {
                final invoice = widget.report.invoices[index];
                return _buildInvoiceCard(invoice);
              },
            ),

          // Add some bottom padding
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  // Build a table of products
  Widget _buildProductsTable(List<ProductSalesSummary> products) {
    if (products.isEmpty) {
      return Center(
        child: Text(
          T('No product data available'),
          style: TextStyle(
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    // Sort products by sales (highest first)
    final sortedProducts = List<ProductSalesSummary>.from(products);
    sortedProducts.sort((a, b) => b.totalSales.compareTo(a.totalSales));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Products by Sales'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),

        // Table header
        Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: context.newPrimaryColor.withOpacity(0.1),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  T('Product'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  T('Quantity'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 1,
                child: Text(
                  T('Unit'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  T('Sales'),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.right,
                ),
              ),
            ],
          ),
        ),

        // Table rows
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: sortedProducts.length,
          itemBuilder: (context, index) {
            final product = sortedProducts[index];
            final isEven = index % 2 == 0;

            return Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                color: isEven ? Colors.grey.shade100 : Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey.shade300,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 3,
                    child: Text(
                      product.productName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      product.quantity.toString(),
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Text(
                      product.unit,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      product.totalSales.toStringAsFixed(2),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  // Build a card for a single invoice
  Widget _buildInvoiceCard(InvoiceModel invoice) {
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm');
    final formattedDate = invoice.entryDate != null
        ? dateFormat.format(invoice.entryDate!)
        : T('Unknown date');

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invoice header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Text(
                    '${T('Invoice')}: ${invoice.appReferanceCode ?? invoice.code ?? T('Unknown')}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Text(
                  formattedDate,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
            const Divider(),

            // Invoice details
            _buildInvoiceDetailRow(
              label: T('Customer'),
              value: invoice.customerName ?? T('Unknown'),
              icon: Icons.person,
            ),
            _buildInvoiceDetailRow(
              label: T('Warehouse'),
              value: invoice.storeName ?? T('Unknown'),
              icon: Icons.warehouse,
            ),
            _buildInvoiceDetailRow(
              label: T('Total'),
              value: (invoice.total ?? 0.0).toStringAsFixed(2),
              icon: Icons.attach_money,
              valueColor: Colors.green.shade700,
              valueBold: true,
            ),
          ],
        ),
      ),
    );
  }

  // Build a detail row for the invoice card
  Widget _buildInvoiceDetailRow({
    required String label,
    required String value,
    required IconData icon,
    Color? valueColor,
    bool valueBold = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 8),
          Text(
            '$label:',
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            value,
            style: TextStyle(
              fontWeight: valueBold ? FontWeight.bold : FontWeight.normal,
              color: valueColor,
            ),
          ),
        ],
      ),
    );
  }

  // Build top products chart
  Widget _buildTopProductsChart(List<ProductSalesSummary> products) {
    if (products.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No product data available'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    // Limit to top 10 products for the chart (increased from 5)
    final topProducts =
        products.length > 10 ? products.sublist(0, 10) : products;

    // Sort products by sales (highest first)
    topProducts.sort((a, b) => b.totalSales.compareTo(a.totalSales));

    // Calculate the maximum sales value for scaling
    final maxSales =
        topProducts.map((p) => p.totalSales).reduce((a, b) => a > b ? a : b);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Top Selling Products'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: topProducts.length,
          itemBuilder: (context, index) {
            final product = topProducts[index];
            // Calculate percentage of max value for bar width
            final percentage = product.totalSales / maxSales;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  // Product name
                  SizedBox(
                    width: 150,
                    child: Text(
                      product.productName,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.right,
                    ),
                  ),
                  const SizedBox(width: 10),

                  // Bar
                  Expanded(
                    child: Stack(
                      children: [
                        // Background
                        Container(
                          height: 25,
                          decoration: BoxDecoration(
                            color: Colors.grey.shade200,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),

                        // Actual bar
                        FractionallySizedBox(
                          widthFactor: percentage,
                          child: Container(
                            height: 25,
                            decoration: BoxDecoration(
                              color: context.newPrimaryColor,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                        ),

                        // Value label
                        Positioned.fill(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Padding(
                              padding: const EdgeInsets.only(right: 8.0),
                              child: Text(
                                NumberFormat('#,##0.00')
                                    .format(product.totalSales),
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: percentage > 0.75
                                      ? Colors.white
                                      : Colors.black,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  // Build sales by date chart
  Widget _buildSalesByDateChart(Map<String, double> salesByDate) {
    if (salesByDate.isEmpty) {
      return SizedBox(
        height: 200,
        child: Center(
          child: Text(
            T('No sales data available'),
            style: TextStyle(
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ),
      );
    }

    // Sort dates
    final sortedDates = salesByDate.keys.toList()..sort();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Sales by Date'),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 300,
          child: LineChart(
            LineChartData(
              lineTouchData: LineTouchData(
                touchTooltipData: LineTouchTooltipData(
                  tooltipBgColor: Colors.grey.shade800,
                  getTooltipItems: (touchedSpots) {
                    return touchedSpots.map((spot) {
                      final date = sortedDates[spot.x.toInt()];
                      final value = spot.y;
                      return LineTooltipItem(
                        '$date\n${NumberFormat('#,##0.00').format(value)}',
                        const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    }).toList();
                  },
                ),
              ),
              titlesData: FlTitlesData(
                show: true,
                rightTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                topTitles: const AxisTitles(
                  sideTitles: SideTitles(showTitles: false),
                ),
                bottomTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value < 0 || value >= sortedDates.length) {
                        return const SizedBox.shrink();
                      }

                      final date = sortedDates[value.toInt()];
                      // Format to show day only
                      final day = date.split('-')[2];

                      return Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          day,
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      );
                    },
                    reservedSize: 30,
                  ),
                ),
                leftTitles: AxisTitles(
                  sideTitles: SideTitles(
                    showTitles: true,
                    getTitlesWidget: (value, meta) {
                      if (value == 0) {
                        return const SizedBox.shrink();
                      }

                      String text = NumberFormat.compact().format(value);

                      return Text(
                        text,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      );
                    },
                    reservedSize: 40,
                  ),
                ),
              ),
              borderData: FlBorderData(
                show: false,
              ),
              gridData: const FlGridData(
                show: true,
                drawVerticalLine: false,
              ),
              lineBarsData: [
                LineChartBarData(
                  spots: List.generate(
                    sortedDates.length,
                    (index) {
                      final date = sortedDates[index];
                      final value = salesByDate[date]!;
                      return FlSpot(index.toDouble(), value);
                    },
                  ),
                  isCurved: true,
                  color: context.newPrimaryColor,
                  barWidth: 3,
                  isStrokeCapRound: true,
                  dotData: const FlDotData(show: true),
                  belowBarData: BarAreaData(
                    show: true,
                    color: context.newPrimaryColor.withOpacity(0.2),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
