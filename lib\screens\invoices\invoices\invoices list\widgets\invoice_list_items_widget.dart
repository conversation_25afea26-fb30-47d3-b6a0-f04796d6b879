import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/double_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/helpers/function.dart';

import 'package:inventory_application/screens/invoices/invoices/invoice%20details/inovice_details_page.dart';
import 'package:provider/provider.dart';

class InvoiceListItemWidget extends StatefulWidget {
  const InvoiceListItemWidget(
      {super.key, required this.model, required this.onDelete});
  final InvoiceDto model;
  final Function onDelete;

  @override
  State<InvoiceListItemWidget> createState() => _InvoiceListItemWidgetState();
}

class _InvoiceListItemWidgetState extends State<InvoiceListItemWidget> {
  void _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required Function delete,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Text(
            title,
            style: TextStyle(
              color: context.newSecondaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            content,
            style: TextStyle(
              color: context.newTextColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: Text(
                T("Cancel"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                delete();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Extraer valores seguros con valores predeterminados
    final String invoiceCode = widget.model.invoiceCode ?? "";
    var invoiceType =
        Provider.of<InvoiceController>(context).filterModel.invoiceType;

    final String customerName = widget.model.custoemrName ?? T("No Customer");
    final String appReferenceCode = widget.model.appReferanceCode ?? "";
    final double totalAfterDiscount = widget.model.totalAfterDiscount ?? 0.0;
    final DateTime? invoiceDate = widget.model.invoiceDate;

    return Container(
      margin: const EdgeInsets.only(bottom: 15),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // Invoice header
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: context.newBackgroundColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.receipt,
                            color: context.newSecondaryColor,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            invoiceType.title,
                            style: TextStyle(
                              color: context.newSecondaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        customerName,
                        style: TextStyle(
                          color: context.newTextColor,
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: context.newPrimaryColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    "".myDateFormatter(invoiceDate, isShowTime: false),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Invoice details
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
            child: Column(
              children: [
                _buildInvoiceInfoRow(
                  context,
                  T("Code"),
                  appReferenceCode,
                  showCopyButton: true,
                ),
                _buildInvoiceInfoRow(
                  context,
                  T("Server Code"),
                  invoiceCode,
                  showCopyButton: true,
                ),
                _buildInvoiceInfoRow(
                  context,
                  T("Total"),
                  0.0.covertDoubleToMoneyReturnString(totalAfterDiscount),
                  isTotal: true,
                ),
              ],
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            decoration: BoxDecoration(
              color: context.newBackgroundColor.withOpacity(0.3),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) =>
                            InoviceDetailsPage(model: widget.model),
                      ));
                    },
                    icon: const Icon(Icons.visibility, size: 18),
                    label: Text(T("Details")),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: context.newPrimaryColor,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () async {
                      _showConfirmDialog(
                        title: T("Warning"),
                        content:
                            T("Are you sure you want to delete this invoice?"),
                        confirmText: T("Delete"),
                        delete: () async {
                          try {
                            final int invoiceId = widget.model.id ?? 0;

                            bool result = await Provider.of<InvoiceController>(
                                    context,
                                    listen: false)
                                .deleteSyncInvoice(invoiceId, invoiceType.name);

                            if (result) {
                              successSnackBar(
                                  message: T("Successfully deleted"));
                            } else {
                              errorSnackBar(
                                  message: T(
                                      "Delete failed. Please try again later"));
                            }

                            if (mounted) {
                              widget.onDelete();
                            }
                          } catch (e) {
                            errorSnackBar(
                                message: T(
                                    "An error occurred while deleting the invoice"));
                          }
                        },
                      );
                    },
                    icon: const Icon(Icons.delete_outline, size: 18),
                    label: Text(T("Delete")),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: const BorderSide(color: Colors.red),
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvoiceInfoRow(BuildContext context, String label, String value,
      {bool isTotal = false, bool showCopyButton = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: context.newTextColor.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  color: isTotal
                      ? context.newSecondaryColor
                      : context.newTextColor,
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                  fontSize: isTotal ? 16 : 14,
                ),
              ),
              if (showCopyButton && value.isNotEmpty)
                IconButton(
                  constraints: BoxConstraints.tight(const Size(32, 32)),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: value));
                    // Show feedback to user
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(T("$label copied to clipboard")),
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.copy,
                    size: 16,
                    color: context.newPrimaryColor.withOpacity(0.7),
                  ),
                  tooltip: T("Copy to clipboard"),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
