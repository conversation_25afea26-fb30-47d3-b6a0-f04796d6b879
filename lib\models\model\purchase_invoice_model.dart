import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/product_model.dart';

enum PurchaseType {
  unknown,
  MaterialRequest,
  Qutation,
  ProformaInvoice,
  Order,
  Receipt,
  Invoice,
  ReturnInvoice,
  PurchaseReturn
}

class PurchaseInvoiceModel {
  int? iD;
  String? code;
  String? appReferanceCode;
  String? entryDateFormated;
  DateTime? entryDate;
  int? currenyID;
  double? exchangeRate;
  int? costCenterID;
  int? supplierID;
  String? supplierInvoiceNo;
  String? purchaseReceiptID;
  String? qutationID;
  String? proformaInvoiceID;
  String? purchaseOrderID;
  String? purchaseType;
  String? transactionsType;
  int? storeID;
  String? notes;
  String? itemsCategory;
  String? ddlItems;
  List<PurchaseItems>? purchaseItems;
  String? offerPeriod;
  String? offerEndingDateFormated;
  String? paymentConditionID;
  String? shippingMethodID;
  String? shippingPeriod;
  double? total;
  double? vATPercent;
  double? discountValue;

  PurchaseInvoiceModel({
    this.iD,
    this.code,
    this.appReferanceCode,
    this.entryDateFormated,
    this.entryDate,
    this.currenyID,
    this.exchangeRate,
    this.costCenterID,
    this.supplierID,
    this.supplierInvoiceNo,
    this.purchaseReceiptID,
    this.qutationID,
    this.proformaInvoiceID,
    this.purchaseOrderID,
    this.purchaseType,
    this.transactionsType,
    this.storeID,
    this.notes,
    this.itemsCategory,
    this.ddlItems,
    this.purchaseItems,
    this.offerPeriod,
    this.offerEndingDateFormated,
    this.paymentConditionID,
    this.shippingMethodID,
    this.shippingPeriod,
    this.total,
    this.vATPercent,
    this.discountValue,
  });

  PurchaseInvoiceModel.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    code = json['Code'];
    appReferanceCode = json['APP_Referance_Code'];
    entryDateFormated = json['Entry_Date_Formated'];
    entryDate = json['entryDate'].toString().contains("-")
        ? DateTime.parse(json['entryDate'])
        : formatDate(json['Entry_Date']);
    currenyID = json['Curreny_ID'];
    exchangeRate = json['Exchange_Rate'] != null
        ? double.parse(json['Exchange_Rate'].toString())
        : 1.0;
    costCenterID = json['Cost_Center_ID'];
    supplierID = json['Supplier_ID'];
    supplierInvoiceNo = json['Supplier_Invoice_No'];
    purchaseReceiptID = json['Purchase_Receipt_ID'];
    qutationID = json['Qutation_ID'];
    proformaInvoiceID = json['Proforma_Invoice_ID'];
    purchaseOrderID = json['Purchase_Order_ID'];
    purchaseType = json['Purchase_Type'];
    transactionsType = json['transactions_type'];
    storeID = json['Store_ID'];
    notes = json['Notes'];
    itemsCategory = json['ItemsCategory'];
    ddlItems = json['ddlItems'];

    if (json['PurchaseItems'] != null) {
      purchaseItems = <PurchaseItems>[];
      json['PurchaseItems'].forEach((v) {
        purchaseItems!.add(PurchaseItems.fromJson(v));
      });
    }

    offerPeriod = json['Offer_Period'];
    offerEndingDateFormated = json['Offer_Ending_Date_Formated'];
    paymentConditionID = json['Payment_Condition_ID'];
    shippingMethodID = json['Shipping_Method_ID'];
    shippingPeriod = json['Shipping_Period'];
    total =
        json['total'] != null ? double.parse(json['total'].toString()) : 0.0;
    vATPercent = json['VAT_Percent'] != null
        ? double.parse(json['VAT_Percent'].toString())
        : 0.0;
    discountValue = json['Discount_Value'] != null
        ? double.parse(json['Discount_Value'].toString())
        : 0.0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ID'] = iD;
    data['Code'] = code;
    data['Entry_Date_Formated'] = entryDateFormated;
    data['APP_Referance_Code'] = appReferanceCode;
    data['entryDate'] = entryDate?.toIso8601String();
    data['Curreny_ID'] = currenyID;
    data['Exchange_Rate'] = exchangeRate;
    data['Cost_Center_ID'] = costCenterID;
    data['Supplier_ID'] = supplierID;
    data['Supplier_Invoice_No'] = supplierInvoiceNo;
    data['Purchase_Receipt_ID'] = purchaseReceiptID;
    data['Qutation_ID'] = qutationID;
    data['Proforma_Invoice_ID'] = proformaInvoiceID;
    data['Purchase_Order_ID'] = purchaseOrderID;
    data['Purchase_Type'] = purchaseType;
    data['transactions_type'] = transactionsType;
    data['Store_ID'] = storeID;
    data['Notes'] = notes;
    data['ItemsCategory'] = itemsCategory;
    data['ddlItems'] = ddlItems;

    if (purchaseItems != null) {
      data['PurchaseItems'] = purchaseItems!.map((v) => v.toJson()).toList();
    }

    data['Offer_Period'] = offerPeriod;
    data['Offer_Ending_Date_Formated'] = offerEndingDateFormated;
    data['Payment_Condition_ID'] = paymentConditionID;
    data['Shipping_Method_ID'] = shippingMethodID;
    data['Shipping_Period'] = shippingPeriod;
    data['total'] = total;
    data['VAT_Percent'] = vATPercent;
    data['Discount_Value'] = discountValue;

    return data;
  }
}

class PurchaseItems {
  double? quantity;
  int? unitID;
  double? unitPrice;
  String? itemName;
  dynamic store;
  String? itemCode;
  String? expirationDateFromat;
  String? expiryPeriod;
  double? amountQuantity;
  int? purchaseItemID;
  int? purchaseID;
  int? storeID;
  int? itemID;
  double? unitPriceLC;
  double? unitCostWithExpencesCalculatedLC;
  double? unitCostWithExpencesLC;
  DateTime? expirationDate;
  dynamic remaining;
  String? batchNumber;
  String? serialNumber;
  double? rejectedQuantity;
  double? orderedQuantity;
  double? vATPercent;
  dynamic constantsDetails;
  dynamic constantsDetails1;
  dynamic items;
  List<ItemAttribute>? attribute;
  dynamic purchase;

  PurchaseItems({
    this.quantity,
    this.unitID,
    this.unitPrice,
    this.itemName,
    this.store,
    this.itemCode,
    this.expirationDateFromat,
    this.expiryPeriod,
    this.amountQuantity,
    this.purchaseItemID,
    this.purchaseID,
    this.storeID,
    this.itemID,
    this.unitPriceLC,
    this.unitCostWithExpencesCalculatedLC,
    this.unitCostWithExpencesLC,
    this.expirationDate,
    this.remaining,
    this.batchNumber,
    this.serialNumber,
    this.rejectedQuantity,
    this.orderedQuantity,
    this.vATPercent,
    this.constantsDetails,
    this.constantsDetails1,
    this.items,
    this.attribute,
    this.purchase,
  });

  PurchaseItems.fromJson(Map<String, dynamic> json) {
    quantity = json['Quantity'] != null
        ? double.parse(json['Quantity'].toString())
        : 0.0;
    unitID = json['Unit_ID'];
    unitPrice = json['Unit_Price'] != null
        ? double.parse(json['Unit_Price'].toString())
        : 0.0;
    itemName = json['Item_Name'];
    store = json['Store'];
    itemCode = json['Item_Code'];
    expirationDateFromat = json['ExpirationDateFromat'];
    expiryPeriod = json['Expiry_Period'];
    amountQuantity = json['Amount_Quantity'] != null
        ? double.parse(json['Amount_Quantity'].toString())
        : 0.0;
    purchaseItemID = json['Purchase_Item_ID'];
    purchaseID = json['Purchase_ID'];
    storeID = json['Store_ID'];
    itemID = json['Item_ID'];
    unitPriceLC = json['Unit_Price_LC'] != null
        ? double.parse(json['Unit_Price_LC'].toString())
        : 0.0;
    unitCostWithExpencesCalculatedLC =
        json['Unit_Cost_With_Expences_Calculated_LC'] != null
            ? double.parse(
                json['Unit_Cost_With_Expences_Calculated_LC'].toString())
            : 0.0;
    unitCostWithExpencesLC = json['Unit_Cost_With_Expences_LC'] != null
        ? double.parse(json['Unit_Cost_With_Expences_LC'].toString())
        : null;
    expirationDate = json['ExpirationDate'] != null
        ? DateTime.parse(json['ExpirationDate'])
        : null;
    remaining = json['Remaining'];
    batchNumber = json['Batch_Number'];
    serialNumber = json['Serial_Number'];
    rejectedQuantity = json['Rejected_Quantity'] != null
        ? double.parse(json['Rejected_Quantity'].toString())
        : null;
    orderedQuantity = json['OrderedQuantity'] != null
        ? double.parse(json['OrderedQuantity'].toString())
        : 0.0;
    vATPercent = json['VAT_Percent'] != null
        ? double.parse(json['VAT_Percent'].toString())
        : 0.0;
    constantsDetails = json['Constants_Details'];
    constantsDetails1 = json['Constants_Details1'];
    items = json['Items'];

    if (json['Attribute'] != null) {
      attribute = <ItemAttribute>[];
      json['Attribute'].forEach((v) {
        attribute!.add(ItemAttribute.fromJson(v));
      });
    }

    purchase = json['Purchase'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Quantity'] = quantity;
    data['Unit_ID'] = unitID;
    data['Unit_Price'] = unitPrice;
    data['Item_Name'] = itemName;
    data['Store'] = store;
    data['Item_Code'] = itemCode;
    data['ExpirationDateFromat'] = expirationDateFromat;
    data['Expiry_Period'] = expiryPeriod;
    data['Amount_Quantity'] = amountQuantity;
    data['Purchase_Item_ID'] = purchaseItemID;
    data['Purchase_ID'] = purchaseID;
    data['Store_ID'] = storeID;
    data['Item_ID'] = itemID;
    data['Unit_Price_LC'] = unitPriceLC;
    data['Unit_Cost_With_Expences_Calculated_LC'] =
        unitCostWithExpencesCalculatedLC;
    data['Unit_Cost_With_Expences_LC'] = unitCostWithExpencesLC;
    data['ExpirationDate'] = expirationDate?.toIso8601String();
    data['Remaining'] = remaining;
    data['Batch_Number'] = batchNumber;
    data['Serial_Number'] = serialNumber;
    data['Rejected_Quantity'] = rejectedQuantity;
    data['OrderedQuantity'] = orderedQuantity;
    data['VAT_Percent'] = vATPercent;
    data['Constants_Details'] = constantsDetails;
    data['Constants_Details1'] = constantsDetails1;
    data['Items'] = items;

    if (attribute != null) {
      data['Attribute'] = attribute!.map((v) => v.toJson()).toList();
    }

    data['Purchase'] = purchase;

    return data;
  }
}
