import 'package:inventory_application/base/lang/language_notifier.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/configration_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/return_invoice_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import '../theme/theme_notifier.dart';

class ApplicationProvider {
  static ApplicationProvider? _instance;
  static ApplicationProvider get instance {
    _instance ??= ApplicationProvider._init();
    return _instance!;
  }

  ApplicationProvider._init();

  List<SingleChildWidget> singleItems = [];
  List<SingleChildWidget> dependItems = [
    ChangeNotifierProvider(
      create: (context) => ThemeNotifier(),
    ),
    ChangeNotifierProvider(
      create: (context) => LanguageNotifier(),
    ),
    ChangeNotifierProvider(
      create: (context) => ProductController(),
    ),
    ChangeNotifierProvider(
      create: (context) => InvoiceController(),
    ),
    ChangeNotifierProvider(
      create: (context) => CategoryController(),
    ),
    ChangeNotifierProvider(
      create: (context) => AuthController(),
    ),
    ChangeNotifierProvider(
      create: (context) => CustomerController(),
    ),
    ChangeNotifierProvider(
      create: (context) => WarehouseController(),
    ),
    ChangeNotifierProvider(
      create: (context) => ReturnInvoiceController(),
    ),
    ChangeNotifierProvider(
      create: (context) => SaleInvoiceController(),
    ),
    ChangeNotifierProvider(
      create: (context) => ConfigrationController(),
    ),
    ChangeNotifierProvider(
      create: (context) => SalesmenController(),
    ),
  ];
  List<SingleChildWidget> uiChangesItems = [];
}
