import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:vector_math/vector_math.dart' as vector;
import '../../../models/warehouse_planner/warehouse_layout.dart';

/// Canvas مبسط لعرض المخطط للعامل
class SimpleWarehouseViewerCanvas extends StatefulWidget {
  final WarehouseLayout layout;
  final String? highlightedShelfId;
  final Function(String shelfId)? onShelfTap;

  const SimpleWarehouseViewerCanvas({
    Key? key,
    required this.layout,
    this.highlightedShelfId,
    this.onShelfTap,
  }) : super(key: key);

  @override
  State<SimpleWarehouseViewerCanvas> createState() =>
      _SimpleWarehouseViewerCanvasState();
}

class _SimpleWarehouseViewerCanvasState
    extends State<SimpleWarehouseViewerCanvas> {
  final TransformationController _transformationController =
      TransformationController();
  String? _hoveredShelfId;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fitToView();
    });
  }

  @override
  void didUpdateWidget(SimpleWarehouseViewerCanvas oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.layout != widget.layout) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _fitToView();
      });
    }
  }

  void _fitToView() {
    final layout = widget.layout;

    if (!mounted) return;

    // حساب المقياس باستخدام أبعاد المخطط الأساسية
    final context = this.context;
    final size = MediaQuery.of(context).size;
    final canvasWidth = size.width - 350; // عرض لوحة البحث
    final canvasHeight = size.height - 100; // ارتفاع AppBar

    // أبعاد المخطط الفعلية
    final layoutWidth = layout.width;
    final layoutHeight = layout.height;

    if (layoutWidth > 0 &&
        layoutHeight > 0 &&
        canvasWidth > 0 &&
        canvasHeight > 0) {
      // حساب المقياس لضمان ظهور المخطط كاملاً
      final scaleX = canvasWidth / layoutWidth;
      final scaleY = canvasHeight / layoutHeight;
      final scale = math.min(scaleX, scaleY) * 0.8; // هامش للرؤية

      // وضع المخطط في وسط الشاشة المتاحة
      final centerX = layoutWidth / 2;
      final centerY = layoutHeight / 2;

      final matrix = Matrix4.identity()
        ..translate(canvasWidth / 2, canvasHeight / 2)
        ..scale(scale)
        ..translate(-centerX, -centerY);

      _transformationController.value = matrix;
    }
  }

  vector.Vector2 _screenToWorld(Offset screenPoint) {
    final matrix = _transformationController.value;
    final invertedMatrix = Matrix4.inverted(matrix);
    final worldPoint = MatrixUtils.transformPoint(invertedMatrix, screenPoint);
    return vector.Vector2(worldPoint.dx, worldPoint.dy);
  }

  String? _hitTestShelf(vector.Vector2 worldPoint) {
    for (final shelf in widget.layout.shelves) {
      final center = vector.Vector2(
        shelf.position.x + shelf.width / 2,
        shelf.position.y + shelf.depth / 2,
      );

      final radians = -shelf.rotation * math.pi / 180;
      final cos = math.cos(radians);
      final sin = math.sin(radians);

      final translated = worldPoint - center;
      final rotated = vector.Vector2(
        translated.x * cos - translated.y * sin,
        translated.x * sin + translated.y * cos,
      );

      if (rotated.x.abs() <= shelf.width / 2 &&
          rotated.y.abs() <= shelf.depth / 2) {
        return shelf.id;
      }
    }
    return null;
  }

  void _onHover(PointerEvent event) {
    final worldPoint = _screenToWorld(event.localPosition);
    final shelfId = _hitTestShelf(worldPoint);

    if (shelfId != _hoveredShelfId) {
      setState(() {
        _hoveredShelfId = shelfId;
      });
    }
  }

  void _onTap(TapDownDetails details) {
    final worldPoint = _screenToWorld(details.localPosition);
    final shelfId = _hitTestShelf(worldPoint);

    if (shelfId != null && widget.onShelfTap != null) {
      widget.onShelfTap!(shelfId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return InteractiveViewer(
      transformationController: _transformationController,
      boundaryMargin: const EdgeInsets.all(100),
      minScale: 0.1,
      maxScale: 5.0,
      child: GestureDetector(
        onTapDown: _onTap,
        child: MouseRegion(
          onHover: _onHover,
          child: RepaintBoundary(
            child: LayoutBuilder(
              builder: (context, constraints) {
                return CustomPaint(
                  size: Size(constraints.maxWidth, constraints.maxHeight),
                  painter: SimpleWarehousePainter(
                    layout: widget.layout,
                    highlightedShelfId: widget.highlightedShelfId,
                    hoveredShelfId: _hoveredShelfId,
                  ),
                  willChange: true,
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _transformationController.dispose();
    super.dispose();
  }
}

/// Painter مبسط للمخطط
class SimpleWarehousePainter extends CustomPainter {
  final WarehouseLayout layout;
  final String? highlightedShelfId;
  final String? hoveredShelfId;

  SimpleWarehousePainter({
    required this.layout,
    this.highlightedShelfId,
    this.hoveredShelfId,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // رسم خلفية الكامل للشاشة
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = Colors.grey.shade50,
    );

    // الشبكة في كامل المساحة
    _drawGrid(canvas, size);

    // الجدران
    _drawWalls(canvas);

    // المداخل
    _drawEntrances(canvas);

    // الخزائن
    _drawShelves(canvas);

    // رسم حدود المخطط الفعلية
    _drawLayoutBounds(canvas);

    // معلومات
    _drawInfo(canvas, size);
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = Colors.grey.shade300.withOpacity(0.2)
      ..strokeWidth = 0.5;

    const gridSize = 50.0; // 50 سم

    // خطوط عمودية
    for (double x = 0; x <= size.width; x += gridSize) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }

    // خطوط أفقية
    for (double y = 0; y <= size.height; y += gridSize) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
  }

  void _drawLayoutBounds(Canvas canvas) {
    // رسم حدود المخطط الفعلية
    final layoutRect = Rect.fromLTWH(0, 0, layout.width, layout.height);

    // خلفية بيضاء للمخطط
    canvas.drawRect(
      layoutRect,
      Paint()..color = Colors.white.withOpacity(0.8),
    );

    // حدود المخطط
    canvas.drawRect(
      layoutRect,
      Paint()
        ..color = Colors.blue.shade600
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3,
    );

    // نص الأبعاد
    final dimensionsText =
        '${layout.width.toInt()} × ${layout.height.toInt()} سم';
    final textSpan = TextSpan(
      text: dimensionsText,
      style: TextStyle(
        color: Colors.blue.shade800,
        fontSize: 14,
        fontWeight: FontWeight.bold,
      ),
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.rtl,
    );

    textPainter.layout();

    // رسم خلفية للنص
    final textBg = Rect.fromLTWH(
      layout.width - textPainter.width - 20,
      layout.height - textPainter.height - 10,
      textPainter.width + 16,
      textPainter.height + 8,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(textBg, const Radius.circular(4)),
      Paint()..color = Colors.white.withOpacity(0.9),
    );

    textPainter.paint(
      canvas,
      Offset(layout.width - textPainter.width - 12,
          layout.height - textPainter.height - 6),
    );
  }

  void _drawWalls(Canvas canvas) {
    for (final wall in layout.walls) {
      if (wall.points.length < 2) continue;

      final wallPaint = Paint()
        ..color = wall.color
        ..strokeWidth = wall.thickness
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      final path = Path();
      path.moveTo(wall.points.first.x, wall.points.first.y);

      for (int i = 1; i < wall.points.length; i++) {
        path.lineTo(wall.points[i].x, wall.points[i].y);
      }

      canvas.drawPath(path, wallPaint);
    }
  }

  void _drawEntrances(Canvas canvas) {
    for (final entrance in layout.entrances) {
      final wall = layout.walls.firstWhere(
        (w) => w.id == entrance.wallId,
        orElse: () => layout.walls.first,
      );

      if (wall.points.length < 2) continue;

      final startPoint = wall.points[0];
      final endPoint = wall.points[1];
      final direction = endPoint - startPoint;
      final segmentLength = direction.length;

      if (segmentLength == 0) continue;

      final normalizedDirection = direction / segmentLength;
      final entranceStart =
          startPoint + normalizedDirection * entrance.startDistance;
      final entranceEnd = startPoint +
          normalizedDirection * (entrance.startDistance + entrance.width);

      final entrancePaint = Paint()
        ..color = Colors.green.shade600
        ..strokeWidth = wall.thickness + 4
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      canvas.drawLine(
        Offset(entranceStart.x, entranceStart.y),
        Offset(entranceEnd.x, entranceEnd.y),
        entrancePaint,
      );
    }
  }

  void _drawShelves(Canvas canvas) {
    for (final shelf in layout.shelves) {
      _drawShelf(canvas, shelf);
    }
  }

  void _drawShelf(Canvas canvas, shelf) {
    final isHighlighted = shelf.id == highlightedShelfId;
    final isHovered = shelf.id == hoveredShelfId;

    canvas.save();

    final center = Offset(
      shelf.position.x + shelf.width / 2,
      shelf.position.y + shelf.depth / 2,
    );
    canvas.translate(center.dx, center.dy);

    if (shelf.rotation != 0) {
      canvas.rotate(shelf.rotation * math.pi / 180);
    }

    // الظل
    if (isHighlighted || isHovered) {
      final shadowPaint = Paint()
        ..color = (isHighlighted ? Colors.green : Colors.blue).withOpacity(0.3)
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 8);

      canvas.drawRect(
        Rect.fromCenter(
          center: Offset.zero,
          width: shelf.width + 20,
          height: shelf.depth + 20,
        ),
        shadowPaint,
      );
    }

    // الخزانة
    final shelfPaint = Paint()
      ..color = isHighlighted
          ? Colors.green.shade100
          : isHovered
              ? Colors.blue.shade100
              : Colors.brown.shade200
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = isHighlighted
          ? Colors.green.shade600
          : isHovered
              ? Colors.blue.shade600
              : Colors.brown.shade400
      ..strokeWidth = isHighlighted ? 3 : 2
      ..style = PaintingStyle.stroke;

    final shelfRect = Rect.fromCenter(
      center: Offset.zero,
      width: shelf.width,
      height: shelf.depth,
    );

    canvas.drawRect(shelfRect, shelfPaint);
    canvas.drawRect(shelfRect, borderPaint);

    // التسمية
    final textStyle = TextStyle(
      color: isHighlighted ? Colors.green.shade800 : Colors.brown.shade700,
      fontSize: 12,
      fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
    );

    final textSpan = TextSpan(
      text: shelf.name,
      style: textStyle,
    );

    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.rtl,
    );

    textPainter.layout();

    final textOffset = Offset(
      -textPainter.width / 2,
      -textPainter.height / 2,
    );

    final bgPaint = Paint()..color = Colors.white.withOpacity(0.9);

    canvas.drawRect(
      Rect.fromLTWH(
        textOffset.dx - 4,
        textOffset.dy - 2,
        textPainter.width + 8,
        textPainter.height + 4,
      ),
      bgPaint,
    );

    textPainter.paint(canvas, textOffset);

    canvas.restore();
  }

  void _drawInfo(Canvas canvas, Size size) {
    // رسم اسم المخطط
    final layoutNameSpan = TextSpan(
      text: layout.name,
      style: TextStyle(
        color: Colors.blue.shade800,
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );

    final layoutNamePainter = TextPainter(
      text: layoutNameSpan,
      textDirection: TextDirection.rtl,
    );

    layoutNamePainter.layout();

    // رسم خلفية للنص في الزاوية العلوية اليسرى
    final bgRect = Rect.fromLTWH(
      10,
      10,
      layoutNamePainter.width + 20,
      layoutNamePainter.height + 10,
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(bgRect, const Radius.circular(8)),
      Paint()..color = Colors.white.withOpacity(0.95),
    );

    canvas.drawRRect(
      RRect.fromRectAndRadius(bgRect, const Radius.circular(8)),
      Paint()
        ..color = Colors.blue.shade300
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5,
    );

    layoutNamePainter.paint(
      canvas,
      const Offset(20, 15),
    );

    // رسم تعليمات في الزاوية السفلية
    final instructions = [
      'انقر على الخزانة لعرض التفاصيل',
      'استخدم البحث للعثور على الأصناف',
      'اسحب للتنقل - العجلة للتكبير',
    ];

    for (int i = 0; i < instructions.length; i++) {
      final textSpan = TextSpan(
        text: instructions[i],
        style: TextStyle(
          color: Colors.grey.shade600,
          fontSize: 11,
        ),
      );

      final textPainter = TextPainter(
        text: textSpan,
        textDirection: TextDirection.rtl,
      );

      textPainter.layout();

      // خلفية للتعليمات
      final instructionBg = Rect.fromLTWH(
        10,
        size.height - 60 + i * 16,
        textPainter.width + 16,
        textPainter.height + 4,
      );

      canvas.drawRRect(
        RRect.fromRectAndRadius(instructionBg, const Radius.circular(4)),
        Paint()..color = Colors.white.withOpacity(0.8),
      );

      textPainter.paint(
        canvas,
        Offset(18, size.height - 58 + i * 16),
      );
    }
  }

  @override
  bool shouldRepaint(SimpleWarehousePainter oldDelegate) {
    return oldDelegate.layout != layout ||
        oldDelegate.highlightedShelfId != highlightedShelfId ||
        oldDelegate.hoveredShelfId != hoveredShelfId;
  }
}
