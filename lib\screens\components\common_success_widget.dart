import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import '../home/<USER>';

class CommonSuccessWidget extends StatelessWidget {
  const CommonSuccessWidget(
      {super.key,
      required this.onClickOk,
      required this.title,
      required this.content});
  final String title;
  final String content;
  final Function onClickOk;
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      // selectedBottomNavbarItem: BottomNavbarItems.none,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset('assets/images/base_images/done.png'),
            const SizedBox(height: 20),
            Text(
              title,
              style: TextStyle(
                color: context.surfaceColor,
                fontSize: 15,
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 5),
            Text(
              content,
              style: TextStyle(
                color: context.surfaceColor,
                fontSize: 15,
                height: 1,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 20),
            SizedBox(
              width: context.width / 2,
              child: CommonMaterialButton(
                backgroundColor: context.onSecondary,
                borderColor: context.onSecondary,
                label: 'متابعة',
                textColor: Colors.black,
                height: 20,
                onPressed: () {
                  onClickOk();
                  // Navigator.of(context).pop();
                },
              ),
            )
          ],
        ),
      ),
    );
  }
}
