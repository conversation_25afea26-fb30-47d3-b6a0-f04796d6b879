import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/return_invoice_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/returns%20invoice/create_return_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/sale%20invoice/create_sale_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/pos/pos_screen.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/services/printer_service.dart';

import 'widgets/invoice_details_base_info_widget.dart';
import 'widgets/invoice_details_items_widget.dart';

// ignore: must_be_immutable
class InoviceDetailsForLocalPage extends StatefulWidget {
  final int id;
  String? status;
  Function(int)? onDelete;
  InoviceDetailsForLocalPage(
      {super.key, required this.id, this.status, this.onDelete});

  @override
  State<InoviceDetailsForLocalPage> createState() =>
      _InoviceDetailsForLocalPageState();
}

class _InoviceDetailsForLocalPageState
    extends State<InoviceDetailsForLocalPage> {
  List<InvoiceDtoWithLiteId> invoices = [];
  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: SingleChildScrollView(
        child: Column(
          children: [
            Container(
              width: context.width - 10,
              color: context.backgroundColor,
              child: CommonHeader(
                icon: Icons.blinds_closed,
                title: T("Details"),
              ),
            ),
            FutureBuilder<InvoiceDto?>(
              future: Provider.of<InvoiceController>(context)
                  .getInoviceById(widget.id),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Text('Error: ${snapshot.error}');
                } else if (snapshot.hasData && snapshot.data != null) {
                  final data = snapshot.data!;

                  return Column(
                    children: [
                      InvoiceDetailsBaseInfoWidget(data: data),
                      InvoiceDetailsItemsWidget(data: data),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                CommonMaterialButton(
                                  width: context.width / 2 - 25,
                                  label: "تكرار",
                                  backgroundColor: context.backgroundColor,
                                  borderRadius: 5,
                                  textColor: context.colors.scrim,
                                  borderColor: Colors.amber,
                                  textStyle: const TextStyle(),
                                  onPressed: () async {
                                    if (snapshot.data?.invoiceCode != null) {
                                      if (snapshot.data!.invoiceCode!
                                          .startsWith("SI")) {
                                        Provider.of<SaleInvoiceController>(
                                                context,
                                                listen: false)
                                            .invoice = snapshot.data!;
                                        Provider.of<SaleInvoiceController>(
                                                    context,
                                                    listen: false)
                                                .selectedSaleInvoiceProduct =
                                            snapshot.data?.salesItems ?? [];
                                        var result = await Provider.of<
                                                    SaleInvoiceController>(
                                                context,
                                                listen: false)
                                            .repeatSaleInvoice();
                                        if (result) {
                                          successSnackBar(
                                              message: T(
                                                  "The operation has been saved"));
                                        } else {
                                          errorSnackBar(
                                              message:
                                                  "لم يتم حفظ الفاتورة يرجى المحاولة لاحقاً");
                                        }
                                      } else {
                                        Provider.of<ReturnInvoiceController>(
                                                context,
                                                listen: false)
                                            .invoice = snapshot.data!;
                                        Provider.of<ReturnInvoiceController>(
                                                    context,
                                                    listen: false)
                                                .selectedReturnInvoiceProduct =
                                            snapshot.data?.salesItems ?? [];
                                        Provider.of<ReturnInvoiceController>(
                                                context,
                                                listen: false)
                                            .notifyListenersFuntion();
                                        Navigator.of(context).push(
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                const CreateReturnInvoiceScreen(),
                                          ),
                                        );
                                        var result = await Provider.of<
                                                    ReturnInvoiceController>(
                                                context,
                                                listen: false)
                                            .repeatReturnInvoice();
                                        if (result) {
                                          successSnackBar(
                                              message: T(
                                                  "The operation has been saved"));
                                        } else {
                                          errorSnackBar(
                                              message:
                                                  "لم يتم حفظ الفاتورة يرجى المحاولة لاحقاً");
                                        }
                                      }
                                    }
                                  },
                                ),
                                CommonMaterialButton(
                                  width: context.width / 2 - 25,
                                  label: "تعديل",
                                  backgroundColor: context.backgroundColor,
                                  borderRadius: 5,
                                  textColor: context.colors.scrim,
                                  borderColor: context.colors.secondary,
                                  textStyle: const TextStyle(),
                                  onPressed: () {
                                    if (snapshot.data?.appReferanceCode !=
                                        null) {
                                      if (snapshot.data!.appReferanceCode!
                                          .startsWith("SI")) {
                                        Provider.of<SaleInvoiceController>(
                                                context,
                                                listen: false)
                                            .invoice = snapshot.data!;
                                        Provider.of<SaleInvoiceController>(
                                                    context,
                                                    listen: false)
                                                .selectedSaleInvoiceProduct =
                                            snapshot.data?.salesItems ?? [];
                                        Provider.of<SaleInvoiceController>(
                                                context,
                                                listen: false)
                                            .notifyListenersFuntion();

                                        final screenWidth = context.width;
                                        final isDesktop = screenWidth >
                                            800; // Threshold for desktop vs mobile

                                        if (isDesktop) {
                                          // Navigate to POS screen for desktop
                                          Navigator.of(context).push(
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  const POSScreen(
                                                      isEditing: true),
                                            ),
                                          );
                                        } else {
                                          // Navigate to sale invoice screen for mobile
                                          Navigator.of(context).push(
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  const CreateSaleInvoiceScreen(),
                                            ),
                                          );
                                        }
                                      } else {
                                        Navigator.of(context).push(
                                          MaterialPageRoute(
                                            builder: (context) =>
                                                const CreateReturnInvoiceScreen(),
                                          ),
                                        );
                                      }
                                    }
                                  },
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                CommonMaterialButton(
                                  width: context.width / 2 - 25,
                                  label: "طباعة",
                                  backgroundColor: context.backgroundColor,
                                  borderRadius: 5,
                                  textColor: context.colors.scrim,
                                  borderColor: Colors.green,
                                  textStyle: const TextStyle(),
                                  onPressed: () async {
                                    try {
                                      await PrinterService.printInvoice(
                                          snapshot.data!, context);
                                    } catch (e) {
                                      errorSnackBar(
                                          message:
                                              'حدث خطأ أثناء طباعة الفاتورة');
                                    }
                                  },
                                ),
                                CommonMaterialButton(
                                  width: context.width / 2 - 25,
                                  label: "حذف",
                                  backgroundColor: context.backgroundColor,
                                  borderRadius: 5,
                                  textColor: context.colors.scrim,
                                  borderColor: context.colors.error,
                                  textStyle: const TextStyle(),
                                  onPressed: () async {
                                    // bool result =
                                    //     await Provider.of<InvoiceController>(
                                    //             context,
                                    //             listen: false)
                                    //         .deleteSyncInvoice(widget.id);
                                    // if (result) {
                                    //   setState(() {
                                    //     widget.status = "deleted";
                                    //   });
                                    //   successSnackBar(
                                    //       message:
                                    //           "Invoice deleted successfully");
                                    // } else {
                                    //   errorSnackBar(
                                    //       message: "Failed to delete invoice");
                                    // }
                                  },
                                )
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }

                return const Text("No data available.");
              },
            )
          ],
        ),
      ),
    );
  }
}
