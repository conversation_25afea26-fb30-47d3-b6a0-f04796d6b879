import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class LanguageNotifier extends ChangeNotifier {
  Locale currentLang = const Locale('ar');
  static int currentLangId = 2;
  int langId = 2;
  static String languageVal = "ar";
  void changeLanguage(BuildContext context, String languageCode) {
    print("Changing language to: $languageCode");
    languageVal = languageCode;
    Locale newLocale =
        Locale(languageVal); // This should correspond to your supported locales
    print("Setting locale to: ${newLocale.languageCode}");
    context.setLocale(newLocale);
    notifyListeners();
  }

  updateCurrentLangId() {
    switch (currentLang.languageCode) {
      case 'ar':
        currentLangId = 2;
        langId = 2;
        break;

      case 'tr':
        currentLangId = 3;
        langId = 3;
        break;

      default:
        currentLangId = 1;
        langId = 1;
        break;
    }
  }
}
