import 'package:inventory_application/models/model/invoice_model.dart';
import 'package:intl/intl.dart';

class MonthlySalesReportDTO {
  final DateTime fromDate;
  final DateTime toDate;
  final double totalSales;
  final double totalDiscount;
  final double netSales;
  final int invoiceCount;
  final List<InvoiceModel> invoices;

  MonthlySalesReportDTO({
    required this.fromDate,
    required this.toDate,
    required this.totalSales,
    required this.totalDiscount,
    required this.netSales,
    required this.invoiceCount,
    required this.invoices,
  });

  // Calculate sales by month
  Map<String, double> calculateSalesByMonth() {
    final Map<String, double> salesByMonth = {};

    for (var invoice in invoices) {
      if (invoice.entryDate != null) {
        final monthKey = DateFormat('yyyy-MM').format(invoice.entryDate!);

        if (salesByMonth.containsKey(monthKey)) {
          salesByMonth[monthKey] =
              salesByMonth[monthKey]! + (invoice.total ?? 0);
        } else {
          salesByMonth[monthKey] = invoice.total ?? 0;
        }
      }
    }

    return salesByMonth;
  }

  // Calculate sales by customer
  Map<String, double> calculateSalesByCustomer() {
    final Map<String, double> salesByCustomer = {};

    for (var invoice in invoices) {
      final customerName = invoice.customerName ?? 'Unknown';

      if (salesByCustomer.containsKey(customerName)) {
        salesByCustomer[customerName] =
            salesByCustomer[customerName]! + (invoice.total ?? 0);
      } else {
        salesByCustomer[customerName] = invoice.total ?? 0;
      }
    }

    return salesByCustomer;
  }

  // Calculate sales by warehouse
  Map<String, double> calculateSalesByWarehouse() {
    final Map<String, double> salesByWarehouse = {};

    for (var invoice in invoices) {
      final warehouseName = invoice.storeName ?? 'Unknown';

      if (salesByWarehouse.containsKey(warehouseName)) {
        salesByWarehouse[warehouseName] =
            salesByWarehouse[warehouseName]! + (invoice.total ?? 0);
      } else {
        salesByWarehouse[warehouseName] = invoice.total ?? 0;
      }
    }

    return salesByWarehouse;
  }

  // Calculate sales by payment method
  // Map<String, double> calculateSalesByPaymentMethod() {
  //   final Map<String, double> salesByPaymentMethod = {};

  //   for (var invoice in invoices) {
  //     final paymentMethod = invoice.paymentMethod ?? 'Unknown';

  //     if (salesByPaymentMethod.containsKey(paymentMethod)) {
  //       salesByPaymentMethod[paymentMethod] = salesByPaymentMethod[paymentMethod]! + (invoice.total ?? 0);
  //     } else {
  //       salesByPaymentMethod[paymentMethod] = invoice.total ?? 0;
  //     }
  //   }

  //   return salesByPaymentMethod;
  // }

  // Get monthly growth rate
  Map<String, double> calculateMonthlyGrowthRate() {
    final Map<String, double> salesByMonth = calculateSalesByMonth();
    final Map<String, double> growthRates = {};

    if (salesByMonth.length <= 1) {
      return growthRates; // Not enough data for growth calculation
    }

    final sortedMonths = salesByMonth.keys.toList()..sort();

    for (int i = 1; i < sortedMonths.length; i++) {
      final currentMonth = sortedMonths[i];
      final previousMonth = sortedMonths[i - 1];

      final currentSales = salesByMonth[currentMonth]!;
      final previousSales = salesByMonth[previousMonth]!;

      if (previousSales > 0) {
        final growthRate =
            ((currentSales - previousSales) / previousSales) * 100;
        growthRates[currentMonth] = growthRate;
      } else {
        growthRates[currentMonth] =
            100; // If previous month had zero sales, growth is 100%
      }
    }

    return growthRates;
  }
}
