import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

import 'common_combobox.dart';

class RadioList extends StatefulWidget {
  const RadioList({
    super.key,
    required this.data,
    this.selectedValue,
    required this.onSelect,
  });

  final List<ComboBoxDataModel> data;
  final int? selectedValue;
  final Function(int id, String name) onSelect;

  @override
  State<RadioList> createState() => _RadioListState();
}

class _RadioListState extends State<RadioList> {
  @override
  Widget build(BuildContext context) {
    var data = widget.data;
    return SizedBox(
      height: 30,
      child: ListView.builder(
        scrollDirection: Axis.horizontal, // Ensure horizontal scrolling
        itemCount: data.length,
        itemBuilder: (context, index) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Radio<int>(
                value: data[index].id,
                groupValue: widget.selectedValue,
                activeColor: context.primaryColor,
                onChanged: (val) {
                  widget.onSelect(val ?? 0, data[index].name);
                },
              ),
              Text(data[index].name), // Label for each radio button
              const SizedBox(width: 20), // Add some spacing between items
            ],
          );
        },
      ),
    );
  }
}
