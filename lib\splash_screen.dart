import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/branch_controller.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/device_setup_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/unit_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/controllers/barcode_controller.dart';
import 'package:inventory_application/helpers/accounts_voucher_counter.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/helpers/purchase_invoice_counter.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/models/model/purchase_invoice_model.dart';
import 'package:inventory_application/screens/auth/SignIn_screen.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/setting/server_settings_screen.dart';
import 'package:inventory_application/services/authentication_service.dart';
import 'package:provider/provider.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    // add post frame callback to wait for the context to be initialized
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      init();
      await Future.delayed(
        const Duration(seconds: 8),
        () async {
          if (await AppController.checkFirstTime()) {
            // ignore: use_build_context_synchronously
            Navigator.of(context).pushReplacement(
              MaterialPageRoute(
                builder: (context) => const ServerSettingsScreen(
                  isFirstTime: true,
                ),
              ),
            );
            return;
          } else {
            if (AppController.isAuth) {
              // ignore: use_build_context_synchronously
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const HomeScreen(),
                ),
              );

              return;
            } else {
              // ignore: use_build_context_synchronously
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const SignInScreen(),
                ),
              );
              return;
            }
          }
        },
      );
    });
    super.initState();
  }

  void init() async {
    // await AuthController.tryAutoLogin();

    AppController.getBaseUrlFromShared();
    AppController.getEcommerceBaseUrlFromShared();
    AppController.getSkylanAccountingHelperURLFromShared();
    AppController.getIsUsingEcommerceFromShared();
    await context.read<AppController>().getDeviceDetails();
    await context.read<DeviceSetupController>().deviceIdSetup();
    // First fetch branches (will load from local DB, or from server if local is empty)
    await context.read<BranchController>().fetchBranches();

    // If there's internet connection, sync branches from server
    if (AppController.isThereConnection == true) {
      await context.read<BranchController>().fetchBranchesFromServer();
    }
    if (AppController.isThereConnection == true) {
      await context
          .read<AuthenticationService>()
          .fetchRolesWithPermissionsFromServer();
    }

    await CounterGenerator.getInvoicesCounterFromServer();
    await InventoryOperationCounterGenerator
        .getInventoryOperationCounterFromServer();
    await PurchaseCounterGenerator.getPurchaseInvoiceCounterFromServer();
    await AccountsVoucherCounterGenerator.getAccountsVoucherCounterFromServer();
    await CounterGenerator.initializeSaleInvoiceCounter();
    await CounterGenerator.initializeReturnInvoiceCounter();
    await CounterGenerator.initializeOrderInvoiceCounter();
    await CounterGenerator.initializeDeliveryNoteCounter();
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.ItemsTransfer);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.Outgoing);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.Incoming);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.DamagedExpired);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.Stocktaking);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.TransferToSection);
    await InventoryOperationCounterGenerator.initializeCounterByType(
        InventoryOperationType.TransferToSectionRequset);
    await PurchaseCounterGenerator.initializeCounterByType(
        PurchaseType.Invoice);
    await AccountsVoucherCounterGenerator.initializeCounterByType(
        AccountsVoucharType.ReceiptVoucher);
    await AccountsVoucherCounterGenerator.initializeCounterByType(
        AccountsVoucharType.PaymentVoucher);
    // Initialize barcode settings
    await context.read<BarcodeController>().initialize();

    await context.read<AuthenticationService>().fetchRolesWithPermissions();
    // ignore: use_build_context_synchronously
    await context.read<CategoryController>().fetchCategories();
    // ignore: use_build_context_synchronously
    await context.read<WarehouseController>().fetchWarehouses();
    // ignore: use_build_context_synchronously
    await context.read<UnitController>().fetchUnits();

    // ignore: use_build_context_synchronously
    await context.read<SalesmenController>().fetchSalesmen();
    // ignore: use_build_context_synchronously
    await context.read<CustomerController>().getCustomers();
    // await context.read<ProductController>().fetchProduct();
    // ignore: use_build_context_synchronously
    await context.read<ProductController>().getProductCount();
    // ignore: use_build_context_synchronously
    await context.read<ProductController>().fetchProductAfterCertineId();
    // ignore: use_build_context_synchronously
    await context.read<ProductController>().getItems();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  // Future<bool> checkFirstTime() async {
  //   var isFirstTime = await AppController.checkFirstTime();
  //   return isFirstTime;
  //   if (isFirstTime) {
  //     Navigator.pushReplacement(
  //       navigatorKey.currentContext!,
  //       MaterialPageRoute(
  //         builder: (context) => const EnterAddressScreen(),
  //       ),
  //     );
  //   } else {
  //     Navigator.pushReplacement(
  //       navigatorKey.currentContext!,
  //       MaterialPageRoute(
  //         builder: (context) => const MainScreen(),
  //       ),
  //     );
  //   }
  // }
}
