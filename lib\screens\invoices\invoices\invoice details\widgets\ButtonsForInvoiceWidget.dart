import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:inventory_application/screens/invoices/invoices/invoice%20details/inovice_details_page.dart';
import 'package:inventory_application/screens/invoices/invoices/returns%20invoice/create_return_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/sale%20invoice/create_sale_invoice_screen.dart';
import 'package:inventory_application/services/printer_service.dart';
import 'package:provider/provider.dart';

class ButtonsForInvoice extends StatelessWidget {
  const ButtonsForInvoice({
    super.key,
    required this.widget,
    required this.data,
  });

  final InoviceDetailsPage widget;
  final InvoiceDto data;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 20,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonMaterialButton(
                width: context.width / 2 - 25,
                label: "تكرار",
                backgroundColor: context.backgroundColor,
                borderRadius: 5,
                textColor: context.colors.scrim,
                borderColor: Colors.amber,
                textStyle: const TextStyle(),
                onPressed: () {},
              ),
              CommonMaterialButton(
                width: context.width / 2 - 25,
                label: "تعديل",
                backgroundColor: context.backgroundColor,
                borderRadius: 5,
                textColor: context.colors.scrim,
                borderColor: context.colors.secondary,
                textStyle: const TextStyle(),
                onPressed: () {
                  if (widget.model.invoiceCode != null) {
                    if (widget.model.invoiceCode!.startsWith("SI")) {
                      Provider.of<SaleInvoiceController>(context, listen: false)
                          .invoice = widget.model;
                      Provider.of<SaleInvoiceController>(context, listen: false)
                              .selectedSaleInvoiceProduct =
                          widget.model.salesItems ?? [];
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const CreateSaleInvoiceScreen(),
                        ),
                      );
                    } else {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) =>
                              const CreateReturnInvoiceScreen(),
                        ),
                      );
                    }
                  }
                },
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonMaterialButton(
                width: context.width / 2 - 25,
                label: "طباعة",
                backgroundColor: context.backgroundColor,
                borderRadius: 5,
                textColor: context.colors.scrim,
                borderColor: Colors.green,
                textStyle: const TextStyle(),
                onPressed: () async {
                  final scaffoldMessenger = ScaffoldMessenger.of(context);
                  final currentContext = context;

                  try {
                    // Print directly using PrinterService
                    await PrinterService.printInvoice(
                      data,
                      currentContext,
                    );
                  } catch (e) {
                    scaffoldMessenger.showSnackBar(
                      SnackBar(
                        content: Text('حدث خطأ أثناء طباعة الفاتورة: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
              ),
              CommonMaterialButton(
                width: context.width / 2 - 25,
                label: "حذف",
                backgroundColor: context.backgroundColor,
                borderRadius: 5,
                textColor: context.colors.scrim,
                borderColor: context.colors.error,
                textStyle: const TextStyle(),
                onPressed: () {},
              ),
            ],
          ),
        ],
      ),
    );
  }
}
