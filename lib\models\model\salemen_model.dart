import 'package:inventory_application/models/dto/response.dart';

class SalesmenRequestDTO {
  String? salesman_ID;
  String? mobile;
  String? supplier_Type_ID;

  DataTableParameters? dataTableParameters;

  SalesmenRequestDTO(
      {this.salesman_ID,
      this.mobile,
      this.supplier_Type_ID,
      this.dataTableParameters});

  SalesmenRequestDTO.fromJson(Map<String, dynamic> json) {
    salesman_ID = json['salesman_ID'];
    mobile = json['Mobile'];
    supplier_Type_ID = json['supplier_Type_ID'];

    dataTableParameters = json['dataTableParameters'] != null
        ? new DataTableParameters.fromJson(json['dataTableParameters'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['salesman_ID'] = this.salesman_ID;
    data['Mobile'] = this.mobile;
    data['supplier_Type_ID'] = this.supplier_Type_ID;

    if (this.dataTableParameters != null) {
      data['dataTableParameters'] = this.dataTableParameters!.toJson();
    }
    return data;
  }
}
