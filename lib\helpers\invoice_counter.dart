import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/models/dto/counter/device_counter_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';

class CounterGenerator {
  // #region Server Sync
  //---------------------------------------------------------
  static Future<int> getCounterByType({required SalesType type}) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());

    return currentCounter;
  }

//---------------------------------------------------------
  static Future<bool> setCounterByType(
      {required SalesType type, required int counter}) async {
    if (counter == 0) {
      if (type == SalesType.Invoice) {
        await initializeSaleInvoiceCounter();
      } else if (type == SalesType.RetrunInvoice)
        // ignore: curly_braces_in_flow_control_structures
        await initializeReturnInvoiceCounter();
      else if (type == SalesType.Order)
        // ignore: curly_braces_in_flow_control_structures
        await initializeOrderInvoiceCounter();
      else if (type == SalesType.DeliveryNote)
        // ignore: curly_braces_in_flow_control_structures
        await initializeDeliveryNoteCounter();
      return false;
    }
    var oldCounter = await getCounterByType(type: type);
    if (oldCounter > counter) {
      await setInvoicesCounterInServer();
      return false;
    }
    counter = counter++;
    LocaleManager.instance.removeKeyByStringKey(type.toString());
    await LocaleManager.instance
        .setStringValueByStringKey(type.toString(), counter.toString());

    return true;
  }

//------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> getInvoicesCounterFromServer() async {
    try {
      var types = [
        SalesType.Invoice,
        SalesType.RetrunInvoice,
        SalesType.Order,
        SalesType.DeliveryNote,
      ];

      String deviceId = LocaleManager.instance
          .getStringValue(PreferencesKeys.DeviceCode)
          .toString();
      for (var type in types) {
        var model = ApiDeviceCounterDTO(
          counterType: type.index,
          deviceCode: deviceId,
          referenceType: ApiDeviceCounterTypeEnum.sales,
        );
        var url = '/Counter/GetCodeByTypes';
        var result = await Api.getWithBody(action: url, body: model.toJson());
        if (result?.isSuccess != null && result?.isSuccess == true) {
          setCounterByType(type: type, counter: result?.data ?? 0);
        } else {
          await initializeReturnInvoiceCounter();
          await initializeSaleInvoiceCounter();
          await initializeOrderInvoiceCounter();

          await initializeDeliveryNoteCounter();
        }
      }
    } catch (e) {
      print(e);
    }
  }

//------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> setInvoicesCounterInServer() async {
    try {
      var types = [
        SalesType.Invoice,
        SalesType.RetrunInvoice,
        SalesType.Order,
        SalesType.DeliveryNote,
      ];
      String deviceId = LocaleManager.instance
          .getStringValue(PreferencesKeys.DeviceCode)
          .toString();
      for (var type in types) {
        var counter = await CounterGenerator.getCounterByType(type: type);
        var model = ApiDeviceCounterDTO(
          counter: counter,
          counterType: type.index,
          deviceCode: deviceId,
          referenceType: ApiDeviceCounterTypeEnum.sales,
        );
        var url = '/Counter/SetCodeByTypes';
        await Api.post(action: url, body: model.toJson());
      }
    } catch (e) {
      print(e);
    }
  }

  // #endregion

  //------------------------------------------------------------------------------------------

  // #region Sale invoice

  static Future<void> initializeSaleInvoiceCounter() async {
    // LocaleManager.instance.removeKey(PreferencesKeys.SaleInvoice);
    if (!await LocaleManager.instance
        .checkKeyByStringKey(SalesType.Invoice.toString())) {
      await LocaleManager.instance.setStringValueByStringKey(
          SalesType.Invoice.toString(), 1.toString());
      // Initial counter value
    }
  }

//--------------------------------------------------------------------------
  static Future<String> getSaleInvoiceCurrentCounter() async {
    var formattedCounter = "1";
    var currentCounter = LocaleManager.instance
        .getStringValueByStringKey(SalesType.Invoice.toString())
        .toString();

    if (currentCounter.isNotEmpty) {
      formattedCounter = currentCounter.toString();
    }

    // Format the counter to ensure it's always 7 digits

    return formattedCounter;
  }

//--------------------------------------------------------------------------
  // Generates the next counter number in the format "*********"
  static Future<String> getSaleInvoiceNextCounter() async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(SalesType.Invoice.toString())
        .toString());
    String deviceId = LocaleManager.instance
        .getStringValue(PreferencesKeys.DeviceCode)
        .toString();

    // Format the counter to ensure it's always 7 digits
    String formattedCounter = currentCounter.toString().padLeft(5, '0');
    return 'SI${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';
  }

  //--------------------------------------------------------------------------
  // Generates the next counter number in the format "*********"
  static Future<bool> checkIfSaleInvoiceCounterUsed(String code) async {
    // Step 1: Extract the last segment (counter) from code
    try {
      final parts = code.split('-');
      if (parts.length != 3) return false;

      final counterString = parts.last; // e.g., "00023"
      final codeCounter = int.tryParse(counterString);
      if (codeCounter == null) return false;

      // Step 2: Get current counter from preferences
      final currentCounterString = LocaleManager.instance
          .getStringValueByStringKey(SalesType.Invoice.toString())
          .toString();

      final currentCounter = int.tryParse(currentCounterString);
      if (currentCounter == null) return false;

      // Step 3: Compare
      return codeCounter < currentCounter;
    } catch (e) {
      print("Error checking invoice counter: $e");
      return false;
    }
  }

  //-------------------------------------------------
  static Future<void> setSaleInvoiceCounter() async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(SalesType.Invoice.toString())
        .toString());
    currentCounter += 1;

    await LocaleManager.instance.setStringValueByStringKey(
        SalesType.Invoice.toString(), currentCounter.toString());
  }
  // #endregion
  //-------------------------------------------------
// #region Return Invoice

  static Future<void> initializeReturnInvoiceCounter() async {
    if (!await LocaleManager.instance
        .checkKeyByStringKey(SalesType.RetrunInvoice.toString())) {
      await LocaleManager.instance.setStringValueByStringKey(
          SalesType.RetrunInvoice.toString(),
          1.toString()); // Initial counter value
    }
  }

//--------------------------------------------------------------------------
  // Generates the next counter number in the format "*********"
  static Future<String> getReturnInvoiceNextCounter() async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(SalesType.RetrunInvoice.toString())
        .toString());
    String deviceId = LocaleManager.instance
        .getStringValue(PreferencesKeys.DeviceCode)
        .toString();

    // Format the counter to ensure it's always 7 digits
    String formattedCounter = currentCounter.toString().padLeft(5, '0');

    // Return the counter in the desired format
    return 'SRI${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';
  }

  //-------------------------------------------------

  static Future<String> getReturnInvoiceCurrentCounter() async {
    var formattedCounter = "1";
    var currentCounter = LocaleManager.instance
        .getStringValueByStringKey(SalesType.RetrunInvoice.toString())
        .toString();

    if (currentCounter.isNotEmpty) {
      formattedCounter = currentCounter.toString();
    }

    // Format the counter to ensure it's always 7 digits

    return formattedCounter;
  }

  //-------------------------------------------------
  static Future<void> setReturnInvoiceCounter() async {
    try {
      LocaleManager.instance
          .getStringValueByStringKey(SalesType.RetrunInvoice.toString())
          .toString();
      int currentCounter = int.parse(LocaleManager.instance
          .getStringValueByStringKey(SalesType.RetrunInvoice.toString())
          .toString());
      var newCounter = currentCounter + 1;

      // Save the updated counter value
      await LocaleManager.instance.setStringValueByStringKey(
          SalesType.RetrunInvoice.toString(), newCounter.toString());

      int.parse(LocaleManager.instance
          .getStringValueByStringKey(SalesType.RetrunInvoice.toString())
          .toString());
    } catch (e) {
      print(e);
    }
  }
// #endregion

// #region Order Invoice

  static Future<void> initializeOrderInvoiceCounter() async {
    if (!await LocaleManager.instance
        .checkKeyByStringKey(SalesType.Order.toString())) {
      await LocaleManager.instance.setStringValueByStringKey(
          SalesType.Order.toString(), 1.toString()); // Initial counter value
    }
  }

//--------------------------------------------------------------------------
  // Generates the next counter number in the format "*********"
  static Future<String> getOrderInvoiceNextCounter() async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(SalesType.Order.toString())
        .toString());
    String deviceId = LocaleManager.instance
        .getStringValue(PreferencesKeys.DeviceCode)
        .toString();

    // Format the counter to ensure it's always 7 digits
    String formattedCounter = currentCounter.toString().padLeft(5, '0');

    // Return the counter in the desired format
    return 'SO${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';
  }

  //-------------------------------------------------

  static Future<String> getOrderInvoiceCurrentCounter() async {
    var formattedCounter = "1";
    var currentCounter = LocaleManager.instance
        .getStringValueByStringKey(SalesType.Order.toString())
        .toString();

    if (currentCounter.isNotEmpty) {
      formattedCounter = currentCounter.toString();
    }

    // Format the counter to ensure it's always 7 digits

    return formattedCounter;
  }

  //-------------------------------------------------
  static Future<void> setOrderInvoiceCounter() async {
    try {
      LocaleManager.instance
          .getStringValueByStringKey(SalesType.Order.toString())
          .toString();
      int currentCounter = int.parse(LocaleManager.instance
          .getStringValueByStringKey(SalesType.Order.toString())
          .toString());
      currentCounter += 1;

      // Save the updated counter value
      await LocaleManager.instance.setStringValueByStringKey(
          SalesType.Order.toString(), currentCounter.toString());
    } catch (e) {
      print(e);
    }
  }
// #endregion

// #region Delivery Note

  static Future<void> initializeDeliveryNoteCounter() async {
    if (!await LocaleManager.instance
        .checkKeyByStringKey(SalesType.DeliveryNote.toString())) {
      await LocaleManager.instance.setStringValueByStringKey(
          SalesType.DeliveryNote.toString(),
          1.toString()); // Initial counter value
    }
  }

//--------------------------------------------------------------------------
  // Generates the next counter number in the format "*********"
  static Future<String> getDeliveryNoteNextCounter() async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(SalesType.DeliveryNote.toString())
        .toString());
    String deviceId = LocaleManager.instance
        .getStringValue(PreferencesKeys.DeviceCode)
        .toString();

    // Format the counter to ensure it's always 7 digits
    String formattedCounter = currentCounter.toString().padLeft(5, '0');

    // Return the counter in the desired format
    return 'DN${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';
  }

  //-------------------------------------------------

  static Future<String> getDeliveryNoteCurrentCounter() async {
    var formattedCounter = "1";
    var currentCounter = LocaleManager.instance
        .getStringValueByStringKey(SalesType.DeliveryNote.toString())
        .toString();

    if (currentCounter.isNotEmpty) {
      formattedCounter = currentCounter.toString();
    }

    // Format the counter to ensure it's always 7 digits

    return formattedCounter;
  }

  //-------------------------------------------------
  static Future<void> setDeliveryNoteCounter() async {
    try {
      LocaleManager.instance
          .getStringValueByStringKey(SalesType.DeliveryNote.toString())
          .toString();
      int currentCounter = int.parse(LocaleManager.instance
          .getStringValueByStringKey(SalesType.DeliveryNote.toString())
          .toString());
      currentCounter += 1;

      // Save the updated counter value
      await LocaleManager.instance.setStringValueByStringKey(
          SalesType.DeliveryNote.toString(), currentCounter.toString());
    } catch (e) {
      print(e);
    }
  }
// #endregion
}
