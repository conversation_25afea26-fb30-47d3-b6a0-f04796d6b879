import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:inventory_application/base/constants/roles/permission_constants.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/invoices/customers/CustomersL%C4%B0st.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/invoices%20list/invoices_list_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/invoices%20list/local_invoices_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/purchase%20invoice/create_purchase_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/returns%20invoice/create_return_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/sale%20invoice/create_sale_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/sale%20order%20invoice/create_sale_order_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/delivery%20note/create_delivery_note_screen.dart';
import 'package:inventory_application/screens/invoices/pos/pos_screen.dart';
import 'package:inventory_application/screens/invoices/products/products_list_screen.dart';
import 'package:inventory_application/screens/invoices/qr_scanner/qr_invoice_scanner_screen.dart';
import 'package:inventory_application/services/authentication_service.dart';
import 'package:provider/provider.dart';

class InvoicesHomeScreen extends StatefulWidget {
  const InvoicesHomeScreen({super.key});

  @override
  State<InvoicesHomeScreen> createState() => _InvoicesHomeScreenState();
}

class _InvoicesHomeScreenState extends State<InvoicesHomeScreen> {
  final GlobalKey repaintBoundaryKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FD),
      body: SafeArea(
        child: Column(
          children: [
            // Modern Header with shadow

            // Status widgets with improved styling
            const Column(
              children: [
                InternetConnectionStatusWidget(),
                SyncizationStatusWidget(),
              ],
            ),

            // Main Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SingleChildScrollView(
                  physics: const BouncingScrollPhysics(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 16),

                      // Section title

                      // Invoice cards grid
                      StaggeredGrid.count(
                        crossAxisCount: 2,
                        mainAxisSpacing: 12,
                        crossAxisSpacing: 12,
                        children: [
                          ActionCard(
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const CreatePurchaseInvoiceScreen(),
                                ),
                              );
                            },
                            icon: Icons.shopping_cart_outlined,
                            text: T("Purchase Invoice"),
                            color1: const Color(0xFF4CA1AF),
                            color2: const Color(0xFF2C3E50),
                          ),
                          ActionCard(
                            onTap: () {
                              if (!Provider.of<AuthenticationService>(context,
                                      listen: false)
                                  .hasPermission(
                                      PermissionConstants.salesInvoiceCreate)) {
                                errorSnackBar(
                                  message: T(
                                      "You don't have permission to access this feature"),
                                  context: context,
                                );
                                return;
                              }
                              var isDesktop =
                                  MediaQuery.of(context).size.width > 800;

                              if (isDesktop) {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const POSScreen(isEditing: false),
                                  ),
                                );
                              } else {
                                Navigator.of(context).push(
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const CreateSaleInvoiceScreen(),
                                  ),
                                );
                              }
                            },
                            icon: Icons.receipt_long_outlined,
                            text: T("Sales Invoice"),
                            color1: const Color(0xFF667EEA),
                            color2: const Color(0xFF764BA2),
                          ),
                          ActionCard(
                            onTap: () {
                              if (!Provider.of<AuthenticationService>(context,
                                      listen: false)
                                  .hasPermission(
                                      PermissionConstants.orderInvocieCreate)) {
                                errorSnackBar(
                                  message: T(
                                      "You don't have permission to access this feature"),
                                  context: context,
                                );
                                return;
                              }
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const CreateSaleOrderInvoiceScreen(),
                                ),
                              );
                            },
                            icon: Icons.receipt,
                            text: T("Order Invocie"),
                            color1: const Color(0xFF6B8DD6),
                            color2: const Color(0xFF8E37D7),
                          ),
                          ActionCard(
                            onTap: () {
                              if (!Provider.of<AuthenticationService>(context,
                                      listen: false)
                                  .hasPermission(PermissionConstants
                                      .returninvoiceCreate)) {
                                errorSnackBar(
                                  message: T(
                                      "You don't have permission to access this feature"),
                                  context: context,
                                );
                                return;
                              }
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const CreateReturnInvoiceScreen(),
                                ),
                              );
                            },
                            icon: Icons.assignment_return_outlined,
                            text: T("Returns invoice"),
                            color1: const Color(0xFFFF8C42),
                            color2: Color.fromARGB(255, 109, 74, 235),
                          ),
                        ],
                      ),

                      const SizedBox(height: 24),

                      // Section title

                      // Management options
                      ListView(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        children: [
                          FeatureCard(
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const CreateDeliveryNoteScreen(),
                                ),
                              );
                            },
                            icon: Icons.qr_code_scanner_rounded,
                            text: T("اذن صرف الى زبون"),
                          ),
                          FeatureCard(
                            onTap: () {
                              if (!Provider.of<AuthenticationService>(context,
                                      listen: false)
                                  .hasPermission(
                                      PermissionConstants.orderInvocieCreate)) {
                                errorSnackBar(
                                  message: T(
                                      "You don't have permission to access this feature"),
                                  context: context,
                                );
                                return;
                              }
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const QRInvoiceScannerScreen(),
                                ),
                              );
                            },
                            icon: Icons.qr_code_scanner_rounded,
                            text: T("Scan order Invoice"),
                          ),
                          FeatureCard(
                            onTap: () {
                              if (!Provider.of<AuthenticationService>(context,
                                      listen: false)
                                  .hasPermission(
                                      PermissionConstants.salesInvoiceView)) {
                                errorSnackBar(
                                  message: T(
                                      "You don't have permission to access this feature"),
                                  context: context,
                                );
                                return;
                              }
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const InvoicesListScreenScreen(),
                                ),
                              );
                            },
                            icon: Icons.description_outlined,
                            text: T("All invoices"),
                          ),
                          FeatureCard(
                            onTap: () {
                              if (!Provider.of<AuthenticationService>(context,
                                      listen: false)
                                  .hasPermission(
                                      PermissionConstants.salesInvoiceView)) {
                                errorSnackBar(
                                  message: T(
                                      "You don't have permission to access this feature"),
                                  context: context,
                                );
                                return;
                              }
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (context) =>
                                      const InvoiceListScreen(),
                                ),
                              );
                            },
                            icon: Icons.settings_input_antenna,
                            text: T("Local invoices"),
                          ),
                          // FeatureCard(
                          //   onTap: () {
                          //     Navigator.of(context).push(
                          //       MaterialPageRoute(
                          //         builder: (context) => const ReportsMenuScreen(),
                          //       ),
                          //     );
                          //   },
                          //   icon: Icons.bar_chart_rounded,
                          //   text: T("Sales Reports"),
                          // ),
                          FeatureCard(
                            onTap: () {
                              if (!Provider.of<AuthenticationService>(context,
                                      listen: false)
                                  .hasPermission(
                                      PermissionConstants.itemseView)) {
                                errorSnackBar(
                                  message: T(
                                      "You don't have permission to access this feature"),
                                  context: context,
                                );
                                return;
                              }
                              Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) =>
                                    const ProductsListScreen(),
                              ));
                            },
                            icon: Icons.inventory_outlined,
                            text: T("View items"),
                          ),
                          // FeatureCard(
                          //   onTap: () {
                          //     Navigator.of(context).push(MaterialPageRoute(
                          //       builder: (context) =>
                          //           const ProductTransactionListScreen(),
                          //     ));
                          //   },
                          //   icon: Icons.swap_horiz_outlined,
                          //   text: T("Item Transactions"),
                          // ),
                          // FeatureCard(
                          //   onTap: () async {
                          //     Navigator.of(context).push(MaterialPageRoute(
                          //       builder: (context) =>
                          //           const ProductTransactionListScreen(),
                          //     ));
                          //   },
                          //   icon: Icons.warehouse_outlined,
                          //   text: T("Warehouse inventory"),
                          // ),
                          FeatureCard(
                            onTap: () async {
                              if (!Provider.of<AuthenticationService>(context,
                                      listen: false)
                                  .hasPermission(
                                      PermissionConstants.customerView)) {
                                errorSnackBar(
                                  message: T(
                                      "You don't have permission to access this feature"),
                                  context: context,
                                );
                                return;
                              }
                              Navigator.of(context).push(MaterialPageRoute(
                                builder: (context) =>
                                    const CustomerslistWidget(),
                              ));
                            },
                            icon: Icons.people_outline_rounded,
                            text: T("Customers"),
                          ),
                        ],
                      ),
                      const SizedBox(height: 30),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class ActionCard extends StatelessWidget {
  final VoidCallback onTap;
  final IconData icon;
  final String text;
  final Color color1;
  final Color color2;

  const ActionCard({
    super.key,
    required this.onTap,
    required this.icon,
    required this.text,
    required this.color1,
    required this.color2,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [color1, color2],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: color1.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                size: 36,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              text,
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FeatureCard extends StatelessWidget {
  final VoidCallback onTap;
  final IconData icon;
  final String text;

  const FeatureCard({
    super.key,
    required this.onTap,
    required this.icon,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shadowColor: Colors.black.withOpacity(0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: const Color(0xFF4CA1AF).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  size: 24,
                  color: const Color(0xFF4CA1AF),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  text,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios_rounded,
                size: 16,
                color: Colors.grey.shade600,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
