import 'package:inventory_application/models/model/invoice_model.dart';

class DailySalesReportDTO {
  final DateTime fromDate;
  final DateTime toDate;
  final double totalSales;
  final double totalDiscount;
  final double netSales;
  final int invoiceCount;
  final List<InvoiceModel> invoices;

  // إضافة البيانات المالية الشاملة
  final double totalReturns; // إجمالي المرتجعات
  final double totalPaid; // إجمالي المدفوع (دخل الخزينة)
  final double totalRemaining; // إجمالي المتبقي
  final double cashInflow; // الداخل للخزينة من المبيعات
  final double cashOutflow; // الخارج من الخزينة للمرتجعات
  final double netCashFlow; // صافي الحركة النقدية
  final int returnInvoiceCount; // عدد فواتير المرتجعات

  // Optional additional metrics
  final Map<String, double>? salesByCustomer;
  final Map<String, double>? salesByProduct;
  final Map<String, double>? salesByWarehouse;
  final Map<String, double>? salesByPaymentMethod;

  DailySalesReportDTO({
    required this.fromDate,
    required this.toDate,
    required this.totalSales,
    required this.totalDiscount,
    required this.netSales,
    required this.invoiceCount,
    required this.invoices,
    this.totalReturns = 0.0,
    this.totalPaid = 0.0,
    this.totalRemaining = 0.0,
    this.cashInflow = 0.0,
    this.cashOutflow = 0.0,
    this.netCashFlow = 0.0,
    this.returnInvoiceCount = 0,
    this.salesByCustomer,
    this.salesByProduct,
    this.salesByWarehouse,
    this.salesByPaymentMethod,
  });

  // حساب إجمالي المرتجعات
  double calculateTotalReturns() {
    double totalReturnAmount = 0.0;
    print('🔍 البحث عن فواتير المرتجعات:');

    int returnCount = 0;
    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';

      // البحث عن أنواع مختلفة من المرتجعات
      bool isReturn = false;
      if (type.toLowerCase().contains('return') ||
          type.toLowerCase().contains('retrun') ||
          type == 'RetrunInvoice' ||
          type == 'ReturnInvoice' ||
          type == 'return' ||
          type == 'Return') {
        isReturn = true;
        returnCount++;
        totalReturnAmount += invoice.total ?? 0.0;
        print(
            '✅ مرتجع موجود: فاتورة ${invoice.code}, نوع="$type", مبلغ=${invoice.total}');
      }
    }

    print('إجمالي فواتير المرتجعات الموجودة: $returnCount');
    print('إجمالي مبلغ المرتجعات: ${totalReturnAmount.toStringAsFixed(2)}');

    if (returnCount == 0) {
      print('❌ لم يتم العثور على أي فواتير مرتجعات!');
      print('أنواع المعاملات الموجودة:');
      Set<String> uniqueTypes =
          invoices.map((e) => e.transactionsType ?? 'null').toSet();
      uniqueTypes.forEach((type) => print('  - "$type"'));
    }

    return totalReturnAmount;
  }

  // حساب إجمالي المدفوع
  double calculateTotalPaid() {
    double totalPaidAmount = 0.0;
    print('🔍 حساب إجمالي المدفوع - تفاصيل الفواتير:');
    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';

      // استثناء المرتجعات
      bool isReturn = type.toLowerCase().contains('return') ||
          type.toLowerCase().contains('retrun') ||
          type == 'RetrunInvoice' ||
          type == 'ReturnInvoice' ||
          type == 'return' ||
          type == 'Return';

      if (!isReturn) {
        final paidAmount = invoice.paidAmount ?? 0.0;
        final totalAmount = invoice.total ?? 0.0;
        print(
            'فاتورة ${invoice.code}: إجمالي=${totalAmount.toStringAsFixed(2)}, مدفوع=${paidAmount.toStringAsFixed(2)}');
        totalPaidAmount += paidAmount;
      }
    }
    print('إجمالي المدفوع النهائي: ${totalPaidAmount.toStringAsFixed(2)}');
    return totalPaidAmount;
  }

  // حساب إجمالي المتبقي
  double calculateTotalRemaining() {
    double totalRemainingAmount = 0.0;
    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';

      // استثناء المرتجعات
      bool isReturn = type.toLowerCase().contains('return') ||
          type.toLowerCase().contains('retrun') ||
          type == 'RetrunInvoice' ||
          type == 'ReturnInvoice' ||
          type == 'return' ||
          type == 'Return';

      if (!isReturn) {
        final total = invoice.total ?? 0.0;
        final paid = invoice.paidAmount ?? 0.0;
        totalRemainingAmount += (total - paid);
      }
    }
    return totalRemainingAmount;
  }

  // حساب الداخل للخزينة (المبالغ المدفوعة من المبيعات)
  double calculateCashInflow() {
    return calculateTotalPaid();
  }

  // حساب الخارج من الخزينة (المرتجعات المدفوعة)
  double calculateCashOutflow() {
    double cashOutflowAmount = 0.0;
    print('🔍 حساب الخارج من الخزينة (مرتجعات مدفوعة):');

    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';

      // استخدام نفس منطق البحث المحسن
      if (type.toLowerCase().contains('return') ||
          type.toLowerCase().contains('retrun') ||
          type == 'RetrunInvoice' ||
          type == 'ReturnInvoice' ||
          type == 'return' ||
          type == 'Return') {
        final paidAmount = invoice.paidAmount ?? 0.0;
        cashOutflowAmount += paidAmount;
        print(
            '💸 مرتجع مدفوع: فاتورة ${invoice.code}, مدفوع=${paidAmount.toStringAsFixed(2)}');
      }
    }

    print('إجمالي الخارج من الخزينة: ${cashOutflowAmount.toStringAsFixed(2)}');
    return cashOutflowAmount;
  }

  // حساب صافي الحركة النقدية
  double calculateNetCashFlow() {
    return calculateCashInflow() - calculateCashOutflow();
  }

  // حساب عدد فواتير المرتجعات
  int calculateReturnInvoiceCount() {
    int count = 0;
    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';

      // استخدام نفس منطق البحث المحسن
      if (type.toLowerCase().contains('return') ||
          type.toLowerCase().contains('retrun') ||
          type == 'RetrunInvoice' ||
          type == 'ReturnInvoice' ||
          type == 'return' ||
          type == 'Return') {
        count++;
      }
    }
    print('🔢 عدد فواتير المرتجعات المحسوب: $count');
    return count;
  }

  // Calculate sales by customer
  Map<String, double> calculateSalesByCustomer() {
    final Map<String, double> result = {};

    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';

      // استثناء المرتجعات
      bool isReturn = type.toLowerCase().contains('return') ||
          type.toLowerCase().contains('retrun') ||
          type == 'RetrunInvoice' ||
          type == 'ReturnInvoice' ||
          type == 'return' ||
          type == 'Return';

      if (!isReturn) {
        final customerName = invoice.customerName ?? 'Unknown';
        final total = invoice.total ?? 0.0;

        if (result.containsKey(customerName)) {
          result[customerName] = result[customerName]! + total;
        } else {
          result[customerName] = total;
        }
      }
    }

    return result;
  }

  // Calculate sales by payment method
  Map<String, double> calculateSalesByPaymentMethod() {
    final Map<String, double> result = {
      'نقدي': 0.0,
      'آجل': 0.0,
    };

    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';

      // استثناء المرتجعات
      bool isReturn = type.toLowerCase().contains('return') ||
          type.toLowerCase().contains('retrun') ||
          type == 'RetrunInvoice' ||
          type == 'ReturnInvoice' ||
          type == 'return' ||
          type == 'Return';

      if (!isReturn) {
        final total = invoice.total ?? 0.0;
        final paidAmount = invoice.paidAmount ?? 0.0;

        if (paidAmount >= total) {
          result['نقدي'] = result['نقدي']! + total;
        } else {
          result['آجل'] = result['آجل']! + total;
        }
      }
    }

    return result;
  }

  // Calculate sales by warehouse
  Map<String, double> calculateSalesByWarehouse() {
    final Map<String, double> result = {};

    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';

      // استثناء المرتجعات
      bool isReturn = type.toLowerCase().contains('return') ||
          type.toLowerCase().contains('retrun') ||
          type == 'RetrunInvoice' ||
          type == 'ReturnInvoice' ||
          type == 'return' ||
          type == 'Return';

      if (!isReturn) {
        final warehouseName = invoice.storeName ?? 'Unknown';
        final total = invoice.total ?? 0.0;

        if (result.containsKey(warehouseName)) {
          result[warehouseName] = result[warehouseName]! + total;
        } else {
          result[warehouseName] = total;
        }
      }
    }

    return result;
  }

  // إنشاء نسخة محسوبة من البيانات
  DailySalesReportDTO withCalculatedValues() {
    // Debug: فحص جميع أنواع المعاملات الموجودة
    print('🔍 فحص أنواع المعاملات في التقرير:');
    print('إجمالي عدد الفواتير: ${invoices.length}');

    Map<String, int> transactionTypes = {};
    for (var invoice in invoices) {
      final type = invoice.transactionsType ?? 'null';
      transactionTypes[type] = (transactionTypes[type] ?? 0) + 1;
      print(
          'فاتورة ${invoice.code}: نوع المعاملة = "$type", إجمالي=${invoice.total}, مدفوع=${invoice.paidAmount}');
    }

    print('ملخص أنواع المعاملات:');
    transactionTypes.forEach((type, count) {
      print('- "$type": $count فاتورة');
    });

    return DailySalesReportDTO(
      fromDate: fromDate,
      toDate: toDate,
      totalSales: totalSales,
      totalDiscount: totalDiscount,
      netSales: netSales,
      invoiceCount: invoiceCount,
      invoices: invoices,
      totalReturns: calculateTotalReturns(),
      totalPaid: calculateTotalPaid(),
      totalRemaining: calculateTotalRemaining(),
      cashInflow: calculateCashInflow(),
      cashOutflow: calculateCashOutflow(),
      netCashFlow: calculateNetCashFlow(),
      returnInvoiceCount: calculateReturnInvoiceCount(),
      salesByCustomer: salesByCustomer,
      salesByProduct: salesByProduct,
      salesByWarehouse: salesByWarehouse,
      salesByPaymentMethod: salesByPaymentMethod,
    );
  }

  // Create a copy with updated values
  DailySalesReportDTO copyWith({
    DateTime? fromDate,
    DateTime? toDate,
    double? totalSales,
    double? totalDiscount,
    double? netSales,
    int? invoiceCount,
    List<InvoiceModel>? invoices,
    double? totalReturns,
    double? totalPaid,
    double? totalRemaining,
    double? cashInflow,
    double? cashOutflow,
    double? netCashFlow,
    int? returnInvoiceCount,
    Map<String, double>? salesByCustomer,
    Map<String, double>? salesByProduct,
    Map<String, double>? salesByWarehouse,
    Map<String, double>? salesByPaymentMethod,
  }) {
    return DailySalesReportDTO(
      fromDate: fromDate ?? this.fromDate,
      toDate: toDate ?? this.toDate,
      totalSales: totalSales ?? this.totalSales,
      totalDiscount: totalDiscount ?? this.totalDiscount,
      netSales: netSales ?? this.netSales,
      invoiceCount: invoiceCount ?? this.invoiceCount,
      invoices: invoices ?? this.invoices,
      totalReturns: totalReturns ?? this.totalReturns,
      totalPaid: totalPaid ?? this.totalPaid,
      totalRemaining: totalRemaining ?? this.totalRemaining,
      cashInflow: cashInflow ?? this.cashInflow,
      cashOutflow: cashOutflow ?? this.cashOutflow,
      netCashFlow: netCashFlow ?? this.netCashFlow,
      returnInvoiceCount: returnInvoiceCount ?? this.returnInvoiceCount,
      salesByCustomer: salesByCustomer ?? this.salesByCustomer,
      salesByProduct: salesByProduct ?? this.salesByProduct,
      salesByWarehouse: salesByWarehouse ?? this.salesByWarehouse,
      salesByPaymentMethod: salesByPaymentMethod ?? this.salesByPaymentMethod,
    );
  }
}
