import 'dart:io';
import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/printer_settings_controller.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/models/dto/reports/server/sales_summary_report_dto.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/helpers/arabic_font_loader.dart';
import 'package:intl/intl.dart';

/// خدمة طباعة التقارير باللغة العربية مع دعم إعدادات الطابعة ورقم الجهاز
class ReportPrinterService {
  /// طباعة تقرير ملخص المبيعات
  static Future<void> printSalesSummaryReport(
    SalesSummaryReportDTO reportData,
    BuildContext context, {
    String? reportTitle,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      final printerSettings =
          Provider.of<PrinterSettingsController>(context, listen: false);
      final pdf = await _createSalesSummaryReportPdf(
        reportData,
        printerSettings,
        reportTitle: reportTitle ?? 'تقرير ملخص المبيعات',
        fromDate: fromDate,
        toDate: toDate,
      );

      await _printReport(pdf, context, reportTitle ?? 'تقرير ملخص المبيعات');
    } catch (e) {
      debugPrint('خطأ في طباعة تقرير ملخص المبيعات: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في طباعة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// طباعة تقرير عام (يمكن استخدامه لأي نوع من التقارير)
  static Future<void> printGenericReport(
    Map<String, dynamic> reportData,
    String reportTitle,
    BuildContext context, {
    DateTime? fromDate,
    DateTime? toDate,
    List<Map<String, String>>? customFields,
  }) async {
    try {
      final printerSettings =
          Provider.of<PrinterSettingsController>(context, listen: false);
      final pdf = await _createGenericReportPdf(
        reportData,
        printerSettings,
        reportTitle: reportTitle,
        fromDate: fromDate,
        toDate: toDate,
        customFields: customFields,
      );

      await _printReport(pdf, context, reportTitle);
    } catch (e) {
      debugPrint('خطأ في طباعة التقرير: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في طباعة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// إنشاء PDF لتقرير ملخص المبيعات
  static Future<pw.Document> _createSalesSummaryReportPdf(
    SalesSummaryReportDTO reportData,
    PrinterSettingsController? printerSettings, {
    required String reportTitle,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    final pdf = pw.Document();
    final arabicFont = await _loadArabicFont();
    final pageFormat = _getPageFormat(printerSettings);

    pdf.addPage(
      pw.Page(
        pageFormat: pageFormat,
        build: (context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // العنوان الرئيسي
              _buildReportHeader(arabicFont, printerSettings, reportTitle),
              pw.SizedBox(height: 10),

              // تاريخ اليوم ومعلومات التقرير
              _buildReportInfo(arabicFont, fromDate, toDate),
              pw.SizedBox(height: 15),

              // معلومات الإجماليات
              _buildSalesSummaryTotals(arabicFont, reportData),
              pw.SizedBox(height: 15),

              // تفاصيل المبيعات حسب المستخدمين
              if (reportData.byUsers.isNotEmpty)
                _buildUsersSalesDetails(arabicFont, reportData.byUsers),

              pw.SizedBox(height: 20),

              // معلومات الجهاز والطابعة
              _buildReportFooter(arabicFont, printerSettings),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  /// إنشاء PDF تقرير عام
  static Future<pw.Document> _createGenericReportPdf(
    Map<String, dynamic> reportData,
    PrinterSettingsController? printerSettings, {
    required String reportTitle,
    DateTime? fromDate,
    DateTime? toDate,
    List<Map<String, String>>? customFields,
  }) async {
    final pdf = pw.Document();
    final arabicFont = await _loadArabicFont();
    final pageFormat = _getPageFormat(printerSettings);

    pdf.addPage(
      pw.Page(
        pageFormat: pageFormat,
        build: (context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // العنوان الرئيسي
              _buildReportHeader(arabicFont, printerSettings, reportTitle),
              pw.SizedBox(height: 10),

              // تاريخ اليوم ومعلومات التقرير
              _buildReportInfo(arabicFont, fromDate, toDate),
              pw.SizedBox(height: 15),

              // بيانات التقرير المخصصة
              _buildGenericReportData(arabicFont, reportData, customFields),

              pw.SizedBox(height: 20),

              // معلومات الجهاز والطابعة
              _buildReportFooter(arabicFont, printerSettings),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  /// بناء رأس التقرير
  static pw.Widget _buildReportHeader(
    pw.Font arabicFont,
    PrinterSettingsController? printerSettings,
    String reportTitle,
  ) {
    return pw.Column(
      children: [
        // شعار الشركة إذا كان مفعلاً
        if (printerSettings?.printLogo == true)
          pw.Center(
            child: pw.Container(
              width: 100,
              height: 40,
              decoration: pw.BoxDecoration(
                border: pw.Border.all(width: 1),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Center(
                child: pw.Text(
                  'الشعار',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
            ),
          ),

        if (printerSettings?.printLogo == true) pw.SizedBox(height: 10),

        // رأس مخصص
        if (printerSettings?.receiptHeader != null &&
            printerSettings!.receiptHeader.isNotEmpty)
          pw.Center(
            child: pw.Text(
              printerSettings.receiptHeader,
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),

        if (printerSettings?.receiptHeader != null &&
            printerSettings!.receiptHeader.isNotEmpty)
          pw.SizedBox(height: 8),

        // عنوان التقرير
        pw.Center(
          child: pw.Text(
            reportTitle,
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),

        pw.SizedBox(height: 5),

        // خط فاصل
        pw.Container(
          width: double.infinity,
          height: 1,
          decoration: const pw.BoxDecoration(
            border: pw.Border(
              bottom: pw.BorderSide(width: 1),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء معلومات التقرير
  static pw.Widget _buildReportInfo(
    pw.Font arabicFont,
    DateTime? fromDate,
    DateTime? toDate,
  ) {
    final now = DateTime.now();
    final dateFormatter = DateFormat('dd/MM/yyyy', 'en');
    final timeFormatter = DateFormat('HH:mm', 'en');

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        // تاريخ ووقت الطباعة
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'تاريخ الطباعة: ${dateFormatter.format(now)}',
              style: pw.TextStyle(font: arabicFont, fontSize: 10),
              textDirection: pw.TextDirection.rtl,
            ),
            pw.Text(
              'الوقت: ${timeFormatter.format(now)}',
              style: pw.TextStyle(font: arabicFont, fontSize: 10),
              textDirection: pw.TextDirection.rtl,
            ),
          ],
        ),

        pw.SizedBox(height: 5),

        // فترة التقرير
        if (fromDate != null || toDate != null)
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              if (fromDate != null)
                pw.Text(
                  'من تاريخ: ${dateFormatter.format(fromDate)}',
                  style: pw.TextStyle(font: arabicFont, fontSize: 10),
                  textDirection: pw.TextDirection.rtl,
                ),
              if (toDate != null)
                pw.Text(
                  'إلى تاريخ: ${dateFormatter.format(toDate)}',
                  style: pw.TextStyle(font: arabicFont, fontSize: 10),
                  textDirection: pw.TextDirection.rtl,
                ),
            ],
          ),

        pw.Container(
          width: double.infinity,
          height: 0.5,
          decoration: const pw.BoxDecoration(
            border: pw.Border(
              bottom: pw.BorderSide(width: 0.5),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء تفاصيل ملخص المبيعات
  static pw.Widget _buildSalesSummaryTotals(
    pw.Font arabicFont,
    SalesSummaryReportDTO reportData,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(width: 0.5),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'الإجماليات العامة',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 8),
          _buildReportRowItem(arabicFont, 'إجمالي المبيعات',
              '${reportData.totalSalesAllUsers.toStringAsFixed(2)}'),
          _buildReportRowItem(arabicFont, 'إجمالي المرتجعات',
              '${reportData.totalReturnsAllUsers.toStringAsFixed(2)}'),
          _buildReportRowItem(arabicFont, 'إجمالي المستلم',
              '${reportData.totalReceivedAllUsers.toStringAsFixed(2)}'),
          _buildReportRowItem(arabicFont, 'إجمالي المتبقي',
              '${reportData.totalRemainingAllUsers.toStringAsFixed(2)}'),
        ],
      ),
    );
  }

  /// بناء تفاصيل المبيعات حسب المستخدمين
  static pw.Widget _buildUsersSalesDetails(
    pw.Font arabicFont,
    List<dynamic> usersSummary,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'ملخص المبيعات حسب المستخدمين',
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
          ),
          textDirection: pw.TextDirection.rtl,
        ),

        pw.SizedBox(height: 8),

        // رأس الجدول
        pw.Container(
          padding: const pw.EdgeInsets.all(4),
          decoration: const pw.BoxDecoration(
            border: pw.Border(
              bottom: pw.BorderSide(width: 1),
              top: pw.BorderSide(width: 1),
            ),
          ),
          child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Container(
                width: 200,
                child: pw.Text(
                  'المستخدم',
                  style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
              pw.Container(
                width: 100,
                child: pw.Text(
                  'المبيعات',
                  style: pw.TextStyle(
                      font: arabicFont,
                      fontSize: 10,
                      fontWeight: pw.FontWeight.bold),
                  textDirection: pw.TextDirection.rtl,
                  textAlign: pw.TextAlign.center,
                ),
              ),
            ],
          ),
        ),

        // بيانات المستخدمين
        ...usersSummary
            .map((user) => pw.Container(
                  padding: const pw.EdgeInsets.all(4),
                  decoration: const pw.BoxDecoration(
                    border: pw.Border(
                      bottom: pw.BorderSide(width: 0.5),
                    ),
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Container(
                        width: 200,
                        child: pw.Text(
                          user.userFullName?.toString() ??
                              user.userName?.toString() ??
                              'غير محدد',
                          style: pw.TextStyle(font: arabicFont, fontSize: 9),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ),
                      pw.Container(
                        width: 100,
                        child: pw.Text(
                          (user.totalSales ?? 0.0).toStringAsFixed(2),
                          style: pw.TextStyle(font: arabicFont, fontSize: 9),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ))
            .toList(),
      ],
    );
  }

  /// بناء بيانات التقرير العامة
  static pw.Widget _buildGenericReportData(
    pw.Font arabicFont,
    Map<String, dynamic> reportData,
    List<Map<String, String>>? customFields,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(width: 0.5),
        borderRadius: pw.BorderRadius.circular(4),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'تفاصيل التقرير',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
            textDirection: pw.TextDirection.rtl,
          ),

          pw.SizedBox(height: 8),

          // عرض البيانات المخصصة
          if (customFields != null)
            ...customFields
                .map((field) => _buildReportRowItem(
                    arabicFont, field['label'] ?? '', field['value'] ?? ''))
                .toList()
          else
            // عرض البيانات من الخريطة مباشرة
            ...reportData.entries
                .map((entry) => _buildReportRowItem(
                    arabicFont, entry.key, entry.value?.toString() ?? ''))
                .toList(),
        ],
      ),
    );
  }

  /// بناء عنصر صف في التقرير
  static pw.Widget _buildReportRowItem(
      pw.Font arabicFont, String label, String value) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 2),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(font: arabicFont, fontSize: 10),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.Text(
            value,
            style: pw.TextStyle(
                font: arabicFont, fontSize: 10, fontWeight: pw.FontWeight.bold),
          ),
        ],
      ),
    );
  }

  /// بناء ذيل التقرير مع معلومات الجهاز والطابعة
  static pw.Widget _buildReportFooter(
    pw.Font arabicFont,
    PrinterSettingsController? printerSettings,
  ) {
    final deviceId = AppController.deviceId ?? 'غير معروف';
    final deviceCode = LocaleManager.instance
        .getStringValue(PreferencesKeys.DeviceCode)
        .toString();

    return pw.Column(
      children: [
        // خط فاصل
        pw.Container(
          width: double.infinity,
          height: 0.5,
          decoration: const pw.BoxDecoration(
            border: pw.Border(
              top: pw.BorderSide(width: 0.5),
            ),
          ),
        ),

        pw.SizedBox(height: 8),

        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'رقم المستخدم: ${AuthController.getUserId()}',
              style: pw.TextStyle(font: arabicFont, fontSize: 8),
              textDirection: pw.TextDirection.rtl,
            ),
            pw.Text(
              'اسم المستخدم : ${AuthController.getUserName()}',
              style: pw.TextStyle(font: arabicFont, fontSize: 8),
              textDirection: pw.TextDirection.rtl,
            ),
          ],
        ),
        pw.SizedBox(height: 4),
        // معلومات الجهاز
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              'رقم الجهاز: $deviceId',
              style: pw.TextStyle(font: arabicFont, fontSize: 8),
              textDirection: pw.TextDirection.rtl,
            ),
            pw.Text(
              'كود الجهاز: $deviceCode',
              style: pw.TextStyle(font: arabicFont, fontSize: 8),
              textDirection: pw.TextDirection.rtl,
            ),
          ],
        ),

        pw.SizedBox(height: 4),

        // ذيل مخصص إذا تم تعيينه
        if (printerSettings?.receiptFooter != null &&
            printerSettings!.receiptFooter.isNotEmpty)
          pw.Center(
            child: pw.Text(
              printerSettings.receiptFooter,
              style: pw.TextStyle(font: arabicFont, fontSize: 9),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          )
        else
          pw.Center(
            child: pw.Text(
              'شكرا لاستخدام نظامنا Pal4it',
              style: pw.TextStyle(font: arabicFont, fontSize: 9),
              textDirection: pw.TextDirection.rtl,
            ),
          ),
      ],
    );
  }

  /// تحميل الخط العربي
  /// تحميل الخط العربي Cairo
  // static Future<pw.Font> _loadArabicFont() async {
  //   try {
  //     // تحميل خط Cairo العربي
  //     final fontData = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
  //     return pw.Font.ttf(fontData);
  //   } catch (e) {
  //     // محاولة تحميل خط Cairo Bold كبديل
  //     final boldFontData = await rootBundle.load('assets/fonts/Cairo-Bold.ttf');
  //     return pw.Font.ttf(boldFontData);
  //   } catch (e2) {
  //     // استخدام الخط الافتراضي كبديل أخير
  //     return pw.Font.helvetica();
  //   }
  // }
  static Future<pw.Font> _loadArabicFont() async {
    try {
      // استخدام خدمة تحميل الخطوط العربية الموحدة
      return await ArabicFontLoader.getSafeArabicFont();
    } catch (e) {
      print('⚠️ Arabic font loading failed in ReportPrinterService: $e');
      // استخدام الخط الافتراضي كحل أخير
      return pw.Font.helvetica();
    }
  }

  /// الحصول على تنسيق الصفحة حسب إعدادات الطابعة
  static PdfPageFormat _getPageFormat(
      PrinterSettingsController? printerSettings) {
    if (printerSettings == null) return PdfPageFormat.roll80;

    switch (printerSettings.paperSize) {
      case PaperSize.mm58:
        return const PdfPageFormat(
            58 * PdfPageFormat.mm, 297 * PdfPageFormat.mm);
      case PaperSize.mm80:
        return PdfPageFormat.roll80;
      case PaperSize.a4:
        return PdfPageFormat.a4;
      case PaperSize.letter:
        return PdfPageFormat.letter;
      default:
        return PdfPageFormat.roll80;
    }
  }

  /// تحويل نوع الطابعة إلى العربية
  static String _getPrinterTypeInArabic(String? printerType) {
    switch (printerType) {
      case 'network':
        return 'شبكة';
      case 'usb':
        return 'USB';
      case 'bluetooth':
        return 'بلوتوث';
      default:
        return 'افتراضي';
    }
  }

  /// طباعة التقرير باستخدام إعدادات الطابعة
  static Future<void> _printReport(
    pw.Document pdf,
    BuildContext context,
    String reportTitle,
  ) async {
    try {
      final printerSettings =
          Provider.of<PrinterSettingsController>(context, listen: false);

      // التحقق من نوع الجهاز
      final bool isMobile =
          !Platform.isWindows && !Platform.isLinux && !Platform.isMacOS;

      // إذا كان على الهاتف وإعدادات الطابعة غير مكونة
      if (isMobile && printerSettings.printerType == null) {
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name: reportTitle,
          format: _getPageFormat(printerSettings),
        );

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال التقرير للطباعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
        return;
      }

      final pageFormat = _getPageFormat(printerSettings);

      // طباعة حسب نوع الطابعة
      if (printerSettings.printerType == 'usb') {
        final printers = await Printing.listPrinters();
        final selectedPrinter = printers.firstWhere(
          (printer) => printer.name == printerSettings.selectedPrinterName,
          orElse: () => throw Exception('الطابعة المحددة غير متوفرة'),
        );

        final result = await Printing.directPrintPdf(
          printer: selectedPrinter,
          onLayout: (_) async => await pdf.save(),
        );

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  result ? 'تم طباعة التقرير بنجاح' : 'فشل في طباعة التقرير'),
              backgroundColor: result ? Colors.green : Colors.red,
            ),
          );
        }
      } else {
        // طباعة عادية أو شبكة أو بلوتوث
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name: reportTitle,
          format: pageFormat,
        );

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال التقرير للطباعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في طباعة التقرير: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في طباعة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      rethrow;
    }
  }
}
