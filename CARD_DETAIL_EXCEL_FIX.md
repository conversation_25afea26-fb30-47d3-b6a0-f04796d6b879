# 🔧 حل مشكلة عدم ظهور التفاصيل الكاملة في Excel

## 🚨 المشكلة:
التقرير المصدر إلى Excel لا يظهر جميع التفاصيل مثل:
- إجمالي الشحن
- إجمالي الإنفاق  
- إجمالي الهدايا
- عدد معاملات الشحن

## ✅ الحل المطبق:

### 1. تحديث دالة التصدير في `ExcelExportService`:
```dart
/// تصدير تقرير تفاصيل الكارت إلى Excel
static Future<void> exportCardDetailReport({
  required BuildContext context,
  required Map<String, dynamic> cardInfo,
  required Map<String, dynamic> balanceSummary,
  required List<Map<String, dynamic>> transactions,
  required String cardNumber,
}) async {
  // تحضير بيانات التقرير
  List<Map<String, dynamic>> reportData = [];

  // إضافة معلومات الكارت
  reportData.add({
    'نوع البيانات': 'معلومات الكارت',
    'رقم الكارت': cardInfo['cardNumber'] ?? 'غير محدد',
    'اسم العضو': cardInfo['memberName'] ?? 'غير محدد',
    'رقم الجوال': cardInfo['mobile'] ?? 'غير محدد',
    'تاريخ التسجيل': cardInfo['memberSince'] ?? 'غير محدد',
    'الرصيد الحالي': cardInfo['currentBalance'] ?? '0',
    'حالة العضو': cardInfo['memberState'] ?? 'غير محدد',
    'إجمالي الشحن': '',
    'إجمالي الإنفاق': '',
    'إجمالي الهدايا': '',
    'معاملات الشحن': '',
  });

  // إضافة ملخص الرصيد
  reportData.add({
    'نوع البيانات': 'ملخص الرصيد',
    'رقم الكارت': '',
    'اسم العضو': '',
    'رقم الجوال': '',
    'تاريخ التسجيل': '',
    'الرصيد الحالي': '',
    'حالة العضو': '',
    'إجمالي الشحن': balanceSummary['totalRechargeAmount'] ?? '0',
    'إجمالي الإنفاق': balanceSummary['totalSpentAmount'] ?? '0',
    'إجمالي الهدايا': balanceSummary['totalGiftAmount'] ?? '0',
    'معاملات الشحن': balanceSummary['totalRechargeTransactions'] ?? '0',
  });

  // إضافة المعاملات مع التفاصيل الكاملة
  for (var transaction in transactions) {
    final isRecharge = transaction['type'] == 'recharge';

    Map<String, dynamic> transactionData = {
      'نوع البيانات': isRecharge ? 'معاملة شحن' : 'معاملة صرف',
      'رقم الكارت': '',
      'اسم العضو': '',
      'رقم الجوال': '',
      'تاريخ التسجيل': transaction['date'] ?? '',
      'الرصيد الحالي': transaction['amount'] ?? '0',
      'حالة العضو': transaction['balanceAfter'] ?? '0',
      'إجمالي الشحن': isRecharge ? (transaction['rechargeAmount'] ?? '0') : '',
      'إجمالي الإنفاق': '',
      'إجمالي الهدايا': isRecharge ? (transaction['giftAmount'] ?? '0') : '',
      'معاملات الشحن': '',
    };

    // إضافة معلومات إضافية
    if (isRecharge) {
      if (transaction['admin'] != null && transaction['admin'].isNotEmpty) {
        transactionData['رقم الجوال'] = 'مشرف: ${transaction['admin']}';
      }
    } else {
      if (transaction['store'] != null && transaction['store'].isNotEmpty) {
        transactionData['اسم العضو'] = 'متجر: ${transaction['store']}';
      }
      if (transaction['company'] != null && transaction['company'].isNotEmpty) {
        transactionData['رقم الجوال'] = 'شركة: ${transaction['company']}';
      }
    }

    reportData.add(transactionData);
  }

  // رؤوس الأعمدة المحدثة
  List<String> headers = [
    'نوع البيانات',
    'رقم الكارت',
    'اسم العضو',
    'رقم الجوال',
    'تاريخ التسجيل',
    'الرصيد الحالي',
    'حالة العضو',
    'إجمالي الشحن',
    'إجمالي الإنفاق',
    'إجمالي الهدايا',
    'معاملات الشحن',
  ];

  await exportGenericReport(
    context: context,
    reportTitle: 'تقرير تفاصيل الكارت - $cardNumber',
    data: reportData,
    headers: headers,
    fileName: 'تقرير_تفاصيل_الكارت_${cardNumber}_${DateTime.now().millisecondsSinceEpoch}.xlsx',
  );
}
```

### 2. تحديث دالة التصدير في تقرير تفاصيل الكارت:
```dart
// دالة تصدير التقرير إلى Excel
Future<void> _exportToExcel() async {
  final controller = context.read<AccountingReportHelperController>();

  if (controller.cardDetailReport == null) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('لا توجد بيانات للتصدير'),
        backgroundColor: Colors.red,
      ),
    );
    return;
  }

  final report = controller.cardDetailReport!;
  final cardInfo = report.cardInfo;
  final balanceSummary = report.balanceSummary;

  // تحضير بيانات الكارت
  Map<String, dynamic> cardInfoData = {};
  if (cardInfo != null) {
    cardInfoData = {
      'cardNumber': cardInfo.cardNumber ?? 'غير محدد',
      'memberName': cardInfo.memberName ?? 'غير محدد',
      'mobile': cardInfo.mobile ?? 'غير محدد',
      'memberSince': cardInfo.memberSince != null
          ? DateFormat('yyyy-MM-dd').format(cardInfo.memberSince!)
          : 'غير محدد',
      'currentBalance': cardInfo.currentBalance?.toStringAsFixed(2) ?? '0',
      'memberState': _getMemberStateText(cardInfo.memberState),
    };
  }

  // تحضير بيانات ملخص الرصيد
  Map<String, dynamic> balanceSummaryData = {};
  if (balanceSummary != null) {
    balanceSummaryData = {
      'totalRechargeAmount': balanceSummary.totalRechargeAmount?.toStringAsFixed(2) ?? '0',
      'totalSpentAmount': balanceSummary.totalSpentAmount?.toStringAsFixed(2) ?? '0',
      'totalGiftAmount': balanceSummary.totalGiftAmount?.toStringAsFixed(2) ?? '0',
      'totalRechargeTransactions': balanceSummary.totalRechargeTransactions?.toString() ?? '0',
    };
  }

  // تحضير بيانات المعاملات
  List<Map<String, dynamic>> transactionsData = [];
  for (var transaction in _sortedCombinedTransactions) {
    final isRecharge = transaction['type'] == 'recharge';
    
    Map<String, dynamic> transactionData = {
      'type': isRecharge ? 'recharge' : 'payment',
      'date': DateFormat('yyyy-MM-dd HH:mm').format(transaction['date']),
      'amount': transaction['amount'].toStringAsFixed(2),
      'balanceAfter': transaction['balanceAfter'].toStringAsFixed(2),
      'description': transaction['description'] ?? '',
    };

    // إضافة معلومات إضافية حسب نوع المعاملة
    if (isRecharge) {
      if (transaction['rechargeAmount'] != null && transaction['rechargeAmount'] > 0) {
        transactionData['rechargeAmount'] = transaction['rechargeAmount'].toStringAsFixed(2);
      }
      if (transaction['giftAmount'] != null && transaction['giftAmount'] > 0) {
        transactionData['giftAmount'] = transaction['giftAmount'].toStringAsFixed(2);
      }
      if (transaction['admin'] != null && transaction['admin'].isNotEmpty) {
        transactionData['admin'] = transaction['admin'];
      }
    } else {
      if (transaction['store'] != null && transaction['store'].isNotEmpty) {
        transactionData['store'] = transaction['store'];
      }
      if (transaction['company'] != null && transaction['company'].isNotEmpty) {
        transactionData['company'] = transaction['company'];
      }
    }

    transactionsData.add(transactionData);
  }

  // تصدير التقرير باستخدام الدالة المخصصة
  await ExcelExportService.exportCardDetailReport(
    context: context,
    cardInfo: cardInfoData,
    balanceSummary: balanceSummaryData,
    transactions: transactionsData,
    cardNumber: _cardNumberController.text.trim(),
  );
}
```

## 📊 النتائج المتوقعة:

### ✅ ما سيظهر في Excel:
1. **معلومات الكارت** (الصف الأول):
   - رقم الكارت
   - اسم العضو
   - رقم الجوال
   - تاريخ التسجيل
   - الرصيد الحالي
   - حالة العضو

2. **ملخص الرصيد** (الصف الثاني):
   - إجمالي الشحن ✅
   - إجمالي الإنفاق ✅
   - إجمالي الهدايا ✅
   - معاملات الشحن ✅

3. **تفاصيل المعاملات** (الصفوف التالية):
   - نوع المعاملة (شحن/صرف)
   - التاريخ والوقت
   - المبلغ
   - الرصيد بعد العملية
   - معلومات إضافية (المشرف، المتجر، الشركة)

## 🧪 للاختبار:

1. شغل التطبيق
2. اذهب لتقرير تفاصيل الكارت
3. أدخل رقم كارت
4. اضغط "بحث"
5. اضغط زر "تصدير Excel"
6. افتح الملف المصدر وتأكد من ظهور:
   - إجمالي الشحن في الصف الثاني
   - إجمالي الإنفاق في الصف الثاني
   - إجمالي الهدايا في الصف الثاني
   - عدد معاملات الشحن في الصف الثاني

## 🔧 الملفات المحدثة:

1. **`lib/services/excel_export_service.dart`**
   - إضافة دالة `exportCardDetailReport()`
   - تحسين تنسيق البيانات

2. **`lib/screens/reports/custom reports/card detail report/card_detail_report_screen.dart`**
   - تحديث دالة `_exportToExcel()`
   - تحسين تحضير البيانات

---
**تاريخ الحل**: ${DateTime.now().toString()}
**الحالة**: ✅ مكتمل وجاهز للاختبار 