import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/barcode_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/back_button_header.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';

class BarcodeSettingsScreen extends StatefulWidget {
  const BarcodeSettingsScreen({super.key});

  @override
  State<BarcodeSettingsScreen> createState() => _BarcodeSettingsScreenState();
}

class _BarcodeSettingsScreenState extends State<BarcodeSettingsScreen> {
  late BarcodeController barcodeController;
  // ignore: unused_field
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initController();
    });
  }

  Future<void> _initController() async {
    barcodeController = Provider.of<BarcodeController>(context, listen: false);
    await barcodeController.loadSettings();
    await barcodeController.scanForDevices();
  }

  Future<void> _saveSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await barcodeController.saveSettings();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          BackButtonHeader(
            title: T('Barcode Reader Settings'),
            icon: Icons.qr_code,
          ),
          Expanded(
            child: Consumer<BarcodeController>(
              builder: (context, controller, child) {
                return _buildContent(controller);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BarcodeController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEnableSection(controller),
          const SizedBox(height: 24),
          _buildDeviceList(controller),
          const SizedBox(height: 24),
          _buildActions(),
        ],
      ),
    );
  }

  Widget _buildEnableSection(BarcodeController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              T('External Barcode Reader'),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: Text(T('Enable External Barcode Reader')),
              subtitle: Text(
                T('Use an external barcode reader device instead of the camera'),
              ),
              value: controller.isExternalBarcodeReaderEnabled,
              onChanged: (value) {
                controller.setExternalBarcodeReaderEnabled(value);
              },
              activeColor: context.newPrimaryColor,
            ),
            const SizedBox(height: 8),
            Text(
              T('When enabled, the application will use the selected external barcode reader instead of the camera for scanning barcodes.'),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceList(BarcodeController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  T('Connected Devices'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: controller.scanForDevices,
                  tooltip: T('Refresh device list'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            controller.isScanning
                ? const Center(
                    child: CircularProgressIndicator(),
                  )
                : controller.availableDevices.isEmpty
                    ? Center(
                        child: Padding(
                          padding: const EdgeInsets.all(24),
                          child: Column(
                            children: [
                              Icon(
                                Icons.devices_other,
                                size: 48,
                                color: Colors.grey.shade400,
                              ),
                              const SizedBox(height: 16),
                              Text(
                                T('No barcode reader devices found'),
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                    : Column(
                        children: controller.availableDevices.map((device) {
                          return RadioListTile<String>(
                            title: Text(device),
                            value: device,
                            groupValue: controller.selectedDevice,
                            onChanged: (value) {
                              controller.setSelectedDevice(value);
                            },
                            activeColor: context.newPrimaryColor,
                          );
                        }).toList(),
                      ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Expanded(
          child: CommonMaterialButton(
            onPressed: _saveSettings,
            label: T('Save Settings'),
            backgroundColor: context.newPrimaryColor,
            textColor: Colors.white,
          ),
        ),
      ],
    );
  }
}
