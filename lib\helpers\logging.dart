import 'dart:io';
import 'dart:async';
import 'package:path_provider/path_provider.dart';

// Method to log errors to a file
Future<void> logErrorToFile(String error, String stackTrace) async {
  try {
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/error_log.txt';
    final file = File(filePath);

    final log = '''
      Date: ${DateTime.now()}
      Error: $error
      StackTrace: $stackTrace
      -----------------------------
    ''';

    await file.writeAsString(log, mode: FileMode.append);
    print("Error logged to file: $filePath");
  } catch (e) {
    print("Failed to log error to file: $e");
  }
}
