class InventoryOperationSectionRequestHistoryDto {
  final int? itemId;
  final String? itemName;
  final double? balance;

  InventoryOperationSectionRequestHistoryDto({
    this.itemId,
    this.itemName,
    this.balance,
  });

  factory InventoryOperationSectionRequestHistoryDto.fromJson(
      Map<String, dynamic> json) {
    return InventoryOperationSectionRequestHistoryDto(
      itemId: json['ItemId'],
      itemName: json['ItemName'],
      balance: (json['Balance'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ItemId': itemId,
      'ItemName': itemName,
      'balance': balance,
    };
  }
}
