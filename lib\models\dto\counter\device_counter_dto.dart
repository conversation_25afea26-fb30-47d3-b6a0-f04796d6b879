class ApiDeviceCounterDTO {
  int? userId;
  String? deviceCode;
  ApiDeviceCounterTypeEnum? referenceType;
  int? counterType;
  int? counter;

  ApiDeviceCounterDTO(
      {this.counter,
      this.counterType,
      this.deviceCode,
      this.referenceType,
      this.userId});

  ApiDeviceCounterDTO.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    deviceCode = json['deviceCode'];
    referenceType = ApiDeviceCounterTypeEnum.values[json['referenceType ']];
    counterType = json['counterType'];
    counter = json['counter'];
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'deviceCode': deviceCode,
      'referenceType': referenceType?.index,
      'counterType': counterType,
      'counter': counter,
    };
  }
}

enum ApiDeviceCounterTypeEnum {
  sales,
  purchase,
  inventoryOperation,
  vouchars,
}
