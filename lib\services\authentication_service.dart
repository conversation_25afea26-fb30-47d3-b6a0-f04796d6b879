import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/models/model/roles_with_permission_model.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

class AuthenticationService with ChangeNotifier {
  List<RolesWithPermission> rolesAndPermissions = [];
  List<int> userRoles = [];
  List<int> userModules = [];
  List<int> userBranches = [];
  bool isMainAdmin = false;

  Future<void> fetchRolesWithPermissions() async {
    try {
      var fromlocalDatabase = await getRolesWithPermissions();
      if (fromlocalDatabase.isNotEmpty) {
        rolesAndPermissions.clear();
        for (var element in fromlocalDatabase) {
          rolesAndPermissions.add(RolesWithPermission.fromJson(element));
        }
        notifyListeners();
        return;
      } else {
        await fetchRolesWithPermissionsFromServer();
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  Future<void> fetchRolesWithPermissionsFromServer() async {
    try {
      if (AppController.isThereConnection == false) return;
      rolesAndPermissions.clear();
      var url = '/Security/GetRolesWithPermission';
      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          for (var element in result.data) {
            setRolesWithPermissions(RolesWithPermission.fromJson(element));
            rolesAndPermissions.add(RolesWithPermission.fromJson(element));
          }
        }
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

  Future<void> clearRolesAndPermissions() async {
    final db = await DatabaseHelper().database;

    await db.transaction((txn) async {
      await txn.delete('Roles');
      await txn.delete('Permissions');
    });
  }

  Future<List<Map<String, dynamic>>> getRolesWithPermissions() async {
    try {
      final db = await DatabaseHelper().database;

      // جلب الأدوار
      final rolesResult = await db.query('Roles');
      if (rolesResult.isEmpty) return [];

      // جلب الصلاحيات
      final permissionsResult = await db.query('Permissions');

      // بناء القائمة الجديدة مع نسخ الـ Maps
      final List<Map<String, dynamic>> rolesWithPermissions = [];

      for (var role in rolesResult) {
        // عمل نسخة قابلة للتعديل
        final roleMap = Map<String, dynamic>.from(role);

        final roleId = roleMap['RoleID'] as int;
        final rolePermissions = permissionsResult
            .where((permission) => permission['RoleID'] == roleId)
            .toList();

        roleMap['Permissions'] = rolePermissions;

        rolesWithPermissions.add(roleMap);
      }

      return rolesWithPermissions;
    } catch (e) {
      print('Error in getRolesWithPermissions: $e');
      return [];
    }
  }

  Future<int> setRolesWithPermissions(RolesWithPermission model) async {
    final db = await DatabaseHelper().database;

    // Use insert with conflict resolution to replace if id exists
    final result = await db.insert(
      'Roles',
      {'RoleID': model.roleID, 'RoleName': model.roleName},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );

    for (var element in model.permissions!) {
      final existing = await db.query(
        'Permissions',
        where: 'PermissionID = ? AND RoleID = ?',
        whereArgs: [element.permissionID, model.roleID],
      );

      if (existing.isNotEmpty) {
        // موجود مسبقًا → تحديث السطر
        await db.update(
          'Permissions',
          {
            'PermissionName': element.permissionName,
            'Parent_ID': element.parentID,
          },
          where: 'PermissionID = ? AND RoleID = ?',
          whereArgs: [element.permissionID, model.roleID],
        );
      } else {
        // غير موجود → إضافة جديد
        await db.insert(
          'Permissions',
          {
            'PermissionID': element.permissionID,
            'PermissionName': element.permissionName,
            'Parent_ID': element.parentID,
            'RoleID': model.roleID,
          },
        );
      }
    }

    return result;
  }

  void setUserRoles(String roles) {
    if (roles.isEmpty) {
      userRoles = [];
      return;
    }
    var data = roles.split(',').map((e) => int.parse(e)).toList();
    userRoles = data;

    notifyListeners();
  }

  void setUserModules(String modules) {
    if (modules.isEmpty) return;
    userModules = modules.split(',').map((e) => int.parse(e)).toList();
    notifyListeners();
  }

  void setUserBranches(String branches) {
    if (branches.isEmpty) return;
    userBranches = branches.split(',').map((e) => int.parse(e)).toList();
    notifyListeners();
  }

  void setIsMainAdmin(bool value) {
    isMainAdmin = value;
    AuthController.setIsMainAdmin(value);
    notifyListeners();
  }

  // bool hasRole(int PermissionsId) {
  //   return userRoles.contains(role);
  // }

  bool hasPermission(int permissionId) {
    if (isMainAdmin) return true;
    for (var role in userRoles) {
      for (var element in rolesAndPermissions) {
        if (element.roleID == role) {
          for (var permission in element.permissions!) {
            if (permission.permissionID == permissionId) {
              return true;
            }
          }
        }
      }
    }
    return false;
  }

  bool hasModule(int module) {
    if (isMainAdmin) return true;
    return userModules.contains(module);
  }

  bool hasBranch(int branch) {
    if (isMainAdmin) return true;
    return userBranches.contains(branch);
  }
}
