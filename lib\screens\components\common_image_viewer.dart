import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

import 'package:photo_view/photo_view_gallery.dart';

class CommonImageViewer extends StatefulWidget {
  const CommonImageViewer(
      {super.key, required this.files, required this.index});
  final List<String> files;
  final int index;

  @override
  State<CommonImageViewer> createState() => _CommonImageViewerState();
}

class _CommonImageViewerState extends State<CommonImageViewer> {
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: widget.index);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Material(
        child: Stack(
          children: [
            PhotoViewGallery.builder(
              backgroundDecoration: const BoxDecoration(
                color: Colors.white,
              ),
              pageController: _pageController,
              itemCount: widget.files.length,
              builder: (BuildContext context, int index) {
                // List<int> bytes = base64.decode(widget.files.elementAt(index));
                return PhotoViewGalleryPageOptions(
                  // imageProvider: MemoryImage(Uint8List.fromList(bytes)),
                  imageProvider: CachedNetworkImageProvider(
                    widget.files[index].toString(),
                  ),
                );
              },
            ),
            Positioned(
              top: 60,
              left: 14,
              child: InkWell(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  decoration: BoxDecoration(
                    color: context.primaryColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.arrow_forward,
                    color: context.backgroundColor,
                    size: 32,
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
