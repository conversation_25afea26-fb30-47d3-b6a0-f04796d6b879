import 'package:flutter/material.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/reports/base_report_screen.dart';
import 'package:inventory_application/screens/reports/widgets/customer_sales_report_widget.dart';
import 'package:provider/provider.dart';

class CustomerSalesReportScreen extends BaseReportScreen {
  const CustomerSalesReportScreen({Key? key}) : super(key: key);

  @override
  State<CustomerSalesReportScreen> createState() => _CustomerSalesReportScreenState();
}

class _CustomerSalesReportScreenState extends BaseReportScreenState<CustomerSalesReportScreen> {
  @override
  ReportType get reportType => ReportType.customerSales;

  @override
  String getReportTitle() => T('Customer Sales');

  @override
  IconData getReportIcon() => Icons.people;

  @override
  Color getHeaderColor() => const Color(0xFF06D6A0);

  @override
  Color getAccentColor() => const Color(0xFF1B9AAA);

  @override
  Future<void> generateReport() async {
    setLoading(true);

    try {
      final reportController = Provider.of<ReportController>(context, listen: false);
      await reportController.generateCustomerSalesReport();
    } catch (e) {
      errorSnackBar(message: 'Error generating customer sales report: $e');
    } finally {
      setLoading(false);
    }
  }

  @override
  Widget buildReportContent(ReportController reportController) {
    final report = reportController.customerSalesReport;
    if (report == null) {
      return Center(
        child: Text(T('No customer report data available')),
      );
    }
    return CustomerSalesReportWidget(report: report);
  }
}
