import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:provider/provider.dart';

class ReportFilterDialog extends StatefulWidget {
  final ReportFilterDTO initialFilter;
  final ReportType selectedReportType;

  const ReportFilterDialog({
    Key? key,
    required this.initialFilter,
    required this.selectedReportType,
  }) : super(key: key);

  @override
  State<ReportFilterDialog> createState() => _ReportFilterDialogState();
}

class _ReportFilterDialogState extends State<ReportFilterDialog> {
  late ReportFilterDTO _filter;
  late ReportType _selectedReportType;

  @override
  void initState() {
    super.initState();
    _filter = widget.initialFilter.copyWith();
    _selectedReportType = widget.selectedReportType;
    _filter = _filter.copyWith(reportType: _selectedReportType);
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Dialog header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  T('Report Filters'),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
            const SizedBox(height: 24),

            // Report type selector
            // Text(
            //   T('Report Type:'),
            //   style: const TextStyle(
            //     fontWeight: FontWeight.bold,
            //   ),
            // ),
            // const SizedBox(height: 8),
            // Container(
            //   width: double.infinity,
            //   padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            //   decoration: BoxDecoration(
            //     color: Colors.grey.shade100,
            //     borderRadius: BorderRadius.circular(8),
            //     border: Border.all(color: Colors.grey.shade300),
            //   ),
            //   child: DropdownButtonHideUnderline(
            //     child: DropdownButton<ReportType>(
            //       value: _selectedReportType,
            //       isExpanded: true,
            //       icon: const Icon(Icons.arrow_drop_down),
            //       onChanged: (ReportType? newValue) {
            //         if (newValue != null) {
            //           setState(() {
            //             _selectedReportType = newValue;
            //             _filter = _filter.copyWith(
            //               reportType: newValue,
            //             );
            //           });
            //         }
            //       },
            //       items: ReportType.values.map((ReportType type) {
            //         return DropdownMenuItem<ReportType>(
            //           value: type,
            //           child: Text(_getReportTypeName(type)),
            //         );
            //       }).toList(),
            //     ),
            //   ),
            // ),
            const SizedBox(height: 24),

            // Date range selector
            Text(
              T('Date Range:'),
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Date filter options based on report type
            _buildDateFilterOptions(),
            const SizedBox(height: 32),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    T('Cancel'),
                    style: TextStyle(
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: () {
                    // Update the filter in the ReportController
                    Provider.of<ReportController>(context, listen: false)
                        .filter = _filter;
                    Navigator.of(context).pop(
                        true); // Return true to indicate filter was applied
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.newPrimaryColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: Text(T('Apply')),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to get report type display name
  String _getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.dailySales:
        return T('Daily Sales');
      case ReportType.monthlySales:
        return T('Monthly Sales');
      case ReportType.productSales:
        return T('Product Sales');
      case ReportType.customerSales:
        return T('Customer Sales');
    }
  }

  // Build date filter options based on report type
  Widget _buildDateFilterOptions() {
    switch (_selectedReportType) {
      case ReportType.dailySales:
        return _buildDailyDateFilter();
      case ReportType.monthlySales:
        return _buildMonthlyDateFilter();
      case ReportType.productSales:
        return _buildProductDateFilter();
      case ReportType.customerSales:
        return _buildCustomerDateFilter();
    }
  }

  // Date filter for daily sales report
  Widget _buildDailyDateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // From date
        Row(
          children: [
            Text(
              T('From:'),
              style: TextStyle(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _filter.fromDate ?? DateTime.now(),
                      firstDate: DateTime(2015),
                      lastDate: DateTime(2101),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: ColorScheme.light(
                              primary: Theme.of(context).primaryColor,
                              onPrimary: Colors.white,
                              onSurface: Colors.black,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setState(() {
                        _filter = _filter.copyWith(
                          fromDate: picked,
                        );
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('yyyy-MM-dd')
                            .format(_filter.fromDate ?? DateTime.now()),
                        style: TextStyle(fontSize: 16),
                      ),
                      Icon(Icons.calendar_today, size: 18),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // To date
        Row(
          children: [
            Text(
              T('To:'),
              style: TextStyle(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 28),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _filter.toDate ?? DateTime.now(),
                      firstDate: DateTime(2015),
                      lastDate: DateTime(2101),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: ColorScheme.light(
                              primary: Theme.of(context).primaryColor,
                              onPrimary: Colors.white,
                              onSurface: Colors.black,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setState(() {
                        _filter = _filter.copyWith(
                          toDate: picked,
                        );
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('yyyy-MM-dd')
                            .format(_filter.toDate ?? DateTime.now()),
                        style: const TextStyle(fontSize: 16),
                      ),
                      const Icon(Icons.calendar_today, size: 18),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Date filter for monthly sales report
  Widget _buildMonthlyDateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Year and month selector
        Row(
          children: [
            Text(
              T('Year:'),
              style: TextStyle(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    value: (_filter.fromDate ?? DateTime.now()).year,
                    isExpanded: true,
                    icon: const Icon(Icons.arrow_drop_down),
                    onChanged: (int? newValue) {
                      if (newValue != null) {
                        final currentDate = _filter.fromDate ?? DateTime.now();
                        final newFromDate =
                            DateTime(newValue, currentDate.month, 1);
                        final lastDay =
                            DateTime(newValue, currentDate.month + 1, 0).day;
                        final newToDate = DateTime(
                            newValue, currentDate.month, lastDay, 23, 59, 59);

                        setState(() {
                          _filter = _filter.copyWith(
                            fromDate: newFromDate,
                            toDate: newToDate,
                          );
                        });
                      }
                    },
                    items: List.generate(5, (index) {
                      final year = DateTime.now().year - index;
                      return DropdownMenuItem<int>(
                        value: year,
                        child: Text(year.toString()),
                      );
                    }),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Text(
              T('Month:'),
              style: TextStyle(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<int>(
                    value: (_filter.fromDate ?? DateTime.now()).month,
                    isExpanded: true,
                    icon: const Icon(Icons.arrow_drop_down),
                    onChanged: (int? newValue) {
                      if (newValue != null) {
                        final currentDate = _filter.fromDate ?? DateTime.now();
                        final newFromDate =
                            DateTime(currentDate.year, newValue, 1);
                        final lastDay =
                            DateTime(currentDate.year, newValue + 1, 0).day;
                        final newToDate = DateTime(
                            currentDate.year, newValue, lastDay, 23, 59, 59);

                        setState(() {
                          _filter = _filter.copyWith(
                            fromDate: newFromDate,
                            toDate: newToDate,
                          );
                        });
                      }
                    },
                    items: List.generate(
                      12,
                      (index) {
                        final month = index + 1;
                        final monthName =
                            DateFormat('MMMM').format(DateTime(2022, month));
                        return DropdownMenuItem<int>(
                          value: month,
                          child: Text(monthName),
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Date range display
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                T('Selected Range:'),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${DateFormat('yyyy-MM-dd').format(_filter.fromDate ?? DateTime.now())} - ${DateFormat('yyyy-MM-dd').format(_filter.toDate ?? DateTime.now())}',
                style: TextStyle(
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Date filter for product sales report
  Widget _buildProductDateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Predefined date ranges
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildDateRangeChip(T('Last 7 Days'), 7),
            _buildDateRangeChip(T('Last 30 Days'), 30),
            _buildDateRangeChip(T('Last 90 Days'), 90),
            _buildDateRangeChip(T('This Year'), 365),
          ],
        ),
        const SizedBox(height: 16),

        // Custom date range
        Text(
          T('Custom Range:'),
          style: TextStyle(
            color: Colors.grey.shade700,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        // From date
        Row(
          children: [
            Text(
              T('From:'),
              style: TextStyle(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _filter.fromDate ?? DateTime.now(),
                      firstDate: DateTime(2015),
                      lastDate: DateTime(2101),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: ColorScheme.light(
                              primary: Theme.of(context).primaryColor,
                              onPrimary: Colors.white,
                              onSurface: Colors.black,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setState(() {
                        _filter = _filter.copyWith(
                          fromDate: picked,
                        );
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('yyyy-MM-dd')
                            .format(_filter.fromDate ?? DateTime.now()),
                        style: const TextStyle(fontSize: 16),
                      ),
                      const Icon(Icons.calendar_today, size: 18),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // To date
        Row(
          children: [
            Text(
              T('To:'),
              style: TextStyle(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 28),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _filter.toDate ?? DateTime.now(),
                      firstDate: DateTime(2015),
                      lastDate: DateTime(2101),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: ColorScheme.light(
                              primary: Theme.of(context).primaryColor,
                              onPrimary: Colors.white,
                              onSurface: Colors.black,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setState(() {
                        _filter = _filter.copyWith(
                          toDate: picked,
                        );
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('yyyy-MM-dd')
                            .format(_filter.toDate ?? DateTime.now()),
                        style: const TextStyle(fontSize: 16),
                      ),
                      const Icon(Icons.calendar_today, size: 18),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Date filter for customer sales report
  Widget _buildCustomerDateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Predefined date ranges
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildDateRangeChip(T('Last 7 Days'), 7),
            _buildDateRangeChip(T('Last 30 Days'), 30),
            _buildDateRangeChip(T('Last 90 Days'), 90),
            _buildDateRangeChip(T('This Year'), 365),
          ],
        ),
        const SizedBox(height: 16),

        // Custom date range
        Text(
          T('Custom Range:'),
          style: TextStyle(
            color: Colors.grey.shade700,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),

        // From date
        Row(
          children: [
            Text(
              T('From:'),
              style: TextStyle(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _filter.fromDate ?? DateTime.now(),
                      firstDate: DateTime(2015),
                      lastDate: DateTime(2101),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: ColorScheme.light(
                              primary: Theme.of(context).primaryColor,
                              onPrimary: Colors.white,
                              onSurface: Colors.black,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setState(() {
                        _filter = _filter.copyWith(
                          fromDate: picked,
                        );
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('yyyy-MM-dd')
                            .format(_filter.fromDate ?? DateTime.now()),
                        style: const TextStyle(fontSize: 16),
                      ),
                      const Icon(Icons.calendar_today, size: 18),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // To date
        Row(
          children: [
            Text(
              T('To:'),
              style: TextStyle(
                color: Colors.grey.shade700,
              ),
            ),
            const SizedBox(width: 28),
            Expanded(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade300),
                ),
                child: InkWell(
                  onTap: () async {
                    final DateTime? picked = await showDatePicker(
                      context: context,
                      initialDate: _filter.toDate ?? DateTime.now(),
                      firstDate: DateTime(2015),
                      lastDate: DateTime(2101),
                      builder: (context, child) {
                        return Theme(
                          data: Theme.of(context).copyWith(
                            colorScheme: ColorScheme.light(
                              primary: Theme.of(context).primaryColor,
                              onPrimary: Colors.white,
                              onSurface: Colors.black,
                            ),
                          ),
                          child: child!,
                        );
                      },
                    );
                    if (picked != null) {
                      setState(() {
                        _filter = _filter.copyWith(
                          toDate: picked,
                        );
                      });
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        DateFormat('yyyy-MM-dd')
                            .format(_filter.toDate ?? DateTime.now()),
                        style: const TextStyle(fontSize: 16),
                      ),
                      const Icon(Icons.calendar_today, size: 18),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  // Helper method to build a date range chip
  Widget _buildDateRangeChip(String label, int days) {
    final now = DateTime.now();
    final endDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
    DateTime startDate;

    if (days == 365) {
      // This year
      startDate = DateTime(now.year, 1, 1);
    } else {
      // Last X days
      startDate = now.subtract(Duration(days: days - 1));
      startDate = DateTime(startDate.year, startDate.month, startDate.day);
    }

    final isSelected = _filter.fromDate?.year == startDate.year &&
        _filter.fromDate?.month == startDate.month &&
        _filter.fromDate?.day == startDate.day &&
        _filter.toDate?.year == endDate.year &&
        _filter.toDate?.month == endDate.month &&
        _filter.toDate?.day == endDate.day;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          setState(() {
            _filter = _filter.copyWith(
              fromDate: startDate,
              toDate: endDate,
            );
          });
        }
      },
      backgroundColor: Colors.grey.shade100,
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      checkmarkColor: Theme.of(context).primaryColor,
      labelStyle: TextStyle(
        color:
            isSelected ? Theme.of(context).primaryColor : Colors.grey.shade800,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }
}
