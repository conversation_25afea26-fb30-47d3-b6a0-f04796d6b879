import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:sqflite/sqflite.dart';
import 'package:inventory_application/controllers/app_controller.dart';

class PaymentTypeController with ChangeNotifier {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  List<ComboBoxDataModel> paymentTypes = [];
  int _fetchedTypeCount = 0;
  bool runningSyncization = false;

  // Getter for the fetchedUnitCount
  int get fetchedTypeCount => _fetchedTypeCount;

  /// Fetches units from local database first, if not available then from server
  Future<void> fetchPaymentTypes() async {
    try {
      var fromLocalDatabase = await getPaymentTypes();
      if (fromLocalDatabase.isNotEmpty) {
        paymentTypes.clear();
        for (var element in fromLocalDatabase) {
          paymentTypes.add(
              ComboBoxDataModel(id: element["ID"], name: element["Name_AR"]));
        }
        notifyListeners();
        return;
      }

      paymentTypes.clear();
      var url = '/PaymentType/GetAllPaymentType';
      var result = await Api.getOne(action: url);
      if (result != null) {
        if (result.isSuccess) {
          List<Map<String, dynamic>> paymentTypesToInsert = [];
          for (var element in result.data) {
            paymentTypesToInsert
                .add({'ID': element["ID"], 'Name_AR': element["Name_AR"]});
            paymentTypes.add(
                ComboBoxDataModel(id: element["ID"], name: element["Name_AR"]));
          }
          // Use bulk insert for better performance
          await _dbHelper.bulkInsert('PaymentType', paymentTypesToInsert);
        }
      }

      notifyListeners();
    } catch (e) {
      if (kDebugMode) {
        print("Error fetching paymentTypes: $e");
      }
    }
  }

  /// Fetches paymentTypes from server with pagination
  Future<void> fetchPayemntTypesFromServer() async {
    try {
      paymentTypes.clear();
      runningSyncization = true;
      _fetchedTypeCount = 0;

      var url = '/PaymentType/GetAllPaymentType';
      var result = await Api.getOne(action: url);

      if (result != null && result.isSuccess) {
        List<Map<String, dynamic>> paymentTypesToInsert = [];
        for (var element in result.data) {
          final paymentType = {
            'ID': element["ID"],
            'Name_AR': element["Name_AR"]
          };
          print('Preparing payment type for insertion: $paymentType');
          paymentTypesToInsert.add(paymentType);
          paymentTypes.add(
              ComboBoxDataModel(id: element["ID"], name: element["Name_AR"]));
          _fetchedTypeCount++;
        }
        print('Bulk inserting ${paymentTypesToInsert.length} payment types');
        await _dbHelper.bulkInsert('PaymentType', paymentTypesToInsert);
      }

      runningSyncization = false;
      notifyListeners();
    } catch (e) {
      runningSyncization = false;
      if (kDebugMode) {
        print("Error fetching paymentTypes from server: $e");
      }
    }
  }

  /// Saves a unit to the local database
  Future<int> setPaymentType(int id, String name) async {
    print('Setting payment type - ID: $id, Name: $name');
    final result = await _dbHelper.insert('PaymentType', {
      'ID': id,
      'Name_AR': name,
    });
    print('Payment type insert result: $result');
    return result;
  }

  /// Gets all units from the local database
  Future<List<Map<String, dynamic>>> getPaymentTypes() async {
    return await _dbHelper.query('PaymentType');
  }

  /// Gets the count of units in the local database
  Future<int> getPayemntTypeCount() async {
    final db = await DatabaseHelper().database;
    var result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM PaymentType WHERE BranchId = ${AppController.currentBranchId}');
    int count = Sqflite.firstIntValue(result) ?? 0;
    _fetchedTypeCount = count;
    return count;
  }

  /// Clears the units table and refetches data from server
  Future<bool> clearAndRefetchData() async {
    try {
      final db = await _dbHelper.database;
      await db.transaction((txn) async {
        await txn.delete(
          'PaymentType',
        );
      });

      await fetchPayemntTypesFromServer();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print("Error clearing and refetching units: $e");
      }
      return false;
    }
  }
}
