import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:inventory_application/helpers/pdf_helper.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_pleasewait.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:share_plus/share_plus.dart';

class InvoicePdfCreaterWidget extends StatefulWidget {
  final InvoiceDto? data;

  const InvoicePdfCreaterWidget({super.key, required this.data});

  @override
  _InvoicePdfCreaterWidgetState createState() =>
      _InvoicePdfCreaterWidgetState();
}

class _InvoicePdfCreaterWidgetState extends State<InvoicePdfCreaterWidget> {
  String? pdfPath;

  @override
  void initState() {
    super.initState();
    generatePdf();
  }

  Future<void> generatePdf() async {
    final file = await createInvoicePdf(widget.data ?? InvoiceDto());

    setState(() {
      pdfPath = file.path;
    });
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: pdfPath == null
                ? const Center(child: PleaseWaitWidget())
                : PDFView(
                    filePath: pdfPath!,
                  ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              children: [
                FloatingActionButton(
                  heroTag: 'back_button_pdf',
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(
                  height: 5,
                ),
                FloatingActionButton(
                  heroTag: 'share_button_pdf',
                  onPressed: () async {
                    if (pdfPath != null) {
                      final pdfFile =
                          XFile(pdfPath!); // Create an XFile from the path
                      Share.shareXFiles([pdfFile],
                          text: 'Here is your invoice PDF.');
                    } else {
                      print('PDF file not available');
                    }
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.share,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(
                  height: 25,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
