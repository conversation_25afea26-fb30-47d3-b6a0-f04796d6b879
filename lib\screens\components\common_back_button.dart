import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class CommonBackButton extends StatelessWidget {
  const CommonBackButton({
    super.key,
    required this.onClick,
    this.backgroundColors,
    this.icon,
  });

  final void Function() onClick;
  final Color? backgroundColors;
  final Icon? icon;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onClick,
      child: Container(
        margin: const EdgeInsets.only(bottom: 20),
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: context.onSecondary,
          borderRadius: BorderRadius.circular(10),
        ),
        child: icon ?? Image.asset('assets/images/back_button.png'),
      ),
    );
  }
}
