class CateogryModel {
  int? iD;
  String? name;
  String? code;
  int? parentID;
  String? parentName;
  int? levelType;
  bool? isParent;

  CateogryModel({
    this.iD,
    this.name,
    this.code,
    this.parentID,
    this.parentName,
    this.levelType,
    this.isParent,
  });

  CateogryModel.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    name = json['Name'];
    code = json['Code'];
    parentID = json['Parent_ID'];
    parentName = json['Parent_Name'];
    levelType = json['Level_Type'];
    isParent = json['isParent'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ID'] = iD;
    data['Name'] = name;
    data['Code'] = code;
    data['Parent_ID'] = parentID;
    data['Parent_Name'] = parentName;
    data['Level_Type'] = levelType;
    data['isParent'] = isParent;

    return data;
  }
}
