# Inventory Comparison Feature

## Overview
تم إضافة ميزة جديدة لمقارنة الجرد الفعلي مع الأرصدة المخزنة في السيرفر.

## Features Added

### 1. Inventory Comparison Button
- تم إضافة زر "جرد" لكل جلسة جرد في قائمة الجرد
- الزر موجود في `lib/screens/inventory/stocktaking/stocktaking_list_screen.dart`
- يستخدم أيقونة `Icons.analytics_outlined`

### 2. New Inventory Comparison Screen
الملف: `lib/screens/inventory/comparison/inventory_comparison_screen.dart`

#### الميزات:
- عرض اسم المخزن وعدد الأصناف
- مقارنة الكميات المجرودة مع الأرصدة الموجودة في السيرفر
- تصنيف الأصناف حسب الحالة:
  - **مطابق** (أخضر): عندما تكون الكمية المجرودة = الرصيد في السيرفر
  - **فائض** (برتقالي): عندما تكون الكمية المجرودة > الرصيد في السيرفر  
  - **عجز** (أحمر): عندما تكون الكمية المجرودة < الرصيد في السيرفر

#### العرض:
- كل صنف يُعرض في كارد منفصل
- إطار ملون حسب حالة الصنف
- عرض ثلاث قيم: الكمية المجرودة، الرصيد في السيرفر، والفرق
- أيقونات وألوان مميزة لكل حالة

### 3. Inventory Comparison Service
الملف: `lib/services/inventory_comparison_service.dart`

#### الوظيفة:
- استدعاء API: `/InventoryOperation/GetBalaceForStckTaking`
- إرسال: `{"StoreId": "3223", "ItemIds": [2970,2971]}`
- استقبال: `[{"ItemId": 2970, "Quantity": 868.00}, {"ItemId": 2971, "Quantity": -10.00}]`
- تحويل البيانات إلى Map للمقارنة السريعة

## How to Use

1. افتح قائمة الجرد (`StocktakingListScreen`)
2. اختر أي جلسة جرد موجودة
3. اضغط على زر "جرد" 
4. ستظهر صفحة المقارنة بين الجرد الفعلي والأرصدة المخزنة
5. راجع الأصناف المصنفة حسب الحالة (مطابق/فائض/عجز)

## Technical Details

### Data Flow:
1. استخراج الأصناف المجرودة من بيانات الجرد المحفوظة محلياً
2. استدعاء API للحصول على الأرصدة الحالية للأصناف
3. مقارنة الكميات وحساب الفروق
4. عرض النتائج بتصنيف مرئي واضح

### Colors Used:
- **أخضر**: للأصناف المطابقة
- **برتقالي**: للفائض
- **أحمر**: للعجز
- **أزرق**: للكميات المجرودة
- **بنفسجي**: لأرصدة السيرفر

### Navigation:
```dart
// من قائمة الجرد إلى صفحة المقارنة
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => InventoryComparisonScreen(
      stocktakingData: draft,
    ),
  ),
);
```

## Files Modified/Created:

### Modified:
- `lib/screens/inventory/stocktaking/stocktaking_list_screen.dart`
  - إضافة زر الجرد
  - إضافة ميثود `_openInventoryComparison`

### Created:
- `lib/screens/inventory/comparison/inventory_comparison_screen.dart`
- `lib/services/inventory_comparison_service.dart`

## Dependencies:
- يستخدم المكتبات الموجودة مسبقاً
- لا يتطلب dependencies إضافية

## Notes:
- الميزة تدعم Pull-to-Refresh لتحديث البيانات
- معالجة أخطاء API والشبكة
- دعم كامل للترجمة بدون الحاجة لإضافة نصوص جديدة 