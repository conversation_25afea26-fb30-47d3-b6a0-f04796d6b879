import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/invoices/invoices/invoice%20details/invoice_details_for_local_screen.dart';
import 'package:provider/provider.dart';

class PosInvoicesListDialog extends StatefulWidget {
  const PosInvoicesListDialog({super.key});

  @override
  State<PosInvoicesListDialog> createState() => _PosInvoicesListDialogState();
}

class _PosInvoicesListDialogState extends State<PosInvoicesListDialog> {
  var invoices = List<InvoiceDtoWithLiteId>.empty();
  var filteredInvoices = List<InvoiceDtoWithLiteId>.empty();
  bool isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_onSearchChanged);
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await fetchInvoices();
    });
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterInvoices();
    });
  }

  void _filterInvoices() {
    if (_searchQuery.isEmpty) {
      filteredInvoices = List.from(invoices);
    } else {
      filteredInvoices = invoices.where((invoice) {
        final invoiceData = invoice.data;
        final code = invoiceData?.code?.toLowerCase() ?? '';
        final appCode = invoiceData?.appReferanceCode?.toLowerCase() ?? '';
        final total = invoiceData?.total?.toString() ?? '';
        final customerName = invoiceData?.customerName?.toLowerCase() ?? '';

        return code.contains(_searchQuery) ||
            appCode.contains(_searchQuery) ||
            total.contains(_searchQuery) ||
            customerName.contains(_searchQuery);
      }).toList();
    }
  }

  Future<bool> fetchInvoices() async {
    setState(() {
      isLoading = true;
    });

    try {
      var data = await Provider.of<InvoiceController>(context, listen: false)
          .fetchLocalInvoices();

      invoices = data
          .where((invoice) => (invoice.data?.code ?? "").startsWith("SI"))
          .toList();

      invoices.sort((a, b) {
        final aDate = a.data?.entryDate ?? DateTime.now();
        final bDate = b.data?.entryDate ?? DateTime.now();
        return bDate.compareTo(aDate);
      });

      filteredInvoices = List.from(invoices);
      setState(() {
        isLoading = false;
      });
      return true;
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: double.infinity,
        constraints: BoxConstraints(
          maxWidth: context.width > 800 ? 800 : context.width - 32,
          maxHeight: context.height * 0.85,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 1,
            ),
          ],
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: context.newPrimaryColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.receipt_long,
                    color: Colors.white,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      T('Local Invoices'),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: Colors.white),
                  ),
                ],
              ),
            ),

            // Search Bar
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                border: Border(
                  bottom: BorderSide(color: Colors.grey.withOpacity(0.2)),
                ),
              ),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: T('Search by code, total, or customer name...'),
                  prefixIcon: const Icon(Icons.search, color: Colors.grey),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: Colors.grey),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),

            // Content
            Expanded(
              child: isLoading
                  ? const Center(
                      child: CircularProgressIndicator(),
                    )
                  : filteredInvoices.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.receipt_outlined,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                _searchQuery.isNotEmpty
                                    ? T('No invoices match your search')
                                    : T('No invoices found'),
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                _searchQuery.isNotEmpty
                                    ? T('Try searching with different keywords')
                                    : T('Local invoices will appear here'),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ),
                        )
                      : Column(
                          children: [
                            // Results count
                            if (_searchQuery.isNotEmpty)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.search,
                                      size: 16,
                                      color: Colors.grey[600],
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      '${T('Found')} ${filteredInvoices.length} ${T('invoices')}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[600],
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            // Invoice list
                            Expanded(
                              child: ListView.builder(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                itemCount: filteredInvoices.length,
                                itemBuilder: (context, index) {
                                  final invoice = filteredInvoices[index];
                                  return _buildInvoiceCard(invoice, index);
                                },
                              ),
                            ),
                          ],
                        ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceCard(InvoiceDtoWithLiteId invoice, int index) {
    final invoiceData = invoice.data;
    final invoiceDate = invoiceData?.entryDate ?? DateTime.now();
    final invoiceCode = invoiceData?.code ?? T('Unknown');
    final total = invoiceData?.total ?? 0.0;
    final customerName = invoiceData?.customerName ?? T('Walk-in Customer');

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Row
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: context.newPrimaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '#${index + 1}',
                    style: TextStyle(
                      color: context.newPrimaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    invoiceCode,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    T('Local'),
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Invoice Details
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailRow(
                        Icons.person_outline,
                        T('Customer'),
                        customerName,
                      ),
                      const SizedBox(height: 8),
                      _buildDetailRow(
                        Icons.calendar_today_outlined,
                        T('Date'),
                        DateFormat('yyyy-MM-dd HH:mm').format(invoiceDate),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      T('Total'),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${total.toStringAsFixed(2)}',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: context.newPrimaryColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewInvoiceDetails(invoice),
                    icon: const Icon(Icons.visibility_outlined, size: 18),
                    label: Text(T('View Details')),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: context.newPrimaryColor,
                      side: BorderSide(color: context.newPrimaryColor),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _returnInvoice(invoice),
                    icon: const Icon(Icons.keyboard_return, size: 18),
                    label: Text(T('Return')),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
              fontWeight: FontWeight.w600,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  void _viewInvoiceDetails(InvoiceDtoWithLiteId invoice) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InoviceDetailsForLocalPage(
          id: invoice.id ?? 0,
        ),
      ),
    );
  }

  Future<void> _returnInvoice(InvoiceDtoWithLiteId invoice) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning_amber_rounded, color: Colors.orange),
            const SizedBox(width: 8),
            Text(T('Return Invoice')),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(T('Are you sure you want to return this invoice?')),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${T('Invoice')}: ${invoice.data?.code ?? ''}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text(
                      '${T('Total')}: ${invoice.data?.total?.toStringAsFixed(2) ?? '0.00'}'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(T('Cancel')),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child:
                Text(T('Return'), style: const TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      pleaseWaitDialog(context: context, isShown: true);
      try {
        var invoiceCode = invoice.data?.appReferanceCode ?? "";

        var result =
            await Provider.of<SaleInvoiceController>(context, listen: false)
                .returnSaleInvoiceByCode(invoiceCode);

        // ignore: use_build_context_synchronously
        pleaseWaitDialog(context: context, isShown: false);

        if (result.isSuccess && mounted) {
          Navigator.of(context).pop();
          successSnackBar(
            message: T('Invoice returned successfully'),
            context: context,
          );
        }

        // Refresh the list
        await fetchInvoices();
      } catch (e) {
        if (mounted) {
          errorSnackBar(
            message: T('Failed to return invoice'),
            context: context,
          );
        }
      }
    }
  }
}
