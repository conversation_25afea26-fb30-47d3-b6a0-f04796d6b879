import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/main.dart';

//===============================================
void errorSnackBar(
    {required String message, String? title, BuildContext? context}) {
  // Use provided context or global navigator key context
  final ctx = context ?? navigatorKey.currentContext!;

  ScaffoldMessenger.of(ctx).hideCurrentSnackBar();
  ScaffoldMessenger.of(ctx).showSnackBar(
    SnackBar(
      content: Row(
        children: [
          const Icon(
            Icons.error_outline,
            color: Colors.white,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null)
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                Text(
                  message,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: Colors.red.shade800,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      margin: const EdgeInsets.all(10),
      duration: const Duration(seconds: 3),
      action: SnackBarAction(
        label: T('Dismiss'),
        textColor: Colors.white,
        onPressed: () {
          ScaffoldMessenger.of(ctx).hideCurrentSnackBar();
        },
      ),
    ),
  );
}

//===============================================================
void successSnackBar(
    {required String message, String? title, BuildContext? context}) {
  // Use provided context or global navigator key context
  final ctx = context ?? navigatorKey.currentContext!;

  ScaffoldMessenger.of(ctx).hideCurrentSnackBar();
  ScaffoldMessenger.of(ctx).showSnackBar(
    SnackBar(
      content: Row(
        children: [
          const Icon(
            Icons.check_circle_outline,
            color: Colors.white,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null)
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                Text(
                  message,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      backgroundColor: Colors.green.shade700,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      margin: const EdgeInsets.all(10),
      duration: const Duration(seconds: 2),
      action: SnackBarAction(
        label: T('OK'),
        textColor: Colors.white,
        onPressed: () {
          ScaffoldMessenger.of(ctx).hideCurrentSnackBar();
        },
      ),
    ),
  );
}

// Alternative success message with gradient background for displaying in UI (not as a SnackBar)
Widget successMessageBox({required String title, required String message}) {
  return Container(
    width: double.infinity,
    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
    decoration: BoxDecoration(
      gradient: LinearGradient(
        colors: [Colors.green.shade600, Colors.green.shade800],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
      borderRadius: BorderRadius.circular(15),
      boxShadow: [
        BoxShadow(
          color: Colors.green.withOpacity(0.3),
          spreadRadius: 1,
          blurRadius: 4,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Row(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.check_circle,
            color: Colors.white,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                message,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.white,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

//===============================================================
Future<bool?> showConfirmDialog({
  String? title,
  String? content,
  String? backText,
  String? confirmText,
  Function? delete,
}) async {
  title ??= 'Confirm Operation'.tr();
  content ??= 'Are you sure you want to continue?'.tr();

  return showDialog<bool>(
    context: navigatorKey.currentContext!,
    builder: (BuildContext context) {
      return AlertDialog(
        title: Text(title!),
        content: SingleChildScrollView(
          child: ListBody(
            children: [
              Text(content!),
            ],
          ),
        ),
        actions: [
          TextButton(
            child: Text(backText ?? 'Cancel'.tr()),
            onPressed: () {
              Navigator.of(context).pop(false);
            },
          ),
          TextButton(
            child: Text(confirmText ?? 'Confirm'.tr()),
            onPressed: () {
              if (delete != null) {
                delete();
              }
              Navigator.of(context).pop(true);
            },
          ),
        ],
      );
    },
  );
}

void pleaseWaitDialog({required BuildContext context, required bool isShown}) {
  if (!isShown) {
    // Navigator.pop(context);
    Navigator.of(context, rootNavigator: true).pop();
  } else {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 6, sigmaY: 6),
          child: AlertDialog(
            backgroundColor: context.backgroundColor,
            shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(20))),
            icon: SizedBox(
              width: context.width - 40,
              height: 140,
              child: Image.asset(
                'assets/images/base_images/loading.gif',
              ),
            ),
            iconColor: context.primaryColor,
            // <-- SEE HERE
            title: Text(
              "الرجاء الانتظار",
              style: TextStyle(color: context.primaryColor),
            ),
          ),
        );
      },
    );
  }
}

//-------------------------------------------------------------------------------------
