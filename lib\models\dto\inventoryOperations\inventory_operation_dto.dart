//----------------------------------------------------------
import 'package:inventory_application/models/model/inventory_operation_model.dart';

class InventoryOperationDtoWithLiteId {
  int? id;
  String? status;
  InventoryOperationModel? data;
  InventoryOperationDtoWithLiteId({this.id, this.data, this.status});

  factory InventoryOperationDtoWithLiteId.fromJson(Map<String, dynamic> json) {
    return InventoryOperationDtoWithLiteId(
      id: json['id'] as int?, // Parse the id from the JSON
      data: json['data'] != null
          ? InventoryOperationModel.fromJson(json['data'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'data': data?.toJson(),
      'status': status,
    };
  }
}
