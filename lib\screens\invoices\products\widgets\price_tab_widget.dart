import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';

class PriceTabWidget extends StatelessWidget {
  final ProductDTO data;
  const PriceTabWidget({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 8),
          padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.3),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            children: [
              Row(
                children: [
                  Text(
                    " الكمية المتبقية : ",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: context.primaryColor),
                  ),
                  Text(
                    (data.stock ?? "لايوجد خصم").toString(),
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              Divider(
                color: context.colors.primary,
                thickness: 1,
              ),
              Row(
                children: [
                  Text(
                    " الكمية الكلية : ",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: context.primaryColor),
                  ),
                  SizedBox(
                    width: context.width / 3,
                    child: Text(
                      data.quantity.toString(),
                      style: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
              Divider(
                color: context.colors.primary,
                thickness: 1,
              ),
              Row(
                children: [
                  Text(
                    " خصم : ",
                    style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: context.primaryColor),
                  ),
                  Text(
                    (data.discountValue ?? "لايوجد خصم").toString(),
                    style: const TextStyle(
                        fontSize: 12, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              Divider(
                color: context.colors.primary,
                thickness: 1,
              ),
              Row(
                children: [
                  SizedBox(
                    child: Text(
                      " السعر : ",
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: context.primaryColor),
                    ),
                  ),
                  SizedBox(
                    child: Text(
                      data.price.toString(),
                      style: const TextStyle(
                          fontSize: 12, fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }
}
