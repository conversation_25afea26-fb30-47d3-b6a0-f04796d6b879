import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class PleaseWaitWidget extends StatelessWidget {
  const PleaseWaitWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () {
        return Future.value(true);
      },
      child: Scaffold(
        body: AlertDialog(
          alignment: Alignment.center,
          backgroundColor: context.backgroundColor,
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(20))),
          icon: SizedBox(
            width: context.width - 40,
            height: 140,
            child: Image.asset(
              'assets/images/base_images/loading.gif',
            ),
          ),
          iconColor: context.primaryColor,
          // <-- SEE HERE
          title: Text(
            "الرجاء الانتظار",
            style: TextStyle(color: context.primaryColor),
          ),
        ),
      ),
    );
  }
}
