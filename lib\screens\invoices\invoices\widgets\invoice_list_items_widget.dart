import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/components/common_text_field.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_item_details_dialog_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/unit_selection_dialog.dart';
import 'package:inventory_application/base/extension/double_extension.dart';

class InvoiceListItemsWidget extends StatefulWidget {
  const InvoiceListItemsWidget({
    super.key,
    required this.id,
    required this.selectedInvoiceProduct,
    required this.onUpdateQuantity,
    required this.onUpdatePrice,
    required this.onDeleteProduct,
    required this.onChangeWarehouse,
    required this.barcode,
    this.virtualProductId,
    required this.onUpdateUnit, // Make onUpdateUnit required
  });
  final int id;
  final String? barcode;
  final String? virtualProductId;
  final List<ProductDTO> selectedInvoiceProduct;
  final Function onUpdateQuantity;
  final Function onUpdatePrice;
  final Function onDeleteProduct;
  final Function onChangeWarehouse;
  final Function? onUpdateUnit; // New callback for unit updates

  @override
  State<InvoiceListItemsWidget> createState() => _InvoiceListItemsWidgetState();
}

class _InvoiceListItemsWidgetState extends State<InvoiceListItemsWidget> {
  var quantitySelctorController = TextEditingController(text: "1");
  var totalController = TextEditingController(text: "0");
  var priceController = TextEditingController(text: "0");
  Timer? _quantityUpdateTimer;

  @override
  void initState() {
    super.initState();
    // Find the correct product using either virtualProductId or barcode
    ProductDTO model;
    if (widget.virtualProductId != null) {
      model = widget.selectedInvoiceProduct.firstWhere(
        (element) => element.virtualProductId == widget.virtualProductId,
      );
    } else {
      model = widget.selectedInvoiceProduct.firstWhere((element) =>
          element.id == widget.id && element.barcode == widget.barcode);
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      quantitySelctorController =
          TextEditingController(text: (model.quantity ?? 1).toString());

      totalController = TextEditingController(
          text:
              (model.total?.covertDoubleToMoneyReturnString(model.total ?? 0) ??
                      0)
                  .toString());
    });
    priceController = TextEditingController(
        text: (model.price?.covertDoubleToMoneyReturnString(model.price ?? 0) ??
                0)
            .toString());
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Find the correct product using either virtualProductId or barcode
    ProductDTO textProvider;
    if (widget.virtualProductId != null) {
      textProvider = widget.selectedInvoiceProduct.firstWhere(
        (element) => element.virtualProductId == widget.virtualProductId,
      );
    } else {
      textProvider = widget.selectedInvoiceProduct.firstWhere((element) =>
          element.id == widget.id && element.barcode == widget.barcode);
    }

    totalController.text = (textProvider.total
                ?.covertDoubleToMoneyReturnString(textProvider.total ?? 0) ??
            0)
        .toString();
  }

  @override
  void didUpdateWidget(InvoiceListItemsWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the list reference has changed or if the product within the list has changed
    if (widget.selectedInvoiceProduct != oldWidget.selectedInvoiceProduct ||
        _hasProductChanged()) {
      // Find the current product
      ProductDTO currentProduct;
      if (widget.virtualProductId != null) {
        currentProduct = widget.selectedInvoiceProduct.firstWhere(
          (element) => element.virtualProductId == widget.virtualProductId,
        );
      } else {
        currentProduct = widget.selectedInvoiceProduct.firstWhere((element) =>
            element.id == widget.id && element.barcode == widget.barcode);
      }

      // Update the text controllers with latest values
      quantitySelctorController.text =
          (currentProduct.quantity ?? 1).toString();
      totalController.text = (currentProduct.total
                  ?.covertDoubleToMoneyReturnString(
                      currentProduct.total ?? 0) ??
              0)
          .toString();
    }
  }

  // Helper method to check if the product data has changed
  bool _hasProductChanged() {
    ProductDTO currentProduct;
    if (widget.virtualProductId != null) {
      currentProduct = widget.selectedInvoiceProduct.firstWhere(
        (element) => element.virtualProductId == widget.virtualProductId,
      );
    } else {
      currentProduct = widget.selectedInvoiceProduct.firstWhere((element) =>
          element.id == widget.id && element.barcode == widget.barcode);
    }

    // Check if quantity or total has changed
    return currentProduct.quantity.toString() !=
            quantitySelctorController.text ||
        currentProduct.total.toString() !=
            totalController.text.replaceAll(RegExp(r'[^\d.]'), '');
  }

  @override
  Widget build(BuildContext context) {
    // Find the correct product using either virtualProductId or barcode
    ProductDTO model;
    if (widget.virtualProductId != null) {
      model = widget.selectedInvoiceProduct.firstWhere(
        (element) => element.virtualProductId == widget.virtualProductId,
      );
    } else {
      model = widget.selectedInvoiceProduct.firstWhere((element) =>
          element.id == widget.id && element.barcode == widget.barcode);
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      width: double.infinity,
      decoration: BoxDecoration(
        color: context.onBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.grey.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Product title and code section
          Container(
            padding: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.withOpacity(0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                // Product image or icon placeholder
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: context.colors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.inventory_2_outlined,
                    color: context.colors.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                // Product title and code
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${model.title} ${model.attribute?.map((e) => e.itemsAttributeOptions?.map((e) => e.optionName).join(","))}',
                        style: TextStyle(
                          color: context.colors.scrim,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: context.colors.primary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              model.code ?? "",
                              style: TextStyle(
                                color: context.colors.primary,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Warehouse and more options
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () {
                        invoiceListItemDetailsDialog(
                          context: context,
                          id: model.id ?? 0,
                          barcode: widget.barcode,
                          virtualProductId: widget.virtualProductId,
                          onDelete: () {
                            Navigator.of(context).pop();
                            if (widget.virtualProductId != null) {
                              widget.onDeleteProduct(
                                  model.id ?? 0, widget.virtualProductId);
                            } else if (model.virtualProductId != null) {
                              widget.onDeleteProduct(
                                  model.id ?? 0, model.virtualProductId);
                            } else {
                              widget.onDeleteProduct(model.id ?? 0);
                            }
                          },
                          onSelect: (id, name) {
                            Navigator.of(context).pop();
                            if (widget.virtualProductId != null) {
                              widget.onChangeWarehouse(model.id ?? 0, id, name,
                                  widget.virtualProductId);
                            } else if (model.virtualProductId != null) {
                              widget.onChangeWarehouse(model.id ?? 0, id, name,
                                  model.virtualProductId);
                            } else {
                              widget.onChangeWarehouse(model.id ?? 0, id, name);
                            }
                          },
                          selectedInvoiceProduct: widget.selectedInvoiceProduct,
                        );
                      },
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.warehouse_outlined,
                              color: context.colors.secondary,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              model.warehouseName ?? "",
                              style: TextStyle(
                                color: context.colors.secondary,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.more_vert,
                              color: context.colors.secondary,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 5),
                    InkWell(
                      onTap: () async {
                        if (widget.onUpdateUnit != null) {
                          final selectedUnit = await showUnitSelectionDialog(
                            context: context,
                            product: model,
                          );

                          if (selectedUnit != null) {
                            if (widget.virtualProductId != null) {
                              widget.onUpdateUnit!(model.id ?? 0, selectedUnit,
                                  widget.virtualProductId);
                            } else {
                              widget.onUpdateUnit!(model.id ?? 0, selectedUnit);
                            }

                            // Update the price controller
                            setState(() {
                              priceController.text =
                                  (selectedUnit.salesPrice ?? 0).toString();

                              // Calculate and update the total
                              double quantity = double.tryParse(
                                      quantitySelctorController.text) ??
                                  0;
                              double price = selectedUnit.salesPrice ?? 0;
                              double total = quantity * price;
                              totalController.text = total
                                  .covertDoubleToMoneyReturnString(total)
                                  .toString();
                            });
                          }
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(5),
                        decoration: BoxDecoration(
                          color: Colors.grey.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.straighten,
                              color: context.colors.primary,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              model.uniteName ?? "قطعة",
                              style: TextStyle(
                                color: context.colors.primary,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Icon(
                              Icons.more_vert,
                              color: context.colors.primary,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          // Warehouse and Unit row

          const SizedBox(height: 8),

          // Quantity, price, and total row
          Row(
            children: [
              // Quantity section
              Expanded(
                flex: 2,
                child: Container(
                  height: 48,
                  margin: const EdgeInsets.symmetric(horizontal: 0),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      // Minus button
                      InkWell(
                        onTap: () {
                          double currentQty =
                              double.tryParse(quantitySelctorController.text) ??
                                  0;
                          if (currentQty > 1) {
                            double newQty = currentQty - 1;
                            quantitySelctorController.text = newQty.toString();
                            if (widget.virtualProductId != null) {
                              widget.onUpdateQuantity(model.id ?? 0, newQty,
                                  widget.virtualProductId);
                            } else {
                              widget.onUpdateQuantity(model.id ?? 0, newQty);
                            }

                            // Calculate and update the total
                            double price =
                                double.tryParse(priceController.text) ?? 0;
                            double total = newQty * price;
                            totalController.text = total
                                .covertDoubleToMoneyReturnString(total)
                                .toString();
                          }
                        },
                        child: Container(
                          width: 30,
                          height: 48,
                          decoration: BoxDecoration(
                            color: context.colors.primary.withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(7),
                              bottomLeft: Radius.circular(7),
                            ),
                          ),
                          child: Icon(
                            Icons.remove,
                            color: context.colors.primary,
                            size: 16,
                          ),
                        ),
                      ),
                      // Quantity input
                      Expanded(
                        child: CommonTextField(
                          controller: quantitySelctorController,
                          keyboardType: TextInputType.number,
                          floatingLabelBehavior: FloatingLabelBehavior.never,
                          label: '',
                          onChanged: (quan) async {
                            if (quan.isNotEmpty) {
                              double quantity = double.tryParse(quan) ?? 0;
                              double price =
                                  double.tryParse(priceController.text) ?? 0;
                              double total = quantity * price;

                              // Update the total controller immediately for visual feedback
                              totalController.text = total
                                  .covertDoubleToMoneyReturnString(total)
                                  .toString();

                              // Cancel previous timer
                              _quantityUpdateTimer?.cancel();

                              // Start new timer for updating the controller
                              _quantityUpdateTimer =
                                  Timer(const Duration(milliseconds: 500), () {
                                if (widget.virtualProductId != null) {
                                  widget.onUpdateQuantity(model.id ?? 0,
                                      quantity, widget.virtualProductId);
                                } else {
                                  widget.onUpdateQuantity(
                                      model.id ?? 0, quantity);
                                }
                              });
                            }
                          },
                        ),
                      ),
                      // Plus button
                      InkWell(
                        onTap: () {
                          double currentQty =
                              double.tryParse(quantitySelctorController.text) ??
                                  0;
                          double newQty = currentQty + 1;
                          quantitySelctorController.text = newQty.toString();
                          if (widget.virtualProductId != null) {
                            widget.onUpdateQuantity(
                                model.id ?? 0, newQty, widget.virtualProductId);
                          } else {
                            widget.onUpdateQuantity(model.id ?? 0, newQty);
                          }

                          // Calculate and update the total
                          double price =
                              double.tryParse(priceController.text) ?? 0;
                          double total = newQty * price;
                          totalController.text = total
                              .covertDoubleToMoneyReturnString(total)
                              .toString();
                        },
                        child: Container(
                          width: 30,
                          height: 48,
                          decoration: BoxDecoration(
                            color: context.colors.primary.withOpacity(0.1),
                            borderRadius: const BorderRadius.only(
                              topRight: Radius.circular(7),
                              bottomRight: Radius.circular(7),
                            ),
                          ),
                          child: Icon(
                            Icons.add,
                            color: context.colors.primary,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Price section
              Expanded(
                flex: 2,
                child: Container(
                  height: 48,
                  // margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    children: [
                      Expanded(
                        child: CommonTextField(
                          controller: priceController,
                          keyboardType: TextInputType.number,
                          floatingLabelBehavior: FloatingLabelBehavior.never,
                          label: '',
                          onChanged: (price) {
                            if (price.isNotEmpty) {
                              double priceValue = double.tryParse(price) ?? 0;
                              double quantity = double.tryParse(
                                      quantitySelctorController.text) ??
                                  0;
                              double total = quantity * priceValue;

                              if (widget.virtualProductId != null) {
                                widget.onUpdatePrice(model.id ?? 0, priceValue,
                                    widget.virtualProductId);
                              } else {
                                widget.onUpdatePrice(model.id ?? 0, priceValue);
                              }
                              setState(() {
                                totalController.text = total
                                    .covertDoubleToMoneyReturnString(total)
                                    .toString();
                              });
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Total section
              Expanded(
                flex: 2,
                child: Container(
                  height: 48,
                  // margin: const EdgeInsets.symmetric(horizontal: 4),
                  decoration: BoxDecoration(
                    color: context.colors.primary.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: context.colors.primary.withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: CommonTextField(
                          enabled: false,
                          floatingLabelBehavior: FloatingLabelBehavior.never,
                          controller: totalController,
                          label: '',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          // SizedBox(height: 5),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _quantityUpdateTimer?.cancel();
    quantitySelctorController.dispose();
    totalController.dispose();
    priceController.dispose();
    super.dispose();
  }
}
