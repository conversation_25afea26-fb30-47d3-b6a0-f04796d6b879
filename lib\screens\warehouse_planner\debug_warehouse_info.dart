import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/warehouse_planner_provider.dart';

/// شاشة تشخيص مشاكل المخططات
class DebugWarehouseInfo extends StatefulWidget {
  const DebugWarehouseInfo({Key? key}) : super(key: key);

  @override
  State<DebugWarehouseInfo> createState() => _DebugWarehouseInfoState();
}

class _DebugWarehouseInfoState extends State<DebugWarehouseInfo> {
  List<Map<String, dynamic>> _fileInfos = [];
  bool _isLoading = true;
  String _saveDirectory = '';

  @override
  void initState() {
    super.initState();
    _analyzeFiles();
  }

  Future<void> _analyzeFiles() async {
    setState(() => _isLoading = true);

    try {
      final provider =
          Provider.of<WarehousePlannerProvider>(context, listen: false);
      final saveDir = await provider.getSaveDirectory();

      setState(() {
        _saveDirectory = saveDir;
      });

      final savedFiles = await provider.getSavedWarehouses();
      List<Map<String, dynamic>> fileInfos = [];

      for (final file in savedFiles) {
        Map<String, dynamic> info = {
          'path': file.path,
          'name': file.path.split('/').last,
          'exists': await file.exists(),
          'size': 0,
          'content': '',
          'isValid': false,
          'error': null,
        };

        if (info['exists'] as bool) {
          try {
            final stat = await file.stat();
            info['size'] = stat.size;

            final fileAsFile = File(file.path);
            final content = await fileAsFile.readAsString();
            info['content'] = content.length > 100
                ? '${content.substring(0, 100)}...'
                : content;

            // محاولة تحليل JSON
            try {
              final jsonData = json.decode(content);
              info['isValid'] = jsonData != null && jsonData.containsKey('id');
              if (info['isValid'] as bool) {
                info['layoutName'] = jsonData['name'] ?? 'غير محدد';
                info['layoutId'] = jsonData['id'];
              }
            } catch (e) {
              info['error'] = 'JSON parsing error: $e';
            }
          } catch (e) {
            info['error'] = 'File reading error: $e';
          }
        } else {
          info['error'] = 'File does not exist';
        }

        fileInfos.add(info);
      }

      setState(() {
        _fileInfos = fileInfos;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      print('Error analyzing files: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص ملفات المخططات'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _analyzeFiles,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المجلد
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'مجلد الحفظ:',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          SelectableText(_saveDirectory),
                          const SizedBox(height: 8),
                          Text('عدد الملفات: ${_fileInfos.length}'),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // قائمة الملفات
                  const Text(
                    'تفاصيل الملفات:',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),

                  if (_fileInfos.isEmpty)
                    const Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Text('لا توجد ملفات'),
                      ),
                    )
                  else
                    ..._fileInfos.map((info) => _buildFileInfoCard(info)),
                ],
              ),
            ),
    );
  }

  Widget _buildFileInfoCard(Map<String, dynamic> info) {
    final isValid = info['isValid'] as bool;
    final exists = info['exists'] as bool;

    Color cardColor = Colors.white;
    if (!exists) {
      cardColor = Colors.red.shade50;
    } else if (!isValid) {
      cardColor = Colors.orange.shade50;
    } else {
      cardColor = Colors.green.shade50;
    }

    return Card(
      color: cardColor,
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // اسم الملف ووضعه
            Row(
              children: [
                Icon(
                  exists
                      ? (isValid ? Icons.check_circle : Icons.warning)
                      : Icons.error,
                  color: exists
                      ? (isValid ? Colors.green : Colors.orange)
                      : Colors.red,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    info['name'] as String,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // المعلومات التفصيلية
            if (exists) ...[
              Text('الحجم: ${info['size']} بايت'),
              if (isValid) ...[
                Text('اسم المخطط: ${info['layoutName'] ?? 'غير محدد'}'),
                Text('معرف المخطط: ${info['layoutId'] ?? 'غير محدد'}'),
              ],
            ],

            // الأخطاء
            if (info['error'] != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'خطأ: ${info['error']}',
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontSize: 12,
                  ),
                ),
              ),
            ],

            // محتوى الملف (عينة)
            if (exists &&
                info['content'] != null &&
                (info['content'] as String).isNotEmpty) ...[
              const SizedBox(height: 8),
              ExpansionTile(
                title: const Text('عرض المحتوى'),
                children: [
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: SelectableText(
                      info['content'] as String,
                      style: const TextStyle(
                        fontSize: 12,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                ],
              ),
            ],

            // أزرار الإجراءات
            const SizedBox(height: 8),
            Row(
              children: [
                if (exists && !isValid)
                  ElevatedButton.icon(
                    onPressed: () => _deleteFile(info['path'] as String),
                    icon: const Icon(Icons.delete, size: 16),
                    label: const Text('حذف'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                    ),
                  ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () => _copyPath(info['path'] as String),
                  child: const Text('نسخ المسار'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _deleteFile(String path) async {
    try {
      final file = File(path);
      if (await file.exists()) {
        await file.delete();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الملف'),
            backgroundColor: Colors.green,
          ),
        );
        _analyzeFiles(); // إعادة تحليل
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حذف الملف: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _copyPath(String path) {
    // يمكن إضافة نسخ النص إلى الحافظة هنا
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('المسار: $path')),
    );
  }
}
