/// Permission constants for better maintainability
/// Based on the ERP system permission structure
class PermissionConstants {
  // Sales & Invoice Permissions
  static const int salesInvoiceCreate = 418;
  static const int salesInvoiceView = 419;
  static const int salesInvoiceEdit = 420;
  static const int salesInvoiceDelete = 421;

  static const int returnInvoiceCreate = 422;
  static const int returnInvoiceView = 423;
  static const int returnInvoiceEdit = 424;
  static const int returnInvoiceDelete = 425;

  static const int orderInvoiceCreate = 426;
  static const int orderInvoiceView = 427;
  static const int orderInvoiceEdit = 428;
  static const int orderInvoiceDelete = 429;

  // Purchase Permissions
  static const int purchaseInvoiceCreate = 507;
  static const int purchaseInvoiceView = 509;
  static const int purchaseInvoiceEdit = 508;
  static const int purchaseInvoiceDelete = 510;

  // Inventory Permissions
  static const int inventoryView = 412;
  static const int inventoryOutgoing = 413;
  static const int inventoryIncoming = 447;
  static const int inventoryTransfer = 452;
  static const int inventoryDamaged = 456;

  // Customer Management
  static const int customerCreate = 380;
  static const int customerView = 381;
  static const int customerEdit = 382;
  static const int customerDelete = 383;

  // Product Management
  static const int productCreate = 384;
  static const int productView = 385;
  static const int productEdit = 386;
  static const int productDelete = 387;

  // Reports
  static const int salesReports = 388;
  static const int inventoryReports = 389;
  static const int financialReports = 390;

  // User Management
  static const int userCreate = 67;
  static const int userView = 70;
  static const int userEdit = 68;
  static const int userDelete = 69;

  // Role Management
  static const int roleCreate = 62;
  static const int roleView = 65;
  static const int roleEdit = 63;
  static const int roleDelete = 64;

  // System Settings
  static const int systemSettings = 391;
  static const int branchSettings = 392;
  static const int deviceSettings = 393;

  /// Helper method to get permission name
  static String getPermissionName(int permissionId) {
    switch (permissionId) {
      case salesInvoiceCreate:
        return 'Sales Invoice - Create';
      case salesInvoiceView:
        return 'Sales Invoice - View';
      case salesInvoiceEdit:
        return 'Sales Invoice - Edit';
      case salesInvoiceDelete:
        return 'Sales Invoice - Delete';
      case returnInvoiceCreate:
        return 'Return Invoice - Create';
      case customerCreate:
        return 'Customer - Create';
      case productView:
        return 'Product - View';
      // Add more cases as needed
      default:
        return 'Unknown Permission ($permissionId)';
    }
  }

  /// Common permission groups for easier management
  static const List<int> allSalesPermissions = [
    salesInvoiceCreate,
    salesInvoiceView,
    salesInvoiceEdit,
    salesInvoiceDelete,
  ];

  static const List<int> allInventoryPermissions = [
    inventoryView,
    inventoryOutgoing,
    inventoryIncoming,
    inventoryTransfer,
    inventoryDamaged,
  ];

  static const List<int> allCustomerPermissions = [
    customerCreate,
    customerView,
    customerEdit,
    customerDelete,
  ];

  static const List<int> allProductPermissions = [
    productCreate,
    productView,
    productEdit,
    productDelete,
  ];
}
