class ProductSalesServerDTO {
  final int? productId;
  final String? productName;
  final double? quantitySold;
  final double? totalSales;

  ProductSalesServerDTO({
    this.productId,
    this.productName,
    this.quantitySold,
    this.totalSales,
  });

  factory ProductSalesServerDTO.fromJson(Map<String, dynamic> json) {
    return ProductSalesServerDTO(
      productId: json['ProductId'] as int?,
      productName: json['ProductName'] as String?,
      quantitySold: (json['QuantitySold'] as num?)?.toDouble(),
      totalSales: (json['TotalSales'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ProductId': productId,
      'ProductName': productName,
      'QuantitySold': quantitySold,
      'TotalSales': totalSales,
    };
  }
}
