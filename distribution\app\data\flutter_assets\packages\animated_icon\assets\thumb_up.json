{"v": "5.6.5", "fr": 24, "ip": 0, "op": 28, "w": 30, "h": 30, "nm": "thumb-up", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "lottie Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15, 15, 0], "ix": 2}, "a": {"a": 0, "k": [15, 15, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [-0.282, -0.281], [-0.688, 1.094], [0, 0], [1.282, 1.243], [1.008, -0.456], [1.859, -0.031], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [1.125, 0.906], [0, 0], [0, 0], [-1.197, -1.161], [-1.539, 0.669], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.969, -3.5], [-0.875, -2.156], [2.813, -1.969], [3.438, -2.906], [-0.251, -6.587], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 5, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [-0.532, -0.219], [-0.688, 1.094], [0, 0], [1.124, 0.616], [0.632, -0.45], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [1.937, 0.594], [0, 0], [0, 0], [-1.124, -0.616], [-0.632, 0.45], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.969, -3.5], [-0.875, -2.156], [3.984, -4.25], [4, -4.281], [-1.251, -7.197], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 7, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [-0.094, 0.219], [-0.75, 3.125], [0, 0], [0.808, 0.33], [0, 0], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [1.968, 1], [0, 0], [0, 0], [-1.624, -0.663], [-0.697, 1.297], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.969, -3.5], [-1.797, -3.781], [3.156, -6.234], [3.156, -6.25], [-3.329, -8.822], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [0.187, 0.969], [1.89, 3.094], [0, 0], [0.687, -0.337], [0, 0], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [1.5, -0.687], [0, 0], [0, 0], [-0.687, 0.337], [-0.039, 0.997], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.563, -3.5], [-1.906, -5.906], [-0.516, -11.781], [-0.516, -11.813], [-6.048, -8.884], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 9, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [0, 0.734], [4.343, 0.063], [0, 0], [0, 0], [0, 0], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [0, -2.251], [0, 0], [0, 0], [0, 0], [-0.367, 1.184], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.516, -3.5], [-1.547, -7.75], [-6.406, -12.563], [-6.438, -12.563], [-6.189, -8.134], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [0, 0.734], [3, 0], [0, 0], [0, 0], [0, 0], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [0, -2.251], [0, 0], [0, 0], [0, 0], [-0.697, 1.297], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1, -3.5], [0, -7.5], [-3, -12.5], [-4, -12.5], [-4.501, -9.009], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 17, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [0, 0.734], [3, 0], [0, 0], [0, 0], [0, 0], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [0, -2.251], [0, 0], [0, 0], [0, 0], [-0.697, 1.297], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1, -3.5], [0, -7.5], [-3, -12.5], [-4, -12.5], [-4.501, -9.009], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [0, 0.734], [4.125, 0], [0, 0], [0, 0], [0, 0], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [0, -2.251], [0, 0], [0, 0], [0, 0], [-0.367, 1.184], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.516, -3.5], [-1.547, -7.75], [-6.25, -12.563], [-6.438, -12.563], [-6.189, -8.134], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [0.187, 0.969], [1.64, 3.219], [0, 0], [0.687, -0.337], [0, 0], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [1.625, -0.625], [0, 0], [0, 0], [-0.687, 0.337], [-0.039, 0.997], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.563, -3.5], [-1.906, -5.906], [-0.453, -11.719], [-0.516, -11.813], [-6.048, -8.884], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 21, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [-0.094, 0.219], [-1.016, 2.75], [0, 0], [0.808, 0.33], [0, 0], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [2.015, 0.859], [0, 0], [0, 0], [-1.624, -0.663], [-0.697, 1.297], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.969, -3.5], [-1.797, -3.781], [3.141, -6.219], [3.156, -6.25], [-3.329, -8.822], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [-0.532, -0.219], [-0.5, 1.625], [0, 0], [1.124, 0.616], [0.632, -0.45], [1.473, 0], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [1.937, 0.594], [0, 0], [0, 0], [-1.124, -0.616], [-0.632, 0.45], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.969, -3.5], [-0.875, -2.156], [4, -4.25], [4, -4.281], [-1.251, -7.197], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}, {"t": 24, "s": [{"i": [[0.612, 0.456], [0, 0.822], [1.381, 0], [0, 0], [-0.282, -0.281], [-0.688, 1.094], [0, 0], [1.282, 1.243], [1.008, -0.456], [1.859, -0.031], [0, 0], [0, 0], [0, 0], [-1.811, 0], [0, 0], [0, 1.105], [0, 0], [0, 1.209], [0.396, 0.442], [0, 1.079]], "o": [[0.612, -0.456], [0, -1.38], [0, 0], [0, 0], [1.125, 0.906], [0, 0], [0, 0], [-1.197, -1.161], [-1.539, 0.669], [0, 0], [0, 0], [0, 0], [1.281, 1.281], [0, 0], [1.104, 0], [0, 0], [1.14, -0.232], [0, -0.64], [0.957, -0.351], [0, -0.822]], "v": [[9.986, 1], [11, -1], [8.5, -3.5], [-1.969, -3.5], [-0.875, -2.156], [3.422, -2.859], [3.438, -2.906], [-0.251, -6.587], [-6.868, -4.606], [-10.391, -2.5], [-11, -2.5], [-11, 10.5], [-9, 10.5], [-4.172, 12.5], [6, 12.5], [8, 10.5], [8, 9.45], [10, 7], [9.356, 5.34], [11, 3]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [15, 14.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}], "markers": []}