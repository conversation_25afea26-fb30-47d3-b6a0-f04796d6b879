import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/controllers/inventory_operation_section_request_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20section%20request/widgets/inventory_operation_section_request_history_widget.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:provider/provider.dart';

class InventoryOperationSectionRequestBaseInfoWidget extends StatefulWidget {
  const InventoryOperationSectionRequestBaseInfoWidget(
      {super.key, required this.onSelectWarehouse});
  final Function onSelectWarehouse;

  @override
  State<InventoryOperationSectionRequestBaseInfoWidget> createState() =>
      _InventoryOperationSectionRequestBaseInfoWidgetState();
}

class _InventoryOperationSectionRequestBaseInfoWidgetState
    extends State<InventoryOperationSectionRequestBaseInfoWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  DateTime startDate = DateTime.now().subtract(const Duration(days: 1));
  DateTime endDate = DateTime.now().subtract(const Duration(days: 1));
  bool _isHistoryExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WarehouseController>(context, listen: false)
          .getSectionsFormServer();
      Provider.of<InventoryOperationSectionRequestController>(context,
              listen: false)
          .getSectionRequestHistory(
        startDate,
        endDate,
      );
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var provider = Provider.of<InventoryOperationSectionRequestController>(
        context,
        listen: false);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(10),
        child: _unifiedVerticalLayout(context, provider),
      ),
    );
  }

  Widget _unifiedVerticalLayout(BuildContext context,
      InventoryOperationSectionRequestController provider) {
    return Column(
      children: [
        _buildFormFields(context, provider),
        const SizedBox(height: 16),
        _buildCollapsibleHistory(context),
      ],
    );
  }

  Widget _buildCollapsibleHistory(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Header with expand/collapse button
          InkWell(
            onTap: () {
              setState(() {
                _isHistoryExpanded = !_isHistoryExpanded;
              });
            },
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(16),
                  topRight: const Radius.circular(16),
                  bottomLeft: _isHistoryExpanded
                      ? Radius.zero
                      : const Radius.circular(16),
                  bottomRight: _isHistoryExpanded
                      ? Radius.zero
                      : const Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.history,
                    color: Theme.of(context).primaryColor,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      T('Request History'),
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  AnimatedRotation(
                    turns: _isHistoryExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 300),
                    child: Icon(
                      Icons.expand_more,
                      color: Theme.of(context).primaryColor,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Collapsible content
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            height: _isHistoryExpanded ? 450 : 0,
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              child: Container(
                padding: _isHistoryExpanded
                    ? const EdgeInsets.all(5)
                    : EdgeInsets.zero,
                child: _isHistoryExpanded
                    ? const InventoryOperationSectionRequestHistoryWidget()
                    : const SizedBox.shrink(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormFields(BuildContext context,
      InventoryOperationSectionRequestController provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.inventory_2_outlined,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                T('Operation Details'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Code and Date in vertical layout
          _buildInfoCard(
            context,
            icon: Icons.tag,
            label: T('Operation Number'),
            value: provider.inventorySectionRequest.aPPReferanceCode ??
                T('Not assigned'),
            color: Colors.blue,
          ),
          const SizedBox(height: 12),
          _buildInfoCard(
            context,
            icon: Icons.calendar_today,
            label: T('Date'),
            value: DateFormat('yyyy-MM-dd').format(DateTime.now()),
            color: Colors.green,
          ),
          const SizedBox(height: 20),

          // Section and Warehouse selection
          _buildWarehouseSelector(context, provider),
        ],
      ),
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseSelector(
    BuildContext context,
    InventoryOperationSectionRequestController provider,
  ) {
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                const Icon(
                  Icons.inbox,
                  size: 16,
                  color: Colors.green,
                ),
                const SizedBox(width: 6),
                Text(
                  T('Destination Warehouse'),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                child: MyComboBox(
                  width: double.infinity,
                  selectedValue: provider.inventorySectionRequest.secationId,
                  caption: provider.inventorySectionRequest.secationName ??
                      T("Select Section"),
                  height: 48,
                  onSelect: (int id, String name) {
                    provider.inventorySectionRequest.secationId = id;
                    provider.inventorySectionRequest.secationName = name;
                    setState(() {});
                    widget.onSelectWarehouse();
                    warehouseController.getWarehousesBySectionIdFormServer(id);
                  },
                  fontSize: 14,
                  modalTitle: T("Section"),
                  data: warehouseController.sections,
                  isShowLabel: false,
                  labelText: "",
                ),
              ),
              const SizedBox(height: 10),
              SizedBox(
                child: MyComboBox(
                  width: double.infinity,
                  selectedValue: provider.inventorySectionRequest.toStoreID,
                  caption: provider.inventorySectionRequest.toStoreName ??
                      T("Select warehouse"),
                  height: 48,
                  onSelect: (int id, String name) {
                    provider.inventorySectionRequest.toStoreID = id;
                    provider.inventorySectionRequest.toStoreName = name;
                    Provider.of<InventoryOperationSectionRequestController>(
                            context,
                            listen: false)
                        .getSectionRequestHistory(
                      startDate,
                      endDate,
                    );
                    setState(() {});
                    widget.onSelectWarehouse();
                  },
                  fontSize: 14,
                  modalTitle: T("Destination Warehouse"),
                  data: warehouseController.warehousesBySection,
                  isShowLabel: false,
                  labelText: "",
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
