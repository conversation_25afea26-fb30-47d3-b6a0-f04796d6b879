class EcommerceConfirmOrderDto {
  String? orderCode;
  int? referenceId;
  String? salesReferenceCode;

  EcommerceConfirmOrderDto({
    this.orderCode,
    this.referenceId,
    this.salesReferenceCode,
  });

  EcommerceConfirmOrderDto.fromJson(Map<String, dynamic> json) {
    orderCode = json['orderCode'];
    referenceId = json['referenceId'];
    salesReferenceCode = json['salesReferenceCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['orderCode'] = this.orderCode;
    data['referenceId'] = this.referenceId;
    data['salesReferenceCode'] = this.salesReferenceCode;
    return data;
  }
}
