import 'package:flutter/material.dart';
import 'package:inventory_application/models/warehouse_planner/editor_state.dart';
import 'package:provider/provider.dart';

import '../../../providers/warehouse_planner_provider.dart';
import '../../../models/warehouse_planner/warehouse_layout.dart';
import 'medicine_search_widget.dart';
import 'expiry_alerts_widget.dart';
import 'shelf_detail_widget.dart';

class WarehouseStatsPanel extends StatefulWidget {
  const WarehouseStatsPanel({Key? key}) : super(key: key);

  @override
  State<WarehouseStatsPanel> createState() => _WarehouseStatsPanelState();
}

class _WarehouseStatsPanelState extends State<WarehouseStatsPanel>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<WarehousePlannerProvider>(
      builder: (context, provider, child) {
        final layout = provider.currentLayout;

        if (layout == null) {
          return _buildNoLayoutWidget();
        }

        return Column(
          children: [
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildStatsTab(layout),
                  _buildSearchTab(),
                  _buildAlertsTab(),
                  _buildDetailsTab(provider),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFE9ECEF)),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(
            icon: Icon(Icons.analytics, size: 16),
            text: 'إحصائيات',
          ),
          Tab(
            icon: Icon(Icons.search, size: 16),
            text: 'بحث',
          ),
          Tab(
            icon: Icon(Icons.warning, size: 16),
            text: 'تنبيهات',
          ),
          Tab(
            icon: Icon(Icons.info, size: 16),
            text: 'تفاصيل',
          ),
        ],
        labelColor: const Color(0xFF3498DB),
        unselectedLabelColor: const Color(0xFF6C757D),
        indicatorColor: const Color(0xFF3498DB),
        labelStyle: const TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
        unselectedLabelStyle: const TextStyle(fontSize: 11),
      ),
    );
  }

  Widget _buildNoLayoutWidget() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warehouse,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد مستودع محمل',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بإنشاء مستودع جديد أو تحميل مستودع موجود',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStatsTab(WarehouseLayout layout) {
    final stats = _calculateStats(layout);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المستودع
          _buildInfoCard(
            'معلومات المستودع',
            [
              _buildInfoRow('الاسم', layout.name),
              if (layout.linkedWarehouseName != null)
                _buildInfoRow('مربوط بـ', layout.linkedWarehouseName!),
              _buildInfoRow('الأبعاد',
                  '${layout.width.toInt()} × ${layout.height.toInt()} سم'),
              _buildInfoRow('المساحة',
                  '${(layout.width * layout.height / 10000).toStringAsFixed(1)} م²'),
            ],
          ),

          const SizedBox(height: 16),

          // إحصائيات الخزائن
          _buildStatsGrid([
            _buildStatCard(
                'الخزائن', '${stats.totalShelves}', Icons.shelves, Colors.blue),
            _buildStatCard(
                'الخانات', '${stats.totalBins}', Icons.grid_view, Colors.green),
            _buildStatCard('الأدوية', '${stats.totalMedicines}',
                Icons.medication, Colors.orange),
            _buildStatCard(
                'نسبة الإشغال',
                '${stats.occupancyRate.toStringAsFixed(1)}%',
                Icons.analytics,
                Colors.purple),
          ]),

          const SizedBox(height: 16),

          // إحصائيات حسب النوع
          _buildInfoCard(
            'تصنيف الخزائن',
            stats.shelfTypeStats.entries.map((entry) {
              return _buildInfoRow(
                  _getShelfTypeName(entry.key), '${entry.value}');
            }).toList(),
          ),

          const SizedBox(height: 16),

          // إحصائيات الصلاحية
          _buildInfoCard(
            'حالة الأدوية',
            [
              _buildInfoRow('صالحة', '${stats.validMedicines}', Colors.green),
              _buildInfoRow(
                  'قريبة الانتهاء', '${stats.expiringSoon}', Colors.orange),
              _buildInfoRow('منتهية الصلاحية', '${stats.expired}', Colors.red),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchTab() {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: MedicineSearchWidget(),
    );
  }

  Widget _buildAlertsTab() {
    return const Padding(
      padding: EdgeInsets.all(16),
      child: ExpiryAlertsWidget(),
    );
  }

  Widget _buildDetailsTab(WarehousePlannerProvider provider) {
    final selectedId = provider.editorState.selectedObjectId;
    final selectedType = provider.editorState.selectedObjectType;

    if (selectedId == null || selectedType != ObjectType.shelf) {
      return Container(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.touch_app,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'اختر خزانة لعرض تفاصيلها',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'انقر على أي خزانة في المخطط لعرض محتوياتها وإحصائياتها',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final shelf = provider.currentLayout!.shelves.firstWhere(
      (s) => s.id == selectedId,
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: ShelfDetailWidget(shelf: shelf),
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: Color(0xFFF8F9FA),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(children: children),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, [Color? valueColor]) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 12),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: valueColor ?? Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsGrid(List<Widget> children) {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 8,
      mainAxisSpacing: 8,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.2,
      children: children,
    );
  }

  Widget _buildStatCard(
      String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 11,
              color: Color(0xFF6C757D),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  WarehouseStats _calculateStats(WarehouseLayout layout) {
    int totalShelves = layout.shelves.length;
    int totalBins = 0;
    int totalMedicines = 0;
    int occupiedBins = 0;
    int validMedicines = 0;
    int expiringSoon = 0;
    int expired = 0;
    Map<ShelfType, int> shelfTypeStats = {};

    final now = DateTime.now();

    for (final shelf in layout.shelves) {
      totalBins += shelf.bins.length;

      // إحصائيات نوع الخزانة
      shelfTypeStats[shelf.type] = (shelfTypeStats[shelf.type] ?? 0) + 1;

      for (final bin in shelf.bins) {
        if (bin.productName != null) {
          occupiedBins++;
          totalMedicines += bin.quantity;

          if (bin.expiryDate != null) {
            final daysUntilExpiry = bin.expiryDate!.difference(now).inDays;
            if (daysUntilExpiry <= 0) {
              expired++;
            } else if (daysUntilExpiry <= 30) {
              expiringSoon++;
            } else {
              validMedicines++;
            }
          } else {
            validMedicines++;
          }
        }
      }
    }

    final occupancyRate =
        totalBins > 0 ? (occupiedBins / totalBins) * 100 : 0.0;

    return WarehouseStats(
      totalShelves: totalShelves,
      totalBins: totalBins,
      totalMedicines: totalMedicines,
      occupancyRate: occupancyRate,
      validMedicines: validMedicines,
      expiringSoon: expiringSoon,
      expired: expired,
      shelfTypeStats: shelfTypeStats,
    );
  }

  String _getShelfTypeName(ShelfType type) {
    switch (type) {
      case ShelfType.standard:
        return 'عادية';
      case ShelfType.refrigerated:
        return 'مبردة';
      case ShelfType.controlled:
        return 'محكمة';
      case ShelfType.hazardous:
        return 'خطرة';
      case ShelfType.narcotics:
        return 'مخدرات';
    }
  }
}

class WarehouseStats {
  final int totalShelves;
  final int totalBins;
  final int totalMedicines;
  final double occupancyRate;
  final int validMedicines;
  final int expiringSoon;
  final int expired;
  final Map<ShelfType, int> shelfTypeStats;

  WarehouseStats({
    required this.totalShelves,
    required this.totalBins,
    required this.totalMedicines,
    required this.occupancyRate,
    required this.validMedicines,
    required this.expiringSoon,
    required this.expired,
    required this.shelfTypeStats,
  });
}
