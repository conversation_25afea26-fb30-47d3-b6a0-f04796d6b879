import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/models/dto/counter/device_counter_dto.dart';
import 'package:inventory_application/models/model/purchase_invoice_model.dart';

class PurchaseCounterGenerator {
  static Future<int> getCounterByType({required PurchaseType type}) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());

    return currentCounter;
  }

  //------------------------------------------------//---------------------------------------------------------------------------
  static Future<void> setCounterByTypeAuto({required PurchaseType type}) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());
    currentCounter += 1;

    await LocaleManager.instance
        .setStringValueByStringKey(type.toString(), currentCounter.toString());
  }

  //---------------------------------------------------------
  static Future<bool> setCounterByType(
      {required PurchaseType type, required int counter}) async {
    if (counter == 0) {
      initializeCounterByType(type);
    }
    var oldCounter = await getCounterByType(type: type);
    if (oldCounter > counter) {
      await setPurchaseInvoiceCounterInServer();
      return false;
    }
    counter = counter++;
    LocaleManager.instance.removeKeyByStringKey(type.toString());
    await LocaleManager.instance
        .setStringValueByStringKey(type.toString(), counter.toString());

    return true;
  }

  //---------------------------------------------------------
  static Future<void> getPurchaseInvoiceCounterFromServer() async {
    try {
      var types = [
        PurchaseType.MaterialRequest,
        PurchaseType.Qutation,
        PurchaseType.ProformaInvoice,
        PurchaseType.Order,
        PurchaseType.Receipt,
        PurchaseType.Invoice,
        PurchaseType.ReturnInvoice,
        PurchaseType.PurchaseReturn
      ];

      String deviceId = LocaleManager.instance
          .getStringValue(PreferencesKeys.DeviceCode)
          .toString();
      for (var type in types) {
        var model = ApiDeviceCounterDTO(
          counterType: type.index,
          deviceCode: deviceId,
          referenceType: ApiDeviceCounterTypeEnum.purchase,
        );
        var url = '/Counter/GetCodeByTypes';
        var result = await Api.getWithBody(action: url, body: model.toJson());
        if (result?.isSuccess != null && result?.isSuccess == true) {
          setCounterByType(type: type, counter: result?.data ?? 0);
        } else {
          await initializeCounterByType(type);
        }
      }
    } catch (e) {
      print(e);
    }
  }

  //-----
  //----------------------------------------------------
  static Future<void> setPurchaseInvoiceCounterInServer() async {
    try {
      var types = [
        PurchaseType.MaterialRequest,
        PurchaseType.Qutation,
        PurchaseType.ProformaInvoice,
        PurchaseType.Order,
        PurchaseType.Receipt,
        PurchaseType.Invoice,
        PurchaseType.ReturnInvoice,
        PurchaseType.PurchaseReturn
      ];
      String deviceId = LocaleManager.instance
          .getStringValue(PreferencesKeys.DeviceCode)
          .toString();
      for (var type in types) {
        var counter = await getCounterByType(type: type);
        var model = ApiDeviceCounterDTO(
          counter: counter,
          counterType: type.index,
          deviceCode: deviceId,
          referenceType: ApiDeviceCounterTypeEnum.purchase,
        );
        var url = '/Counter/SetCodeByTypes';
        await Api.post(action: url, body: model.toJson());
      }
    } catch (e) {
      print(e);
    }
  }

  //---------------------------------------------------------
  static Future<void> initializeCounterByType(PurchaseType type) async {
    // LocaleManager.instance.removeKey(PreferencesKeys.SaleInvoice);
    if (!await LocaleManager.instance.checkKeyByStringKey(type.toString())) {
      await LocaleManager.instance
          .setStringValueByStringKey(type.toString(), 1.toString());
      // Initial counter value
    }
  }

  //---------------------------------------------------------
  static Future<String> getCurrentCounteByType(PurchaseType type) async {
    var formattedCounter = "1";
    var currentCounter = LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString();

    if (currentCounter.isNotEmpty) {
      formattedCounter = currentCounter.toString();
    }

    // Format the counter to ensure it's always 7 digits

    return formattedCounter;
  }

  //---------------------------------------------------------
  static Future<String> getNextCounterByType(PurchaseType type) async {
    int currentCounter = int.parse(LocaleManager.instance
        .getStringValueByStringKey(type.toString())
        .toString());
    String deviceId = LocaleManager.instance
        .getStringValue(PreferencesKeys.DeviceCode)
        .toString();
    String formattedCounter = currentCounter.toString().padLeft(5, '0');
    switch (type) {
      case PurchaseType.unknown:
        break;
      case PurchaseType.MaterialRequest:
        return 'MR${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';

      case PurchaseType.Qutation:
        return 'Q${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';

      case PurchaseType.ProformaInvoice:
        return 'PI${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';
      case PurchaseType.Order:
        return 'PO${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';

      case PurchaseType.Receipt:
        return 'PR${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';

      case PurchaseType.Invoice:
        return 'PIN${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';

      case PurchaseType.ReturnInvoice:
        return 'PRINV${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';

      case PurchaseType.PurchaseReturn:
        return 'PR${DateTime.now().year.toString().substring(2)}-$deviceId-$formattedCounter';
    }
    return "";
    // Format the counter to ensure it's always 7 digits
  }

  //-------------------------------------------------------
  static Future<bool> checkIfCounterUsed(PurchaseType type, String code) async {
    // Step 1: Extract the last segment (counter) from code
    try {
      final parts = code.split('-');
      if (parts.length != 3) return false;

      final counterString = parts.last; // e.g., "00023"
      final codeCounter = int.tryParse(counterString);
      if (codeCounter == null) return false;

      // Step 2: Get current counter from preferences
      final currentCounterString = LocaleManager.instance
          .getStringValueByStringKey(type.toString())
          .toString();

      final currentCounter = int.tryParse(currentCounterString);
      if (currentCounter == null) return false;

      // Step 3: Compare
      return codeCounter < currentCounter;
    } catch (e) {
      print("Error checking invoice counter: $e");
      return false;
    }
  }
}
