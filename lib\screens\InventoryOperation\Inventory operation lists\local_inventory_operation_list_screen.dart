import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/inventoryOperations/inventory_operation_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20detials/inventory_operation_details.dart';

import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart' as ptr;

import '../../../controllers/Inventory_operation_controller.dart';

class InventoryOperationsScreen extends StatefulWidget {
  const InventoryOperationsScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _InventoryOperationsScreenState createState() =>
      _InventoryOperationsScreenState();
}

class _InventoryOperationsScreenState extends State<InventoryOperationsScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final ptr.RefreshController _refreshController =
      ptr.RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  String _selectedFilter = "All";
  List<InventoryOperationDtoWithLiteId> _operations = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _onRefresh();
  }

  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  void _onRefresh() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final operations = await Provider.of<InventoryOperationController>(
              context,
              listen: false)
          .fetchLocalInvoices();
      setState(() {
        _operations = operations;
        _isLoading = false;
      });
      _refreshController.refreshCompleted();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _refreshController.refreshFailed();
    }
  }

  void _onLoading() async {
    // Load more functionality can be implemented here if needed
    // For now, we just complete the loading since we disabled pull-up
    _refreshController.loadComplete();
  }

  void _fetchAllOperations() {
    setState(() {
      _selectedFilter = "All";
    });
    _onRefresh();
  }

  void _fetchSyncedOperations() {
    setState(() {
      _selectedFilter = "Synced";
    });
    _onRefresh();
  }

  void _fetchPendingOperations() {
    setState(() {
      _selectedFilter = "Pending";
    });
    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Column(
        children: [
          // Header
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  offset: const Offset(0, 2),
                  blurRadius: 6,
                )
              ],
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CommonHeader(
                  icon: Icons.inventory_2_outlined,
                  title: T("Inventory Operations"),
                ),
              ],
            ),
          ),

          // Sync Button
          Container(
            width: double.infinity,
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
            child: ElevatedButton.icon(
              onPressed: () async {
                try {
                  final controller = Provider.of<InventoryOperationController>(
                      context,
                      listen: false);

                  // Show loading indicator
                  showDialog(
                    context: context,
                    barrierDismissible: false,
                    builder: (context) => const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );

                  // Sync pending operations with server
                  await controller.syncOperationWithSever();

                  if (!mounted) return;

                  // Close loading dialog
                  Navigator.of(context).pop();

                  // Refresh the list
                  _onRefresh();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(T("Operations synced successfully")),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  if (!mounted) return;

                  // Close loading dialog if open
                  Navigator.of(context).pop();

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(T("Failed to sync operations")),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              icon: const Icon(Icons.sync),
              label: Text(T("Sync Operations")),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.newSecondaryColor,
                foregroundColor: Colors.white,
                elevation: 0,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

          // Filter Buttons
          Container(
            margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Row(
              children: [
                Expanded(
                  child: _buildFilterButton(
                    label: T("All"),
                    isSelected: _selectedFilter == "All",
                    onTap: _fetchAllOperations,
                    icon: Icons.article_outlined,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildFilterButton(
                    label: T("Pending"),
                    isSelected: _selectedFilter == "Pending",
                    onTap: _fetchPendingOperations,
                    icon: Icons.pending_actions_outlined,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildFilterButton(
                    label: T("Synced"),
                    isSelected: _selectedFilter == "Synced",
                    onTap: _fetchSyncedOperations,
                    icon: Icons.check_circle_outline,
                  ),
                ),
              ],
            ),
          ),

          // Hybrid SmartRefresher with Mouse Scroll Support
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: _buildSmartRefresherWithMouseSupport(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmartRefresherWithMouseSupport() {
    // For desktop platforms (Windows, macOS, Linux), use SmartRefresher with enhanced scrolling
    if (kIsWeb ||
        defaultTargetPlatform == TargetPlatform.windows ||
        defaultTargetPlatform == TargetPlatform.macOS ||
        defaultTargetPlatform == TargetPlatform.linux) {
      return Scrollbar(
        controller: _scrollController,
        thumbVisibility: true,
        trackVisibility: true,
        child: ptr.SmartRefresher(
          enablePullDown: true,
          enablePullUp: false,
          physics: const BouncingScrollPhysics(), // This enables mouse wheel
          header: ptr.WaterDropHeader(
            waterDropColor: context.newPrimaryColor,
            complete: Icon(
              Icons.done,
              color: context.newPrimaryColor,
            ),
            failed: Icon(
              Icons.error,
              color: Colors.red,
            ),
          ),
          controller: _refreshController,
          onRefresh: _onRefresh,
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildOperationsList(),
        ),
      );
    }

    // For mobile platforms, use SmartRefresher as usual
    return Scrollbar(
      controller: _scrollController,
      thumbVisibility: true,
      trackVisibility: true,
      child: ptr.SmartRefresher(
        enablePullDown: true,
        enablePullUp: false,
        physics: const BouncingScrollPhysics(),
        header: ptr.WaterDropHeader(
          waterDropColor: context.newPrimaryColor,
          complete: Icon(
            Icons.done,
            color: context.newPrimaryColor,
          ),
          failed: Icon(
            Icons.error,
            color: Colors.red,
          ),
        ),
        controller: _refreshController,
        onRefresh: _onRefresh,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : _buildOperationsList(),
      ),
    );
  }

  Widget _buildOperationsList() {
    if (_operations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 80,
              color: context.newTextColor.withOpacity(0.3),
            ),
            const SizedBox(height: 15),
            Text(
              T("No operations found"),
              style: TextStyle(
                color: context.newTextColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              T("Try adjusting your filter parameters"),
              style: TextStyle(
                color: context.newTextColor.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    // Apply filter based on selected filter
    var filteredOperations = _operations;
    if (_selectedFilter == "Pending") {
      filteredOperations = _operations
          .where((operation) => operation.status == "pending")
          .toList();
    } else if (_selectedFilter == "Synced") {
      filteredOperations = _operations
          .where((operation) => operation.status == "synced")
          .toList();
    }

    if (filteredOperations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.filter_list_off,
              size: 80,
              color: context.newTextColor.withOpacity(0.3),
            ),
            const SizedBox(height: 15),
            Text(
              T("No operations match the current filter"),
              style: TextStyle(
                color: context.newTextColor,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 10),
            Text(
              T("Try selecting a different filter"),
              style: TextStyle(
                color: context.newTextColor.withOpacity(0.7),
                fontSize: 14,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: filteredOperations.length + 1, // +1 for Load More button
      itemBuilder: (context, index) {
        // If it's the last item, show Load More button
        if (index == filteredOperations.length) {
          return Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            child: ElevatedButton.icon(
              onPressed: _onLoading,
              icon: const Icon(Icons.refresh, size: 20),
              label: Text(T("Load More")),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.newPrimaryColor,
                foregroundColor: Colors.white,
                elevation: 2,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          );
        }

        // Regular operation item
        final operation = filteredOperations[index];
        final invoice = operation.data;
        final status = operation.status ?? "pending";
        final invoiceType =
            InventoryOperationType.values[invoice?.operationType ?? 0].name;

        final String localCode = invoice?.aPPReferanceCode ?? "";
        final String serverCode = invoice?.code ?? "";

        return _buildOperationCard(
          context: context,
          operation: operation,
          invoice: invoice,
          status: status,
          invoiceType: invoiceType,
          localCode: localCode,
          serverCode: serverCode,
        );
      },
    );
  }

  Widget _buildOperationCard({
    required BuildContext context,
    required InventoryOperationDtoWithLiteId operation,
    required InventoryOperationModel? invoice,
    required String status,
    required String invoiceType,
    required String localCode,
    required String serverCode,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // Operation header
          Container(
            padding: const EdgeInsets.all(15),
            decoration: BoxDecoration(
              color: context.newBackgroundColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            invoiceType,
                            style: TextStyle(
                              color: context.newPrimaryColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          if (serverCode.isNotEmpty)
                            IconButton(
                              icon: const Icon(Icons.qr_code),
                              onPressed: () => _showQRCode(serverCode),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: status == "pending"
                        ? Colors.orange.withOpacity(0.1)
                        : Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: status == "pending"
                          ? Colors.orange.withOpacity(0.3)
                          : Colors.green.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    status == "pending" ? T("Pending") : T("Synced"),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: status == "pending" ? Colors.orange : Colors.green,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Operation details
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 12),
            child: Column(
              children: [
                _buildOperationInfoRow(
                  context,
                  T("Local Code"),
                  localCode,
                  showCopyButton: true,
                  valueToCopy: localCode,
                ),
                if (serverCode.isNotEmpty)
                  _buildOperationInfoRow(
                    context,
                    T("Server Code"),
                    serverCode,
                    showCopyButton: true,
                    valueToCopy: serverCode,
                  ),
                _buildOperationInfoRow(
                  context,
                  T("Status"),
                  status == "pending" ? T("Pending") : T("Synced"),
                  valueColor:
                      status == "pending" ? Colors.orange : Colors.green,
                ),
                if (invoice?.entryDateFormated != null)
                  _buildOperationInfoRow(
                    context,
                    T("Date"),
                    invoice!.entryDateFormated!,
                  ),
              ],
            ),
          ),

          // Action buttons
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: TextButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => InvetoryDetailsPage(
                              id: operation.id ?? 0,
                              isFromLocal: true,
                              operationType: InventoryOperationType.values[
                                  operation.data?.operationType ?? 0] // default
                              ),
                        ),
                      );
                    },
                    icon: const Icon(Icons.visibility_outlined, size: 16),
                    label: Text(T("View")),
                    style: TextButton.styleFrom(
                      foregroundColor: context.newPrimaryColor,
                    ),
                  ),
                ),
                Expanded(
                  child: TextButton.icon(
                    onPressed: () => _deleteOperation(operation),
                    icon: const Icon(Icons.delete_outline, size: 16),
                    label: Text(T("Delete")),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.red,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? context.newPrimaryColor
              : context.newPrimaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? context.newPrimaryColor
                : context.newPrimaryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : context.newPrimaryColor,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : context.newPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOperationInfoRow(
      BuildContext context, String label, String value,
      {bool isTotal = false,
      Color? valueColor,
      bool showCopyButton = false,
      String? valueToCopy}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: context.newTextColor.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  color: valueColor ??
                      (isTotal
                          ? context.newSecondaryColor
                          : context.newTextColor),
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                  fontSize: isTotal ? 16 : 14,
                ),
              ),
              if (showCopyButton &&
                  valueToCopy != null &&
                  valueToCopy.isNotEmpty)
                IconButton(
                  constraints: BoxConstraints.tight(const Size(32, 32)),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: valueToCopy));
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(T("$label copied to clipboard")),
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.copy,
                    size: 16,
                    color: context.newPrimaryColor.withOpacity(0.7),
                  ),
                  tooltip: T("Copy to clipboard"),
                ),
            ],
          ),
        ],
      ),
    );
  }

  void _showQRCode(String code) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        insetPadding: EdgeInsets.zero,
        contentPadding: EdgeInsets.zero,
        content: SizedBox(
          width: 300,
          height: 300,
          child: Column(
            children: [
              const SizedBox(height: 10),
              // QrImageView(
              //   data: code,
              //   version: QrVersions.auto,
              //   size: 260.0,
              // ),
              Container(
                width: 260,
                height: 260,
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Center(
                  child: Text(
                    'QR Code\n$code',
                    textAlign: TextAlign.center,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
              Text(code),
              const SizedBox(height: 10),
            ],
          ),
        ),
      ),
    );
  }

  void _deleteOperation(InventoryOperationDtoWithLiteId operation) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Text(
            T("Delete Operation"),
            style: TextStyle(
              color: context.newSecondaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            T("Are you sure you want to delete this operation?"),
            style: TextStyle(
              color: context.newTextColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: Text(
                T("Cancel"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(dialogContext).pop();

                try {
                  // Delete operation from local database
                  // You can implement the delete functionality here
                  // await controller.deleteLocalOperation(operation.id);

                  // For now, just remove from the list and refresh
                  setState(() {
                    _operations.remove(operation);
                  });

                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(T("Operation deleted successfully")),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(T("Failed to delete operation")),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(T("Delete")),
            ),
          ],
        );
      },
    );
  }
}
