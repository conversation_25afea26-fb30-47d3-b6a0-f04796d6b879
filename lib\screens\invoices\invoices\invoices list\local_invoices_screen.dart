import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/invoice%20details/invoice_details_for_local_screen.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class InvoiceListScreen extends StatefulWidget {
  const InvoiceListScreen({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _InvoiceListScreenState createState() => _InvoiceListScreenState();
}

class _InvoiceListScreenState extends State<InvoiceListScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  final InvoiceController _invoiceController = InvoiceController();
  late Future<List<InvoiceDtoWithLiteId>> _invoicesFuture;
  String _selectedFilter = T("All");

  @override
  void initState() {
    super.initState();

    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _invoicesFuture = _invoiceController.fetchLocalInvoices();
    _onRefresh();
  }

  //==================================================//
  @override
  void dispose() {
    animationController?.dispose();
    _scrollController.dispose();
    _refreshController.dispose();
    super.dispose();
  }

  //==================================================//
  void _onRefresh() async {
    await Provider.of<InvoiceController>(context, listen: false)
        .fetchLocalInvoices();
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
  }

  //==================================================//
  void _onLoading() async {
    await Provider.of<InvoiceController>(context, listen: false)
        .fetchLocalInvoices();
    // if failed,use loadFailed(),if no data return,use LoadNodata()
    if (mounted) setState(() {});
    _refreshController.loadComplete();
  }

  void _fetchAllInvoices() {
    setState(() {
      _selectedFilter = T("All");
      _invoicesFuture = _invoiceController.fetchLocalInvoices();
    });
  }

  void _fetchSyncedInvoices() {
    setState(() {
      _selectedFilter = T("synced");
      _invoicesFuture = _invoiceController.fetchLocalInvoices();
    });
  }

  void _fetchPendingInvoices() {
    setState(() {
      _selectedFilter = T("Pending");
      _invoicesFuture = _invoiceController.fetchLocalInvoices();
    });
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: CustomScrollView(
        slivers: [
          // Header
          SliverToBoxAdapter(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(0, 2),
                    blurRadius: 6,
                  )
                ],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title Section
                  CommonHeader(
                    icon: Icons.receipt_long,
                    title: T("Local invoices"),
                  ),
                ],
              ),
            ),
          ),

          // Sync Button
          SliverToBoxAdapter(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: ElevatedButton.icon(
                onPressed: () {
                  if (AppController.isThereConnection == true) {
                    Provider.of<InvoiceController>(context, listen: false)
                        .syncInvoicesWithSever();
                  } else {
                    errorSnackBar(
                        message: T(
                            "You cannot sync invoices without an internet connection. Please check your connection"));
                  }
                },
                icon: const Icon(Icons.sync),
                label: Text(T("Sync Invoices")),
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.newSecondaryColor,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ),

          // Filter Buttons
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 16, 16, 8),
              child: Row(
                children: [
                  Expanded(
                    child: _buildFilterButton(
                      label: T("All"),
                      isSelected: _selectedFilter == T("All"),
                      onTap: _fetchAllInvoices,
                      icon: Icons.article_outlined,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildFilterButton(
                      label: T("Pending"),
                      isSelected: _selectedFilter == T("Pending"),
                      onTap: _fetchPendingInvoices,
                      icon: Icons.pending_actions_outlined,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildFilterButton(
                      label: T("Synced"),
                      isSelected: _selectedFilter == T("synced"),
                      onTap: _fetchSyncedInvoices,
                      icon: Icons.check_circle_outline,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // SmartRefresher for Invoice List
          SliverFillRemaining(
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: SmartRefresher(
                enablePullDown: true,
                enablePullUp: true,
                header: WaterDropHeader(
                  waterDropColor: context.newPrimaryColor,
                ),
                footer: CustomFooter(
                  builder: (context, mode) {
                    Widget body;
                    if (mode == LoadStatus.loading) {
                      body = const CupertinoActivityIndicator();
                    } else if (mode == LoadStatus.idle) {
                      body = Text(
                        T("Pull up to load more"),
                        style: TextStyle(
                          color: context.newTextColor,
                        ),
                      );
                    } else if (mode == LoadStatus.failed) {
                      body = Text(
                        T("Load Failed! Click to retry!"),
                        style: TextStyle(
                          color: context.newTextColor,
                        ),
                      );
                    } else if (mode == LoadStatus.canLoading) {
                      body = Text(
                        T("Release to load more"),
                        style: TextStyle(
                          color: context.newTextColor,
                        ),
                      );
                    } else {
                      body = Text(
                        T("No more invoices"),
                        style: TextStyle(
                          color: context.newTextColor,
                        ),
                      );
                    }
                    return SizedBox(
                      height: 55.0,
                      child: Center(child: body),
                    );
                  },
                ),
                controller: _refreshController,
                onRefresh: _onRefresh,
                onLoading: _onLoading,
                child: FutureBuilder<List<InvoiceDtoWithLiteId>>(
                  future: _invoicesFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (snapshot.hasError) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              size: 80,
                              color: context.newTextColor.withOpacity(0.3),
                            ),
                            const SizedBox(height: 15),
                            Text(
                              T("Error loading invoices"),
                              style: TextStyle(
                                color: context.newTextColor,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      );
                    } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt_long,
                              size: 80,
                              color: context.newTextColor.withOpacity(0.3),
                            ),
                            const SizedBox(height: 15),
                            Text(
                              T("No invoices found"),
                              style: TextStyle(
                                color: context.newTextColor,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 10),
                            Text(
                              T("Try adjusting your filter parameters"),
                              style: TextStyle(
                                color: context.newTextColor.withOpacity(0.7),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      );
                    } else {
                      var filteredInvoices = snapshot.data!;

                      // Apply filter based on selected filter
                      if (_selectedFilter == T("Pending")) {
                        filteredInvoices = filteredInvoices
                            .where((invoice) => invoice.status == "pending")
                            .toList();
                      } else if (_selectedFilter == T("synced")) {
                        filteredInvoices = filteredInvoices
                            .where((invoice) => invoice.status == "synced")
                            .toList();
                      }

                      return ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemCount: filteredInvoices.length,
                        itemBuilder: (context, index) {
                          final invoice = filteredInvoices[index].data;
                          final status =
                              filteredInvoices[index].status ?? "pending";
                          final String invoiceCode = invoice?.code ?? "";
                          final invoiceType = invoiceCode.startsWith("SI")
                              ? T("Sales Invoice")
                              : T("Return Invoice");

                          final String customerName =
                              invoice?.customerName ?? T("No Customer Name");
                          final String localCode =
                              invoice?.appReferanceCode ?? "";
                          final String serverCode = invoice?.code ?? "";
                          final double paidAmount =
                              invoice?.paidAmount?.toDouble() ?? 0.0;

                          return Container(
                            margin: const EdgeInsets.only(bottom: 16),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.2),
                                  spreadRadius: 1,
                                  blurRadius: 3,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                // Invoice header
                                Container(
                                  padding: const EdgeInsets.all(15),
                                  decoration: BoxDecoration(
                                    color: context.newBackgroundColor,
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(12),
                                      topRight: Radius.circular(12),
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              invoiceType,
                                              style: TextStyle(
                                                color: context.newPrimaryColor,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16,
                                              ),
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              customerName,
                                              style: TextStyle(
                                                color: context.newTextColor,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: status == "pending"
                                              ? Colors.orange.withOpacity(0.1)
                                              : Colors.green.withOpacity(0.1),
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          border: Border.all(
                                            color: status == "pending"
                                                ? Colors.orange.withOpacity(0.3)
                                                : Colors.green.withOpacity(0.3),
                                            width: 1,
                                          ),
                                        ),
                                        child: Text(
                                          status == "pending"
                                              ? T("Pending")
                                              : T("Synced"),
                                          style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.bold,
                                            color: status == "pending"
                                                ? Colors.orange
                                                : Colors.green,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                // Invoice details
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15, vertical: 12),
                                  child: Column(
                                    children: [
                                      _buildInvoiceInfoRow(
                                        context,
                                        T("Local Code"),
                                        localCode,
                                        showCopyButton: true,
                                        valueToCopy: localCode,
                                      ),
                                      _buildInvoiceInfoRow(
                                        context,
                                        T("Server Code"),
                                        serverCode,
                                        showCopyButton: true,
                                        valueToCopy: serverCode,
                                      ),
                                      _buildInvoiceInfoRow(
                                        context,
                                        T("Status"),
                                        status == "pending"
                                            ? T("Pending")
                                            : T("Synced"),
                                        valueColor: status == "pending"
                                            ? Colors.orange
                                            : Colors.green,
                                      ),
                                      _buildInvoiceInfoRow(
                                        context,
                                        T("Total"),
                                        paidAmount.toString(),
                                        isTotal: true,
                                      ),
                                    ],
                                  ),
                                ),

                                // Action buttons
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 15, vertical: 8),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: TextButton.icon(
                                          onPressed: () {
                                            // Navigate to invoice detail screen
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    InoviceDetailsForLocalPage(
                                                  id: filteredInvoices[index]
                                                          .id ??
                                                      0,
                                                ),
                                              ),
                                            );
                                          },
                                          icon: const Icon(
                                              Icons.visibility_outlined,
                                              size: 16),
                                          label: Text(T("View")),
                                          style: TextButton.styleFrom(
                                            foregroundColor:
                                                context.newPrimaryColor,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: TextButton.icon(
                                          onPressed: () {
                                            // Show delete confirmation dialog
                                            showDialog(
                                              context: context,
                                              builder:
                                                  (BuildContext dialogContext) {
                                                return AlertDialog(
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            15),
                                                  ),
                                                  title: Text(
                                                    T("Delete Invoice"),
                                                    style: TextStyle(
                                                      color: context
                                                          .newSecondaryColor,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                  ),
                                                  content: Text(
                                                    T("Are you sure you want to delete this invoice?"),
                                                    style: TextStyle(
                                                      color:
                                                          context.newTextColor,
                                                    ),
                                                  ),
                                                  actions: [
                                                    TextButton(
                                                      onPressed: () {
                                                        Navigator.of(
                                                                dialogContext)
                                                            .pop();
                                                      },
                                                      child: Text(
                                                        T("Cancel"),
                                                        style: TextStyle(
                                                          color: context
                                                              .newTextColor,
                                                        ),
                                                      ),
                                                    ),
                                                    ElevatedButton(
                                                      onPressed: () async {
                                                        Navigator.of(
                                                                dialogContext)
                                                            .pop();

                                                        // Delete invoice based on status
                                                        if (status ==
                                                            "pending") {
                                                          int result =
                                                              await Provider.of<
                                                                          InvoiceController>(
                                                                      context,
                                                                      listen:
                                                                          false)
                                                                  .deleteInvoice(
                                                                      localCode);

                                                          if (result > 0) {
                                                            successSnackBar(
                                                                message: T(
                                                                    "Successfully deleted"));
                                                            _onRefresh();
                                                          } else {
                                                            errorSnackBar(
                                                                message: T(
                                                                    "Delete failed. Please try again later"));
                                                          }
                                                        } else if (status ==
                                                            "synced") {
                                                          // For synced invoices, we need to use the server ID and transaction type
                                                          if (invoice != null) {
                                                            // For synced invoice, use the server ID and the invoice type
                                                            int serverId =
                                                                invoice.iD ?? 0;
                                                            String
                                                                transactionType =
                                                                invoice.transactionsType ??
                                                                    "Invoice";

                                                            if (serverId > 0) {
                                                              bool result = await Provider.of<
                                                                          InvoiceController>(
                                                                      context,
                                                                      listen:
                                                                          false)
                                                                  .deleteSyncInvoice(
                                                                      serverId,
                                                                      transactionType);

                                                              if (result) {
                                                                successSnackBar(
                                                                    message: T(
                                                                        "Successfully deleted"));
                                                                _onRefresh();
                                                              } else {
                                                                errorSnackBar(
                                                                    message: T(
                                                                        "Delete failed. Please try again later"));
                                                              }
                                                            }
                                                          }
                                                        }
                                                      },
                                                      style: ElevatedButton
                                                          .styleFrom(
                                                        backgroundColor:
                                                            Colors.red,
                                                        foregroundColor:
                                                            Colors.white,
                                                        shape:
                                                            RoundedRectangleBorder(
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(8),
                                                        ),
                                                      ),
                                                      child: Text(T("Delete")),
                                                    ),
                                                  ],
                                                );
                                              },
                                            );
                                          },
                                          icon: const Icon(Icons.delete_outline,
                                              size: 16),
                                          label: Text(T("Delete")),
                                          style: TextButton.styleFrom(
                                            foregroundColor: Colors.red,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    }
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncIndicator() {
    final bool isConnected = AppController.isThereConnection ?? false;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: isConnected
            ? Colors.green.withOpacity(0.1)
            : Colors.red.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: isConnected
              ? Colors.green.withOpacity(0.3)
              : Colors.red.withOpacity(0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isConnected ? Icons.cloud_done_outlined : Icons.cloud_off_outlined,
            size: 16,
            color: isConnected ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 4),
          Text(
            isConnected ? T("Online") : T("Offline"),
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: isConnected ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required IconData icon,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? context.newPrimaryColor
              : context.newPrimaryColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected
                ? context.newPrimaryColor
                : context.newPrimaryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 16,
              color: isSelected ? Colors.white : context.newPrimaryColor,
            ),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : context.newPrimaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInvoiceInfoRow(BuildContext context, String label, String value,
      {bool isTotal = false,
      Color? valueColor,
      bool showCopyButton = false,
      String? valueToCopy}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              color: context.newTextColor.withOpacity(0.7),
              fontSize: 14,
            ),
          ),
          Row(
            children: [
              Text(
                value,
                style: TextStyle(
                  color: valueColor ??
                      (isTotal
                          ? context.newSecondaryColor
                          : context.newTextColor),
                  fontWeight: isTotal ? FontWeight.bold : FontWeight.w500,
                  fontSize: isTotal ? 16 : 14,
                ),
              ),
              if (showCopyButton &&
                  valueToCopy != null &&
                  valueToCopy.isNotEmpty)
                IconButton(
                  constraints: BoxConstraints.tight(const Size(32, 32)),
                  padding: EdgeInsets.zero,
                  onPressed: () {
                    Clipboard.setData(ClipboardData(text: valueToCopy));
                    // Show feedback to user
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(T("$label copied to clipboard")),
                        duration: const Duration(seconds: 1),
                        behavior: SnackBarBehavior.floating,
                      ),
                    );
                  },
                  icon: Icon(
                    Icons.copy,
                    size: 16,
                    color: context.newPrimaryColor.withOpacity(0.7),
                  ),
                  tooltip: T("Copy to clipboard"),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
