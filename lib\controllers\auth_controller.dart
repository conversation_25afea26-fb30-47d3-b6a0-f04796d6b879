import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/branch_controller.dart';
import 'package:inventory_application/controllers/category_controller.dart';
import 'package:inventory_application/controllers/cost_center_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/controllers/device_setup_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/payment_type_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/controllers/supplier_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/accounts_voucher_counter.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/inventory_operation_counter.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/helpers/purchase_invoice_counter.dart';
import 'package:inventory_application/main.dart';
import 'package:inventory_application/models/dto/auth/login_dto.dart';
import 'package:inventory_application/models/dto/response.dart';
import 'package:inventory_application/screens/auth/SignIn_screen.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/auth/initial_sync_screen.dart';
import 'package:inventory_application/services/authentication_service.dart';
import 'package:inventory_application/services/initial_sync_service.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:provider/provider.dart';

class AuthController with ChangeNotifier {
  static String _token = "";
  static int _userId = 0;
  static String _userName = "";
  static bool _isMainAdmin = false;

  static String getToken() => _token;
  static String getUserName() => _userName;
  static bool getIsMainAdmin() => _isMainAdmin;
  static int getUserId() => _userId;
  static setIsMainAdmin(bool value) {
    _isMainAdmin = value;
  }

  static setUserName(String value) {
    _userName = value;
  }

  static Map<String, String> headers = Map.fromEntries({
    'Accept-Language': appCtrl.languageVal,
    "Authorization": 'Basic ${getToken()}',
    'Accept': 'application/json',
  }.entries);

  Future<void> login(
      {required BuildContext context, required LogInDto model}) async {
    if (AppController.isThereConnection == false) {
      await loginLocaly(context: context, model: model);
      return;
    }
    pleaseWaitDialog(context: navigatorKey.currentContext!, isShown: true);

    try {
      var url = 'Security/Login';
      var counter = await CounterGenerator.getSaleInvoiceCurrentCounter();
      var deviceId = AppController.deviceId;
      var result = await Api.post(action: url, body: {
        "User_Name": model.usernameOrEmail,
        "User_Password": model.password,
        "Current_Branch_ID": model.currentBranchID,
        "Counter": counter,
        "Device_ID": deviceId,
      });

      if (result != null) {
        if (result.isSuccess) {
          addUserToLocalDataBase(
            model: LogInDto(
              usernameOrEmail: model.usernameOrEmail,
              password: hashPassword(model.password ?? ""),
            ),
            token: result.data["Token"],
          );
          _token = result.data["Token"];
          var tokenBody = decodeJWTToken(_token);
          _userId = int.parse(tokenBody["UserID"].toString());
          _userName = tokenBody["sub"] ?? '';

          LocaleManager.instance
              .setString(PreferencesKeys.TOKEN, result.data["Token"]);
          LocaleManager.instance.setInt(PreferencesKeys.UserId,
              int.parse(tokenBody["UserID"].toString()));

          AppController.isAuth = true;

          // التحقق من المزامنة الأولية
          // ignore: use_build_context_synchronously
          final syncService =
              Provider.of<InitialSyncService>(context, listen: false);

          // ignore: use_build_context_synchronously
          bool isFirstTime = await syncService.checkIfFirstTimeUserInBranch(
            model.usernameOrEmail ?? '',
            AppController.currentBranchId,
          );

          if (isFirstTime) {
            // التوجه إلى شاشة المزامنة الأولية
            Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
              MaterialPageRoute(
                  builder: (context) => const InitialSyncScreen()),
              (Route<dynamic> route) => false,
            );
          } else {
            try {
              // ignore: use_build_context_synchronously
              await initAfterLogin(context: context, tokenBody: tokenBody);
            } catch (e) {
              rethrow;
            }
            // التوجه إلى الشاشة الرئيسية مباشرة
            Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
              MaterialPageRoute(builder: (context) => const HomeScreen()),
              (Route<dynamic> route) => false,
            );

            // ignore: use_build_context_synchronously
          }
        } else {
          pleaseWaitDialog(
              context: navigatorKey.currentContext!, isShown: false);
          errorSnackBar(message: "خطأ في اسم المستخدم او كلمة المرور");
          return;
        }
      }

      notifyListeners();
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------------------------
  Future<void> initAfterLogin(
      {required BuildContext context,
      required Map<String, dynamic> tokenBody}) async {
    // await AuthController.tryAutoLogin();
    try {
      await context.read<AppController>().getDeviceDetails();
      // ignore: use_build_context_synchronously
      await context.read<DeviceSetupController>().deviceIdSetup();

      await CounterGenerator.getInvoicesCounterFromServer();
      await InventoryOperationCounterGenerator
          .getInventoryOperationCounterFromServer();
      await PurchaseCounterGenerator.getPurchaseInvoiceCounterFromServer();
      await AccountsVoucherCounterGenerator
          .getAccountsVoucherCounterFromServer();
      AppController.currentBranchId =
          int.parse(tokenBody["BranchID"].toString());
      AppController.setBranchName();
      // ignore: use_build_context_synchronously
      context
          .read<AuthenticationService>()
          .setUserBranches(tokenBody["UserBranchs"]);
      // ignore: use_build_context_synchronously
      context
          .read<AuthenticationService>()
          .setUserModules(tokenBody["UserModule"]);
      // ignore: use_build_context_synchronously
      context
          .read<AuthenticationService>()
          .setUserRoles(tokenBody["UserRoles"]);
      if (tokenBody["IsMainAdmin"] != null) {
        // ignore: use_build_context_synchronously
        context
            .read<AuthenticationService>()
            .setIsMainAdmin(tokenBody["IsMainAdmin"] == "True" ? true : false);
      }
      if (tokenBody["IsSharedProducts"] != null) {
        AppController.setIsSharedProducts(
            tokenBody["IsSharedProducts"] == "True" ? true : false);
        AppController.isSharedProducts =
            tokenBody["IsSharedProducts"] == "True" ? true : false;
      }
      if (tokenBody["IsPharmacy"] != null) {
        AppController.setisPharmacy(
            tokenBody["IsPharmacy"] == "True" ? true : false);
        AppController.isPharmacy =
            tokenBody["IsPharmacy"] == "True" ? true : false;
      }

      // Sync branches from server after login
      // ignore: use_build_context_synchronously
      await context.read<BranchController>().fetchBranchesFromServer();
      await context
          .read<AuthenticationService>()
          .fetchRolesWithPermissionsFromServer();
      await context
          .read<AuthenticationService>()
          .fetchRolesWithPermissionsFromServer();

      // ignore: use_build_context_synchronously
      await context.read<CategoryController>().fetchCategories();
      await context.read<PaymentTypeController>().fetchPaymentTypes();
      // ignore: use_build_context_synchronously
      await context.read<WarehouseController>().fetchWarehouses();
      // ignore: use_build_context_synchronously
      await context.read<SalesmenController>().fetchSalesmen();
      // ignore: use_build_context_synchronously
      await context.read<CustomerController>().getCustomers();
      await context.read<SupplierController>().getSuppliers();
      await context.read<CostCenterController>().getCostCenter();
      // await context.read<ProductController>().fetchProduct();
      // ignore: use_build_context_synchronously
      await context.read<ProductController>().getProductCount();
      // ignore: use_build_context_synchronously
      await context.read<ProductController>().fetchProductAfterCertineId();
      await context.read<InvoiceSettingsController>().loadSettings();
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------------------------
  Future<void> checkAuth({required BuildContext context}) async {
    try {
      var token = LocaleManager.instance.getStringValue(PreferencesKeys.TOKEN);
      if (token.isNotEmpty) {
        bool expirationDate = JwtDecoder.isExpired(token);
        var tokenBody = decodeJWTToken(token);

        if (expirationDate) {
          AppController.isAuth = false;
        } else {
          AppController.isAuth = true;
          _token = token;
          _userId = int.parse(tokenBody["UserID"].toString());
          _userName = tokenBody["sub"] ?? '';

          // التحقق من المزامنة الأولية عند التحقق التلقائي من المصادقة
          final syncService =
              Provider.of<InitialSyncService>(context, listen: false);
          bool isFirstTime = await syncService.checkIfFirstTimeUserInBranch(
            tokenBody["sub"] ?? '',
            AppController.currentBranchId,
          );

          if (isFirstTime) {
            // لا نقوم بالمزامنة الأولية هنا، فقط نضع علامة
            syncService.resetSyncState();
            await initAfterLogin(context: context, tokenBody: tokenBody);
          } else {
            await initAfterLogin(context: context, tokenBody: tokenBody);
          }
        }
      } else {
        AppController.isAuth = false;
      }
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------------------------
  Future<void> logOut() async {
    try {
      LocaleManager.instance.removeKey(PreferencesKeys.TOKEN);
      AppController.isAuth = false;
      Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const SignInScreen()),
        (Route<dynamic> route) => false,
      );
    } catch (e) {
      print(e);
    }
  }

//--------------------------------------------------------------

// #region Local Auth
  String hashPassword(String password) {
    var bytes = utf8.encode(password); // Convert password to bytes
    var hashedPassword = sha256.convert(bytes); // Hash the password
    return hashedPassword.toString(); // Return the hash as a string
  }

  //----------------------------------------------------------
  Future<bool> addUserToLocalDataBase(
      {required LogInDto model, String? token}) async {
    try {
      final db = await DatabaseHelper().database;

      // Check if the user already exists by username
      var result = await db.query(
        'User',
        where: 'usernameOrEmail = ? AND BranchId = ?',
        whereArgs: [model.usernameOrEmail, AppController.currentBranchId],
      );

      if (result.isNotEmpty) {
        // User already exists, update token if provided
        if (token != null) {
          await db.update(
            'User',
            {'token': token},
            where: 'usernameOrEmail = ? AND BranchId = ?',
            whereArgs: [model.usernameOrEmail, AppController.currentBranchId],
          );
        }
        return false;
      }

      // If user doesn't exist, add them to the database
      model.id = 0;
      var userData = model.toJson();
      if (token != null) {
        userData['token'] = token;
      }
      userData['BranchId'] = model.currentBranchID;

      var id = await db.insert('User', {
        "usernameOrEmail": model.usernameOrEmail,
        "password": model.password,
        "token": token,
        "BranchId": AppController.currentBranchId,
      });
      return id > 0;
    } catch (e) {
      // Handle any errors
      return false;
    }
  }

  //----------------------------------------------------------
  Future<LogInDto?> checkIfUserExist(LogInDto model) async {
    try {
      final db = await DatabaseHelper().database;
      var result = await db.query(
        'User',
        where: 'usernameOrEmail = ? AND BranchId = ?',
        whereArgs: [model.usernameOrEmail, AppController.currentBranchId],
      );
      if (result.isEmpty) {
        // User already exists
        return null;
      }
      var user = LogInDto(
          password: result.first['password'] as String,
          usernameOrEmail: result.first['usernameOrEmail'] as String,
          token: result.first['token'] as String,
          currentBranchID: result.first['BranchId'] as int);
      return user;
    } catch (e) {
      return null;
    }
  }

  //----------------------------------------------------------
  Future<void> loginLocaly(
      {required BuildContext context, required LogInDto model}) async {
    try {
      pleaseWaitDialog(context: navigatorKey.currentContext!, isShown: true);
      var user = await checkIfUserExist(model);
      if (user == null) {
        pleaseWaitDialog(context: navigatorKey.currentContext!, isShown: false);
        errorSnackBar(message: "خطأ في اسم المستخدم او كلمة المرور");
        return;
      }
      var isPasswordCurrect =
          hashPassword(model.password ?? "") == user.password;

      if (!isPasswordCurrect) {
        pleaseWaitDialog(context: navigatorKey.currentContext!, isShown: false);
        errorSnackBar(message: "خطأ في اسم المستخدم او كلمة المرور");
        return;
      }

      AppController.isAuth = true;

      // التحقق من المزامنة الأولية
      // ignore: use_build_context_synchronously
      final syncService =
          Provider.of<InitialSyncService>(context, listen: false);
      // ignore: use_build_context_synchronously
      bool isFirstTime = await syncService.checkIfFirstTimeUserInBranch(
        model.usernameOrEmail ?? '',
        AppController.currentBranchId,
      );

      if (isFirstTime) {
        // التوجه إلى شاشة المزامنة الأولية
        Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const InitialSyncScreen()),
          (Route<dynamic> route) => false,
        );
      } else {
        // التوجه إلى الشاشة الرئيسية مباشرة
        Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
          (Route<dynamic> route) => false,
        );

        // ignore: use_build_context_synchronously
        try {
          // ignore: use_build_context_synchronously
          await initAfterLogin(
            context: context,
            tokenBody: decodeJWTToken(user.token ?? ""),
          );
        } catch (e) {
          rethrow;
        }
      }
    } catch (e) {
      print(e);
    }
  }

  //----------------------------------------------------------
  Future<ResponseResultModel> changePassword({
    required String oldPassword,
    required String newPassword,
  }) async {
    try {
      var url = '/Users/<USER>';
      var body = {
        "UserId": _userId.toString(),
        "OldPassword": oldPassword,
        "NewPassword": newPassword,
      };

      var result = await Api.post(action: url, body: body);

      if (result != null) {
        return result;
      } else {
        return ResponseResultModel(isSuccess: false, message: result?.message);
      }
    } catch (e) {
      print('Error changing password: $e');
      return ResponseResultModel(isSuccess: false, message: [e.toString()]);
    }
  }

// #endregion
}
