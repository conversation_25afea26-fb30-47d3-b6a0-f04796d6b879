import 'package:flutter/material.dart';
import 'package:inventory_application/models/dto/device_dto.dart';
import 'package:inventory_application/models/dto/user_dto.dart';
import 'package:inventory_application/services/server_reports_service.dart';

class DeviceUserController with ChangeNotifier {
  final ServerReportsService _reportsService = ServerReportsService();

  // Devices
  List<DeviceDTO> _devices = [];
  DeviceDTO? _selectedDevice = DeviceDTO();
  bool _isLoadingDevices = false;
  String? _devicesError;

  // Users
  List<UserDTO> _users = [];
  UserDTO? _selectedUser;
  bool _isLoadingUsers = false;
  String? _usersError;

  // Getters
  List<DeviceDTO> get devices => _devices;
  DeviceDTO? get selectedDevice => _selectedDevice;
  bool get isLoadingDevices => _isLoadingDevices;
  String? get devicesError => _devicesError;

  List<UserDTO> get users => _users;
  UserDTO? get selectedUser => _selectedUser;
  bool get isLoadingUsers => _isLoadingUsers;
  String? get usersError => _usersError;

  /// Load all devices from server
  Future<void> loadDevices() async {
    _isLoadingDevices = true;
    _devicesError = null;
    notifyListeners();

    try {
      final devices = await _reportsService.getDevices();
      if (devices != null) {
        _devices = devices;
        _devicesError = null;
      } else {
        _devicesError = 'فشل في تحميل الأجهزة';
        _devices = [];
      }
    } catch (e) {
      _devicesError = 'خطأ في تحميل الأجهزة: ${e.toString()}';
      _devices = [];
    } finally {
      _isLoadingDevices = false;
      notifyListeners();
    }
  }

  /// Load users for selected device
  Future<void> loadUsersForDevice(String? deviceId) async {
    if (deviceId == null || deviceId.isEmpty) {
      _users = [];
      _selectedUser = null;
      _usersError = null;
      notifyListeners();
      return;
    }

    _isLoadingUsers = true;
    _usersError = null;
    _selectedUser = null;
    notifyListeners();

    try {
      final users = await _reportsService.getUsersByDeviceId(deviceId);
      if (users != null) {
        _users = users;
        _usersError = null;
      } else {
        _usersError = 'فشل في تحميل المستخدمين';
        _users = [];
      }
    } catch (e) {
      _usersError = 'خطأ في تحميل المستخدمين: ${e.toString()}';
      _users = [];
    } finally {
      _isLoadingUsers = false;
      notifyListeners();
    }
  }

  void setSelectedDevice(DeviceDTO? device) {
    _selectedDevice = device;
    notifyListeners();
  }

  void setSelectedUser(UserDTO? user) {
    _selectedUser = user;
    notifyListeners();
  }

  /// Clear all selections
  void clearSelections() {
    _selectedDevice = null;
    _selectedUser = null;
    _users = [];
    _usersError = null;
    notifyListeners();
  }

  /// Reset controller
  void reset() {
    _devices = [];
    _selectedDevice = null;
    _isLoadingDevices = false;
    _devicesError = null;

    _users = [];
    _selectedUser = null;
    _isLoadingUsers = false;
    _usersError = null;

    notifyListeners();
  }
}
