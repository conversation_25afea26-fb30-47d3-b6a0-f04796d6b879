import 'product_sales_server_dto.dart';

class ProductSalesServerReportDTO {
  final List<ProductSalesServerDTO> products;
  final DateTime? fromDate;
  final DateTime? toDate;
  final double totalSales;
  final double totalQuantity;
  final int totalProducts;

  ProductSalesServerReportDTO({
    required this.products,
    this.fromDate,
    this.toDate,
    required this.totalSales,
    required this.totalQuantity,
    required this.totalProducts,
  });

  factory ProductSalesServerReportDTO.fromProductsList(
    List<ProductSalesServerDTO> products, {
    DateTime? fromDate,
    DateTime? toDate,
  }) {
    final totalSales = products.fold<double>(
      0.0,
      (sum, product) => sum + (product.totalSales ?? 0.0),
    );

    final totalQuantity = products.fold<double>(
      0.0,
      (sum, product) => sum + (product.quantitySold ?? 0.0),
    );

    return ProductSalesServerReportDTO(
      products: products,
      fromDate: fromDate,
      toDate: toDate,
      totalSales: totalSales,
      totalQuantity: totalQuantity,
      totalProducts: products.length,
    );
  }

  // Get top selling products by sales amount
  List<ProductSalesServerDTO> getTopSellingProducts({int limit = 10}) {
    final sortedProducts = List<ProductSalesServerDTO>.from(products);
    sortedProducts
        .sort((a, b) => (b.totalSales ?? 0).compareTo(a.totalSales ?? 0));
    return sortedProducts.take(limit).toList();
  }

  // Get top products by quantity sold
  List<ProductSalesServerDTO> getTopProductsByQuantity({int limit = 10}) {
    final sortedProducts = List<ProductSalesServerDTO>.from(products);
    sortedProducts
        .sort((a, b) => (b.quantitySold ?? 0).compareTo(a.quantitySold ?? 0));
    return sortedProducts.take(limit).toList();
  }
}
