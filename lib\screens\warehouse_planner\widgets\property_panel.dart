import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../providers/warehouse_planner_provider.dart';
import '../../../models/warehouse_planner/warehouse_layout.dart';
import '../../../models/warehouse_planner/editor_state.dart';

class PropertyPanel extends StatelessWidget {
  const PropertyPanel({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<WarehousePlannerProvider>(
      builder: (context, provider, child) {
        final selectedId = provider.editorState.selectedObjectId;
        final selectedType = provider.editorState.selectedObjectType;

        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(color: const Color(0xFFE9ECEF)),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              // رأس اللوحة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Color(0xFF3498DB),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.tune, color: Colors.white, size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'خصائص العنصر',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // المحتوى
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: selectedId == null
                      ? _buildEmptyState()
                      : _buildSelectedObjectProperties(
                          context, provider, selectedId, selectedType),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.touch_app,
          size: 64,
          color: Colors.grey[300],
        ),
        const SizedBox(height: 16),
        Text(
          'اختر عنصراً لعرض خصائصه',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'انقر على جدار، خزانة أو مدخل لتعديل خصائصه',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[500],
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSelectedObjectProperties(
    BuildContext context,
    WarehousePlannerProvider provider,
    String selectedId,
    ObjectType? selectedType,
  ) {
    switch (selectedType) {
      case ObjectType.shelf:
        return _buildShelfProperties(context, provider, selectedId);
      case ObjectType.wall:
        return _buildWallProperties(context, provider, selectedId);
      case ObjectType.entrance:
        return _buildEntranceProperties(context, provider, selectedId);
      default:
        return _buildEmptyState();
    }
  }

  Widget _buildShelfProperties(
    BuildContext context,
    WarehousePlannerProvider provider,
    String shelfId,
  ) {
    final shelf = provider.currentLayout?.shelves.firstWhere(
      (s) => s.id == shelfId,
    );

    if (shelf == null) return _buildEmptyState();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات أساسية
          _buildPropertyCard(
            '📦 معلومات الخزانة',
            [
              _buildPropertyRow('الاسم', shelf.name),
              _buildPropertyRow('النوع', _getShelfTypeName(shelf.type)),
              _buildPropertyRow('الموضع',
                  '(${shelf.position.x.toInt()}, ${shelf.position.y.toInt()})'),
              _buildPropertyRow('الدوران', '${shelf.rotation.toInt()}°'),
            ],
          ),

          const SizedBox(height: 16),

          // أبعاد الخزانة - أكبر وأوضح
          _buildPropertyCard(
            '📏 الأبعاد والتكوين',
            [
              _buildLargeNumberField('عدد المستويات', shelf.levels.toDouble(),
                  (value) {
                final levels = value.toInt();
                if (levels >= 1 && levels <= 10) {
                  provider.updateShelfProperties(shelfId, levels: levels);
                }
              }),
              _buildLargeNumberField(
                  'خانات في المستوى', shelf.slotsPerLevel.toDouble(), (value) {
                final slots = value.toInt();
                if (slots >= 1 && slots <= 20) {
                  provider.updateShelfProperties(shelfId, slotsPerLevel: slots);
                }
              }),
              _buildPropertyRow('إجمالي الخانات', '${shelf.bins.length}'),
            ],
          ),

          const SizedBox(height: 16),

          // خريطة الخزانة المكبرة
          _buildPropertyCard(
            '🗺️ خريطة الخزانة',
            [
              Container(
                height: 150,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFE9ECEF)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: GridView.builder(
                  padding: const EdgeInsets.all(4),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: shelf.slotsPerLevel,
                    crossAxisSpacing: 2,
                    mainAxisSpacing: 2,
                    childAspectRatio: 1,
                  ),
                  itemCount: shelf.bins.length,
                  itemBuilder: (context, index) {
                    final bin = shelf.bins[index];
                    return Container(
                      decoration: BoxDecoration(
                        color: bin.statusColor,
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(color: Colors.white, width: 1),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'L${bin.level + 1}',
                              style: const TextStyle(
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            Text(
                              'S${bin.slot + 1}',
                              style: const TextStyle(
                                fontSize: 8,
                                color: Colors.white,
                              ),
                            ),
                            if (bin.productName != null)
                              Text(
                                bin.productCode?.substring(0, 2) ?? 'XX',
                                style: const TextStyle(
                                  fontSize: 6,
                                  color: Colors.white,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // إحصائيات كبيرة
          _buildPropertyCard(
            '📊 إحصائيات الاستخدام',
            [
              Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                        'مشغولة', '${_getOccupiedBins(shelf)}', Colors.blue),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildStatCard(
                        'فارغة',
                        '${shelf.bins.length - _getOccupiedBins(shelf)}',
                        Colors.grey),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              _buildStatCard(
                  'نسبة الإشغال',
                  '${(_getOccupancyRate(shelf) * 100).toStringAsFixed(1)}%',
                  Colors.green),
            ],
          ),

          const SizedBox(height: 16),

          // إجراءات سريعة
          _buildActionButtons(context, provider, shelf),
        ],
      ),
    );
  }

  Widget _buildStatCard(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLargeNumberField(
      String label, double value, Function(double) onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
          ),
          const SizedBox(height: 8),
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFF3498DB)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextFormField(
              initialValue: value.toInt().toString(),
              keyboardType: TextInputType.number,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              decoration: const InputDecoration(
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(12),
              ),
              onFieldSubmitted: (val) {
                final parsed = double.tryParse(val);
                if (parsed != null) onChanged(parsed);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWallProperties(
    BuildContext context,
    WarehousePlannerProvider provider,
    String wallId,
  ) {
    final wall = provider.currentLayout?.walls.firstWhere(
      (w) => w.id == wallId,
    );

    if (wall == null) return _buildEmptyState();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات الجدار
          _buildPropertyCard(
            '🧱 خصائص الجدار',
            [
              _buildPropertyRow('السماكة', '${wall.thickness.toInt()} سم'),
              _buildPropertyRow('عدد النقاط', '${wall.points.length}'),
              _buildPropertyRow('الطول',
                  '${_calculateWallLength(wall).toStringAsFixed(1)} سم'),
            ],
          ),

          const SizedBox(height: 16),

          // تعديل السماكة
          _buildEditablePropertyCard(
            '🎨 تعديل المظهر',
            [
              // تعديل السماكة
              _buildSliderProperty(
                'السماكة (سم)',
                wall.thickness,
                10.0,
                50.0,
                (value) {
                  provider.updateWallProperties(wallId, thickness: value);
                },
              ),

              const SizedBox(height: 16),

              // تعديل اللون
              _buildColorProperty(
                'لون الجدار',
                wall.color,
                (color) {
                  provider.updateWallProperties(wallId, color: color);
                },
              ),
            ],
          ),

          const SizedBox(height: 16),

          // أزرار التحكم
          _buildActionCard(
            '⚙️ إجراءات الجدار',
            [
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _showDeleteWallConfirmation(context, provider, wallId);
                      },
                      icon: const Icon(Icons.delete, color: Colors.white),
                      label: const Text('حذف الجدار',
                          style: TextStyle(color: Colors.white)),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEntranceProperties(
    BuildContext context,
    WarehousePlannerProvider provider,
    String entranceId,
  ) {
    final entrance = provider.currentLayout?.entrances.firstWhere(
      (e) => e.id == entranceId,
    );

    if (entrance == null) return _buildEmptyState();

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPropertyCard(
            '🚪 خصائص المدخل',
            [
              _buildPropertyRow('العرض', '${entrance.width.toInt()} سم'),
              _buildPropertyRow('الجدار المرتبط', entrance.wallId),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyCard(String title, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: const Color(0xFFE9ECEF)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(12),
            child: Column(children: children),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 13),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(
      BuildContext context, WarehousePlannerProvider provider, Shelf shelf) {
    return Column(
      children: [
        const Divider(),
        const SizedBox(height: 8),

        // زر تفاصيل كاملة كبير
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              _showLargeShelfDetailDialog(context, shelf, provider);
            },
            icon: const Icon(Icons.zoom_in, size: 20),
            label:
                const Text('عرض تفاصيل كاملة', style: TextStyle(fontSize: 14)),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF3498DB),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.all(12),
            ),
          ),
        ),

        const SizedBox(height: 8),

        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  // دوران الخزانة
                  provider.rotateShelf(shelf.id, shelf.rotation + 90);
                },
                icon: const Icon(Icons.rotate_right, size: 16),
                label: const Text('دوران', style: TextStyle(fontSize: 12)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(8),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  provider.deleteShelf(shelf.id);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم حذف الخزانة')),
                  );
                },
                icon: const Icon(Icons.delete, size: 16),
                label: const Text('حذف', style: TextStyle(fontSize: 12)),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.all(8),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _showLargeShelfDetailDialog(
      BuildContext context, Shelf shelf, WarehousePlannerProvider provider) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: 700,
          height: 600,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.shelves, color: shelf.color, size: 32),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          shelf.name,
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          _getShelfTypeName(shelf.type),
                          style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF6C757D),
                          ),
                        ),
                      ],
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close, size: 28),
                  ),
                ],
              ),

              const SizedBox(height: 24),

              // خريطة كبيرة للخزانة
              Container(
                height: 250,
                width: double.infinity,
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFE9ECEF), width: 2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: GridView.builder(
                  padding: const EdgeInsets.all(8),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: shelf.slotsPerLevel,
                    crossAxisSpacing: 3,
                    mainAxisSpacing: 3,
                    childAspectRatio: 1,
                  ),
                  itemCount: shelf.bins.length,
                  itemBuilder: (context, index) {
                    final bin = shelf.bins[index];
                    return Container(
                      decoration: BoxDecoration(
                        color: bin.statusColor,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.white, width: 2),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              'L${bin.level + 1}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                            Text(
                              'S${bin.slot + 1}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                              ),
                            ),
                            if (bin.productName != null)
                              Text(
                                bin.productCode?.substring(0, 3) ?? 'XXX',
                                style: const TextStyle(
                                  fontSize: 8,
                                  color: Colors.white,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),

              // قائمة الأدوية مع تفاصيل أكبر
              const Text(
                'الأدوية المخزنة:',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView.builder(
                  itemCount:
                      shelf.bins.where((bin) => bin.productName != null).length,
                  itemBuilder: (context, index) {
                    final medicatedBins = shelf.bins
                        .where((bin) => bin.productName != null)
                        .toList();
                    if (index >= medicatedBins.length) {
                      return const SizedBox.shrink();
                    }

                    final bin = medicatedBins[index];
                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        contentPadding: const EdgeInsets.all(16),
                        leading: Container(
                          width: 50,
                          height: 50,
                          decoration: BoxDecoration(
                            color: bin.statusColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Text(
                              bin.productCode?.substring(0, 3) ?? 'XXX',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ),
                        title: Text(
                          bin.productName!,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'الموقع: مستوى ${bin.level + 1} - خانة ${bin.slot + 1}',
                              style: const TextStyle(fontSize: 13),
                            ),
                            Text(
                              'الكمية: ${bin.quantity} ${bin.unitName ?? 'وحدة'}',
                              style: const TextStyle(fontSize: 13),
                            ),
                            if (bin.expiryDate != null)
                              Text(
                                'ينتهي: ${bin.expiryDate!.toString().substring(0, 10)}',
                                style: TextStyle(
                                  fontSize: 13,
                                  color: _getExpiryColor(bin.expiryDate!),
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Helper methods
  int _getOccupiedBins(Shelf shelf) {
    return shelf.bins.where((bin) => bin.status != BinStatus.empty).length;
  }

  double _getOccupancyRate(Shelf shelf) {
    final occupied = _getOccupiedBins(shelf);
    return shelf.bins.isNotEmpty ? occupied / shelf.bins.length : 0.0;
  }

  double _calculateWallLength(Wall wall) {
    double length = 0.0;
    for (int i = 0; i < wall.points.length - 1; i++) {
      final p1 = wall.points[i];
      final p2 = wall.points[i + 1];
      length += (p2 - p1).length;
    }
    return length;
  }

  String _getShelfTypeName(ShelfType type) {
    switch (type) {
      case ShelfType.standard:
        return 'خزانة عادية';
      case ShelfType.refrigerated:
        return 'خزانة مبردة';
      case ShelfType.controlled:
        return 'خزانة محكمة';
      case ShelfType.hazardous:
        return 'مواد خطرة';
      case ShelfType.narcotics:
        return 'خزانة مخدرات';
    }
  }

  Color _getExpiryColor(DateTime expiryDate) {
    final now = DateTime.now();
    final daysUntilExpiry = expiryDate.difference(now).inDays;

    if (daysUntilExpiry <= 0) {
      return Colors.red;
    } else if (daysUntilExpiry <= 30) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  // دالة لإنشاء slider للتحكم في القيم الرقمية
  Widget _buildSliderProperty(
    String label,
    double value,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              '${value.toInt()}',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: Color(0xFF3498DB),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Slider(
          value: value,
          min: min,
          max: max,
          divisions: (max - min).toInt(),
          activeColor: const Color(0xFF3498DB),
          onChanged: onChanged,
        ),
      ],
    );
  }

  // دالة لإنشاء منتقي اللون
  Widget _buildColorProperty(
    String label,
    Color currentColor,
    Function(Color) onChanged,
  ) {
    final colors = [
      const Color(0xFF34495E), // رمادي داكن
      const Color(0xFF2C3E50), // أزرق داكن
      const Color(0xFF8E44AD), // بنفسجي
      const Color(0xFF3498DB), // أزرق
      const Color(0xFF16A085), // أخضر مزرق
      const Color(0xFF27AE60), // أخضر
      const Color(0xFFF39C12), // برتقالي
      const Color(0xFFE74C3C), // أحمر
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          children: colors.map((color) {
            final isSelected = color == currentColor;
            return GestureDetector(
              onTap: () => onChanged(color),
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(
                    color: isSelected ? Colors.black : Colors.grey.shade300,
                    width: isSelected ? 3 : 1,
                  ),
                ),
                child: isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : null,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  // دالة لإنشاء كارت قابل للتعديل
  Widget _buildEditablePropertyCard(String title, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.shade100,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  // دالة لإنشاء كارت الإجراءات
  Widget _buildActionCard(String title, List<Widget> children) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2C3E50),
              ),
            ),
            const SizedBox(height: 12),
            ...children,
          ],
        ),
      ),
    );
  }

  // دالة لإظهار تأكيد حذف الجدار
  void _showDeleteWallConfirmation(
    BuildContext context,
    WarehousePlannerProvider provider,
    String wallId,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🗑️ حذف الجدار'),
        content: const Text(
          'هل أنت متأكد من حذف هذا الجدار؟\n\nسيتم أيضاً حذف جميع المداخل المرتبطة به.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              provider.deleteWall(wallId);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('🗑️ تم حذف الجدار بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text(
              'حذف',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
