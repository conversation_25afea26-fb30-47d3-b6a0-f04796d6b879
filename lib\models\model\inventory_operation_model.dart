import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/product_model.dart';

enum InventoryOperationType {
  unknown,
  DamagedExpired,
  Incoming,
  Outgoing,
  OpeningBalance,
  ItemsTransfer,
  Stocktaking,
  Shortage,
  Surplus,
  TransferToSection,
  TransferToSectionRequset,
}

class InventoryOperationModel {
  String? code;
  int? localId;
  String? entryDateFormated;
  int? fromStoreID;
  int? toStoreID;
  String? storeName;
  String? toStoreName;
  int? iD;
  int? operationType;
  DateTime? entryDate;
  String? notes;
  bool? isPosted;
  int? sourceID;
  int? sourceReference1;
  String? sourceReference1Name;
  int? secationId;
  String? secationName;
  String? sourceReference2;
  String? aPPReferanceCode;
  String? submit;
  double? total;
  List<InventoryOperationItems>? inventoryOperationItems;

  InventoryOperationModel({
    this.code,
    this.localId,
    this.entryDateFormated,
    this.inventoryOperationItems,
    this.fromStoreID,
    this.toStoreID,
    this.storeName,
    this.toStoreName,
    this.iD,
    this.operationType,
    this.entryDate,
    this.notes,
    this.isPosted,
    this.sourceID,
    this.secationId,
    this.secationName,
    this.sourceReference1,
    this.sourceReference1Name,
    this.sourceReference2,
    this.total,
    this.submit,
    this.aPPReferanceCode,
  });

  InventoryOperationModel copyWith({
    String? code,
    int? localId,
    String? entryDateFormated,
    List<InventoryOperationItems>? inventoryOperationItems,
    int? fromStoreID,
    int? toStoreID,
    String? storeName,
    String? toStoreName,
    int? iD,
    int? operationType,
    DateTime? entryDate,
    String? notes,
    bool? isPosted,
    int? sourceID,
    int? sourceReference1,
    String? sourceReference1Name,
    int? secationId,
    String? secationName,
    String? sourceReference2,
    double? total,
    String? submit,
    String? aPPReferanceCode,
  }) {
    return InventoryOperationModel(
      code: code ?? this.code,
      localId: localId ?? this.localId,
      entryDateFormated: entryDateFormated ?? this.entryDateFormated,
      inventoryOperationItems:
          inventoryOperationItems ?? this.inventoryOperationItems,
      fromStoreID: fromStoreID ?? this.fromStoreID,
      toStoreID: toStoreID ?? this.toStoreID,
      storeName: storeName ?? this.storeName,
      toStoreName: toStoreName ?? this.toStoreName,
      iD: iD ?? this.iD,
      operationType: operationType ?? this.operationType,
      entryDate: entryDate ?? this.entryDate,
      notes: notes ?? this.notes,
      isPosted: isPosted ?? this.isPosted,
      sourceID: sourceID ?? this.sourceID,
      sourceReference1: sourceReference1 ?? this.sourceReference1,
      sourceReference1Name: sourceReference1Name ?? this.sourceReference1Name,
      secationId: secationId ?? this.secationId,
      secationName: secationName ?? this.secationName,
      sourceReference2: sourceReference2 ?? this.sourceReference2,
      total: total ?? this.total,
      submit: submit ?? this.submit,
      aPPReferanceCode: aPPReferanceCode ?? this.aPPReferanceCode,
    );
  }

  InventoryOperationModel.fromJson(Map<String, dynamic> json) {
    code = json['Code'];
    localId = json['localId'];

    entryDateFormated = json['Entry_Date_Formated'];
    if (json['InventoryOperationItems'] != null) {
      inventoryOperationItems = <InventoryOperationItems>[];
      json['InventoryOperationItems'].forEach((v) {
        inventoryOperationItems!.add(new InventoryOperationItems.fromJson(v));
      });
    }
    fromStoreID = json['From_Store_ID'];
    toStoreID = json['To_Store_ID'];
    storeName = json['Store_Name'];
    toStoreName = json['To_Store_Name'];
    secationId = json['Secation_ID'];
    secationName = json['secationName'];

    iD = json['ID'];
    operationType = json['Operation_Type'];
    entryDate = json['entryDate'].toString().contains("-")
        ? DateTime.parse(json['entryDate'])
        : formatDate(json['Entry_Date']);

    notes = json['Notes'];

    isPosted = json['Is_Posted'];
    sourceID = json['Source_ID'];
    sourceReference1 = json['Source_Reference_1'];
    sourceReference1Name = json['sourceReference1Name'];
    sourceReference2 = json['Source_Reference_2'];
    total = json['total'];
    submit = json['Submit'];
    aPPReferanceCode = json['APP_Referance_Code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Code'] = code;
    data['localId'] = localId;
    data['Entry_Date_Formated'] = entryDateFormated;
    if (inventoryOperationItems != null) {
      data['InventoryOperationItems'] =
          inventoryOperationItems!.map((v) => v.toJson()).toList();
    }
    data['From_Store_ID'] = fromStoreID;
    data['To_Store_ID'] = toStoreID;
    data['Store_Name'] = storeName;
    data['To_Store_Name'] = toStoreName;
    // data['Secation_ID'] = secationId;
    data['ID'] = iD;
    data['Operation_Type'] = operationType;
    data['Entry_Date'] = entryDate?.toIso8601String();
    data['Notes'] = notes;
    data['Is_Posted'] = isPosted;
    data['total'] = total;
    data['Source_ID'] = sourceID;
    data['Source_Reference_1'] = sourceReference1;
    data['sourceReference1Name'] = sourceReference1Name;
    data['Source_Reference_2'] = sourceReference2;
    data['Submit'] = submit;
    data['APP_Referance_Code'] = aPPReferanceCode;
    return data;
  }
}

class InventoryOperationItems {
  double? quantity;
  double? balance;
  int? unitID;
  double? unitPrice;
  String? itemName;
  String? itemCode;
  int? operationItemID;
  int? operationID;
  int? itemID;

  int? storeID;
  String? barcode;
  bool? hasSelectedAttributes;
  List<int>? selectedOptionIds;
  String? selectedAttribute;
  String? batchNumber;
  String? serialNumber;
  DateTime? expirationDate;
  List<ItemAttribute>? attribute;
  List<ItemAttribute>? itemAttribute;

  InventoryOperationItems(
      {this.quantity,
      this.balance,
      this.unitID,
      this.unitPrice,
      this.itemName,
      this.itemCode,
      this.operationItemID,
      this.operationID,
      this.itemID,
      this.storeID,
      this.barcode,
      this.selectedAttribute,
      this.itemAttribute,
      this.attribute,
      this.hasSelectedAttributes,
      this.batchNumber,
      this.serialNumber,
      this.expirationDate,
      this.selectedOptionIds});
  InventoryOperationItems copy() {
    return InventoryOperationItems(
      quantity: quantity,
      balance: balance,
      unitID: unitID,
      unitPrice: unitPrice,
      itemName: itemName,
      itemCode: itemCode,
      operationItemID: operationItemID,
      operationID: operationID,
      itemID: itemID,
      storeID: storeID,
      barcode: barcode,
      hasSelectedAttributes: hasSelectedAttributes,
      attribute: attribute,
      itemAttribute: attribute,
      selectedAttribute: selectedAttribute,
      batchNumber: batchNumber,
      serialNumber: serialNumber,
      expirationDate: expirationDate,
      selectedOptionIds:
          selectedOptionIds != null ? List<int>.from(selectedOptionIds!) : null,
    );
  }

  InventoryOperationItems.fromJson(Map<String, dynamic> json) {
    quantity = json['Quantity'];
    balance = json['Balance'];
    unitID = json['Unit_ID'];
    unitPrice = json['Unit_Price'];
    itemName = json['Item_Name'];
    itemCode = json['Item_Code'];
    operationItemID = json['Operation_Item_ID'];
    operationID = json['Operation_ID'];
    itemID = json['Item_ID'];
    storeID = json['Store_ID'];
    barcode = json['barcode'];
    hasSelectedAttributes = json['hasSelectedAttributes'];
    batchNumber = json['Batch_Number'];
    serialNumber = json['Serial_Number'];
    expirationDate = json['Expiration_Date'] != null
        ? json['Expiration_Date'].toString().contains("-")
            ? DateTime.parse(json['Expiration_Date'])
            : parseAspNetDate(json['Expiration_Date'])
        : DateTime.now();
    selectedAttribute = json['SelectedAttribute'];
    if (json['selectedOptionIds'] != null) {
      selectedOptionIds = List<int>.from(json['selectedOptionIds']);
    }
    if (json['ItemAttribute'] != null) {
      // Deserialize ItemAttributes
      itemAttribute = <ItemAttribute>[];
      json['ItemAttribute'].forEach((v) {
        itemAttribute?.add(ItemAttribute.fromJson(v));
      });
    }
    if (json['Attribute'] != null) {
      // Deserialize ItemAttributes
      attribute = <ItemAttribute>[];
      json['Attribute'].forEach((v) {
        attribute?.add(ItemAttribute.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Quantity'] = quantity;
    data['Balance'] = balance;
    data['Unit_ID'] = unitID;
    data['Unit_Price'] = unitPrice;
    data['Item_Name'] = itemName;
    data['Item_Code'] = itemCode;
    data['Operation_Item_ID'] = operationItemID;
    data['Operation_ID'] = operationID;
    data['Item_ID'] = itemID;
    data['Store_ID'] = storeID;
    data['barcode'] = barcode;
    data['hasSelectedAttributes'] = hasSelectedAttributes;
    data['SelectedAttribute'] = selectedAttribute;
    data['Batch_Number'] = batchNumber;
    data['Serial_Number'] = serialNumber;
    data['Expiration_Date'] = expirationDate?.toIso8601String();
    if (selectedOptionIds != null) {
      data['selectedOptionIds'] = selectedOptionIds;
    }

    if (itemAttribute != null) {
      data['ItemAttribute'] = itemAttribute!.map((v) => v.toJson()).toList();
    }
    if (attribute != null) {
      data['Attribute'] = attribute!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
