import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/printer_settings_controller.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/helpers/arabic_font_loader.dart';

class InventoryPrintDto {
  final String code;
  final DateTime date;
  final String? user;
  final String typeName;
  final List<InventoryProductItem> items;

  InventoryPrintDto({
    required this.code,
    required this.date,
    this.user,
    required this.typeName,
    required this.items,
  });
}

class InventoryProductItem {
  final String title;
  final double quantity;
  final String? unit;

  InventoryProductItem({
    required this.title,
    required this.quantity,
    this.unit,
  });
}

class PrinterService {
  // Helper method to create a PDF document from an invoice
  static Future<pw.Document> _createInvoicePdf(InvoiceDto invoice,
      [PrinterSettingsController? printerSettings]) async {
    // Create PDF document with proper RTL support
    final pdf = pw.Document();

    // Load Arabic font using the centralized font loader
    pw.Font arabicFont;

    try {
      // استخدام خدمة تحميل الخطوط العربية الموحدة
      arabicFont = await ArabicFontLoader.getSafeArabicFont();
      debugPrint("✅ Arabic font loaded successfully for printing");
    } catch (e) {
      debugPrint("⚠️ Arabic font failed, falling back to default: $e");
      // Fall back to default font
      arabicFont = pw.Font.helvetica();
    }

    // Default page format
    PdfPageFormat pageFormat = PdfPageFormat.roll80;

    // Default header and footer
    String receiptHeader = '';
    String receiptFooter = '';
    bool printLogo = false;

    // If printer settings are provided, use them
    if (printerSettings != null) {
      // Get page format from settings
      switch (printerSettings.paperSize) {
        case PaperSize.mm58:
          pageFormat = const PdfPageFormat(
              58 * PdfPageFormat.mm, 297 * PdfPageFormat.mm);
          break;
        case PaperSize.mm80:
          pageFormat = PdfPageFormat.roll80;
          break;
        case PaperSize.a4:
          pageFormat = PdfPageFormat.a4;
          break;
        case PaperSize.letter:
          pageFormat = PdfPageFormat.letter;
          break;
        default:
          pageFormat = PdfPageFormat.roll80;
      }

      // Get header and footer from settings
      receiptHeader = printerSettings.receiptHeader;
      receiptFooter = printerSettings.receiptFooter;
      printLogo = printerSettings.printLogo;
    }

    // Add content to PDF
    pdf.addPage(
      pw.Page(
        pageFormat: pageFormat,
        build: (context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Logo if configured
              if (printLogo)
                pw.Center(
                  child: pw.SizedBox(
                    width: 150,
                    height: 50,
                    child: pw.Container(
                      alignment: pw.Alignment.center,
                      child: pw.Text(
                        'LOGO',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),

              // Custom Header
              if (receiptHeader.isNotEmpty)
                pw.Center(
                  child: pw.Text(
                    receiptHeader,
                    style: pw.TextStyle(font: arabicFont, fontSize: 12),
                    textDirection: pw.TextDirection.rtl,
                    textAlign: pw.TextAlign.center,
                  ),
                ),

              // Header
              pw.Center(
                child: pw.Text(
                  'فاتورة مبيعات',
                  style: pw.TextStyle(font: arabicFont, fontSize: 15),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
              pw.SizedBox(height: 10),

              // Invoice Info
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'رقم الفاتورة: ${invoice.invoiceCode ?? "N/A"}',
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.Text(
                    'التاريخ: ${invoice.invoiceDate?.toString().split(' ')[0] ?? "N/A"}',
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ],
              ),
              pw.SizedBox(height: 10),

              // Customer Info
              pw.Text(
                'العميل: ${invoice.custoemrName ?? "N/A"}',
                style: pw.TextStyle(font: arabicFont, fontSize: 10),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(height: 10),

              // Items Header
              pw.Table(
                border: pw.TableBorder.symmetric(), // بدون خطوط
                columnWidths: {
                  0: const pw.FlexColumnWidth(), // Batch
                  1: const pw.FlexColumnWidth(), // السعر
                  2: const pw.FlexColumnWidth(), // الكمية
                  3: const pw.FlexColumnWidth(), // المنتج
                },
                children: [
                  // العناوين
                  pw.TableRow(
                    children: [
                      AppController.isPharmacy
                          ? pw.Text('Batch',
                              textDirection: pw.TextDirection.rtl,
                              textAlign: pw.TextAlign.left,
                              style:
                                  pw.TextStyle(font: arabicFont, fontSize: 10))
                          : pw.SizedBox(),
                      pw.Text('السعر',
                          textDirection: pw.TextDirection.rtl,
                          textAlign: pw.TextAlign.left,
                          style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                      pw.Text('الكمية',
                          textDirection: pw.TextDirection.rtl,
                          textAlign: pw.TextAlign.left,
                          style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                      pw.Text('المنتج',
                          textDirection: pw.TextDirection.rtl,
                          textAlign: pw.TextAlign.left,
                          style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                    ],
                  ),

                  // العناصر
                  ...invoice.salesItems?.map(
                        (item) => pw.TableRow(
                          children: [
                            AppController.isPharmacy
                                ? pw.Text(item.batchNumber ?? "",
                                    style: pw.TextStyle(
                                        font: arabicFont, fontSize: 10))
                                : pw.SizedBox(),
                            pw.Text(item.price?.toStringAsFixed(2) ?? "0.00",
                                style: pw.TextStyle(
                                    font: arabicFont, fontSize: 10)),
                            pw.Text(item.quantity?.toString() ?? "0",
                                style: pw.TextStyle(
                                    font: arabicFont, fontSize: 10)),
                            pw.Text(
                                '${item.title} ${item.attribute?.map((e) => e.itemsAttributeOptions?.map((e) => e.optionName).join(",") ?? "").join(", ") ?? ""}',
                                textAlign: pw.TextAlign.right,
                                style: pw.TextStyle(
                                    font: arabicFont, fontSize: 10)),
                          ],
                        ),
                      ) ??
                      [],
                ],
              ),
              pw.SizedBox(height: 20),

              // Totals
              pw.Container(
                padding: const pw.EdgeInsets.all(5),
                decoration: const pw.BoxDecoration(
                  border: pw.Border(
                    bottom: pw.BorderSide(width: 1),
                    top: pw.BorderSide(width: 1),
                  ),
                ),
                child: pw.Column(
                  children: [
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          invoice.total?.toStringAsFixed(2) ?? "0.00",
                          style: pw.TextStyle(font: arabicFont, fontSize: 10),
                        ),
                        pw.Text(
                          'المجموع',
                          style: pw.TextStyle(font: arabicFont, fontSize: 10),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ],
                    ),
                    if (invoice.discountValue != null &&
                        invoice.discountValue! > 0)
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text(
                            invoice.discountValue?.toStringAsFixed(2) ?? "0.00",
                            style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 10,
                            ),
                          ),
                          pw.Text(
                            'الخصم',
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                            textDirection: pw.TextDirection.rtl,
                          ),
                        ],
                      ),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          invoice.totalAfterDiscount?.toStringAsFixed(2) ??
                              "0.00",
                          style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold),
                        ),
                        pw.Text(
                          'الإجمالي',
                          style: pw.TextStyle(
                              font: arabicFont,
                              fontSize: 12,
                              fontWeight: pw.FontWeight.bold),
                          textDirection: pw.TextDirection.rtl,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // Custom Footer
              if (receiptFooter.isNotEmpty)
                pw.Center(
                  child: pw.Text(
                    receiptFooter,
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                    textAlign: pw.TextAlign.center,
                  ),
                ),

              // Default Footer
              pw.Center(
                child: pw.Text(
                  'شكراً لتعاملكم معنا',
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
              pw.Center(
                child: pw.Text(
                  'Pal4it',
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  static Future<void> printInvoice(
      InvoiceDto invoice, BuildContext context) async {
    try {
      // Check if we're on mobile or desktop
      final bool isMobile =
          !Platform.isWindows && !Platform.isLinux && !Platform.isMacOS;

      // Get printer settings
      final printerSettings =
          Provider.of<PrinterSettingsController>(context, listen: false);

      // If on mobile and printer settings are not configured, just show the print dialog directly
      if (isMobile && printerSettings.printerType == null) {
        // Create PDF document
        final pdf = await _createInvoicePdf(invoice);

        // Show print dialog directly
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name: 'Invoice ${invoice.invoiceCode}',
          format: PdfPageFormat.roll80, // Default to 80mm receipt
        );

        // Show success message
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال الفاتورة للطباعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
        return;
      }

      // For desktop or configured mobile printers, use the printer settings
      // Get the appropriate page format based on printer settings
      PdfPageFormat pageFormat;
      switch (printerSettings.paperSize) {
        case PaperSize.mm58:
          pageFormat = const PdfPageFormat(
              58 * PdfPageFormat.mm, 297 * PdfPageFormat.mm);
          break;
        case PaperSize.mm80:
          pageFormat = PdfPageFormat.roll80;
          break;
        case PaperSize.a4:
          pageFormat = PdfPageFormat.a4;
          break;
        case PaperSize.letter:
          pageFormat = PdfPageFormat.letter;
          break;
        default:
          pageFormat = PdfPageFormat.roll80;
      }

      // Create PDF document
      final pdf = await _createInvoicePdf(invoice, printerSettings);

      // Save PDF to temporary file
      final output = await getTemporaryDirectory();
      final file = File(
          '${output.path}/invoice_${invoice.invoiceCode ?? DateTime.now().millisecondsSinceEpoch}.pdf');
      await file.writeAsBytes(await pdf.save());

      // Print based on printer type
      if (printerSettings.printerType == 'network') {
        // Network printer
        if (printerSettings.printerIP != null) {
          // Use Printing package to print to network printer
          try {
            await Printing.layoutPdf(
              onLayout: (_) async => await pdf.save(),
              name: 'Invoice ${invoice.invoiceCode}',
              format: pageFormat,
            );

            // Check if context is still valid
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال الفاتورة للطباعة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          } catch (e) {
            debugPrint('Error printing to network printer: $e');
            // Check if context is still valid
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('فشل في الطباعة: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        }
      } else if (printerSettings.printerType == 'usb') {
        // USB printer
        try {
          // Find the selected printer
          final printers = await Printing.listPrinters();
          final selectedPrinter = printers.firstWhere(
            (printer) => printer.name == printerSettings.selectedPrinterName,
            orElse: () => throw Exception('الطابعة المحددة غير متوفرة'),
          );

          // Print to the selected printer
          final result = await Printing.directPrintPdf(
            printer: selectedPrinter,
            onLayout: (_) async => await pdf.save(),
          );

          // Check if context is still valid
          if (context.mounted) {
            if (result) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم طباعة الفاتورة بنجاح'),
                  backgroundColor: Colors.green,
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('فشل في طباعة الفاتورة'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        } catch (e) {
          debugPrint('Error printing to USB printer: $e');
          // Check if context is still valid
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في الطباعة: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else if (printerSettings.printerType == 'bluetooth') {
        // Bluetooth printer
        try {
          debugPrint(
              'Printing to Bluetooth printer: ${printerSettings.selectedPrinterName}');

          // For now, we'll use the generic print dialog
          // In a real implementation, you would need to:
          // 1. Find the Bluetooth device by address
          // 2. Connect to it
          // 3. Send the ESC/POS commands for printing

          // This is a simplified implementation
          await Printing.layoutPdf(
            onLayout: (_) async => await pdf.save(),
            name: 'Invoice ${invoice.invoiceCode}',
            format: pageFormat,
          );

          // Check if context is still valid
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم إرسال الفاتورة للطباعة بنجاح'),
                backgroundColor: Colors.green,
              ),
            );
          }

          // Note: For actual Bluetooth printing, you would need to:
          // 1. Convert the PDF to a bitmap or ESC/POS commands
          // 2. Connect to the Bluetooth printer
          // 3. Send the commands to the printer
          // This requires platform-specific code and a library like esc_pos_bluetooth
        } catch (e) {
          debugPrint('Error printing to Bluetooth printer: $e');
          // Check if context is still valid
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('فشل في الطباعة: $e'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } else {
        // No printer configured, show print dialog
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name: 'Invoice ${invoice.invoiceCode}',
          format: pageFormat,
        );
      }
    } catch (e) {
      debugPrint('Error printing invoice: $e');
      // Check if context is still valid
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء طباعة الفاتورة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      rethrow;
    }
  }
}

class PrinterServiceforinventory {
  static Future<pw.Document> _createInventoryPdf(
    InventoryOperationModel data,
    PrinterSettingsController? printerSettings,
  ) async {
    final pdf = pw.Document();

    final fontData = await rootBundle.load("assets/fonts/NeoSans-Regular.ttf");
    final arabicFont = pw.Font.ttf(fontData);

    PdfPageFormat pageFormat = PdfPageFormat.roll80;
    String receiptHeader = '';
    String receiptFooter = '';
    bool printLogo = false;

    if (printerSettings != null) {
      switch (printerSettings.paperSize) {
        case PaperSize.mm58:
          pageFormat = const PdfPageFormat(
              58 * PdfPageFormat.mm, 297 * PdfPageFormat.mm);
          break;
        case PaperSize.a4:
          pageFormat = PdfPageFormat.a4;
          break;
        case PaperSize.letter:
          pageFormat = PdfPageFormat.letter;
          break;
        default:
          pageFormat = PdfPageFormat.roll80;
      }
      receiptHeader = printerSettings.receiptHeader;
      receiptFooter = printerSettings.receiptFooter;
      printLogo = printerSettings.printLogo;
    }

    pdf.addPage(
      pw.Page(
        pageFormat: pageFormat,
        build: (context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              if (printLogo)
                pw.Center(
                  child: pw.SizedBox(
                    width: 150,
                    height: 50,
                    child: pw.Container(
                      alignment: pw.Alignment.center,
                      child: pw.Text(
                        'LOGO',
                        style: pw.TextStyle(
                          font: arabicFont,
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              if (receiptHeader.isNotEmpty)
                pw.Center(
                  child: pw.Text(
                    receiptHeader,
                    style: pw.TextStyle(font: arabicFont, fontSize: 12),
                    textDirection: pw.TextDirection.rtl,
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              pw.Center(
                child: pw.Text(
                  'تفاصيل المخزون',
                  style: pw.TextStyle(font: arabicFont, fontSize: 15),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text(
                    'كود المخزون: ${data.code ?? "N/A"}',
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                  ),
                  pw.Text(
                    'التاريخ: ${data.entryDateFormated?.toString().split(' ')[0] ?? "N/A"}',
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                  ),
                ],
              ),
              pw.SizedBox(height: 10),
              // pw.Text(
              //   'العميل: ${data.insertUserID ?? "N/A"}',
              //   style: pw.TextStyle(font: arabicFont, fontSize: 10),
              //   textDirection: pw.TextDirection.rtl,
              // ),
              pw.SizedBox(height: 10),
              pw.Container(
                padding: const pw.EdgeInsets.all(5),
                decoration: const pw.BoxDecoration(
                  border: pw.Border(
                    bottom: pw.BorderSide(width: 1),
                    top: pw.BorderSide(width: 1),
                  ),
                ),
                child: pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Batch',
                        textDirection: pw.TextDirection.rtl,
                        style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                    pw.Text('السعر',
                        textDirection: pw.TextDirection.rtl,
                        style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                    pw.Text('الكمية',
                        textDirection: pw.TextDirection.rtl,
                        style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                    pw.Text('المنتج',
                        textDirection: pw.TextDirection.rtl,
                        style: pw.TextStyle(font: arabicFont, fontSize: 10)),
                  ],
                ),
              ),
              ...data.inventoryOperationItems?.map(
                    (item) => pw.Container(
                      padding: const pw.EdgeInsets.all(5),
                      decoration: const pw.BoxDecoration(
                        border: pw.Border(
                          bottom: pw.BorderSide(width: 0.5),
                        ),
                      ),
                      child: pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          item.batchNumber == null
                              ? pw.SizedBox()
                              : pw.Text(
                                  item.batchNumber ?? "N/A",
                                  style: pw.TextStyle(
                                      font: arabicFont, fontSize: 10),
                                ),
                          pw.Text(
                            item.unitPrice?.toStringAsFixed(2) ?? "0.00",
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                          ),
                          pw.Text(
                            item.quantity?.toString() ?? "0",
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                          ),
                          pw.Text(
                            item.itemName ?? "N/A",
                            textDirection: pw.TextDirection.rtl,
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(font: arabicFont, fontSize: 10),
                          ),
                        ],
                      ),
                    ),
                  ) ??
                  [],
              pw.SizedBox(height: 20),
              if (receiptFooter.isNotEmpty)
                pw.Center(
                  child: pw.Text(
                    receiptFooter,
                    style: pw.TextStyle(font: arabicFont, fontSize: 10),
                    textDirection: pw.TextDirection.rtl,
                    textAlign: pw.TextAlign.center,
                  ),
                ),
              pw.Center(
                child: pw.Text(
                  'شكراً لتعاملكم معنا',
                  style: pw.TextStyle(font: arabicFont, fontSize: 12),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
            ],
          );
        },
      ),
    );

    return pdf;
  }

  // ✅ Add this method for external printing
  static Future<void> printInventoryOperation(
    InventoryOperationModel data,
    BuildContext context,
  ) async {
    try {
      final printerSettings =
          Provider.of<PrinterSettingsController>(context, listen: false);
      final pdf = await _createInventoryPdf(data, printerSettings);
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );
    } catch (e) {
      debugPrint('Error printing inventory: $e');
      rethrow;
    }
  }
}
