import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class BubbledContainer<PERSON>ithIcon extends StatelessWidget {
  const BubbledContainerWithIcon({
    super.key,
    required this.title,
    required this.icon,
    required this.selected,
    required this.onTap,
  });
  final String title;
  final IconData icon;
  final bool selected;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 50,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          border: Border.all(
              color: selected
                  ? context.primaryColor
                  : Colors.grey.withOpacity(.5)),
          color: selected
              ? context.primaryColor.withOpacity(.2)
              : Colors.transparent,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(title),
            const SizedBox(width: 5),
            Icon(icon),
          ],
        ),
      ),
    );
  }
}
