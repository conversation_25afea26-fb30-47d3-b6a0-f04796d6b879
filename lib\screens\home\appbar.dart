import 'package:flutter/material.dart';

import 'dart:ui' as ui;

import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/screens/setting/setting_page.dart';
import 'package:inventory_application/screens/warehouse_planner/worker_warehouse_display.dart';

class MyAppBar extends StatefulWidget {
  const MyAppBar({super.key, required this.onClick});

  final Function onClick;
  @override
  State<MyAppBar> createState() => _MyAppBarState();
}

class _MyAppBarState extends State<MyAppBar> {
  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: ui.TextDirection.ltr,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white,
              Colors.white.withOpacity(0.95),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 20,
              offset: const Offset(0, 4),
            ),
          ],
          borderRadius: const BorderRadius.only(
            bottomLeft: Radius.circular(24),
            bottomRight: Radius.circular(24),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Left side: Logo and Branch Name
            Expanded(
              child: Row(
                children: [
                  // Modern Logo Container
                  Container(
                    padding: const EdgeInsets.all(5),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      // boxShadow: [
                      //   BoxShadow(
                      //     color: Colors.grey.withOpacity(0.3),
                      //     blurRadius: 12,
                      //     offset: const Offset(0, 4),
                      //   ),
                      // ],
                    ),
                    child: Image.asset(
                      "assets/images/base_images/main_logo.png",
                      width: 50,
                      height: 50,
                      fit: BoxFit.contain,
                      // color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 16),

                  // Branch Name with modern styling
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          "ERP System",
                          style: TextStyle(
                            color: context.colors.primary.withOpacity(0.7),
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            letterSpacing: 0.5,
                          ),
                        ),
                        const SizedBox(height: 2),
                        Text(
                          AppController.currentBranchName,
                          style: TextStyle(
                            color: context.colors.primary,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            letterSpacing: 0.3,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Right side: Action buttons
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Settings Button
                _buildModernActionButton(
                  context: context,
                  icon: Icons.settings_outlined,
                  onTap: () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const SettingPage(),
                      ),
                    );
                  },
                  tooltip: "Settings",
                ),
                const SizedBox(width: 12),

                // Menu Button
                _buildModernActionButton(
                  context: context,
                  icon: Icons.menu_rounded,
                  onTap: () {
                    widget.onClick();
                  },
                  tooltip: "Menu",
                  isPrimary: true,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernActionButton({
    required BuildContext context,
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
    bool isPrimary = false,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(14),
          child: Container(
            padding: const EdgeInsets.all(5),
            decoration: BoxDecoration(
              gradient: isPrimary
                  ? LinearGradient(
                      colors: [
                        context.colors.primary,
                        context.colors.primary.withOpacity(0.8),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    )
                  : null,
              color: isPrimary ? null : context.colors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(14),
              border: Border.all(
                color: isPrimary
                    ? Colors.transparent
                    : context.colors.primary.withOpacity(0.2),
                width: 1,
              ),
              boxShadow: isPrimary
                  ? [
                      BoxShadow(
                        color: context.colors.primary.withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ]
                  : null,
            ),
            child: Icon(
              icon,
              color: isPrimary ? Colors.white : context.colors.primary,
              size: 22,
            ),
          ),
        ),
      ),
    );
  }
}
