import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/inventory_operation_transfare_to_section_controlle.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:provider/provider.dart';

class InventoryOperationTransfareToSectionBaseInfoWidget
    extends StatefulWidget {
  const InventoryOperationTransfareToSectionBaseInfoWidget(
      {super.key,
      required this.onSelectWarehouse,
      required this.onselecteRequest});
  final Function onSelectWarehouse;
  final Function onselecteRequest;

  @override
  State<InventoryOperationTransfareToSectionBaseInfoWidget> createState() =>
      _InventoryOperationTransfareToSectionBaseInfoWidgetState();
}

class _InventoryOperationTransfareToSectionBaseInfoWidgetState
    extends State<InventoryOperationTransfareToSectionBaseInfoWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _animationController.forward();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WarehouseController>(context, listen: false)
          .getSectionsFormServer();
      Provider.of<InventoryOperationController>(context, listen: false)
          .getSectionsRequests();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var provider = Provider.of<InventoryOperationTransfareToSectionController>(
        context,
        listen: false);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        padding: const EdgeInsets.all(10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section with Operation Info
            _buildHeaderSection(context, provider),
            const SizedBox(height: 20),

            // Warehouse Incoming Section
            _buildWarehouseIncomingSection(context, provider),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context,
      InventoryOperationTransfareToSectionController provider) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withOpacity(0.1),
            Theme.of(context).primaryColor.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).primaryColor.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.inventory_2_outlined,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                T('Operation Details'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoCard(
                  context,
                  icon: Icons.tag,
                  label: T('Operation Number'),
                  value: provider.inventorySectionTransfare.aPPReferanceCode ??
                      T('Not assigned'),
                  color: Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildInfoCard(
                  context,
                  icon: Icons.calendar_today,
                  label: T('Date'),
                  value: DateFormat('yyyy-MM-dd').format(DateTime.now()),
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 16,
                color: color,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseIncomingSection(BuildContext context,
      InventoryOperationTransfareToSectionController provider) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.input,
                  color: Colors.green,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                T('Warehouse'),
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          // Destination Warehouse Selector
          _buildWarehouseSelector(
            context,
            provider,
          ),
        ],
      ),
    );
  }

  Widget _buildWarehouseSelector(
    BuildContext context,
    InventoryOperationTransfareToSectionController provider,
  ) {
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: Colors.grey.withOpacity(0.3),
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.grey.withOpacity(0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.inbox,
                  size: 16,
                  color: Colors.green,
                ),
                const SizedBox(width: 6),
                Text(
                  T('Destination Warehouse'),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: context.width / 2 - 50,
                child: MyComboBox(
                  width: double.infinity,
                  selectedValue:
                      provider.inventorySectionTransfare.sourceReference1,
                  caption:
                      provider.inventorySectionTransfare.sourceReference1Name ??
                          T("Select Request"),
                  height: 48,
                  onSelect: (int id, String name) {
                    provider.inventorySectionTransfare.sourceReference1 = id;
                    provider.inventorySectionTransfare.sourceReference1Name =
                        name;
                    widget.onselecteRequest(id.toString(), name);
                    // setState(() {});
                    // warehouseController.getWarehousesBySectionIdFormServer(id);
                  },
                  fontSize: 14,
                  modalTitle: T("Request"),
                  data: Provider.of<InventoryOperationController>(context,
                          listen: false)
                      .sectionsRequests,
                  isShowLabel: false,
                  labelText: "",
                ),
              ),
              SizedBox(
                width: context.width / 2 - 50,
                child: MyComboBox(
                  width: double.infinity,
                  selectedValue: provider.inventorySectionTransfare.fromStoreID,
                  caption: provider.inventorySectionTransfare.storeName ??
                      T("From Warehouse"),
                  height: 48,
                  onSelect: (int id, String name) {
                    provider.inventorySectionTransfare.fromStoreID = id;
                    provider.inventorySectionTransfare.storeName = name;
                    widget.onSelectWarehouse();
                    // setState(() {});
                    // warehouseController.getWarehousesBySectionIdFormServer(id);
                  },
                  fontSize: 14,
                  modalTitle: T("From Warehouse"),
                  data: Provider.of<WarehouseController>(context, listen: false)
                      .warehouses,
                  isShowLabel: false,
                  labelText: "",
                ),
              ),
            ],
          ),
          const SizedBox(height: 10),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: context.width / 2 - 50,
                child: MyComboBox(
                  width: double.infinity,
                  selectedValue: provider.inventorySectionTransfare.secationId,
                  caption: provider.inventorySectionTransfare.secationName ??
                      T("Select Section"),
                  height: 48,
                  onSelect: (int id, String name) {
                    provider.inventorySectionTransfare.secationId = id;
                    provider.inventorySectionTransfare.secationName = name;
                    provider.inventorySectionTransfare.toStoreID = null;
                    provider.inventorySectionTransfare.toStoreName = null;
                    setState(() {});
                    widget.onSelectWarehouse();
                    warehouseController.getWarehousesBySectionIdFormServer(id);
                  },
                  fontSize: 14,
                  modalTitle: T("Section"),
                  data: warehouseController.sections,
                  isShowLabel: false,
                  labelText: "",
                ),
              ),
              // const SizedBox(width: 10),
              SizedBox(
                width: context.width / 2 - 50,
                child: MyComboBox(
                  width: double.infinity,
                  selectedValue: provider.inventorySectionTransfare.toStoreID,
                  caption: provider.inventorySectionTransfare.toStoreName ??
                      T("Select warehouse"),
                  height: 48,
                  onSelect: (int id, String name) {
                    provider.inventorySectionTransfare.toStoreID = id;
                    provider.inventorySectionTransfare.toStoreName = name;
                    setState(() {});
                    widget.onSelectWarehouse();
                  },
                  fontSize: 14,
                  modalTitle: T("Destination Warehouse"),
                  data: warehouseController.warehousesBySection,
                  isShowLabel: false,
                  labelText: "",
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
