import 'package:inventory_application/models/model/product_model.dart';

class ProductDTO {
  int? id;
  String? title;
  String? barcode;
  String? mainImageUrl;
  String? barcodeName;
  String? code;
  String? description;
  double? price;
  double? total;
  double? discountValue;
  double? stock;
  int? uniteId;
  String? uniteName;
  int? categoryId;
  String? category;
  double? quantity = 1;
  int? warehouseId;
  String? warehouseName;
  String? thumbnail;
  List<ProductWarehousesDTO>? warehouse;
  List<ItemPriceDTO>? units;
  List<Barcodes>? barcodes;
  List<ItemAttribute>? itemAttributes;
  List<ItemAttribute>? attribute;
  List<int>? selectedOptionIds;
  bool hasSelectedAttributes =
      false; // Flag to indicate if attributes are pre-selected
  String? virtualProductId; // Virtual ID for product + attributes combination

  // Purchase-specific fields
  double? vatPercent; // VAT_Percent
  double? unitCost; // Unit_Price (purchase price)
  double? unitCostWithExpenses; // Unit_Cost_With_Expences_Calculated_LC
  String? batchNumber; // Batch_Number
  String? serialNumber; // Serial_Number
  DateTime? expirationDate; // ExpirationDate

  ProductDTO({
    this.id,
    this.title,
    this.barcode,
    this.mainImageUrl,
    this.barcodeName,
    this.code,
    this.description,
    this.price,
    this.quantity = 1, // Default quantity set to 1
    this.discountValue,
    this.stock,
    this.uniteId,
    this.uniteName,
    this.units,
    this.categoryId,
    this.category,
    this.thumbnail,
    this.total,
    this.barcodes,
    this.warehouse,
    this.warehouseId,
    this.warehouseName,
    this.itemAttributes,
    this.attribute,
    this.hasSelectedAttributes = false,
    this.virtualProductId,
    this.selectedOptionIds,
    this.vatPercent,
    this.unitCost,
    this.unitCostWithExpenses,
    this.batchNumber,
    this.serialNumber,
    this.expirationDate,
  }) {
    // Calculate total as quantity * price
    total = total ?? (price ?? 0) * (quantity ?? 1);
    warehouseId = warehouseId ?? warehouse?.first.id;
    warehouseName = warehouseName ?? warehouse?.first.name;
  }
  factory ProductDTO.from(ProductDTO other) {
    return ProductDTO(
      id: other.id,
      title: other.title,
      barcode: other.barcode,
      mainImageUrl: other.mainImageUrl,
      barcodeName: other.barcodeName,
      code: other.code,
      description: other.description,
      price: other.price,
      quantity: other.quantity,
      discountValue: other.discountValue,
      stock: other.stock,
      uniteId: other.uniteId,
      uniteName: other.uniteName,
      units: other.units != null ? List.from(other.units!) : null,
      categoryId: other.categoryId,
      category: other.category,
      thumbnail: other.thumbnail,
      total: other.total,
      barcodes: other.barcodes != null ? List.from(other.barcodes!) : null,
      warehouse: other.warehouse != null ? List.from(other.warehouse!) : null,
      warehouseId: other.warehouseId,
      warehouseName: other.warehouseName,
      itemAttributes: other.itemAttributes != null
          ? List.from(other.itemAttributes!)
          : null,
      attribute: other.attribute != null ? List.from(other.attribute!) : null,
      selectedOptionIds: other.selectedOptionIds != null
          ? List.from(other.selectedOptionIds!)
          : null,
      hasSelectedAttributes: other.hasSelectedAttributes,
      virtualProductId: other.virtualProductId,
      vatPercent: other.vatPercent,
      unitCost: other.unitCost,
      unitCostWithExpenses: other.unitCostWithExpenses,
      batchNumber: other.batchNumber,
      serialNumber: other.serialNumber,
      expirationDate: other.expirationDate,
    );
  }
  // Method to convert from JSON to Dart object (fromJson)
  factory ProductDTO.fromJson(Map<String, dynamic> json) {
    return ProductDTO(
      id: json['id'],
      title: json['title'],
      mainImageUrl: json['mainImageUrl'],
      description: json['description'],
      price: json['price'].toDouble(),
      discountValue: json['discountValue'],
      stock: json['stock'],
      category: json['category'],
      barcode: json['barcode'],
      quantity: json['quantity']?.toDouble(),
      categoryId: json['categoryId'],
      thumbnail: json['thumbnail'],
      attribute: json['attribute'] != null
          ? (json['attribute'] as List<dynamic>)
              .map((item) => ItemAttribute.fromJson(item))
              .toList()
          : null,
      itemAttributes: json['itemAttributes'] != null
          ? (json['itemAttributes'] as List<dynamic>)
              .map((item) => ItemAttribute.fromJson(item))
              .toList()
          : null,
      selectedOptionIds: json['selectedOptionIds'] != null
          ? List<int>.from(json['selectedOptionIds'])
          : null,
      virtualProductId: json['virtualProductId'],
      vatPercent: json['vatPercent'] != null
          ? double.tryParse(json['vatPercent'].toString())
          : null,
      unitCost: json['unitCost'] != null
          ? double.tryParse(json['unitCost'].toString())
          : null,
      unitCostWithExpenses: json['unitCostWithExpenses'] != null
          ? double.tryParse(json['unitCostWithExpenses'].toString())
          : null,
      batchNumber: json['batchNumber'],
      serialNumber: json['serialNumber'],
      expirationDate: json['expirationDate'] != null
          ? DateTime.tryParse(json['expirationDate'])
          : null,
    );
  }

  // Method to convert from Dart object to JSON (toJson)
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'mainImageUrl': mainImageUrl,
      'description': description,
      'price': price,
      'discountValue': discountValue,
      'stock': stock,
      'category': category,
      'thumbnail': thumbnail,
      "barcode": barcode,
      "quantity": quantity,
      'selectedOptionIds': selectedOptionIds,
      'itemAttributes': itemAttributes?.map((e) => e.toJson()).toList(),
      'attribute': attribute?.map((e) => e.toJson()).toList(),
      'virtualProductId': virtualProductId,
      // Purchase extras
      'vatPercent': vatPercent,
      'unitCost': unitCost,
      'unitCostWithExpenses': unitCostWithExpenses,
      'batchNumber': batchNumber,
      'serialNumber': serialNumber,
      'expirationDate': expirationDate?.toIso8601String(),
    };
  }
}

//----------------------------------------------------------------------------------------
class ProductWarehousesDTO {
  int? id;
  String? name;
  double? quantity;

  ProductWarehousesDTO({
    this.id,
    this.name,
    this.quantity = 1,
  });
}

//---------------------------------------------------------`-------------------------------
class ItemPriceDTO {
  int? iD;
  int? itemID;
  int? unitID;
  String? unitName;
  bool? isDefult;
  // int? purchasePrice;
  double? salesPrice;

  ItemPriceDTO(
      {this.iD,
      this.itemID,
      this.unitID,
      this.unitName,
      this.isDefult,
      // this.purchasePrice,
      this.salesPrice});

  ItemPriceDTO.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    itemID = json['Item_ID'];
    unitID = json['Unit_ID'];
    unitName = json['Unit_Name'];
    // isDefult = json['Is_Defult'];
    // purchasePrice = json['Purchase_Price'];
    salesPrice = json['Sales_Price'] != null
        ? double.parse(json['Sales_Price'].toString())
        : 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ID'] = iD;
    data['Item_ID'] = itemID;
    data['Unit_ID'] = unitID;
    data['Unit_Name'] = unitName;
    data['Is_Defult'] = isDefult;
    // data['Purchase_Price'] = this.purchasePrice;
    data['Sales_Price'] = salesPrice;
    return data;
  }
}
//---------------------------------------------------------`-------------------------------