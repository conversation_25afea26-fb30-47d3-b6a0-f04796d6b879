import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';

// ignore: must_be_immutable
class ProductListItemsWidget extends StatefulWidget {
  ProductDTO data;
  ProductListItemsWidget({super.key, required this.data});

  @override
  State<ProductListItemsWidget> createState() => _ProductListItemsWidget();
}

class _ProductListItemsWidget extends State<ProductListItemsWidget> {
  @override
  Widget build(BuildContext context) {
    // Extraer y proporcionar valores predeterminados seguros para propiedades potencialmente nulas
    final String title = widget.data.title ?? T("No Title");
    final String category = widget.data.category ?? T("No Category");
    final String price = widget.data.price?.toString() ?? "0";
    final String stock = widget.data.stock?.toString() ?? "0";

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header with product name and category
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: context.newBackgroundColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Product image
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: context.newPrimaryColor.withOpacity(0.3),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 3,
                      ),
                    ],
                    image: DecorationImage(
                      image: NetworkImage(defulteImage()),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: 10),

                // Product info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                          color: context.newSecondaryColor,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: context.newPrimaryColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          category,
                          style: TextStyle(
                            fontSize: 11,
                            color: context.newPrimaryColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 8),

                // Price badge
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: context.newPrimaryColor,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        T("Price"),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 11,
                        ),
                      ),
                      Text(
                        price,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // Product details
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildInfoRow(
                  context,
                  T("Warehouses"),
                  _formatWarehousesText(widget.data.warehouse),
                ),
                const SizedBox(height: 6),
                _buildInfoRow(
                  context,
                  T("Stock"),
                  stock,
                  isHighlighted: true,
                ),
              ],
            ),
          ),

          // Footer with action
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: context.newBackgroundColor.withOpacity(0.3),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(12),
                bottomRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                OutlinedButton.icon(
                  onPressed: () {
                    // Inventory movement action
                  },
                  icon: const Icon(Icons.bar_chart, size: 16),
                  label: Text(T("Inventory Movements"),
                      style: const TextStyle(fontSize: 12)),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: context.newPrimaryColor,
                    side: BorderSide(color: context.newPrimaryColor),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    minimumSize: const Size(0, 30),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: context.newTextColor.withOpacity(0.6),
                  size: 14,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value,
      {bool isHighlighted = false}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: context.newTextColor.withOpacity(0.7),
            fontSize: 13,
          ),
        ),
        Container(
          constraints:
              BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.5),
          child: Text(
            value,
            style: TextStyle(
              color: isHighlighted
                  ? context.newSecondaryColor
                  : context.newTextColor,
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.w500,
              fontSize: isHighlighted ? 14 : 13,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  String _formatWarehousesText(List<dynamic>? warehouses) {
    if (warehouses == null || warehouses.isEmpty) {
      return T("None");
    }

    try {
      // Filtrar elementos nulos o con nombre nulo
      final validWarehouses =
          warehouses.where((w) => w != null && w.name != null).toList();

      if (validWarehouses.isEmpty) {
        return T("None");
      }

      // Si hay más de 2 almacenes, mostrar los 2 primeros y el contador
      if (validWarehouses.length > 2) {
        final firstTwo =
            validWarehouses.take(2).map((w) => w.name ?? "").join(", ");
        return "$firstTwo +${validWarehouses.length - 2} ${T("more")}";
      }

      // Si hay 1 o 2 almacenes, mostrarlos directamente
      return validWarehouses.map((w) => w.name ?? "").join(", ");
    } catch (e) {
      // En caso de cualquier error, devolver un valor predeterminado seguro
      return T("None");
    }
  }
}
