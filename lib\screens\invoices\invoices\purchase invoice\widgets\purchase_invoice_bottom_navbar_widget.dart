import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/double_extension.dart';
import 'package:inventory_application/controllers/purchase_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_calculator_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/purchase%20invoice/widgets/create_purchase_invoice_dialog_widget.dart';
import 'package:provider/provider.dart';

class PurchaseInvoiceBottomNavbarWidget extends StatefulWidget {
  const PurchaseInvoiceBottomNavbarWidget({super.key});

  @override
  State<PurchaseInvoiceBottomNavbarWidget> createState() =>
      _PurchaseInvoiceBottomNavbarWidgetState();
}

class _PurchaseInvoiceBottomNavbarWidgetState
    extends State<PurchaseInvoiceBottomNavbarWidget> {
  // New color palette to match home screen
  final Color primaryColor = const Color(0xFF667EEA);
  final Color secondaryColor = const Color(0xFF764BA2);
  final Color backgroundColor = const Color(0xFFF8F9FD);
  final Color accentColor = const Color(0xFFFF8C42);
  final Color textColor = const Color(0xFF2C3E50);

  @override
  Widget build(BuildContext context) {
    var model = Provider.of<PurchaseInvoiceController>(context).invoice;
    return Container(
      width: context.width,
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -3),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Total amount bar with gradient background
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [primaryColor, secondaryColor],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withOpacity(0.3),
                  blurRadius: 6,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  T("Total"),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  0.0.covertDoubleToMoneyReturnString(model.total ?? 0),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 8),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  label: T("Edit Information"),
                  icon: Icons.edit_outlined,
                  onPressed: () {
                    Navigator.of(context).push(DialogRoute(
                      context: context,
                      barrierDismissible: false,
                      builder: (context) => CreatePurchaseInvoiceDialogWidget(),
                    ));
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  label: T("Calculator"),
                  icon: Icons.calculate_outlined,
                  isOutlined: true,
                  onPressed: () {
                    showBottomSheetWithCalculator(context);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String label,
    required String value,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 6),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 14, color: color),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 3),
            Text(
              value,
              style: TextStyle(
                color: textColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    bool isOutlined = false,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isOutlined ? Colors.white : primaryColor,
        foregroundColor: isOutlined ? primaryColor : Colors.white,
        padding: const EdgeInsets.symmetric(vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: primaryColor,
            width: isOutlined ? 1 : 0,
          ),
        ),
        elevation: isOutlined ? 0 : 1,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 14),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
