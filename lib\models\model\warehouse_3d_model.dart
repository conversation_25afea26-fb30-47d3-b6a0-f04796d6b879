class Warehouse3D {
  int? id;
  String? name;
  String? description;
  double? length;
  double? width;
  double? height;
  int? branchId;
  List<Cabinet3D>? cabinets;
  // إحداثيات المستودع في الخريطة العامة
  double? xPosition;
  double? yPosition;
  double? zPosition;

  Warehouse3D({
    this.id,
    this.name,
    this.description,
    this.length,
    this.width,
    this.height,
    this.branchId,
    this.cabinets,
    this.xPosition,
    this.yPosition,
    this.zPosition,
  });

  Warehouse3D.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    description = json['description'];
    length = json['length']?.toDouble();
    width = json['width']?.toDouble();
    height = json['height']?.toDouble();
    branchId = json['branchId'];
    xPosition = json['xPosition']?.toDouble();
    yPosition = json['yPosition']?.toDouble();
    zPosition = json['zPosition']?.toDouble();

    if (json['cabinets'] != null) {
      cabinets = <Cabinet3D>[];
      json['cabinets'].forEach((v) {
        cabinets!.add(Cabinet3D.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['description'] = description;
    data['length'] = length;
    data['width'] = width;
    data['height'] = height;
    data['branchId'] = branchId;
    data['xPosition'] = xPosition;
    data['yPosition'] = yPosition;
    data['zPosition'] = zPosition;

    if (cabinets != null) {
      data['cabinets'] = cabinets!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Cabinet3D {
  int? id;
  String? name;
  String? code;
  int? warehouseId;
  double? length;
  double? width;
  double? height;
  // إحداثيات الخزانة داخل المستودع
  double? xPosition;
  double? yPosition;
  double? zPosition;
  // لون الخزانة للتمييز البصري
  String? color;
  List<Shelf3D>? shelves;

  Cabinet3D({
    this.id,
    this.name,
    this.code,
    this.warehouseId,
    this.length,
    this.width,
    this.height,
    this.xPosition,
    this.yPosition,
    this.zPosition,
    this.color,
    this.shelves,
  });

  Cabinet3D.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    code = json['code'];
    warehouseId = json['warehouseId'];
    length = json['length']?.toDouble();
    width = json['width']?.toDouble();
    height = json['height']?.toDouble();
    xPosition = json['xPosition']?.toDouble();
    yPosition = json['yPosition']?.toDouble();
    zPosition = json['zPosition']?.toDouble();
    color = json['color'];

    if (json['shelves'] != null) {
      shelves = <Shelf3D>[];
      json['shelves'].forEach((v) {
        shelves!.add(Shelf3D.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['code'] = code;
    data['warehouseId'] = warehouseId;
    data['length'] = length;
    data['width'] = width;
    data['height'] = height;
    data['xPosition'] = xPosition;
    data['yPosition'] = yPosition;
    data['zPosition'] = zPosition;
    data['color'] = color;

    if (shelves != null) {
      data['shelves'] = shelves!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Shelf3D {
  int? id;
  String? name;
  String? code;
  int? cabinetId;
  double? length;
  double? width;
  double? height;
  // إحداثيات الرف داخل الخزانة
  double? xPosition;
  double? yPosition;
  double? zPosition;
  // حالة الرف - ممتلئ، فارغ، جزئي
  ShelfStatus? status;
  List<ProductLocation3D>? products;
  // سعة الرف القصوى
  int? maxCapacity;
  int? currentOccupancy;

  Shelf3D({
    this.id,
    this.name,
    this.code,
    this.cabinetId,
    this.length,
    this.width,
    this.height,
    this.xPosition,
    this.yPosition,
    this.zPosition,
    this.status,
    this.products,
    this.maxCapacity,
    this.currentOccupancy,
  });

  Shelf3D.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    code = json['code'];
    cabinetId = json['cabinetId'];
    length = json['length']?.toDouble();
    width = json['width']?.toDouble();
    height = json['height']?.toDouble();
    xPosition = json['xPosition']?.toDouble();
    yPosition = json['yPosition']?.toDouble();
    zPosition = json['zPosition']?.toDouble();
    maxCapacity = json['maxCapacity'];
    currentOccupancy = json['currentOccupancy'];

    if (json['status'] != null) {
      status = ShelfStatus.values.firstWhere(
        (e) => e.toString() == 'ShelfStatus.${json['status']}',
        orElse: () => ShelfStatus.empty,
      );
    }

    if (json['products'] != null) {
      products = <ProductLocation3D>[];
      json['products'].forEach((v) {
        products!.add(ProductLocation3D.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['name'] = name;
    data['code'] = code;
    data['cabinetId'] = cabinetId;
    data['length'] = length;
    data['width'] = width;
    data['height'] = height;
    data['xPosition'] = xPosition;
    data['yPosition'] = yPosition;
    data['zPosition'] = zPosition;
    data['maxCapacity'] = maxCapacity;
    data['currentOccupancy'] = currentOccupancy;
    data['status'] = status?.toString().split('.').last;

    if (products != null) {
      data['products'] = products!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ProductLocation3D {
  int? id;
  int? productId;
  String? productName;
  String? productCode;
  int? shelfId;
  int? cabinetId;
  int? warehouseId;
  double? quantity;
  // إحداثيات دقيقة للمنتج داخل الرف
  double? xPosition;
  double? yPosition;
  double? zPosition;
  DateTime? lastUpdated;
  String? batchNumber;
  DateTime? expiryDate;

  ProductLocation3D({
    this.id,
    this.productId,
    this.productName,
    this.productCode,
    this.shelfId,
    this.cabinetId,
    this.warehouseId,
    this.quantity,
    this.xPosition,
    this.yPosition,
    this.zPosition,
    this.lastUpdated,
    this.batchNumber,
    this.expiryDate,
  });

  ProductLocation3D.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    productId = json['productId'];
    productName = json['productName'];
    productCode = json['productCode'];
    shelfId = json['shelfId'];
    cabinetId = json['cabinetId'];
    warehouseId = json['warehouseId'];
    quantity = json['quantity']?.toDouble();
    xPosition = json['xPosition']?.toDouble();
    yPosition = json['yPosition']?.toDouble();
    zPosition = json['zPosition']?.toDouble();
    batchNumber = json['batchNumber'];

    if (json['lastUpdated'] != null) {
      lastUpdated = DateTime.parse(json['lastUpdated']);
    }
    if (json['expiryDate'] != null) {
      expiryDate = DateTime.parse(json['expiryDate']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['productId'] = productId;
    data['productName'] = productName;
    data['productCode'] = productCode;
    data['shelfId'] = shelfId;
    data['cabinetId'] = cabinetId;
    data['warehouseId'] = warehouseId;
    data['quantity'] = quantity;
    data['xPosition'] = xPosition;
    data['yPosition'] = yPosition;
    data['zPosition'] = zPosition;
    data['batchNumber'] = batchNumber;
    data['lastUpdated'] = lastUpdated?.toIso8601String();
    data['expiryDate'] = expiryDate?.toIso8601String();
    return data;
  }
}

enum ShelfStatus {
  empty,
  partial,
  full,
  overloaded,
}

// نموذج لحفظ تخطيط المستودع المخصص
class WarehouseLayout {
  int? warehouseId;
  String? layoutName;
  String? description;
  DateTime? createdDate;
  DateTime? lastModified;
  String? createdBy;
  Map<String, dynamic>? layoutData; // بيانات التخطيط ثلاثي الأبعاد

  WarehouseLayout({
    this.warehouseId,
    this.layoutName,
    this.description,
    this.createdDate,
    this.lastModified,
    this.createdBy,
    this.layoutData,
  });

  WarehouseLayout.fromJson(Map<String, dynamic> json) {
    warehouseId = json['warehouseId'];
    layoutName = json['layoutName'];
    description = json['description'];
    createdBy = json['createdBy'];
    layoutData = json['layoutData'];

    if (json['createdDate'] != null) {
      createdDate = DateTime.parse(json['createdDate']);
    }
    if (json['lastModified'] != null) {
      lastModified = DateTime.parse(json['lastModified']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['warehouseId'] = warehouseId;
    data['layoutName'] = layoutName;
    data['description'] = description;
    data['createdBy'] = createdBy;
    data['layoutData'] = layoutData;
    data['createdDate'] = createdDate?.toIso8601String();
    data['lastModified'] = lastModified?.toIso8601String();
    return data;
  }
}
