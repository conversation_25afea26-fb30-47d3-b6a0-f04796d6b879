import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';

import 'package:inventory_application/controllers/customer_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/server/customer_sales_server_dto.dart';
import 'package:inventory_application/models/dto/reports/server/customer_sales_report_dto.dart';
import 'package:inventory_application/services/server_reports_service.dart';

// Define DateRangeType enum locally
enum DateRangeType {
  daily,
  monthly,
  yearly,
  custom,
}

class CustomerSalesReportScreen extends StatefulWidget {
  const CustomerSalesReportScreen({
    Key? key,
  }) : super(key: key);

  @override
  State<CustomerSalesReportScreen> createState() =>
      _CustomerSalesReportScreenState();
}

class _CustomerSalesReportScreenState extends State<CustomerSalesReportScreen>
    with TickerProviderStateMixin {
  CustomerSalesServerReportDTO? _reportData;
  bool _isLoading = false;
  DateTime? _fromDate;
  DateTime? _toDate;
  DateRangeType? _selectedDateRangeType;

  // Filter values
  int? _selectedCustomerId;

  // Controllers
  late AnimationController _animationController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Services
  final ServerReportsService _reportsService = ServerReportsService();

  // Date range types with Arabic names
  final Map<DateRangeType, String> _dateRangeOptions = {
    DateRangeType.daily: 'يومي',
    DateRangeType.monthly: 'شهري',
    DateRangeType.yearly: 'سنوي',
    DateRangeType.custom: 'تخصيص التاريخ',
  };

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadCustomers();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _slideController.forward();
  }

  void _loadCustomers() async {
    final customerController =
        Provider.of<CustomerController>(context, listen: false);
    await customerController.getCustomers();
  }

  Future<void> _loadReport() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      print('Loading customer sales report with filters:');
      print('Customer ID: $_selectedCustomerId');
      print('From Date: $_fromDate');
      print('To Date: $_toDate');

      final data = await _reportsService.getCustomerSalesReport(
        fromDate: _fromDate,
        toDate: _toDate,
        customerId: _selectedCustomerId,
      );

      if (data != null) {
        setState(() {
          _reportData = CustomerSalesServerReportDTO.fromCustomersList(
            data,
            fromDate: _fromDate,
            toDate: _toDate,
          );
        });
        _animationController.forward();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(T('Failed to load report data')),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      print('Error loading report: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(T('Error loading report: ') + e.toString()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _applyDateRangeType(DateRangeType type) {
    final now = DateTime.now();

    switch (type) {
      case DateRangeType.daily:
        _fromDate = DateTime(now.year, now.month, now.day);
        _toDate = DateTime(now.year, now.month, now.day, 23, 59, 59);
        break;
      case DateRangeType.monthly:
        _fromDate = DateTime(now.year, now.month, 1);
        _toDate = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
        break;
      case DateRangeType.yearly:
        _fromDate = DateTime(now.year, 1, 1);
        _toDate = DateTime(now.year, 12, 31, 23, 59, 59);
        break;
      case DateRangeType.custom:
        // Don't automatically set dates for custom
        break;
    }

    setState(() {
      _selectedDateRangeType = type;
    });
  }

  String _getDateRangeTypeDisplayName(DateRangeType type) {
    return _dateRangeOptions[type] ?? type.toString();
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: (_fromDate != null && _toDate != null)
          ? DateTimeRange(start: _fromDate!, end: _toDate!)
          : null,
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
        _selectedDateRangeType = DateRangeType.custom;
      });
    }
  }

  double get _totalQuantity {
    return _reportData?.totalQuantity ?? 0.0;
  }

  double get _totalSales {
    return _reportData?.totalSales ?? 0.0;
  }

  @override
  void dispose() {
    _animationController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: Text(T('Customer Sales Report')),
        backgroundColor: const Color(0xFF6366F1),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SlideTransition(
        position: _slideAnimation,
        child: Column(
          children: [
            // Filters section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 0,
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    T('Filters'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF374151),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildFiltersRow(),
                  const SizedBox(height: 16),
                  _buildActionButtons(),
                ],
              ),
            ),

            // Summary cards (if data is loaded)
            if (_reportData != null) _buildSummaryCards(),

            // Content area
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 0,
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: _isLoading
                    ? const Center(
                        child: CircularProgressIndicator(
                          color: Color(0xFF6366F1),
                        ),
                      )
                    : (_reportData?.customers.isEmpty ?? true)
                        ? _buildEmptyState()
                        : _buildCustomersList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFiltersRow() {
    return Consumer<CustomerController>(
      builder: (context, customerController, child) {
        // Get customers list
        final customers = customerController.customers
            .where((customer) => customer.iD != null && customer.iD! > 0)
            .toList();

        return Column(
          children: [
            // First row: Customer and Date Range Type
            Row(
              children: [
                // Customer filter
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T('Customer'),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<int>(
                            value: _selectedCustomerId,
                            isExpanded: true,
                            hint: Text(T('Select Customer')),
                            onChanged: (value) {
                              setState(() {
                                _selectedCustomerId = value;
                              });
                            },
                            items: [
                              DropdownMenuItem<int>(
                                value: null,
                                child: Text(T('All Customers')),
                              ),
                              ...customers
                                  .map((customer) => DropdownMenuItem<int>(
                                        value: customer.iD!,
                                        child:
                                            Text(customer.name ?? T('Unknown')),
                                      )),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),

                // Date Range Type
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        T('التاريخ'),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<DateRangeType>(
                            value: _selectedDateRangeType,
                            isExpanded: true,
                            hint: Text(T('اختار تاريخ')),
                            onChanged: (DateRangeType? value) {
                              if (value != null) {
                                _applyDateRangeType(value);
                              }
                            },
                            items: _dateRangeOptions.entries
                                .map((entry) => DropdownMenuItem<DateRangeType>(
                                      value: entry.key,
                                      child: Text(entry.value),
                                    ))
                                .toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Second row: Custom date selection (if custom is selected)
            if (_selectedDateRangeType == DateRangeType.custom) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildDateDisplayCard(
                      label: T('From Date'),
                      date: _fromDate,
                      onTap: _selectDateRange,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildDateDisplayCard(
                      label: T('To Date'),
                      date: _toDate,
                      onTap: _selectDateRange,
                    ),
                  ),
                ],
              ),
            ],

            // Display selected date range info
            if (_fromDate != null && _toDate != null) ...[
              const SizedBox(height: 12),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: const Color(0xFF6366F1).withOpacity(0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.date_range,
                      size: 16,
                      color: Color(0xFF6366F1),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '${DateFormat('yyyy-MM-dd').format(_fromDate!)} ${T('to')} ${DateFormat('yyyy-MM-dd').format(_toDate!)}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF6366F1),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (_selectedDateRangeType != null) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: const Color(0xFF6366F1),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _getDateRangeTypeDisplayName(_selectedDateRangeType!),
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ],
        );
      },
    );
  }

  Widget _buildDateDisplayCard({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF6B7280),
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: 16,
                  color: Colors.grey.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  date != null
                      ? DateFormat('yyyy-MM-dd').format(date)
                      : T('Select Date'),
                  style: TextStyle(
                    fontSize: 14,
                    color: date != null ? Colors.black87 : Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _loadReport,
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6366F1),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: _isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : Text(T('انشاء التقرير')),
          ),
        ),
        const SizedBox(width: 12),
        ElevatedButton(
          onPressed: () {
            setState(() {
              _selectedCustomerId = null;
              _selectedDateRangeType = null;
              _fromDate = null;
              _toDate = null;
              _reportData = null;
            });
            _animationController.reset();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.grey.shade200,
            foregroundColor: Colors.grey.shade700,
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: Text(T('Clear')),
        ),
      ],
    );
  }

  Widget _buildSummaryCards() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: _buildSummaryCard(
              title: T('Total Sales'),
              value: NumberFormat('#,##0.00').format(_totalSales),
              icon: Icons.attach_money,
              color: Colors.green,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryCard(
              title: T('Total Quantity'),
              value: NumberFormat('#,##0').format(_totalQuantity),
              icon: Icons.shopping_cart,
              color: Colors.blue,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildSummaryCard(
              title: T('Customers'),
              value: (_reportData?.totalCustomers ?? 0).toString(),
              icon: Icons.people,
              color: Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 0,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF6B7280),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            T('No customer data found'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T('Try adjusting your filters and generate the report again'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomersList() {
    final customers = _reportData?.customers ?? [];

    return FadeTransition(
      opacity: _fadeAnimation,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: customers.length,
        itemBuilder: (context, index) {
          final customer = customers[index];
          return _buildCustomerCard(customer, index);
        },
      ),
    );
  }

  Widget _buildCustomerCard(CustomerSalesServerDTO customer, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey.shade200,
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.05),
            spreadRadius: 0,
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Customer info
            Expanded(
              flex: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    customer.customerName ?? T('Unknown Customer'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF374151),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${T('Customer ID')}: ${customer.customerId ?? T('N/A')}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),

            // Quantity
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    T('Quantity'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    NumberFormat('#,##0').format(customer.totalQuantity ?? 0),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),

            // Sales
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    T('مبيعات'),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    NumberFormat('#,##0.00').format(customer.totalSales ?? 0),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class DateRangeSelector extends StatelessWidget {
  final DateRangeType? selectedType;
  final Function(DateRangeType) onTypeChanged;
  final DateTime? fromDate;
  final DateTime? toDate;
  final VoidCallback onCustomDateTap;

  const DateRangeSelector({
    Key? key,
    required this.selectedType,
    required this.onTypeChanged,
    required this.fromDate,
    required this.toDate,
    required this.onCustomDateTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          T('Date Range'),
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF6B7280),
          ),
        ),
        const SizedBox(height: 8),

        // Date range type buttons
        Wrap(
          spacing: 8,
          children: [
            _buildDateRangeChip(context, DateRangeType.daily, 'يومي'),
            _buildDateRangeChip(context, DateRangeType.monthly, 'شهري'),
            _buildDateRangeChip(context, DateRangeType.yearly, 'سنوي'),
            _buildDateRangeChip(context, DateRangeType.custom, 'مخصص'),
          ],
        ),

        // Custom date range display
        if (selectedType == DateRangeType.custom &&
            fromDate != null &&
            toDate != null) ...[
          const SizedBox(height: 12),
          InkWell(
            onTap: onCustomDateTap,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.date_range, color: Colors.grey.shade600, size: 20),
                  const SizedBox(width: 8),
                  Text(
                    '${DateFormat('yyyy-MM-dd').format(fromDate!)} - ${DateFormat('yyyy-MM-dd').format(toDate!)}',
                    style: const TextStyle(fontSize: 14),
                  ),
                  const Spacer(),
                  Icon(Icons.edit, color: Colors.grey.shade600, size: 16),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildDateRangeChip(
      BuildContext context, DateRangeType type, String label) {
    final isSelected = selectedType == type;

    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          onTypeChanged(type);
        }
      },
      selectedColor: const Color(0xFF6366F1).withOpacity(0.2),
      checkmarkColor: const Color(0xFF6366F1),
      labelStyle: TextStyle(
        color: isSelected ? const Color(0xFF6366F1) : Colors.grey.shade700,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
    );
  }
}
