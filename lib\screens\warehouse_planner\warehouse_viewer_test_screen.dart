import 'package:flutter/material.dart';
import '../../models/warehouse_planner/warehouse_layout.dart';
import 'warehouse_viewer_screen.dart';

/// شاشة تجربة للوصول إلى عارض المستودع
class WarehouseViewerTestScreen extends StatelessWidget {
  const WarehouseViewerTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تجربة عارض المستودع'),
        backgroundColor: Colors.blue.shade700,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.warehouse,
              size: 100,
              color: Colors.blue.shade300,
            ),
            const SizedBox(height: 20),
            const Text(
              'عارض المستودع للعامل',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 40),
              child: Text(
                'هذه الشاشة مخصصة للعامل لعرض مخطط المستودع والبحث عن الأصناف وتحديد مواقعها',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ),
            const SizedBox(height: 40),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const WarehouseViewerScreen(
                      layoutId: 'test-layout',
                      layoutName: 'مستودع تجريبي',
                    ),
                  ),
                );
              },
              icon: const Icon(Icons.visibility),
              label: const Text('تجربة العارض'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade700,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 30,
                  vertical: 15,
                ),
              ),
            ),
            const SizedBox(height: 20),
            TextButton(
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('مميزات العارض'),
                    content: const Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('🔍 البحث عن الأصناف بالاسم أو الباركود'),
                        SizedBox(height: 8),
                        Text('📍 تحديد موقع الصنف على المخطط'),
                        SizedBox(height: 8),
                        Text('🏗️ عرض تفاصيل كل خزانة ومحتوياتها'),
                        SizedBox(height: 8),
                        Text('🎯 تحديد بصري للخزائن والمواقع'),
                        SizedBox(height: 8),
                        Text('🔄 تكبير وتصغير وتنقل في المخطط'),
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('فهمت'),
                      ),
                    ],
                  ),
                );
              },
              child: const Text('المميزات والخصائص'),
            ),
          ],
        ),
      ),
    );
  }
}
