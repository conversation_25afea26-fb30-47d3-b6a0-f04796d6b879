import 'dart:io';
import 'package:flutter/material.dart';
import 'package:inventory_application/models/warehouse_planner/warehouse_layout.dart';
import 'package:provider/provider.dart';
import 'package:vector_math/vector_math.dart' as vector;

import '../../providers/warehouse_planner_provider.dart';
import '../../models/warehouse_planner/editor_state.dart';
import '../../controllers/warehouse_controller.dart';
import 'warehouse_3d_preview_screen.dart';
import 'warehouse_viewer_screen.dart';
import 'widgets/warehouse_editor_canvas.dart';
import 'widgets/medicine_assignment_dialog.dart';
import 'widgets/editor_toolbar.dart';
import 'widgets/shelf_palette.dart';
import 'widgets/property_panel.dart';
import 'widgets/warehouse_stats_panel.dart';

/// الشاشة الرئيسية لمحرر المستودع ثنائي الأبعاد
class WarehouseEditorScreen extends StatefulWidget {
  final String? warehouseId;

  const WarehouseEditorScreen({
    Key? key,
    this.warehouseId,
  }) : super(key: key);

  @override
  State<WarehouseEditorScreen> createState() => _WarehouseEditorScreenState();
}

class _WarehouseEditorScreenState extends State<WarehouseEditorScreen> {
  late WarehousePlannerProvider _provider;
  late WarehouseController _warehouseController;
  bool _showPropertyPanel = true;
  bool _showStatsPanel = false;

  @override
  void initState() {
    super.initState();
    _provider = context.read<WarehousePlannerProvider>();
    _warehouseController = WarehouseController();

    // إنشاء مستودع جديد أو تحميل موجود
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeData();
    });
  }

  void _initializeData() async {
    // تحميل المستودعات من النظام
    await _warehouseController.fetchWarehouses();

    if (widget.warehouseId == null) {
      _showNewWarehouseDialog();
    } else {
      // TODO: تحميل المستودع الموجود
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: Consumer<WarehousePlannerProvider>(
        builder: (context, provider, child) {
          if (!provider.hasLayout) {
            return _buildEmptyState();
          }

          return Row(
            children: [
              // لوحة الأدوات الجانبية
              Container(
                width: 280,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    right: BorderSide(color: Colors.grey.shade300),
                  ),
                ),
                child: Column(
                  children: [
                    // شريط الأدوات
                    EditorToolbar(
                      currentMode: provider.editorState.mode,
                      onModeChanged: (mode) => provider.setEditMode(mode),
                      onUndo: _undo,
                      onRedo: _redo,
                      onZoomFit: _zoomFit,
                      onToggleGrid: _toggleGrid,
                      onToggleMeasurements: _toggleMeasurements,
                    ),

                    const Divider(height: 1),

                    // لوحة الخزائن
                    Expanded(
                      child: ShelfPalette(
                        selectedTemplateId: provider
                            .editorState.shelfPlacing.selectedTemplateId,
                        onTemplateSelected: (templateId) {
                          provider.selectShelfTemplate(templateId);
                          provider.setEditMode(EditMode.placeShelf);
                        },
                      ),
                    ),
                  ],
                ),
              ),

              // منطقة الرسم الرئيسية
              Expanded(
                child: Column(
                  children: [
                    // شريط المعلومات العلوي
                    _buildInfoBar(provider),

                    // منطقة الرسم
                    Expanded(
                      child: Container(
                        margin: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: WarehouseEditorCanvas(
                            layout: provider.currentLayout!,
                            editorState: provider.editorState,
                            settings: provider.settings,
                            onStateUpdate: provider.updateEditorState,
                            onWallDrawStart: provider.startWallDrawing,
                            onWallDrawAdd: provider.addWallPoint,
                            onWallDrawFinish: provider.finishDrawingWall,
                            onWallDelete: provider.deleteWall,
                            onEntranceAdd: provider.addEntrance,
                            onShelfAdd: provider.addShelf,
                            onShelfMove: provider.moveShelf,
                            onShelfRotate: provider.rotateShelf,
                            onObjectSelect: provider.selectObject,
                            onPreviewUpdate: provider.updateShelfPreview,
                            onShelfDoubleClick: _openMedicineAssignment,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // لوحة الخصائص (اختيارية)
              if (_showPropertyPanel)
                Container(
                  width: 300,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      left: BorderSide(color: Colors.grey.shade300),
                    ),
                  ),
                  child: const PropertyPanel(),
                ),
            ],
          );
        },
      ),

      // لوحة إحصائيات عائمة
      floatingActionButton: _buildFloatingActions(),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: const Color(0xFF2C3E50),
      foregroundColor: Colors.white,
      elevation: 0,
      title: Consumer<WarehousePlannerProvider>(
        builder: (context, provider, child) {
          final layout = provider.currentLayout;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                layout?.name ?? 'محرر المستودع',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              if (layout?.linkedWarehouseName != null)
                Text(
                  '🏥 مربوط بـ: ${layout!.linkedWarehouseName}',
                  style: const TextStyle(
                    fontSize: 11,
                    color: Colors.white70,
                    fontWeight: FontWeight.normal,
                  ),
                ),
            ],
          );
        },
      ),
      actions: [
        // أزرار التحكم في اللوحات
        IconButton(
          icon: Icon(
              _showPropertyPanel ? Icons.close_fullscreen : Icons.open_in_full),
          tooltip:
              _showPropertyPanel ? 'إخفاء لوحة الخصائص' : 'إظهار لوحة الخصائص',
          onPressed: () =>
              setState(() => _showPropertyPanel = !_showPropertyPanel),
        ),

        IconButton(
          icon: Icon(
              _showStatsPanel ? Icons.analytics : Icons.analytics_outlined),
          tooltip: _showStatsPanel ? 'إخفاء الإحصائيات' : 'إظهار الإحصائيات',
          onPressed: () => setState(() => _showStatsPanel = !_showStatsPanel),
        ),

        const VerticalDivider(width: 1),

        // زر وضع العرض
        IconButton(
          icon: const Icon(Icons.visibility),
          tooltip: 'وضع العرض للعامل',
          onPressed: _openViewerMode,
        ),

        const VerticalDivider(width: 1),

        // أزرار الحفظ والتصدير
        IconButton(
          icon: const Icon(Icons.save),
          tooltip: 'حفظ',
          onPressed: _saveWarehouse,
        ),

        IconButton(
          icon: const Icon(Icons.file_download),
          tooltip: 'تصدير',
          onPressed: _exportWarehouse,
        ),

        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'new',
              child: ListTile(
                leading: Icon(Icons.add),
                title: Text('مستودع جديد'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'open',
              child: ListTile(
                leading: Icon(Icons.folder_open),
                title: Text('فتح'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'save_as',
              child: ListTile(
                leading: Icon(Icons.save_as),
                title: Text('حفظ باسم'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuDivider(),
            const PopupMenuItem(
              value: '3d_preview',
              child: ListTile(
                leading: Icon(Icons.view_in_ar),
                title: Text('معاينة ثلاثية الأبعاد'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('إعدادات'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoBar(WarehousePlannerProvider provider) {
    final stats = provider.getWarehouseStats();

    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: [
          // معلومات الوضع الحالي
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: _getModeColor(provider.editorState.mode),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getModeIcon(provider.editorState.mode),
                  size: 16,
                  color: Colors.white,
                ),
                const SizedBox(width: 6),
                Text(
                  _getModeText(provider.editorState.mode),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),

          const Spacer(),

          // إحصائيات سريعة
          _buildQuickStat(
              'خزائن', '${stats['totalShelves']}', Icons.inventory_2),
          const SizedBox(width: 16),
          _buildQuickStat('خانات',
              '${stats['occupiedBins']}/${stats['totalBins']}', Icons.grid_on),
          const SizedBox(width: 16),
          _buildQuickStat('منتهية', '${stats['expiredItems']}', Icons.warning,
              stats['expiredItems'] > 0 ? Colors.red : null),
        ],
      ),
    );
  }

  Widget _buildQuickStat(String label, String value, IconData icon,
      [Color? color]) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color ?? Colors.grey.shade600),
        const SizedBox(width: 4),
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: color ?? const Color(0xFF2C3E50),
              ),
            ),
            Text(
              label,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warehouse,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'مرحباً بك في محرر المستودع',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإنشاء مستودع جديد أو فتح مستودع موجود',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 32),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              ElevatedButton.icon(
                onPressed: _showNewWarehouseDialog,
                icon: const Icon(Icons.add),
                label: const Text('مستودع جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF3498DB),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
              const SizedBox(width: 16),
              OutlinedButton.icon(
                onPressed: _openWarehouse,
                icon: const Icon(Icons.folder_open),
                label: const Text('فتح مستودع'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFloatingActions() {
    if (!_showStatsPanel) {
      return FloatingActionButton(
        heroTag: 'stats',
        onPressed: () => setState(() => _showStatsPanel = true),
        child: const Icon(Icons.analytics),
        backgroundColor: const Color(0xFF3498DB),
      );
    }

    return Container(
      width: 280,
      height: 200,
      child: const WarehouseStatsPanel(),
    );
  }

  // === أحداث التفاعل ===

  void _showNewWarehouseDialog() {
    _showWarehouseSelectionDialog();
  }

  void _showWarehouseSelectionDialog() {
    // استخدام المستودعات الحقيقية من النظام
    final systemWarehouses = _warehouseController.warehouses;

    if (systemWarehouses.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content:
              Text('⚠️ لا توجد مستودعات متاحة. يرجى مزامنة البيانات أولاً.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    int? selectedWarehouseId;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: Row(
            children: [
              const Icon(Icons.warehouse, color: Color(0xFF3498DB)),
              const SizedBox(width: 8),
              const Text('اختر المستودع للربط'),
            ],
          ),
          content: SizedBox(
            width: 400,
            height: 350,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'اختر المستودع من النظام لربطه بالمخطط:',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF6C757D),
                  ),
                ),
                const SizedBox(height: 16),
                Expanded(
                  child: ListView.builder(
                    itemCount: systemWarehouses.length,
                    itemBuilder: (context, index) {
                      final warehouse = systemWarehouses[index];
                      final isSelected = selectedWarehouseId == warehouse.id;

                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: isSelected
                                ? const Color(0xFF3498DB)
                                : const Color(0xFFE9ECEF),
                            width: isSelected ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: isSelected
                              ? const Color(0xFF3498DB).withOpacity(0.1)
                              : Colors.white,
                        ),
                        child: ListTile(
                          leading: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? const Color(0xFF3498DB)
                                  : const Color(0xFF6C757D),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Center(
                              child: Text(
                                '${warehouse.id}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          title: Text(
                            warehouse.name,
                            style: TextStyle(
                              fontWeight: isSelected
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: isSelected
                                  ? const Color(0xFF3498DB)
                                  : Colors.black,
                            ),
                          ),
                          subtitle: Text(
                            'المعرف: #${warehouse.id}',
                            style: const TextStyle(fontSize: 12),
                          ),
                          trailing: isSelected
                              ? const Icon(Icons.check_circle,
                                  color: Color(0xFF3498DB))
                              : null,
                          onTap: () {
                            setState(() {
                              selectedWarehouseId = warehouse.id;
                            });
                          },
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: selectedWarehouseId != null
                  ? () {
                      final selectedWarehouse = systemWarehouses.firstWhere(
                        (w) => w.id == selectedWarehouseId,
                      );
                      Navigator.pop(context);
                      _showWarehouseDetailsDialog(selectedWarehouse);
                    }
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF3498DB),
              ),
              child:
                  const Text('التالي', style: TextStyle(color: Colors.white)),
            ),
          ],
        ),
      ),
    );
  }

  void _showWarehouseDetailsDialog(dynamic selectedWarehouse) {
    final nameController = TextEditingController(
      text: 'مخطط ${selectedWarehouse.name}',
    );
    final widthController = TextEditingController(text: '1500');
    final heightController = TextEditingController(text: '1200');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.edit, color: Color(0xFF3498DB)),
            const SizedBox(width: 8),
            const Text('تفاصيل المخطط'),
          ],
        ),
        content: SizedBox(
          width: 450,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عرض المستودع المختار
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF3498DB).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                      color: const Color(0xFF3498DB).withOpacity(0.3)),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.link, color: Color(0xFF3498DB)),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '🔗 مربوط بـ: ${selectedWarehouse.name}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF3498DB),
                              fontSize: 14,
                            ),
                          ),
                          Text(
                            '📍 المعرف: #${selectedWarehouse.id}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF6C757D),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المخطط',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.label),
                ),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: widthController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'العرض (سم)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.straighten),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextField(
                      controller: heightController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'الطول (سم)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.straighten),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              final width = double.tryParse(widthController.text) ?? 1500;
              final height = double.tryParse(heightController.text) ?? 1200;

              if (name.isNotEmpty) {
                _provider.createNewWarehouse(
                  name: name,
                  width: width,
                  height: height,
                  linkedWarehouseId: selectedWarehouse.id,
                  linkedWarehouseName: selectedWarehouse.name,
                );
                Navigator.pop(context);

                // عرض رسالة نجاح مع تفاصيل الربط
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Row(
                      children: [
                        const Icon(Icons.check_circle, color: Colors.white),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                              '✅ تم إنشاء المخطط وربطه بـ ${selectedWarehouse.name} بنجاح!'),
                        ),
                      ],
                    ),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 4),
                    action: SnackBarAction(
                      label: 'ابدأ التصميم',
                      textColor: Colors.white,
                      onPressed: () {
                        // فتح لوحة الخصائص مباشرة
                        setState(() {
                          _showPropertyPanel = true;
                        });
                      },
                    ),
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            ),
            child: const Text('🚀 إنشاء المخطط',
                style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _saveWarehouse() async {
    if (!_provider.hasLayout) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا يوجد مستودع للحفظ')),
      );
      return;
    }

    final success = await _provider.saveWarehouse();
    if (success) {
      final saveDir = await _provider.getSaveDirectory();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('✅ تم حفظ المستودع بنجاح'),
              Text('📁 المجلد: $saveDir', style: const TextStyle(fontSize: 12)),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 4),
          action: SnackBarAction(
            label: 'فتح المجلد',
            textColor: Colors.white,
            onPressed: () => _openSaveDirectory(),
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ خطأ في حفظ المستودع'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _openWarehouse() async {
    final files = await _provider.getSavedWarehouses();

    if (files.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا توجد ملفات محفوظة')),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر مستودع للتحميل'),
        content: SizedBox(
          width: 400,
          height: 300,
          child: ListView.builder(
            itemCount: files.length,
            itemBuilder: (context, index) {
              final file = files[index];
              final fileName =
                  file.path.split('/').last.replaceAll('.json', '');
              final fileStat = file.statSync();

              return ListTile(
                leading: const Icon(Icons.warehouse, color: Color(0xFF3498DB)),
                title: Text(fileName),
                subtitle: Text(
                    'تاريخ التعديل: ${fileStat.modified.toString().substring(0, 16)}'),
                onTap: () async {
                  Navigator.pop(context);
                  final success = await _provider.loadWarehouse(file.path);
                  if (success) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('✅ تم تحميل المستودع بنجاح'),
                        backgroundColor: Colors.green,
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('❌ خطأ في تحميل المستودع'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => _openSaveDirectory(),
            child: const Text('فتح المجلد'),
          ),
        ],
      ),
    );
  }

  void _openSaveDirectory() async {
    final saveDir = await _provider.getSaveDirectory();
    try {
      // محاولة فتح المجلد في Windows
      await Process.run('explorer', [saveDir.replaceAll('/', '\\')]);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('المجلد: $saveDir')),
      );
    }
  }

  void _exportWarehouse() async {
    if (!_provider.hasLayout) return;

    try {
      // تصدير كـ JSON
      final success = await _provider.saveWarehouse();
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ تم تصدير المستودع بنجاح كملف JSON'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('فشل في الحفظ');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ خطأ في التصدير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'new':
        _showNewWarehouseDialog();
        break;
      case 'open':
        _openWarehouse();
        break;
      case 'save_as':
        // TODO: حفظ باسم
        break;
      case '3d_preview':
        _show3DPreview();
        break;
      case 'settings':
        _showSettings();
        break;
    }
  }

  void _show3DPreview() {
    if (!_provider.hasLayout) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('قم بإنشاء مستودع أولاً')),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Warehouse3DPreviewScreen(
          layout: _provider.currentLayout!,
        ),
      ),
    );
  }

  void _showSettings() {
    // TODO: عرض الإعدادات
  }

  void _undo() {
    // TODO: تنفيذ نظام التراجع
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة التراجع قيد التطوير')),
    );
  }

  void _redo() {
    // TODO: تنفيذ نظام الإعادة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة الإعادة قيد التطوير')),
    );
  }

  void _zoomFit() {
    // إعادة تعيين العرض إلى الوضع الافتراضي
    final newState = _provider.editorState.copyWith(
      zoom: 1.0,
      panOffset: vector.Vector2.zero(),
    );
    _provider.updateEditorState(newState);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تم إعادة تعيين العرض')),
    );
  }

  void _toggleGrid() {
    final newState = _provider.editorState.copyWith(
      showGrid: !_provider.editorState.showGrid,
    );
    _provider.updateEditorState(newState);

    final message = newState.showGrid ? 'تم إظهار الشبكة' : 'تم إخفاء الشبكة';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _toggleMeasurements() {
    final newState = _provider.editorState.copyWith(
      showMeasurements: !_provider.editorState.showMeasurements,
    );
    _provider.updateEditorState(newState);

    final message =
        newState.showMeasurements ? 'تم إظهار القياسات' : 'تم إخفاء القياسات';
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  void _updateSelectedObject(Map<String, dynamic> updates) {
    final selectedId = _provider.editorState.selectedObjectId;
    final selectedType = _provider.editorState.selectedObjectType;

    if (selectedId == null || selectedType == null) return;

    switch (selectedType) {
      case ObjectType.shelf:
        _updateShelfProperties(selectedId, updates);
        break;
      case ObjectType.wall:
        // TODO: تحديث خصائص الجدار
        break;
      case ObjectType.entrance:
        // TODO: تحديث خصائص المدخل
        break;
      case ObjectType.bin:
        // TODO: تحديث خصائص الخانة
        break;
    }
  }

  void _openMedicineAssignment(String shelfId) {
    final shelf = _provider.currentLayout?.shelves.firstWhere(
      (s) => s.id == shelfId,
    );

    if (shelf != null) {
      if (_provider.currentLayout?.linkedWarehouseId == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يجب ربط المخطط بمستودع أولاً لتوزيع الأدوية'),
            backgroundColor: Colors.orange,
          ),
        );
        return;
      }

      showDialog(
        context: context,
        builder: (context) => ChangeNotifierProvider.value(
          value: _provider,
          child: MedicineAssignmentDialog(shelf: shelf),
        ),
      );
    }
  }

  void _updateShelfProperties(String shelfId, Map<String, dynamic> updates) {
    if (_provider.currentLayout == null) return;

    final shelfIndex =
        _provider.currentLayout!.shelves.indexWhere((s) => s.id == shelfId);
    if (shelfIndex == -1) return;

    final shelf = _provider.currentLayout!.shelves[shelfIndex];

    // إنشاء خزانة محدثة
    final updatedShelf = Shelf(
      id: shelf.id,
      name: updates['name'] ?? shelf.name,
      position: shelf.position,
      width: shelf.width,
      depth: shelf.depth,
      height: shelf.height,
      rotation: updates['rotation'] ?? shelf.rotation,
      type: shelf.type,
      color: shelf.color,
      levels: updates['levels'] ?? shelf.levels,
      slotsPerLevel: updates['slotsPerLevel'] ?? shelf.slotsPerLevel,
    );

    // استبدال الخزانة في القائمة
    _provider.currentLayout!.shelves[shelfIndex] = updatedShelf;

    // إشعار المستمعين بالتحديث
    _provider.updateEditorState(_provider.editorState);

    // ScaffoldMessenger.of(context).showSnackBar(
    //   const SnackBar(content: Text('✅ تم تحديث خصائص الخزانة')),
    // );
  }

  void _deleteSelectedObject() {
    final selectedId = _provider.editorState.selectedObjectId;
    final selectedType = _provider.editorState.selectedObjectType;

    if (selectedId == null || selectedType == null) return;

    switch (selectedType) {
      case ObjectType.wall:
        _provider.deleteWall(selectedId);
        break;
      case ObjectType.entrance:
        _provider.deleteEntrance(selectedId);
        break;
      case ObjectType.shelf:
        _provider.deleteShelf(selectedId);
        break;
      case ObjectType.bin:
        // TODO: تنفيذ حذف الخانة (مسح المحتوى)
        break;
    }
  }

  // === أدوات مساعدة ===

  Color _getModeColor(EditMode mode) {
    switch (mode) {
      case EditMode.select:
        return Colors.grey.shade600;
      case EditMode.drawWall:
        return const Color(0xFF34495E);
      case EditMode.deleteWall:
        return Colors.red;
      case EditMode.addEntrance:
        return const Color(0xFFE67E22);
      case EditMode.placeShelf:
        return const Color(0xFF3498DB);
      case EditMode.editShelf:
        return const Color(0xFF9B59B6);
    }
  }

  IconData _getModeIcon(EditMode mode) {
    switch (mode) {
      case EditMode.select:
        return Icons.pan_tool;
      case EditMode.drawWall:
        return Icons.linear_scale;
      case EditMode.deleteWall:
        return Icons.delete_outline;
      case EditMode.addEntrance:
        return Icons.door_front_door;
      case EditMode.placeShelf:
        return Icons.add_box;
      case EditMode.editShelf:
        return Icons.edit;
    }
  }

  String _getModeText(EditMode mode) {
    switch (mode) {
      case EditMode.select:
        return 'تحديد';
      case EditMode.drawWall:
        return 'رسم جدار';
      case EditMode.deleteWall:
        return 'مسح جدار';
      case EditMode.addEntrance:
        return 'إضافة مدخل';
      case EditMode.placeShelf:
        return 'وضع خزانة';
      case EditMode.editShelf:
        return 'تعديل خزانة';
    }
  }

  void _openViewerMode() {
    final layout = _provider.currentLayout;
    if (layout == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا يوجد مخطط لعرضه'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WarehouseViewerScreen(
          layoutId: layout.id,
          layoutName: layout.name,
        ),
      ),
    );
  }
}
