import 'dart:convert';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/accounts_vouchers_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/accounts_voucher_counter.dart';
import 'package:inventory_application/models/model/vouchar_model.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';

class ReceiptVoucherController with ChangeNotifier {
  AccountsVoucharsModel receiptVoucher = AccountsVoucharsModel();
  List<AccountsVoucharsModel> receiptVouchers = [];

  //---------------------------------------------------------------------------
  Future<String> getReceiptVoucherNumber() async {
    try {
      var number = await AccountsVoucherCounterGenerator.getNextCounterByType(
          AccountsVoucharType.ReceiptVoucher);
      notifyListeners();
      return number;
    } catch (e) {
      print(e);
      return "";
    }
  }

  //---------------------------------------------------------------------------
  void clearReceiptVoucher() {
    receiptVoucher = AccountsVoucharsModel();
    notifyListeners();
  }

  //---------------------------------------------------------------------------
  void updateReceiptVoucherField({
    String? note,
    double? voucharAmount,
    int? accountIdDebit,
    int? accountIdCredit,
    int? currencyId,
    double? exchangeRate,
    String? voucharDateFormated,
    String? customerName,
    String? invoiceNo,
    int? paymentTypeId,
  }) {
    if (note != null) receiptVoucher.note = note;
    if (voucharAmount != null) receiptVoucher.voucharAmount = voucharAmount;
    if (accountIdDebit != null) receiptVoucher.accountIdDebit = accountIdDebit;
    if (accountIdCredit != null)
      receiptVoucher.accountIdCredit = accountIdCredit;
    if (currencyId != null) receiptVoucher.currencyId = currencyId;
    if (exchangeRate != null) receiptVoucher.exchangeRate = exchangeRate;
    if (voucharDateFormated != null)
      receiptVoucher.voucharDateFormated = voucharDateFormated;
    if (customerName != null) receiptVoucher.customerName = customerName;
    if (invoiceNo != null) receiptVoucher.invoiceNo = invoiceNo;
    if (paymentTypeId != null) receiptVoucher.paymentTypeId = paymentTypeId;

    notifyListeners();
  }

  //---------------------------------------------------------------------------
  Future<ResponseResultModel> saveReceiptVoucher() async {
    try {
      var url = '/AccountsVouchers/Manage?voucher_type=ReceiptVoucher';
      final db = AccountsVouchersController();

      // Set receipt type
      receiptVoucher.receiptTypeInt = AccountsVoucharType.ReceiptVoucher.value;
      receiptVoucher.receiptType =
          AccountsVoucherCounterGenerator.getVoucherTypeDisplayName(
              AccountsVoucharType.ReceiptVoucher);

      // Set date if not provided
      receiptVoucher.voucharDateFormated ??=
          DateFormat('dd/MM/yyyy', 'en').format(DateTime.now());

      // Generate code if not provided
      if (receiptVoucher.code == null || receiptVoucher.code == "") {
        receiptVoucher.code = await getReceiptVoucherNumber();
      } else {
        if (await AccountsVoucherCounterGenerator.checkIfCounterUsed(
            AccountsVoucharType.ReceiptVoucher, receiptVoucher.code ?? "")) {
          receiptVoucher.code = await getReceiptVoucherNumber();
        }
      }

      // Check if offline
      if (await isThereNetworkConnection() == false) {
        var localId = await db.insertOrUpdateAccountsVoucher(
            SqlLiteInvoiceModel(
                    data: jsonEncode(receiptVoucher.toJson()),
                    status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
                    id: null,
                    localCode: receiptVoucher.code,
                    type: AccountsVoucharType.ReceiptVoucher.name)
                .toJson());

        clearReceiptVoucher();
        await AccountsVoucherCounterGenerator.setCounterByTypeAuto(
            type: AccountsVoucharType.ReceiptVoucher);
        notifyListeners();

        return ResponseResultModel(isSuccess: true, data: {"localId": localId});
      }

      // Save to server
      var result = await Api.post(
        action: url,
        body: receiptVoucher.toJson(),
      );

      if (result != null && result.isSuccess) {
        await AccountsVoucherCounterGenerator
            .setAccountsVoucherCounterInServer();
        await AccountsVoucherCounterGenerator.setCounterByTypeAuto(
            type: AccountsVoucharType.ReceiptVoucher);

        var localId = await db.insertOrUpdateAccountsVoucher(
            SqlLiteInvoiceModel(
                    data: jsonEncode(receiptVoucher.toJson()),
                    status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                    id: null,
                    localCode: receiptVoucher.code,
                    type: AccountsVoucharType.ReceiptVoucher.name)
                .toJson());

        clearReceiptVoucher();
        notifyListeners();

        return ResponseResultModel(data: {
          "id": result.data["ID"],
          "code": result.data["Code"],
          "localId": localId
        }, isSuccess: true);
      }

      return ResponseResultModel(
          isSuccess: false, message: [result?.message ?? "Unknown error"]);
    } catch (e) {
      print("Error in saveReceiptVoucher: $e");
      return ResponseResultModel(isSuccess: false, message: [e.toString()]);
    }
  }

  //---------------------------------------------------------------------------
  Future<bool> validateReceiptVoucher() async {
    if (receiptVoucher.voucharAmount == null ||
        receiptVoucher.voucharAmount! <= 0) {
      return false;
    }
    if (receiptVoucher.accountIdDebit == null) {
      return false;
    }
    if (receiptVoucher.accountIdCredit == null) {
      return false;
    }
    return true;
  }

  //---------------------------------------------------------------------------
  Future<List<AccountsVoucherDtoWithLiteId>> getReceiptVouchers() async {
    final db = AccountsVouchersController();
    var allVouchers = await db.fetchLocalVouchers();

    // Filter only receipt vouchers
    return allVouchers
        .where((voucher) =>
            voucher.data?.receiptTypeInt ==
            AccountsVoucharType.ReceiptVoucher.value)
        .toList();
  }
}
