import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class CommonMaterialButton extends StatefulWidget {
  const CommonMaterialButton({
    super.key,
    required this.label,
    this.onPressed,
    this.width,
    this.height,
    this.future,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
    this.borderRadius,
    this.textStyle,
    this.icon,
  });

  final String label;
  final void Function()? onPressed;
  final double? width;
  final double? height;
  final Future Function()? future;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;
  final double? borderRadius;
  final TextStyle? textStyle;
  final IconData? icon;
  @override
  State<CommonMaterialButton> createState() => _CommonMaterialButtonState();
}

class _CommonMaterialButtonState extends State<CommonMaterialButton> {
  bool isProcessing = false;

  @override
  Widget build(BuildContext context) {
    return MaterialButton(
      minWidth: widget.width ?? MediaQuery.of(context).size.width,
      height: widget.height ?? 35,
      colorBrightness: Brightness.light,
      color: widget.backgroundColor ?? context.onPrimary,
      disabledColor: context.onPrimary,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(widget.borderRadius ?? 20),
        // add border color
        side: BorderSide(
          color: widget.borderColor ?? context.onPrimary,
          width: 1,
        ),
      ),
      onPressed: isProcessing ? null : onPressed,
      child: isProcessing
          ? CircularProgressIndicator(color: context.backgroundColor)
          : Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (widget.icon != null)
                  Icon(
                    widget.icon,
                    color: widget.textColor ?? context.backgroundColor,
                  ),
                const SizedBox(width: 5),
                Text(
                  widget.label,
                  style: widget.textStyle ??
                      TextStyle(
                          color: widget.textColor ?? context.backgroundColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 16),
                ),
              ],
            ),
    );
  }

  void onPressed() {
    isProcessing = true;
    setState(() {});
    if (widget.future != null) {
      widget.future!().then((value) {
        isProcessing = false;
        setState(() {});
      });
    } else {
      widget.onPressed!();
      isProcessing = false;
      setState(() {});
    }
  }
}
