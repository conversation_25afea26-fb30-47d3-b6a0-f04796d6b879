import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class CommonCircleButton extends StatelessWidget {
  const CommonCircleButton(
      {super.key,
      required this.icon,
      required this.onClick,
      this.backgroundColors,
      this.iconColor});

  final Icon icon;
  final void Function() onClick;
  final Color? backgroundColors;
  final Color? iconColor;
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: onClick,
      style: ButtonStyle(
        shape: MaterialStateProperty.all(const CircleBorder()),
        padding: MaterialStateProperty.all(const EdgeInsets.all(15)),
        backgroundColor: MaterialStateProperty.all(
            backgroundColors ?? context.onSecondary), // <-- Button color
        overlayColor: MaterialStateProperty.resolveWith<Color?>((states) {
          if (states.contains(MaterialState.pressed)) {
            return context.primaryColor; // <-- Splash color
          }
          return null;
        }),
      ),
      child: icon,
    );
  }
}
