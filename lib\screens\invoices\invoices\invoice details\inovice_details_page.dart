import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/return_invoice_controller.dart';
import 'package:inventory_application/controllers/sale_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/invoices%20list/invoices_list_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/returns%20invoice/create_return_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/sale%20invoice/create_sale_invoice_screen.dart';
import 'package:inventory_application/screens/invoices/pos/pos_screen.dart';
import 'package:inventory_application/services/printer_service.dart';
import 'package:provider/provider.dart';

import 'widgets/invoice_details_base_info_widget.dart';
import 'widgets/invoice_details_items_widget.dart';

class InoviceDetailsPage extends StatefulWidget {
  final InvoiceDto model;
  const InoviceDetailsPage({super.key, required this.model});

  @override
  State<InoviceDetailsPage> createState() => _InoviceDetailsPageState();
}

class _InoviceDetailsPageState extends State<InoviceDetailsPage> {
  late Future<InvoiceDto?> _invoiceFuture;

  @override
  void initState() {
    super.initState();
    _invoiceFuture = Provider.of<InvoiceController>(context, listen: false)
        .getInvoiceByTypeAndCode2(
      type: widget.model.invoiceCode != null
          ? widget.model.invoiceCode!.startsWith("SI")
              ? "Invoice"
              : "RetrunInvoice"
          : "",
      code: widget.model.invoiceCode ?? "",
    );
  }

  void _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required Function delete,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Text(
            title,
            style: TextStyle(
              color: context.newSecondaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            content,
            style: TextStyle(
              color: context.newTextColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: Text(
                T("Cancel"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                delete();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Modern Header
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: context.newBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonHeader(
                    icon: Icons.receipt_long,
                    title: T("Invoice details"),
                  ),
                ],
              ),
            ),

            // Invoice Data Section
            FutureBuilder<InvoiceDto?>(
              future: _invoiceFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Container(
                    height: 300,
                    alignment: Alignment.center,
                    child: CircularProgressIndicator(
                      color: context.newPrimaryColor,
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Container(
                    margin: const EdgeInsets.all(20),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          T("Error loading invoice data"),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: context.newTextColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${snapshot.error}',
                          style: TextStyle(
                            color: context.newTextColor.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  );
                } else if (snapshot.hasData && snapshot.data != null) {
                  return Column(
                    children: [
                      // Base Info Section
                      Container(
                        margin: const EdgeInsets.only(
                            top: 15, left: 15, right: 15, bottom: 5),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: context.newPrimaryColor.withOpacity(0.15),
                              spreadRadius: 1,
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section Header
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 15),
                              decoration: BoxDecoration(
                                color: context.newSecondaryColor,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(15),
                                  topRight: Radius.circular(15),
                                ),
                              ),
                              child: Text(
                                T("Invoice Information"),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),

                            // Base Info Widget
                            InvoiceDetailsBaseInfoWidget(data: snapshot.data),
                          ],
                        ),
                      ),

                      // Items Section
                      Container(
                        margin: const EdgeInsets.only(
                            top: 5, left: 15, right: 15, bottom: 15),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: context.newPrimaryColor.withOpacity(0.15),
                              spreadRadius: 1,
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section Header
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 15),
                              decoration: BoxDecoration(
                                color: context.newSecondaryColor,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(15),
                                  topRight: Radius.circular(15),
                                ),
                              ),
                              child: Text(
                                T("Invoice Items"),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),

                            // Items Widget
                            Padding(
                              padding: const EdgeInsets.all(10),
                              child: InvoiceDetailsItemsWidget(
                                data: snapshot.data ?? InvoiceDto(),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Action Buttons Section
                      Container(
                        margin: const EdgeInsets.only(
                            left: 15, right: 15, bottom: 25),
                        padding: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: context.newPrimaryColor.withOpacity(0.15),
                              spreadRadius: 1,
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Action Buttons Header
                            Container(
                              width: double.infinity,
                              margin: const EdgeInsets.only(bottom: 15),
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                color: context.newBackgroundColor,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color:
                                      context.newPrimaryColor.withOpacity(0.3),
                                ),
                              ),
                              child: Text(
                                T("Invoice Actions"),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: context.newSecondaryColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),

                            // First Row Buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Duplicate Button
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () async {
                                      if (snapshot.data?.invoiceCode != null) {
                                        if (snapshot.data!.invoiceCode!
                                            .startsWith("SI")) {
                                          Provider.of<SaleInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .invoice = snapshot.data!;
                                          Provider.of<SaleInvoiceController>(
                                                      context,
                                                      listen: false)
                                                  .selectedSaleInvoiceProduct =
                                              snapshot.data?.salesItems ?? [];

                                          var result = await Provider.of<
                                                      SaleInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .repeatSaleInvoice();

                                          if (result) {
                                            successSnackBar(
                                                message: T(
                                                    "The operation has been saved"));
                                          } else {
                                            errorSnackBar(
                                                message: T(
                                                    "The invoice was not saved, please try again later."));
                                          }
                                        } else {
                                          Provider.of<ReturnInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .invoice = snapshot.data!;
                                          Provider.of<ReturnInvoiceController>(
                                                      context,
                                                      listen: false)
                                                  .selectedReturnInvoiceProduct =
                                              snapshot.data?.salesItems ?? [];
                                          Provider.of<ReturnInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .notifyListenersFuntion();
                                          Navigator.of(context).push(
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  const CreateReturnInvoiceScreen(),
                                            ),
                                          );

                                          var result = await Provider.of<
                                                      ReturnInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .repeatReturnInvoice();

                                          if (result) {
                                            successSnackBar(
                                                message: T(
                                                    "The operation has been saved"));
                                          } else {
                                            errorSnackBar(
                                                message: T(
                                                    "The invoice was not saved, please try again later."));
                                          }
                                        }
                                      }
                                    },
                                    icon: const Icon(Icons.content_copy,
                                        size: 18),
                                    label: Text(T("Duplicate")),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.amber,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),

                                // Edit Button
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      if (snapshot.data?.invoiceCode != null) {
                                        if (snapshot.data!.invoiceCode!
                                            .startsWith("SI")) {
                                          // Load invoice data into controller
                                          Provider.of<SaleInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .invoice = snapshot.data!;
                                          Provider.of<SaleInvoiceController>(
                                                      context,
                                                      listen: false)
                                                  .selectedSaleInvoiceProduct =
                                              snapshot.data?.salesItems ?? [];
                                          Provider.of<SaleInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .notifyListenersFuntion();

                                          // Check screen width to determine device type
                                          final screenWidth = context.width;
                                          final isDesktop = screenWidth >
                                              800; // Threshold for desktop vs mobile

                                          if (isDesktop) {
                                            // Navigate to POS screen for desktop
                                            Navigator.of(context).push(
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const POSScreen(
                                                        isEditing: true),
                                              ),
                                            );
                                          } else {
                                            // Navigate to sale invoice screen for mobile
                                            Navigator.of(context).push(
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const CreateSaleInvoiceScreen(),
                                              ),
                                            );
                                          }
                                        } else {
                                          Provider.of<ReturnInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .invoice = snapshot.data!;
                                          Provider.of<ReturnInvoiceController>(
                                                      context,
                                                      listen: false)
                                                  .selectedReturnInvoiceProduct =
                                              snapshot.data?.salesItems ?? [];
                                          Provider.of<ReturnInvoiceController>(
                                                  context,
                                                  listen: false)
                                              .notifyListenersFuntion();
                                          Navigator.of(context).push(
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  const CreateReturnInvoiceScreen(),
                                            ),
                                          );
                                        }
                                      }
                                    },
                                    icon: const Icon(Icons.edit, size: 18),
                                    label: Text(T("Edit")),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          context.newSecondaryColor,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),

                            // Second Row Buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Print Button
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () async {
                                      try {
                                        // Print directly using PrinterService
                                        await PrinterService.printInvoice(
                                          snapshot.data!,
                                          context,
                                        );
                                      } catch (e) {
                                        errorSnackBar(
                                          message:
                                              'حدث خطأ أثناء طباعة الفاتورة: $e',
                                        );
                                      }
                                    },
                                    icon: const Icon(Icons.print, size: 18),
                                    label: Text(T("Print")),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),

                                // Delete Button
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      _showConfirmDialog(
                                        title: T("Warning"),
                                        content: T(
                                            "Are you sure you want to delete this invoice?"),
                                        confirmText: T("Delete"),
                                        delete: () async {
                                          bool result = await Provider.of<
                                                      InvoiceController>(
                                                  context,
                                                  listen: false)
                                              .deleteSyncInvoice(
                                                  widget.model.id ?? 0,
                                                  widget.model.invoiceCode !=
                                                          null
                                                      ? widget.model
                                                              .invoiceCode!
                                                              .startsWith("SI")
                                                          ? "Invoice"
                                                          : "RetrunInvoice"
                                                      : "");
                                          if (result) {
                                            // ignore: use_build_context_synchronously
                                            Navigator.of(context)
                                                .pushReplacement(
                                                    MaterialPageRoute(
                                              builder: (context) =>
                                                  const InvoicesListScreenScreen(),
                                            ));
                                            successSnackBar(
                                                message:
                                                    T("Successfully deleted"));
                                          } else {
                                            errorSnackBar(
                                                message: T(
                                                    "Delete failed. Please try again later"));
                                          }
                                        },
                                      );
                                    },
                                    icon: const Icon(Icons.delete_outline,
                                        size: 18),
                                    label: Text(T("Delete")),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }

                return Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: context.newTextColor.withOpacity(0.5),
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        T("No data available"),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: context.newTextColor,
                        ),
                      ),
                    ],
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
