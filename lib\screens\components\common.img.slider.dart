import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_slideshow/flutter_image_slideshow.dart';
import 'package:inventory_application/base/extension/context_extension.dart';

class CommonImgSlider extends StatelessWidget {
  const CommonImgSlider(
      {super.key,
      required this.attachments,
      this.autoScroll = true,
      this.onTap});
  final List<String> attachments;
  final bool autoScroll;
  final Function? onTap;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Stack(children: [
          ImageSlideshow(
            /// Width of the [ImageSlideshow].
            width: double.infinity,

            /// Height of the [ImageSlideshow].
            height: 232,

            /// The page to show when first creating the [ImageSlideshow].
            initialPage: 0,

            /// The color to paint the indicator.
            indicatorColor: context.primaryColor,
            indicatorRadius: 4,

            /// The color to paint behind th indicator.
            indicatorBackgroundColor: Colors.grey,

            /// Called whenever the page in the center of the viewport changes.
            onPageChanged: (value) {
              //print('Page changed: $value');
            },

            /// Auto scroll interval.
            /// Do not auto scroll with null or 0.
            autoPlayInterval: autoScroll ? 3000 : 0,

            /// Loops back to first slide.
            isLoop: true,

            /// The widgets to display in the [ImageSlideshow].
            /// Add the sample image file into the images folder
            children: [
              ...attachments.map((e) {
                return InkWell(
                  onTap: () {
                    if (onTap != null) {
                      onTap!();
                      return;
                    }
                    // openScreen(
                    //     context,
                    //     CommonImageViewer(
                    //       files: [...attachments],
                    //       index: attachments
                    //           .indexWhere((element) => element == element),
                    //     ));
                  },
                  child: Material(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: CachedNetworkImage(
                          imageUrl: e,
                          fit: BoxFit.cover,
                        ),
                        // child: Image.memory(
                        //   bytesImage,
                        //   fit: BoxFit.cover,
                        // ),
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ]),
      ],
    );
  }
}
