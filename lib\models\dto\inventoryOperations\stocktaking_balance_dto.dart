class StocktakingBalanceDto {
  final int? itemId;
  final double? quantity;

  StocktakingBalanceDto({
    this.itemId,
    this.quantity,
  });

  factory StocktakingBalanceDto.fromJson(Map<String, dynamic> json) {
    return StocktakingBalanceDto(
      itemId: json['ItemId'],
      quantity: (json['Quantity'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ItemId': itemId,
      'Quantity': quantity,
    };
  }
}
