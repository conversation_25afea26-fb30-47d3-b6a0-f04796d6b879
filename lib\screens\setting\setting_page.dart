import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/setting/barcode_settings_screen.dart';
import 'package:inventory_application/screens/setting/invoice_settings_screen.dart';
import 'package:inventory_application/screens/setting/my_profile_screen.dart';
import 'package:inventory_application/screens/setting/server_settings_screen.dart';
import 'package:inventory_application/screens/setting/synchronization/synchronization_screen.dart';
import 'package:inventory_application/screens/setting/printer_settings_screen.dart';
import 'package:inventory_application/screens/warehouse_planner/worker_warehouse_display.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  _SettingPageState createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  String? languageVal;
  String? printType;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    languageVal = context.locale.languageCode;
    _loadPrintType();
  }

  Future<void> _savePrintType(String type) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('printType', type);
    setState(() {
      printType = type;
    });
  }

  Future<void> _loadPrintType() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      printType = prefs.getString('printType') ?? 'Roll80';
    });
  }

  void _onLanguageChanged(String languageCode) {
    Locale newLocale = Locale(languageCode);
    if (context.supportedLocales.contains(newLocale)) {
      context.setLocale(newLocale);
    } else {
      context.setLocale(const Locale('en', 'US'));
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: SingleChildScrollView(
        child: Column(
          children: [
            CommonHeader(icon: Icons.settings, title: T("Settings")),
            // _buildHeader(),
            const SizedBox(height: 15),

            if (AppController.isAuth) const SizedBox(height: 12),
            _buildLanguageSection(),
            // const SizedBox(height: 12),
            // _buildPrintTypeSection(),
            if (AppController.isAuth)
              _buildNavigationItem(
                icon: Icons.person,
                title: T("My Profile"),
                onTap: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const MyProfileScreen(),
                    ),
                  );
                },
              ),
            const SizedBox(height: 12),
            _buildNavigationItem(
              icon: Icons.receipt_long,
              title: T("Invoice Settings"),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const InvoiceSettingsScreen(),
                  ),
                );
              },
            ),
            const SizedBox(height: 12),
            _buildNavigationItem(
              icon: Icons.cloud_sync_rounded,
              title: T("Synchronization"),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const SynchronizationScreen(),
                  ),
                );
              },
            ),
            const SizedBox(height: 12),
            _buildNavigationItem(
              icon: Icons.qr_code_scanner,
              title: T("Barcode Reader Settings"),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const BarcodeSettingsScreen(),
                  ),
                );
              },
            ),
            const SizedBox(height: 12),
            _buildNavigationItem(
              icon: Icons.print,
              title: T("Printer Settings"),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const PrinterSettingsScreen(),
                  ),
                );
              },
            ),
            const SizedBox(height: 12),
            _buildNavigationItem(
              icon: Icons.cloud,
              title: T("Server Settings & Backups"),
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const ServerSettingsScreen(
                      isFirstTime: false,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 12),
            _buildNavigationItem(
              icon: Icons.search,
              title: "البحث في المستودع",
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const WorkerWarehouseDisplay(),
                  ),
                );
              },
            ),
            const SizedBox(height: 12),
            // _buildNavigationItem(
            //   icon: Icons.straighten,
            //   title: T("Units Management"),
            //   onTap: () {
            //     Navigator.of(context).push(
            //       MaterialPageRoute(
            //         builder: (context) => const UnitsScreen(),
            //       ),
            //     );
            //   },
            // ),
            const SizedBox(height: 20),
            if (AppController.isAuth)
              ElevatedButton.icon(
                onPressed: () async {
                  await _showLogoutConfirmationDialog();
                },
                icon: const Icon(Icons.logout),
                label: Text(T('Log out')),
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.newSecondaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 52),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
              ),
            const SizedBox(height: 60),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 16),
      decoration: BoxDecoration(
        color: context.newPrimaryColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: context.newPrimaryColor.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                borderRadius: BorderRadius.circular(50),
                splashColor: Colors.white.withOpacity(0.1),
                highlightColor: Colors.white.withOpacity(0.1),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    border: Border.all(
                        color: Colors.white.withOpacity(0.2), width: 1),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: Icon(
                    Icons.arrow_back,
                    color: Colors.white,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
          Row(
            children: [
              const SizedBox(width: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.settings,
                  size: 28,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSection() {
    return _buildSettingSection(
      icon: Icons.language,
      title: T("Language"),
      content: Column(
        children: [
          _buildRadioOption(
            title: T('English'),
            value: 'en',
            groupValue: languageVal,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  languageVal = value;
                  _onLanguageChanged(value);
                });
              }
            },
          ),
          const Divider(height: 1),
          _buildRadioOption(
            title: T('Arabic'),
            value: 'ar',
            groupValue: languageVal,
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  languageVal = value;
                  _onLanguageChanged(value);
                });
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPrintTypeSection() {
    return _buildSettingSection(
      icon: Icons.print,
      title: T("Print Type"),
      content: Column(
        children: [
          _buildRadioOption(
            title: 'Roll80',
            value: 'Roll80',
            groupValue: printType,
            onChanged: (value) {
              if (value != null) {
                _savePrintType(value);
              }
            },
          ),
          const Divider(height: 1),
          _buildRadioOption(
            title: 'Roll57',
            value: 'Roll57',
            groupValue: printType,
            onChanged: (value) {
              if (value != null) {
                _savePrintType(value);
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSettingSection({
    required IconData icon,
    required String title,
    required Widget content,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      clipBehavior: Clip.antiAlias,
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
          colorScheme: ColorScheme.light(
            primary: context.newPrimaryColor,
          ),
        ),
        child: ExpansionTile(
          leading: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: context.newPrimaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: context.newPrimaryColor,
              size: 22,
            ),
          ),
          title: Text(
            title,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: context.newTextColor,
            ),
          ),
          children: [
            Container(
              decoration: BoxDecoration(
                color: context.newBackgroundColor.withOpacity(0.5),
              ),
              child: content,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRadioOption({
    required String title,
    required String value,
    required String? groupValue,
    required Function(String?)? onChanged,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => onChanged?.call(value),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: Row(
            children: [
              Radio<String>(
                value: value,
                groupValue: groupValue,
                activeColor: context.newPrimaryColor,
                onChanged: onChanged,
              ),
              Text(
                title,
                style: TextStyle(
                  fontSize: 15,
                  color: context.newTextColor,
                  fontWeight:
                      groupValue == value ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.white,
      borderRadius: BorderRadius.circular(16),
      elevation: 2,
      shadowColor: Colors.grey.withOpacity(0.2),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: context.newPrimaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: context.newPrimaryColor,
                  size: 22,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                    color: context.newTextColor,
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: context.newTextColor.withOpacity(0.5),
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<bool?> _showLogoutConfirmationDialog() async {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          T('Confirm Logout'),
          style: TextStyle(
            color: context.newSecondaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          T('Are you sure you want to log out?'),
          style: TextStyle(
            color: context.newTextColor,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              T('Cancel'),
              style: TextStyle(
                color: context.newTextColor,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context, true);
              await Provider.of<AuthController>(context, listen: false)
                  .logOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: context.newSecondaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(T('Log out')),
          ),
        ],
      ),
    );
  }
}
