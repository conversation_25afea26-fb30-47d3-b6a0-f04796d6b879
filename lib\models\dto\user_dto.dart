class UserDTO {
  int? id;
  String? name;

  UserDTO({
    this.id,
    this.name,
  });

  factory UserDTO.fromJson(Map<String, dynamic> json) {
    return UserDTO(
      id: json['Id'] != null ? int.tryParse(json['Id'].toString()) : null,
      name: json['Name']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Id': id,
      'Name': name,
    };
  }

  @override
  String toString() {
    return 'UserDTO(id: $id, name: $name)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserDTO && other.id == id && other.name == name;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode;
}
