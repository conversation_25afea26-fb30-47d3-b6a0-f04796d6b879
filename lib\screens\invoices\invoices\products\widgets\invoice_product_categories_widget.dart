import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/category_controller.dart';
// ignore: unused_import
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:provider/provider.dart';

class InvoiceProductCategoriesWidget extends StatefulWidget {
  const InvoiceProductCategoriesWidget(
      {super.key, this.cateogryId, required this.onSelecte});
  final int? cateogryId;
  final Function onSelecte;

  @override
  State<InvoiceProductCategoriesWidget> createState() =>
      _InvoiceProductCategoriesWidgetState();
}

class _InvoiceProductCategoriesWidgetState
    extends State<InvoiceProductCategoriesWidget> {
  @override
  Widget build(BuildContext context) {
    var categories = Provider.of<CategoryController>(context).categories;

    return RawScrollbar(
      thumbVisibility: true,
      thickness: 6,
      radius: const Radius.circular(3),
      child: ScrollConfiguration(
        behavior: ScrollConfiguration.of(context).copyWith(
          dragDevices: {
            PointerDeviceKind.touch,
            PointerDeviceKind.mouse,
            PointerDeviceKind.trackpad,
            PointerDeviceKind.stylus,
          },
          scrollbars: true,
        ),
        child: Container(
          width: context.width,
          height: 50,
          margin: const EdgeInsets.symmetric(vertical: 5),
          child: categories.isEmpty
              ? _buildEmptyCategories()
              : ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: categories.length,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemBuilder: (context, index) {
                    final isSelected =
                        widget.cateogryId == categories[index].iD;
                    return Padding(
                      padding: const EdgeInsets.only(right: 10),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            widget.onSelecte(categories[index].iD);
                          },
                          borderRadius: BorderRadius.circular(12),
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: isSelected
                                  ? [
                                      BoxShadow(
                                        color: context.newPrimaryColor
                                            .withOpacity(0.3),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      )
                                    ]
                                  : null,
                              border: Border.all(
                                width: 1.5,
                                color: isSelected
                                    ? context.newPrimaryColor
                                    : context.newTextColor.withOpacity(0.3),
                              ),
                              color: isSelected
                                  ? context.newPrimaryColor
                                  : context.newBackgroundColor,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (isSelected)
                                  Padding(
                                    padding: const EdgeInsets.only(right: 6),
                                    child: Icon(
                                      Icons.check_circle,
                                      size: 16,
                                      color: Colors.white,
                                    ),
                                  ),
                                Text(
                                  categories[index].name ?? "",
                                  style: TextStyle(
                                    fontWeight: isSelected
                                        ? FontWeight.bold
                                        : FontWeight.w500,
                                    fontSize: 14,
                                    color: isSelected
                                        ? Colors.white
                                        : context.newTextColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
        ),
      ),
    );
  }

  Widget _buildEmptyCategories() {
    return Center(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: context.newBackgroundColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: context.newTextColor.withOpacity(0.2),
          ),
        ),
        child: Text(
          T('No categories available'),
          style: TextStyle(
            color: context.newTextColor.withOpacity(0.7),
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
