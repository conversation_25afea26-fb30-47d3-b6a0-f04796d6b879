import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/payment_type_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/controllers/return_invoice_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/main.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/invoice/invoice_response_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';
import 'package:provider/provider.dart';

class POSOrder {
  final String id;
  InvoiceDto invoice;
  List<ProductDTO> products;
  bool isActive;

  POSOrder({
    required this.id,
    required this.invoice,
    required this.products,
    this.isActive = false,
  });
}

class SaleInvoiceController with ChangeNotifier {
  List<POSOrder> _orders = [];
  int _activeOrderIndex = 0;
  String lastAddedInvoiceCode = "";

  List<POSOrder> get orders => _orders;
  POSOrder get activeOrder => _orders[_activeOrderIndex];
  int get activeOrderIndex => _activeOrderIndex;
  int get orderCount => _orders.length;

  // For backward compatibility
  InvoiceDto get invoice => activeOrder.invoice;
  set invoice(InvoiceDto value) => activeOrder.invoice = value;

  List<ProductDTO> get selectedSaleInvoiceProduct => activeOrder.products;
  set selectedSaleInvoiceProduct(List<ProductDTO> value) =>
      activeOrder.products = value;

  SaleInvoiceController() {
    // Initialize with one empty order
    _createNewOrder();
  }
//--------------------------------------------------------------------------
  void _createNewOrder() {
    final invoiceSettingsController = Provider.of<InvoiceSettingsController>(
        navigatorKey.currentContext!,
        listen: false);
    final paymentTypeController = Provider.of<PaymentTypeController>(
        navigatorKey.currentContext!,
        listen: false);
    var defultWarehouse = invoiceSettingsController.defaultWarehouseId;
    var defultCustomer = invoiceSettingsController.defaultCustomerId;

    // Create the new order first
    final newOrder = POSOrder(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      invoice:
          InvoiceDto(customerId: defultCustomer, warehouseId: defultWarehouse),
      products: [],
    );

    // Set payment method if available
    if (paymentTypeController.paymentTypes.isNotEmpty) {
      if (paymentTypeController.paymentTypes.isNotEmpty) {
        newOrder.invoice.pyamentMethodId =
            paymentTypeController.paymentTypes.first.id;
        print(
            '🎯 تم تحديد طريقة الدفع تلقائياً في الفاتورة الجديدة: ${paymentTypeController.paymentTypes.first.name}');
      }
    }

    _orders.add(newOrder);
    _setActiveOrder(_orders.length - 1);
  }

  InvoiceDto initializeInvoice() {
    final invoiceSettingsController = Provider.of<InvoiceSettingsController>(
        navigatorKey.currentContext!,
        listen: false);
    final paymentTypeController = Provider.of<PaymentTypeController>(
        navigatorKey.currentContext!,
        listen: false);
    var defultWarehouse = invoiceSettingsController.defaultWarehouseId;
    var defultCustomer = invoiceSettingsController.defaultCustomerId;

    // تحديد طريقة الدفع الأولى تلقائياً مع التحقق من وجودها
    int? defultPaymentMethod;
    if (paymentTypeController.paymentTypes.isNotEmpty) {
      defultPaymentMethod = paymentTypeController.paymentTypes.first.id;
      print(
          '🎯 تم تحديد طريقة الدفع تلقائياً في initializeInvoice: ${paymentTypeController.paymentTypes.first.name}');
    }

    return InvoiceDto(
      customerId: defultCustomer,
      warehouseId: defultWarehouse,
      pyamentMethodId: defultPaymentMethod,
    );
  }

  // Create the new order first

//--------------------------------------------------------------------------
  void _setActiveOrder(int index) {
    if (index >= 0 && index < _orders.length) {
      // Deactivate all orders
      for (var order in _orders) {
        order.isActive = false;
      }
      // Activate the selected order
      _orders[index].isActive = true;
      _activeOrderIndex = index;
      notifyListeners();
    }
  }

  void switchToOrder(int index) {
    _setActiveOrder(index);
  }

  void createNewOrder() {
    _createNewOrder();

    // Get a new invoice number for the new order
    getSaleInvoiceNumber();
  }

  void removeOrder(int index) {
    if (index >= 0 && index < _orders.length) {
      _orders.removeAt(index);
      if (_orders.isEmpty) {
        _createNewOrder();
      } else if (_activeOrderIndex >= _orders.length) {
        _setActiveOrder(_orders.length - 1);
      }
      notifyListeners();
    }
  }

//--------------------------------------------------------------------------
  void addProductToSelectedList(ProductDTO model) {
    // IMPORTANT: Apply default unit first, before any other processing
    final invoiceSettingsController = Provider.of<InvoiceSettingsController>(
      navigatorKey.currentContext!,
      listen: false,
    );

    // Force apply default unit if default is set (regardless of whether product already has a unit)
    if (invoiceSettingsController.defaultUnitId != null) {
      model.uniteId = invoiceSettingsController.defaultUnitId;
      model.uniteName = invoiceSettingsController.defaultUnitName;
    }

    // Apply default warehouse if available
    if (activeOrder.invoice.warehouseId != null) {
      model.warehouseId = activeOrder.invoice.warehouseId;
      model.warehouseName = activeOrder.invoice.warehouseName ?? "";
    }

    // Create a copy of the model to ensure it's a completely separate object
    ProductDTO newProduct = ProductDTO(
        id: model.id,
        title: model.title,
        barcode: model.barcode,
        barcodeName: model.barcodeName,
        code: model.code,
        description: model.description,
        price: model.price,
        total: model.total,
        discountValue: model.discountValue,
        stock: model.stock,
        uniteId: model.uniteId,
        uniteName: model.uniteName,
        category: model.category,
        quantity: model.quantity,
        warehouseId: model.warehouseId,
        warehouseName: model.warehouseName,
        thumbnail: model.thumbnail,
        warehouse: model.warehouse,
        units: model.units,
        barcodes: model.barcodes,
        attribute: model.attribute,
        itemAttributes: model.itemAttributes,
        hasSelectedAttributes: (model.itemAttributes ?? []).isNotEmpty,
        virtualProductId: model.virtualProductId);

    // Check if this exact product + attributes combination already exists
    int existingIndex = -1;
    if (model.virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      existingIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.virtualProductId == model.virtualProductId,
      );
    } else {
      // Otherwise, fall back to checking by regular ID
      existingIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) =>
            element.id == model.id &&
            element.barcode == model.barcode &&
            !element.hasSelectedAttributes,
      );
    }
//--------------------------------------------------------------------------
    // If we found the exact same product, increase its quantity instead of adding a new one
    if (existingIndex >= 0) {
      double newQuantity =
          (selectedSaleInvoiceProduct[existingIndex].quantity ?? 1) +
              (newProduct.quantity ?? 1);

      // Update the quantity
      selectedSaleInvoiceProduct[existingIndex].quantity = newQuantity;

      // Update the total
      double price = selectedSaleInvoiceProduct[existingIndex].price ?? 0;
      selectedSaleInvoiceProduct[existingIndex].total = price * newQuantity;
    } else {
      // Add as a new item
      selectedSaleInvoiceProduct.add(newProduct);
    }

    calculateInvoiceTotal();
    notifyListeners();
  }

//--------------------------------------------------------------------------
  void deleteProductFromSelectedList(int id, {String? virtualProductId}) {
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      selectedSaleInvoiceProduct.removeAt(productIndex);
      calculateInvoiceTotal();
      notifyListeners();
    }
  }

//--------------------------------------------------------------------------
  void updateProductQuantity(int id, double quantity,
      [String? virtualProductId]) async {
    // Find the product index
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      // Update the quantity
      selectedSaleInvoiceProduct[productIndex].quantity = quantity;

      // Update the total amount based on the new quantity and existing price
      double price = selectedSaleInvoiceProduct[productIndex].price ?? 0;
      selectedSaleInvoiceProduct[productIndex].total = price * quantity;

      // Calculate the invoice total
      calculateInvoiceTotal();

      // Notify listeners for UI update
      notifyListeners();

      // Small delay to ensure UI updates
      await Future.delayed(const Duration(milliseconds: 100));

      // Notify again to ensure UI is updated
      notifyListeners();
    }
  }

//--------------------------------------------------------------------------
  void updateProductPrice(int id, double price, [String? virtualProductId]) {
    if (virtualProductId != null) {
      // Find the product by virtual ID
      var productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );

      if (productIndex >= 0) {
        selectedSaleInvoiceProduct[productIndex].price = price;
        selectedSaleInvoiceProduct[productIndex].total =
            price * (selectedSaleInvoiceProduct[productIndex].quantity ?? 1);
      }
    } else {
      // Find the product by regular ID
      var productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.id == id,
      );

      if (productIndex >= 0) {
        selectedSaleInvoiceProduct[productIndex].price = price;
        selectedSaleInvoiceProduct[productIndex].total =
            price * (selectedSaleInvoiceProduct[productIndex].quantity ?? 1);
      }
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

//--------------------------------------------------------------------------
  void updateProductUnit(int id, ItemPriceDTO unit,
      [String? virtualProductId]) {
    if (virtualProductId != null) {
      // Find the product by virtual ID
      var productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );

      if (productIndex >= 0) {
        // Only update the unit ID and name, not the price
        selectedSaleInvoiceProduct[productIndex].uniteId = unit.unitID;
        selectedSaleInvoiceProduct[productIndex].uniteName = unit.unitName;

        // Keep the existing price and total
        double price = selectedSaleInvoiceProduct[productIndex].price ?? 0;
        double quantity =
            selectedSaleInvoiceProduct[productIndex].quantity ?? 1;
        selectedSaleInvoiceProduct[productIndex].total = price * quantity;
      }
    } else {
      // Find the product by regular ID
      var productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.id == id,
      );

      if (productIndex >= 0) {
        // Only update the unit ID and name, not the price
        selectedSaleInvoiceProduct[productIndex].uniteId = unit.unitID;
        selectedSaleInvoiceProduct[productIndex].uniteName = unit.unitName;

        // Keep the existing price and total
        double price = selectedSaleInvoiceProduct[productIndex].price ?? 0;
        double quantity =
            selectedSaleInvoiceProduct[productIndex].quantity ?? 1;
        selectedSaleInvoiceProduct[productIndex].total = price * quantity;
      }
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

//--------------------------------------------------------------------------
  Future<dynamic> getItemByBarcode({String? barcode}) async {
    try {
      if (barcode == null || barcode.isEmpty) {
        return false;
      }

      // Notify that we're starting to process
      notifyListeners();

      // Use getItemByBarcodeWithWriting instead to handle final barcodes
      var result =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);
      if (result != null) {
        // If the product has attributes but they haven't been selected yet,
        // and it's not a final barcode, return the product so the UI can show the attribute dialog
        if (result.itemAttributes != null &&
            result.itemAttributes!.isNotEmpty &&
            !result.hasSelectedAttributes) {
          return result; // Return the product so the UI can show the attribute selection dialog
        }

        // First check if we already have this product with the same barcode
        int existingIndex = -1;

        // Handle products with virtual ID (products with attributes)
        if (result.virtualProductId != null) {
          existingIndex = selectedSaleInvoiceProduct.indexWhere(
            (element) => element.virtualProductId == result.virtualProductId,
          );
        } else {
          // UPDATED LOGIC: Match by exact barcode for both final and non-final barcodes
          for (int i = 0; i < selectedSaleInvoiceProduct.length; i++) {
            var product = selectedSaleInvoiceProduct[i];

            // IMPORTANT: For FINAL barcodes, we need to match by exact barcode
            // regardless of hasSelectedAttributes status
            if (product.barcode != null &&
                result.barcode != null &&
                product.barcode!.isNotEmpty &&
                result.barcode!.isNotEmpty &&
                product.barcode == barcode) {
              // For final barcodes (hasSelectedAttributes = true), match by exact barcode
              if (result.hasSelectedAttributes == true &&
                  product.hasSelectedAttributes == true) {
                existingIndex = i;
                break;
              }
              // For non-final barcodes (hasSelectedAttributes = false), also match by exact barcode
              else if (result.hasSelectedAttributes != true &&
                  product.hasSelectedAttributes != true) {
                existingIndex = i;
                break;
              }
            }
          }

          // If no barcode match found, check by product ID (only if both have no barcode)
          if (existingIndex == -1) {
            for (int i = 0; i < selectedSaleInvoiceProduct.length; i++) {
              var product = selectedSaleInvoiceProduct[i];

              // Skip items that have virtual IDs
              if (product.virtualProductId != null) {
                continue;
              }

              // Only match by ID if both products have no barcode or empty barcode
              bool bothHaveNoBarcode =
                  (result.barcode == null || result.barcode!.isEmpty) &&
                      (product.barcode == null || product.barcode!.isEmpty);

              if (bothHaveNoBarcode &&
                  product.id != null &&
                  result.id != null &&
                  product.id == result.id &&
                  result.hasSelectedAttributes ==
                      product.hasSelectedAttributes) {
                existingIndex = i;
                break;
              }
            }
          }
        }

        // If we found an existing product, increase its quantity
        if (existingIndex >= 0) {
          var existingProduct = selectedSaleInvoiceProduct[existingIndex];
          double newQuantity = (existingProduct.quantity ?? 1) + 1;

          // Update the quantity
          selectedSaleInvoiceProduct[existingIndex].quantity = newQuantity;

          // Update the total
          double price = selectedSaleInvoiceProduct[existingIndex].price ?? 0;
          selectedSaleInvoiceProduct[existingIndex].total = price * newQuantity;

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        } else {
          // This is a new product, add it to the list
          // Apply default unit if default is set
          final invoiceSettingsController =
              Provider.of<InvoiceSettingsController>(
            navigatorKey.currentContext!,
            listen: false,
          );

          // Force apply default unit if default is set
          if (invoiceSettingsController.defaultUnitId != null) {
            result.uniteId = invoiceSettingsController.defaultUnitId;
            result.uniteName = invoiceSettingsController.defaultUnitName;
          }

          // Set warehouse if available
          if (invoice.warehouseId != null) {
            result.warehouseId = invoice.warehouseId;
            result.warehouseName = invoice.warehouseName ?? "";
          }

          // IMPORTANT: For products with final barcodes (hasSelectedAttributes = true)
          // that don't have a virtualProductId, generate one to ensure proper identification in the UI
          if (result.virtualProductId == null &&
              result.hasSelectedAttributes == true &&
              result.barcode != null &&
              result.barcode!.isNotEmpty) {
            // Generate a consistent virtual product ID based on barcode for final barcodes
            result.virtualProductId = '${result.id}_final_${result.barcode}';
          }

          // Ensure quantity is at least 1
          result.quantity = result.quantity ?? 1;

          // Calculate total
          result.total = (result.price ?? 0) * result.quantity!;

          // Add to list
          selectedSaleInvoiceProduct.add(result);

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        }
      }

      return false;
    } catch (e) {
      print("Error in getItemByBarcode: $e");
      return false;
    }
  }

//--------------------------------------------------------------------------
  void setProductDiscount(int id, double value) {
    var productIndex = selectedSaleInvoiceProduct.indexWhere(
      (element) => element.id == id,
    );

    if (productIndex >= 0) {
      selectedSaleInvoiceProduct[productIndex].discountValue = value;
      calculateInvoiceTotal();
      notifyListeners();
    }
  }

//--------------------------------------------------------------------------
  void setInvoiceDiscount(InvoiceDiscountType type, double value) {
    if (type == InvoiceDiscountType.persantage) {
      invoice.discountType = InvoiceDiscountType.persantage;
      invoice.discountValue = 0;
      invoice.discountPercentage = value;
    } else {
      invoice.discountType = InvoiceDiscountType.value;
      invoice.discountValue = value;
      invoice.discountPercentage = 0;
    }
    calculateInvoiceTotal();
    notifyListeners();
  }

//--------------------------------------------------------------------------
  void calculateInvoiceTotal() {
    double total = 0;
    double totalQuantity = 0;
    double totalDiscount = 0;

    for (var product in selectedSaleInvoiceProduct) {
      total += product.total ?? 0;
      totalQuantity += product.quantity ?? 0;
    }

    invoice.total = total;
    invoice.productsTotalCount = totalQuantity;

    // Calculate discount
    if (invoice.discountType == InvoiceDiscountType.persantage) {
      double discountPercentage = invoice.discountPercentage ?? 0;
      totalDiscount = (total * discountPercentage) / 100;
    } else {
      totalDiscount = invoice.discountValue ?? 0;
    }

    invoice.totalDiscount = totalDiscount;
    invoice.totalAfterDiscount = total - totalDiscount;

    // Only set payment amount to match the net total if it hasn't been manually set
    // or if there are no products yet (initial state)
    if (invoice.paymentValue == null || selectedSaleInvoiceProduct.isEmpty) {
      invoice.paymentValue = invoice.totalAfterDiscount;
    }

    // Ensure UI updates
    notifyListeners();

    // Add a small delay and notify again to ensure UI updates
    Future.delayed(const Duration(milliseconds: 100), () {
      notifyListeners();
    });
  }

//--------------------------------------------------------------------------
  void setProductWarehouse(int id, int warehouseId, String warehouseName,
      [String? virtualProductId]) {
    int productIndex;

    if (virtualProductId != null) {
      // If we have a virtual ID, use that to find the product
      productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.virtualProductId == virtualProductId,
      );
    } else {
      // Otherwise use the regular product ID
      productIndex = selectedSaleInvoiceProduct.indexWhere(
        (element) => element.id == id,
      );
    }

    if (productIndex >= 0) {
      selectedSaleInvoiceProduct[productIndex].warehouseId = warehouseId;
      selectedSaleInvoiceProduct[productIndex].warehouseName = warehouseName;
      notifyListeners();
    }
  }

//--------------------------------------------------------------------------
  Future<String> getSaleInvoiceNumber() async {
    try {
      var number = await CounterGenerator.getSaleInvoiceNextCounter();
      invoice.appReferanceCode ??= number;

      notifyListeners();
      // var url = '/Sales/GetCode?transactions_type=Invoice';

      // var result = await Api.getOne(action: url);
      // if (result != null) {
      //   if (result.isSuccess) {
      //     invoice.invoiceCode = result.data;
      //     notifyListeners();
      //   }
      // }
      return invoice.appReferanceCode ?? "";
    } catch (e) {
      print(e);
      return "";
    }
  }

//--------------------------------------------------------------------------
  Future<ResponseResultModel> saveSaleInvoice() async {
    try {
      var url = '/Sales/Manage';
      final db = InvoiceController();

      var mapedInvoice = mapInvoiceDto(invoice, selectedSaleInvoiceProduct);
      // mapedInvoice.paidAmount = 10;
      //TODO
      // if (AppController.isThereConnection == true) {
      //   if (mapedInvoice.customerID == 0) {
      //     // errorSnackBar(
      //     //     message: "يجب مزامنة العميل المضاف اولا من شاشة العملاء");
      //     invoice.customerId = null;
      //     invoice.custoemrName = null;

      //     return ResponseResultModel(
      //         isSuccess: false,
      //         message: ["يجب مزامنة العميل المضاف اولا من شاشة العملاء"]);
      //   }
      // }

      mapedInvoice.paidAmount ??= 0.0;

      mapedInvoice.transactionsType = "Invoice";
      mapedInvoice.vATPercent = 0;

      mapedInvoice.entryDateFormated = DateFormat('dd/MM/yyyy', 'en')
          .format(invoice.invoiceDate ?? DateTime.now());

      if (mapedInvoice.iD == null || (mapedInvoice.iD ?? 0) == 0) {
        mapedInvoice.entryDate = DateTime.now();
        mapedInvoice.code = "*";
      }

      if (mapedInvoice.appReferanceCode == null ||
          mapedInvoice.appReferanceCode == "") {
        mapedInvoice.appReferanceCode = await getSaleInvoiceNumber();
      } else {
        if (await CounterGenerator.checkIfSaleInvoiceCounterUsed(
            mapedInvoice.appReferanceCode ?? "")) {
          mapedInvoice.appReferanceCode = await getSaleInvoiceNumber();
        }
      }

      mapedInvoice.vATPercent = 0;

      if (await isThereNetworkConnection() == false) {
        var localId = await db.insertOrUpdateInvoice(SqlLiteInvoiceModel(
                data: jsonEncode(mapedInvoice.toJson()),
                status: getInvoiceSyncStatus(InvoiceSyncStatus.pending),
                id: null,
                localCode: mapedInvoice.appReferanceCode,
                type: getInvoiceTypeName(SalesType.Invoice))
            .toJson());
        var newOrder = initializeInvoice();
        invoice = newOrder;
        selectedSaleInvoiceProduct.clear();
        if (mapedInvoice.iD == 0) {
          await CounterGenerator.setSaleInvoiceCounter();
        }
        notifyListeners();
        lastAddedInvoiceCode = mapedInvoice.appReferanceCode ?? "";
        return ResponseResultModel(
            isSuccess: true, data: InvoiceResponseDto(localId: localId));
      }

      var result = await Api.post(
        action: url,
        body: mapedInvoice.toJson(),
      );
      if (result != null) {
        if (result.isSuccess) {
          if (mapedInvoice.iD == 0) {
            await CounterGenerator.setInvoicesCounterInServer();
            await CounterGenerator.setSaleInvoiceCounter();
          }
          var response = InvoiceResponseDto.fromJson(result.data);
          mapedInvoice.iD = response.id;
          mapedInvoice.code = response.code;

          var localId = await db.insertOrUpdateInvoice(SqlLiteInvoiceModel(
                  data: jsonEncode(mapedInvoice.toJson()),
                  status: getInvoiceSyncStatus(InvoiceSyncStatus.synced),
                  id: null,
                  localCode: mapedInvoice.appReferanceCode,
                  type: getInvoiceTypeName(SalesType.Invoice))
              .toJson());
          response.localId = localId;
          var newOrder = initializeInvoice();
          invoice = newOrder;
          selectedSaleInvoiceProduct.clear();

          // if (mapedInvoice.salesOrderID == null)
          //   await db.saveOrderToEcommerce(mapedInvoice);
          lastAddedInvoiceCode = mapedInvoice.appReferanceCode ?? "";
          notifyListeners();
          return ResponseResultModel(data: response, isSuccess: true);
        }
      }
      return result ?? ResponseResultModel(isSuccess: false);
    } catch (e) {
      print(e);
      return ResponseResultModel(isSuccess: false);
    }
  }

//--------------------------------------------------------------------------
  void notifyListenersFuntion() {
    notifyListeners();
  }

//--------------------------------------------------------------------------
  Future<bool> repeatSaleInvoice() async {
    invoice.id = 0;
    invoice.appReferanceCode =
        await CounterGenerator.getSaleInvoiceNextCounter();
    var result = await saveSaleInvoice();
    invoice = InvoiceDto();
    selectedSaleInvoiceProduct = [];
    return result.isSuccess;
  }

//--------------------------------------------------------------------------
//--------------------------------------------------------------------------
  Future<ResponseResultModel> returnLastSaleInvoice() async {
    try {
      if (lastAddedInvoiceCode.isEmpty) {
        return ResponseResultModel(isSuccess: false);
      }

      final db = InvoiceController();
      final returnDb = ReturnInvoiceController();
      var invoice =
          await db.getInoviceByCode(invoiceLocalCode: lastAddedInvoiceCode);
      if (invoice == null) {
        return ResponseResultModel(isSuccess: false);
      }

      returnDb.setSaleInvoiceToReturnModel(invoice);
      var result = await returnDb.saveReturnInvoice();
      if (result.isSuccess) {
        lastAddedInvoiceCode = "";
        notifyListeners();
        return ResponseResultModel(
            isSuccess: true, data: result.data.aPPReferanceCode);
      }
      return ResponseResultModel(isSuccess: false);
    } catch (e) {
      print(e);
      return ResponseResultModel(isSuccess: false);
    }
  }

  //--------------------------------------------------------------------------
  Future<ResponseResultModel> returnSaleInvoiceByCode(
      String invoiceCode) async {
    try {
      if (invoiceCode.isEmpty) {
        return ResponseResultModel(isSuccess: false);
      }

      final db = InvoiceController();
      final returnDb = ReturnInvoiceController();
      var invoice = await db.getInoviceByCode(invoiceLocalCode: invoiceCode);
      if (invoice == null) {
        return ResponseResultModel(isSuccess: false);
      }

      returnDb.setSaleInvoiceToReturnModel(invoice);
      var result = await returnDb.saveReturnInvoice();
      if (result.isSuccess) {
        notifyListeners();
        return ResponseResultModel(
            isSuccess: true, data: result.data.aPPReferanceCode);
      }
      return ResponseResultModel(isSuccess: false);
    } catch (e) {
      print(e);
      return ResponseResultModel(isSuccess: false);
    }
  }

//--------------------------------------------------------------------------
  void setPaymentValue(double value) {
    activeOrder.invoice.paymentValue = value;
    notifyListeners();
  }
}
