import 'dart:convert';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/network/api.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/invoice_controller.dart';
import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/product_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/helpers/invoice_counter.dart';
import 'package:inventory_application/helpers/purchase_invoice_counter.dart';
import 'package:inventory_application/main.dart';
import 'package:inventory_application/models/dto/purchase_invoice/purchase_invoice_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/dto/response.dart';
import 'package:inventory_application/models/dto/invoice/invoice_response_dto.dart';
import 'package:inventory_application/models/model/purchase_invoice_model.dart';
import 'package:provider/provider.dart';

String getPurchaseTypeString(PurchaseType type) {
  switch (type) {
    case PurchaseType.Order:
      return 'Order';
    case PurchaseType.ProformaInvoice:
      return 'ProformaInvoice';

    case PurchaseType.Invoice:
      return 'Invoice';
    case PurchaseType.ReturnInvoice:
      return 'ReturnInvoice';
    default:
      return 'Invoice';
  }
}

String getPurchaseInvoiceSyncStatus(PurchaseInvoiceSyncStatus status) {
  switch (status) {
    case PurchaseInvoiceSyncStatus.pending:
      return 'pending';
    case PurchaseInvoiceSyncStatus.syncing:
      return 'syncing';
    case PurchaseInvoiceSyncStatus.synced:
      return 'synced';
    default:
      return 'pending';
  }
}

class PurchaseInvoiceOrder {
  final String id;
  PurchaseInvoiceDto invoice;
  List<ProductDTO> products;
  bool isActive;

  PurchaseInvoiceOrder({
    required this.id,
    required this.invoice,
    required this.products,
    this.isActive = false,
  });
}

class PurchaseInvoiceController with ChangeNotifier {
  List<PurchaseInvoiceOrder> _orders = [];
  int _activeOrderIndex = 0;
  String lastAddedInvoiceCode = "";

  List<PurchaseInvoiceOrder> get orders => _orders;
  PurchaseInvoiceOrder get activeOrder => _orders[_activeOrderIndex];
  int get activeOrderIndex => _activeOrderIndex;
  int get orderCount => _orders.length;

  // For backward compatibility
  PurchaseInvoiceDto get invoice => activeOrder.invoice;
  set invoice(PurchaseInvoiceDto value) => activeOrder.invoice = value;

  List<ProductDTO> get selectedPurchaseInvoiceProduct => activeOrder.products;
  set selectedPurchaseInvoiceProduct(List<ProductDTO> value) =>
      activeOrder.products = value;
  final DatabaseHelper _dbHelper = DatabaseHelper();

  PurchaseInvoiceController() {
    _initializeFirstOrder();
  }

  void _initializeFirstOrder() {
    if (_orders.isEmpty) {
      _orders.add(PurchaseInvoiceOrder(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        invoice: initializePurchaseInvoice(),
        products: [],
        isActive: true,
      ));
    }
  }

  PurchaseInvoiceDto initializePurchaseInvoice() {
    return PurchaseInvoiceDto(
      id: 0,
      invoiceDate: DateTime.now(),
      invoiceType: PurchaseType.Invoice,
      invoiceTypeName: getPurchaseTypeString(PurchaseType.Invoice),
      transactionsType: "Invoice",
      currencyId: 101,
      exchangeRate: 1.0,
      discountValue: 0.0,
      paymentValue: 0.0,
      total: 0.0,
      totalAfterDiscount: 0.0,
      totalDiscount: 0.0,
      vATPercent: 0.0,
      purchaseItems: [],
    );
  }

  Future<String> getPurchaseInvoiceNumber() async {
    try {
      var number = await PurchaseCounterGenerator.getNextCounterByType(
          PurchaseType.Invoice);
      invoice.appReferanceCode = number;
      notifyListeners();
      return number;
    } catch (e) {
      print("Error getting purchase invoice number: $e");
      return "PINV${DateTime.now().millisecondsSinceEpoch}";
    }
  }

  void addProductToSelectedList(ProductDTO product) {
    try {
      var existingIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (existingProduct) =>
            existingProduct.id == product.id &&
            existingProduct.virtualProductId == product.virtualProductId,
      );

      if (existingIndex != -1) {
        var existingProduct = selectedPurchaseInvoiceProduct[existingIndex];
        existingProduct.quantity = (existingProduct.quantity ?? 0) + 1;
        selectedPurchaseInvoiceProduct[existingIndex] = existingProduct;
      } else {
        product.quantity = 1;
        selectedPurchaseInvoiceProduct.add(product);
      }

      calculateInvoiceTotal();
      notifyListeners();
    } catch (e) {
      print("Error adding product to purchase invoice: $e");
    }
  }

  void deleteProductFromSelectedList(int productId,
      {String? virtualProductId}) {
    try {
      if (virtualProductId != null) {
        selectedPurchaseInvoiceProduct.removeWhere((product) =>
            product.id == productId &&
            product.virtualProductId == virtualProductId);
      } else {
        selectedPurchaseInvoiceProduct
            .removeWhere((product) => product.id == productId);
      }

      calculateInvoiceTotal();
      notifyListeners();
    } catch (e) {
      print("Error deleting product from purchase invoice: $e");
    }
  }

  void setProductWarehouse(int productId, int warehouseId,
      String? warehouseName, String? virtualProductId) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) => product.id == productId,
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].warehouseId = warehouseId;
        selectedPurchaseInvoiceProduct[productIndex].warehouseName =
            warehouseName;
        notifyListeners();
      }
    } catch (e) {
      print("Error setting product warehouse: $e");
    }
  }

  void calculateInvoiceTotal() {
    try {
      double total = 0.0;
      double productsTotalCount = 0.0;

      for (var product in selectedPurchaseInvoiceProduct) {
        double quantity = product.quantity ?? 0;
        double price = product.price ?? 0;
        total += quantity * price;
        productsTotalCount += quantity;
      }

      invoice.total = total;
      invoice.productsTotalCount = productsTotalCount;

      // Calculate discount
      double discountAmount = 0.0;
      if (invoice.discountValue != null && invoice.discountValue! > 0) {
        discountAmount = invoice.discountValue!;
      }

      invoice.totalDiscount = discountAmount;
      invoice.totalAfterDiscount = total - discountAmount;

      notifyListeners();
    } catch (e) {
      print("Error calculating invoice total: $e");
    }
  }

  void setDiscountValue(double discountValue) {
    invoice.discountValue = discountValue;
    calculateInvoiceTotal();
    notifyListeners();
  }

  void setPaymentValue(double paymentValue) {
    invoice.paymentValue = paymentValue;
    notifyListeners();
  }

  void updateProductPrice(int productId, double price,
      [String? virtualProductId]) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) =>
            product.id == productId &&
            (virtualProductId == null ||
                product.virtualProductId == virtualProductId),
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].price = price;
        selectedPurchaseInvoiceProduct[productIndex].total =
            (selectedPurchaseInvoiceProduct[productIndex].quantity ?? 0) *
                price;
        calculateInvoiceTotal();
        notifyListeners();
      }
    } catch (e) {
      print("Error updating product price: $e");
    }
  }

  void updateProductQuantity(int productId, double quantity,
      [String? virtualProductId]) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) =>
            product.id == productId &&
            (virtualProductId == null ||
                product.virtualProductId == virtualProductId),
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].quantity = quantity;
        selectedPurchaseInvoiceProduct[productIndex].total = quantity *
            (selectedPurchaseInvoiceProduct[productIndex].price ?? 0);
        calculateInvoiceTotal();
        notifyListeners();
      }
    } catch (e) {
      print("Error updating product quantity: $e");
    }
  }

  void updateProductUnit(int productId, ItemPriceDTO unit,
      [String? virtualProductId]) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) =>
            product.id == productId &&
            (virtualProductId == null ||
                product.virtualProductId == virtualProductId),
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].uniteId = unit.unitID;
        selectedPurchaseInvoiceProduct[productIndex].uniteName = unit.unitName;
        selectedPurchaseInvoiceProduct[productIndex].price = unit.salesPrice;
        selectedPurchaseInvoiceProduct[productIndex].total =
            (selectedPurchaseInvoiceProduct[productIndex].quantity ?? 0) *
                (unit.salesPrice ?? 0);
        calculateInvoiceTotal();
        notifyListeners();
      }
    } catch (e) {
      print("Error updating product unit: $e");
    }
  }

  Future<dynamic> getItemByBarcode({String? barcode}) async {
    try {
      if (barcode == null || barcode.isEmpty) {
        return false;
      }

      // Notify that we're starting to process
      notifyListeners();

      // Use getItemByBarcodeWithWriting instead to handle final barcodes
      var result =
          await ProductController.getItemByBarcodeWithWriting(barcode: barcode);
      if (result != null) {
        // If the product has attributes but they haven't been selected yet,
        // and it's not a final barcode, return the product so the UI can show the attribute dialog
        if (result.itemAttributes != null &&
            result.itemAttributes!.isNotEmpty &&
            !result.hasSelectedAttributes) {
          return result; // Return the product so the UI can show the attribute selection dialog
        }

        // First check if we already have this product with the same barcode
        int existingIndex = -1;

        // Handle products with virtual ID (products with attributes)
        if (result.virtualProductId != null) {
          existingIndex = selectedPurchaseInvoiceProduct.indexWhere(
            (element) => element.virtualProductId == result.virtualProductId,
          );
        } else {
          // UPDATED LOGIC: Match by exact barcode for both final and non-final barcodes
          for (int i = 0; i < selectedPurchaseInvoiceProduct.length; i++) {
            var product = selectedPurchaseInvoiceProduct[i];

            // IMPORTANT: For FINAL barcodes, we need to match by exact barcode
            // regardless of hasSelectedAttributes status
            if (product.barcode != null &&
                result.barcode != null &&
                product.barcode!.isNotEmpty &&
                result.barcode!.isNotEmpty &&
                product.barcode == barcode) {
              // For final barcodes (hasSelectedAttributes = true), match by exact barcode
              if (result.hasSelectedAttributes == true &&
                  product.hasSelectedAttributes == true) {
                existingIndex = i;
                break;
              }
              // For non-final barcodes (hasSelectedAttributes = false), also match by exact barcode
              else if (result.hasSelectedAttributes != true &&
                  product.hasSelectedAttributes != true) {
                existingIndex = i;
                break;
              }
            }
          }

          // If no barcode match found, check by product ID (only if both have no barcode)
          if (existingIndex == -1) {
            for (int i = 0; i < selectedPurchaseInvoiceProduct.length; i++) {
              var product = selectedPurchaseInvoiceProduct[i];

              // Skip items that have virtual IDs
              if (product.virtualProductId != null) {
                continue;
              }

              // Only match by ID if both products have no barcode or empty barcode
              bool bothHaveNoBarcode =
                  (result.barcode == null || result.barcode!.isEmpty) &&
                      (product.barcode == null || product.barcode!.isEmpty);

              if (bothHaveNoBarcode &&
                  product.id != null &&
                  result.id != null &&
                  product.id == result.id &&
                  result.hasSelectedAttributes ==
                      product.hasSelectedAttributes) {
                existingIndex = i;
                break;
              }
            }
          }
        }

        // If we found an existing product, increase its quantity
        if (existingIndex >= 0) {
          var existingProduct = selectedPurchaseInvoiceProduct[existingIndex];
          double newQuantity = (existingProduct.quantity ?? 1) + 1;

          // Update the quantity
          selectedPurchaseInvoiceProduct[existingIndex].quantity = newQuantity;

          // Update the total
          double price =
              selectedPurchaseInvoiceProduct[existingIndex].price ?? 0;
          selectedPurchaseInvoiceProduct[existingIndex].total =
              price * newQuantity;

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        } else {
          // IMPORTANT: For products with final barcodes (hasSelectedAttributes = true)
          // that don't have a virtualProductId, generate one to ensure proper identification in the UI
          if (result.virtualProductId == null &&
              result.hasSelectedAttributes == true &&
              result.barcode != null &&
              result.barcode!.isNotEmpty) {
            // Generate a consistent virtual product ID based on barcode for final barcodes
            result.virtualProductId = '${result.id}_final_${result.barcode}';
          }

          // Ensure quantity is at least 1
          result.quantity = result.quantity ?? 1;

          // Calculate total
          result.total = (result.price ?? 0) * result.quantity!;

          // Add to list
          selectedPurchaseInvoiceProduct.add(result);

          // Recalculate invoice total
          calculateInvoiceTotal();

          // Force UI refresh
          notifyListeners();

          // Small delay to ensure UI updates
          await Future.delayed(const Duration(milliseconds: 100));

          // Notify again to ensure UI is updated
          notifyListeners();

          return true;
        }
      }

      return false;
    } catch (e) {
      print("Error in getItemByBarcode: $e");
      return false;
    }
  }

  Future<ResponseResultModel> savePurchaseInvoice() async {
    try {
      if (selectedPurchaseInvoiceProduct.isEmpty) {
        return ResponseResultModel(
          isSuccess: false,
          message: ["لا يمكن حفظ فاتورة شراء فارغة"],
        );
      }

      // Map the invoice data
      var mappedInvoice = PurchaseInvoiceModel(
        iD: invoice.id,
        code: "*",
        // : invoice.appReferanceCode,
        supplierID: invoice.supplierId,
        // : invoice.supplierName,
        supplierInvoiceNo: invoice.supplierInvoiceNo,
        storeID: invoice.warehouseId,
        // warehouseName: invoice.warehouseName,

        // entryDate: DateTime.now(),
        entryDateFormated:
            DateFormat('dd/MM/yyyy', 'en').format(DateTime.now()),
        purchaseType: "Invoice",
        transactionsType: "Invoice",
        notes: invoice.note,

        discountValue: invoice.discountValue ?? 0.0,

        total: invoice.total ?? 0.0,

        vATPercent: invoice.vATPercent ?? 0.0,
        currenyID: invoice.currencyId ?? 101,
        exchangeRate: invoice.exchangeRate ?? 1.0,
        purchaseItems: selectedPurchaseInvoiceProduct
            .map((e) => PurchaseItems(
                  itemID: e.id,
                  storeID: e.warehouseId,
                  quantity: e.quantity ?? 0.0,
                  unitPrice: e.price ?? 0.0,
                  unitID: e.uniteId ?? 0,
                  itemName: e.title,
                  itemCode: e.code,
                  batchNumber: e.batchNumber,
                  serialNumber: e.serialNumber,
                  expirationDate: e.expirationDate,
                  expirationDateFromat: e.expirationDate != null
                      ? DateFormat('dd/MM/yyyy', 'en')
                          .format(e.expirationDate ?? DateTime.now())
                      : null,
                  vATPercent: e.vatPercent,
                  unitCostWithExpencesLC: e.unitCost,
                  unitCostWithExpencesCalculatedLC: e.unitCostWithExpenses,
                  unitPriceLC: e.price,
                  // amountQuantity: e.quantity,
                  // orderedQuantity: e.quantity,
                  rejectedQuantity: 0.0,
                  // remaining: e.quantity,
                  attribute: e.attribute,
                ))
            .toList(),
        costCenterID: invoice.costCenterId,
      );
      if (mappedInvoice.appReferanceCode == null ||
          mappedInvoice.appReferanceCode == "") {
        mappedInvoice.appReferanceCode = await getPurchaseInvoiceNumber();
      } else {
        if (await PurchaseCounterGenerator.checkIfCounterUsed(
            PurchaseType.Invoice, mappedInvoice.appReferanceCode ?? "")) {
          mappedInvoice.appReferanceCode = await getPurchaseInvoiceNumber();
        }
      }
      print(jsonEncode(mappedInvoice.toJson()));

      // Check network connection
      if (await isThereNetworkConnection() == false) {
        // Save locally if no internet

        var localId = await insertOrUpdateInvoice(SqlLiteInvoiceModel(
          data: jsonEncode(mappedInvoice.toJson()),
          status:
              getPurchaseInvoiceSyncStatus(PurchaseInvoiceSyncStatus.pending),
          id: null,
          localCode: mappedInvoice.appReferanceCode,
          type: getPurchaseTypeString(PurchaseType.Invoice),
        ).toJson());

        // Reset invoice
        var newInvoice = initializePurchaseInvoice();
        invoice = newInvoice;
        selectedPurchaseInvoiceProduct.clear();

        if (mappedInvoice.iD == 0) {
          PurchaseCounterGenerator.setCounterByTypeAuto(
              type: PurchaseType.Invoice);
        }

        notifyListeners();
        lastAddedInvoiceCode = mappedInvoice.appReferanceCode ?? "";

        return ResponseResultModel(
          isSuccess: true,
          data: InvoiceResponseDto(localId: localId),
        );
      }
      // mappedInvoice.code = "PINV2500034";
      // Save to server
      var result = await Api.post(
        action: "Purchase/Manage?transactions_type=Invoice",
        body: mappedInvoice.toJson(),
      );

      if (result != null && result.isSuccess) {
        if (mappedInvoice.iD == 0) {
          PurchaseCounterGenerator.setCounterByTypeAuto(
              type: PurchaseType.Invoice);
          PurchaseCounterGenerator.setPurchaseInvoiceCounterInServer();
        }

        var response = InvoiceResponseDto.fromJson(result.data);
        mappedInvoice.iD = response.id;
        mappedInvoice.code = response.code;

        // Save to local database

        var localId = await insertOrUpdateInvoice(SqlLiteInvoiceModel(
          data: jsonEncode(mappedInvoice.toJson()),
          status:
              getPurchaseInvoiceSyncStatus(PurchaseInvoiceSyncStatus.synced),
          id: null,
          localCode: mappedInvoice.appReferanceCode,
          type: getPurchaseTypeString(PurchaseType.Invoice),
        ).toJson());

        response.localId = localId;

        // Reset invoice
        var newInvoice = initializePurchaseInvoice();
        invoice = newInvoice;
        selectedPurchaseInvoiceProduct.clear();

        lastAddedInvoiceCode = mappedInvoice.appReferanceCode ?? "";
        notifyListeners();

        return ResponseResultModel(isSuccess: true, data: response);
      } else {
        return ResponseResultModel(
          isSuccess: false,
          message: result?.message ?? ["فشل في حفظ فاتورة الشراء"],
        );
      }
    } catch (e) {
      print("Error saving purchase invoice: $e");
      return ResponseResultModel(
        isSuccess: false,
        message: ["خطأ في حفظ فاتورة الشراء: $e"],
      );
    }
  }

  // Order management methods
  void createNewOrder() {
    _orders.add(PurchaseInvoiceOrder(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      invoice: initializePurchaseInvoice(),
      products: [],
      isActive: false,
    ));
    notifyListeners();
  }

  void switchToOrder(int index) {
    if (index >= 0 && index < _orders.length) {
      _orders[_activeOrderIndex].isActive = false;
      _activeOrderIndex = index;
      _orders[_activeOrderIndex].isActive = true;
      notifyListeners();
    }
  }

  void deleteOrder(int index) {
    if (_orders.length > 1 && index >= 0 && index < _orders.length) {
      _orders.removeAt(index);
      if (_activeOrderIndex >= _orders.length) {
        _activeOrderIndex = _orders.length - 1;
      }
      _orders[_activeOrderIndex].isActive = true;
      notifyListeners();
    }
  }

  void clearActiveOrder() {
    activeOrder.invoice = initializePurchaseInvoice();
    activeOrder.products.clear();
    notifyListeners();
  }

  // Methods for updating additional purchase-specific fields
  void updateProductBatchNumber(int productId, String batchNumber,
      [String? virtualProductId]) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) =>
            product.id == productId &&
            (virtualProductId == null ||
                product.virtualProductId == virtualProductId),
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].batchNumber = batchNumber;
        notifyListeners();
      }
    } catch (e) {
      print("Error updating product batch number: $e");
    }
  }

  void updateProductSerialNumber(int productId, String serialNumber,
      [String? virtualProductId]) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) =>
            product.id == productId &&
            (virtualProductId == null ||
                product.virtualProductId == virtualProductId),
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].serialNumber =
            serialNumber;
        notifyListeners();
      }
    } catch (e) {
      print("Error updating product serial number: $e");
    }
  }

  void updateProductExpirationDate(int productId, DateTime expirationDate,
      [String? virtualProductId]) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) =>
            product.id == productId &&
            (virtualProductId == null ||
                product.virtualProductId == virtualProductId),
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].expirationDate =
            expirationDate;
        notifyListeners();
      }
    } catch (e) {
      print("Error updating product expiration date: $e");
    }
  }

  void updateProductUnitCost(int productId, double unitCost,
      [String? virtualProductId]) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) =>
            product.id == productId &&
            (virtualProductId == null ||
                product.virtualProductId == virtualProductId),
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].unitCost = unitCost;
        // You might want to recalculate unit cost with expenses here
        // selectedPurchaseInvoiceProduct[productIndex].unitCostWithExpenses =
        //     unitCost + (unitCost * expensePercentage / 100);
        notifyListeners();
      }
    } catch (e) {
      print("Error updating product unit cost: $e");
    }
  }

  void updateProductVatPercent(int productId, double vatPercent,
      [String? virtualProductId]) {
    try {
      var productIndex = selectedPurchaseInvoiceProduct.indexWhere(
        (product) =>
            product.id == productId &&
            (virtualProductId == null ||
                product.virtualProductId == virtualProductId),
      );

      if (productIndex != -1) {
        selectedPurchaseInvoiceProduct[productIndex].vatPercent = vatPercent;
        notifyListeners();
      }
    } catch (e) {
      print("Error updating product VAT percent: $e");
    }
  }

  //---------------------------------------------------------------------------
  // Function to insert an invoice
  Future<int> insertOrUpdateInvoice(Map<String, dynamic> invoice) async {
    var IdToReturn = 0;
    var checkIfExist = await checkIfInvoiceExists(invoice["localCode"]);
    invoice["inseatedUserId"] = AuthController.getUserId();
    invoice["inseatedUserName"] = AuthController.getUserName();

    if (checkIfExist == false) {
      IdToReturn = await _dbHelper.insert('PurchaseInvoice', invoice);
    } else {
      IdToReturn = await updateInvoice(
          invoice["localCode"], json.decode(invoice["data"]));
    }

    return IdToReturn;
  }

  //------------------------------------------------------------------------------
  Future<bool> checkIfInvoiceExists(String invoiceLocalCode) async {
    final db = await DatabaseHelper().database;

    var result = await db.query(
      'PurchaseInvoice',
      columns: ['data'],
      where: 'localCode = ?',
      whereArgs: [invoiceLocalCode],
    );

    return result.isNotEmpty;
  }
  //------------------------------------------------

  // Function to update invoice sync status
  Future<int> updateInvoiceSyncStatus(String status, int id) async {
    final db = await DatabaseHelper().database;
    return await db.update(
      'PurchaseInvoice',
      {'status': status},
      where: 'ID = ?',
      whereArgs: [id],
    );
  }

  //------------------------------------------------
  Future<int> updateInvoice(
      String invoiceLocalCode, Map<String, dynamic> invoice) async {
    final db = await DatabaseHelper().database;

    // Retrieve the existing data for the invoice
    var result = await db.query(
      'PurchaseInvoice',
      columns: ['data'],
      where: 'localCode = ?',
      whereArgs: [invoiceLocalCode],
    );

    if (result.isNotEmpty) {
      // Parse the JSON string to a Map
      Map<String, dynamic> data = jsonDecode(result.first['data'] as String);

      data = invoice;

      // Convert the updated map back to a JSON string
      String updatedDataJson = jsonEncode(data);

      // Update only the data field in the database
      var response = await db.update(
        'PurchaseInvoice',
        {
          'data': updatedDataJson, // Update the data with the new ID
        },
        where: 'localCode = ?',
        whereArgs: [invoiceLocalCode],
      );
      // var sadas = response;
      return response;
    } else {
      // If no record is found, return 0 to indicate failure
      return 0;
    }
  }
}

// For backward compatibility, alias the controller
