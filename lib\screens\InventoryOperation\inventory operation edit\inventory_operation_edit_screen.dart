import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/invoice_sql_lite_model.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/inventoryOperations/inventory_operation_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20edit/widgets/inventory_operation_edit_base_info_widget.dart';
import 'package:inventory_application/screens/InventoryOperation/shared/inventory_operation_no_products_widget.dart';
import 'package:inventory_application/screens/InventoryOperation/shared/inventory_opertaion_bottom_bar_widget.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:inventory_application/screens/shared/add_product_with_selected_attributes_widget.dart';
import 'package:inventory_application/screens/shared/product_attribute_selection_dialog_widget.dart';
import 'package:provider/provider.dart';

class InventoryOperationEditScreen extends StatefulWidget {
  const InventoryOperationEditScreen({super.key, required this.model});
  final InventoryOperationModel model;

  @override
  State<InventoryOperationEditScreen> createState() =>
      _InventoryOperationEditScreenState();
}

class _InventoryOperationEditScreenState
    extends State<InventoryOperationEditScreen> {
  late InventoryOperationModel _editableModel;
  List<ProductDTO> _selectedProducts = [];

  @override
  void initState() {
    super.initState();
    _initializeEditableModel();
  }

  void _initializeEditableModel() {
    // Create a copy of the model for editing
    _editableModel = widget.model.copyWith();

    // Convert inventory operation items to ProductDTO list with correct quantities
    _selectedProducts = mapListInventoryOperationItemToProductDto(
        _editableModel.inventoryOperationItems ?? []);

    // Ensure quantities and prices are correctly set from the original operation items
    for (int i = 0; i < _selectedProducts.length; i++) {
      final originalItem = _editableModel.inventoryOperationItems?[i];
      if (originalItem != null) {
        _selectedProducts[i].quantity = originalItem.quantity ?? 1.0;
        _selectedProducts[i].price = originalItem.unitPrice ?? 0.0;
        _selectedProducts[i].warehouseId = originalItem.storeID;

        // Calculate total for each product
        _selectedProducts[i].total = (_selectedProducts[i].quantity ?? 0) *
            (_selectedProducts[i].price ?? 0);

        // Ensure the product has the correct virtual product ID for attributes
        if (originalItem.hasSelectedAttributes == true) {
          _selectedProducts[i].virtualProductId =
              originalItem.selectedAttribute ?? _selectedProducts[i].title;
          _selectedProducts[i].hasSelectedAttributes = true;
          _selectedProducts[i].selectedOptionIds =
              originalItem.selectedOptionIds;
        }
      }
    }

    // Force UI update to show the correct quantities
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  String _getOperationTitle() {
    final operationType = getOperationTypeFromCode(_editableModel.code ?? '');
    switch (operationType) {
      case InventoryOperationType.Incoming:
        return T("Edit Incoming Operation");
      case InventoryOperationType.Outgoing:
        return T("Edit Outgoing Operation");
      case InventoryOperationType.DamagedExpired:
        return T("Edit Damaged/Expired Operation");
      case InventoryOperationType.ItemsTransfer:
        return T("Edit Transfer Operation");
      case InventoryOperationType.Stocktaking:
        return T("Edit Stocktaking Operation");
      default:
        return T("Edit Operation");
    }
  }

  IconData _getOperationIcon() {
    final operationType = getOperationTypeFromCode(_editableModel.code ?? '');
    switch (operationType) {
      case InventoryOperationType.Incoming:
        return Icons.input;
      case InventoryOperationType.Outgoing:
        return Icons.output;
      case InventoryOperationType.DamagedExpired:
        return Icons.warning;
      case InventoryOperationType.ItemsTransfer:
        return Icons.swap_horiz;
      case InventoryOperationType.Stocktaking:
        return Icons.inventory;
      default:
        return Icons.edit;
    }
  }

  void _addProduct(ProductDTO product) {
    setState(() {
      // Check if product already exists
      final existingIndex = _selectedProducts.indexWhere((p) =>
          p.id == product.id && p.virtualProductId == product.virtualProductId);

      if (existingIndex >= 0) {
        // Update quantity if product exists
        _selectedProducts[existingIndex].quantity =
            (_selectedProducts[existingIndex].quantity ?? 0) +
                (product.quantity ?? 1);
        // Update total for existing product
        _selectedProducts[existingIndex].total =
            (_selectedProducts[existingIndex].quantity ?? 0) *
                (_selectedProducts[existingIndex].price ?? 0);
      } else {
        // Add new product and calculate its total
        product.total = (product.quantity ?? 0) * (product.price ?? 0);
        _selectedProducts.add(product);
      }

      _updateInventoryOperationItems();
    });
  }

  void _removeProduct(int productId, [String? virtualProductId]) {
    setState(() {
      _selectedProducts.removeWhere((p) =>
          p.id == productId &&
          (virtualProductId == null || p.virtualProductId == virtualProductId));

      _updateInventoryOperationItems();
    });
  }

  void _updateProductQuantity(int productId, double quantity,
      [String? virtualProductId]) {
    setState(() {
      final index = _selectedProducts.indexWhere((p) =>
          p.id == productId &&
          (virtualProductId == null || p.virtualProductId == virtualProductId));

      if (index >= 0) {
        _selectedProducts[index].quantity = quantity;
        // Update total for this product
        _selectedProducts[index].total =
            quantity * (_selectedProducts[index].price ?? 0);
        _updateInventoryOperationItems();
      }
    });
  }

  void _updateProductPrice(int productId, double price,
      [String? virtualProductId]) {
    setState(() {
      final index = _selectedProducts.indexWhere((p) =>
          p.id == productId &&
          (virtualProductId == null || p.virtualProductId == virtualProductId));

      if (index >= 0) {
        _selectedProducts[index].price = price;
        // Update total for this product
        _selectedProducts[index].total =
            (_selectedProducts[index].quantity ?? 0) * price;
        _updateInventoryOperationItems();
      }
    });
  }

  void _updateProductUnit(int productId, ItemPriceDTO unit,
      [String? virtualProductId]) {
    setState(() {
      final index = _selectedProducts.indexWhere((p) =>
          p.id == productId &&
          (virtualProductId == null || p.virtualProductId == virtualProductId));

      if (index >= 0) {
        _selectedProducts[index].uniteId = unit.unitID;
        _selectedProducts[index].uniteName = unit.unitName;
        _selectedProducts[index].price = unit.salesPrice;
        // Update total for this product
        _selectedProducts[index].total =
            (_selectedProducts[index].quantity ?? 0) * (unit.salesPrice ?? 0);
        _updateInventoryOperationItems();
      }
    });
  }

  void _updateInventoryOperationItems() {
    _editableModel.inventoryOperationItems = _selectedProducts.map((product) {
      return InventoryOperationItems(
        itemID: product.id,
        itemName: product.title,
        itemCode: product.code,
        quantity: product.quantity,
        unitPrice: product.price,
        unitID: product.uniteId,
        barcode: product.barcode,
        hasSelectedAttributes: product.hasSelectedAttributes,
        selectedOptionIds: product.selectedOptionIds,
        attribute: product.attribute,
        storeID: _editableModel.fromStoreID,
      );
    }).toList();

    // Calculate total
    _editableModel.total = _selectedProducts.fold<double>(
        0.0,
        (sum, product) =>
            sum + ((product.price ?? 0) * (product.quantity ?? 0)));
  }

  Future<void> _handleProductWithAttributes(ProductDTO product) async {
    if (product.itemAttributes != null &&
        product.itemAttributes!.isNotEmpty &&
        !product.hasSelectedAttributes) {
      final selectedOptions = await showDialog<Map<int, ItemAttributeOption>>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) {
          return AttributeSelectionDialog(product: product);
        },
      );

      if (selectedOptions != null) {
        addProductWithSelectedAttributes(
          product: product,
          selectedOptions: selectedOptions,
          currentProducts: _selectedProducts,
          onUpdateExisting: (updatedProduct, index) {
            setState(() {
              _selectedProducts[index] = updatedProduct;
              _updateInventoryOperationItems();
            });
          },
          onAddNew: (newProduct) {
            setState(() {
              _selectedProducts.add(newProduct);
              _updateInventoryOperationItems();
            });
          },
          onAfterChange: () {},
        );
      }
    } else {
      _addProduct(product);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Column(
            children: [
              CommonHeader(
                icon: _getOperationIcon(),
                title: _getOperationTitle(),
              ),

              // Base Info Section
              InventoryOperationEditBaseInfoWidget(
                onSelectWarehouse: () {
                  setState(() {});
                },
                model: _editableModel,
              ),

              // Content Section
              Column(
                children: [
                  // Product Selection Widget
                  InvoiceSelectProductWidget(
                    selectedProducts: _selectedProducts,
                    canAdd: _editableModel.fromStoreID != null,
                    oncannotAddProduct: () {
                      errorSnackBar(
                          message: "يرجى اختيار المستودع", context: context);
                    },
                    onChange: () {
                      setState(() {});
                    },
                    onAddProduct: _handleProductWithAttributes,
                    onRemoveProduct: _removeProduct,
                    onSearchByBarcode: (String barcode) async {
                      // Handle barcode search if needed
                    },
                  ),

                  // Products List
                  if (_selectedProducts.isNotEmpty) ...[
                    Container(
                      width: double.infinity,
                      margin: EdgeInsets.zero,
                      child: InvoiceProductListHeaderWidget(
                        backgroundColor: context.primaryColor,
                        textColor: Colors.white,
                      ),
                    ),
                    ListView.builder(
                      itemCount: _selectedProducts.length,
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      physics: const NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        final product = _selectedProducts[index];
                        return InvoiceListItemsWidget(
                          id: product.id ?? 0,
                          barcode: product.barcode,
                          virtualProductId: product.virtualProductId,
                          selectedInvoiceProduct: _selectedProducts,
                          onChangeWarehouse: (int productId, int warehouseId,
                              String warehouseName,
                              [String? virtualProductId]) {
                            // Update warehouse if needed
                            setState(() {
                              _editableModel.fromStoreID = warehouseId;
                              _editableModel.storeName = warehouseName;
                            });
                          },
                          onDeleteProduct: _removeProduct,
                          onUpdatePrice: _updateProductPrice,
                          onUpdateQuantity: _updateProductQuantity,
                          onUpdateUnit: _updateProductUnit,
                        );
                      },
                    ),
                  ] else ...[
                    // Empty State
                    const InventoryOperationNoProductsWidget(),
                  ],
                ],
              ),
            ],
          ),
        ),
        bottomNavigationBar: InventoryOpertaionBottomBarWidget(
          model: _editableModel,
          products: _selectedProducts,
          onclear: () {
            setState(() {
              _selectedProducts.clear();
              _updateInventoryOperationItems();
            });
          },
          onSave: _saveOperation,
        ),
      ),
    );
  }

  Future<void> _saveOperation() async {
    if (_editableModel.fromStoreID == null) {
      errorSnackBar(message: "يرجى اختيار المستودع");
      return;
    }

    if (_selectedProducts.isEmpty) {
      errorSnackBar(message: "يرجى إضافة منتجات للعملية");
      return;
    }

    pleaseWaitDialog(context: context, isShown: true);

    try {
      final controller =
          Provider.of<InventoryOperationController>(context, listen: false);
      final operationType = getOperationTypeFromCode(_editableModel.code ?? '');
      var success = false;
      if (_editableModel.iD != null && _editableModel.iD != 0) {
        success = await controller.editInventoryOperation(
          model: _editableModel,
          type: operationType?.name ?? '',
        );
      } else {
        var id = await controller.insertOrUpdateInventoryOperation(
          SqlLiteInvoiceModel(
                  data: jsonEncode(_editableModel.toJson()),
                  id: 0,
                  status: "pending",
                  localCode: _editableModel.aPPReferanceCode)
              .toJson(),
        );
        if (id > 0) success = true;
      }

      if (!mounted) return;

      pleaseWaitDialog(context: context, isShown: false);

      if (success) {
        successSnackBar(message: T("Operation updated successfully"));
        Navigator.of(context).pop(true); // Return true to indicate success
      } else {
        errorSnackBar(message: T("Failed to update operation"));
      }
    } catch (e) {
      if (!mounted) return;
      pleaseWaitDialog(context: context, isShown: false);
      errorSnackBar(message: "خطأ في حفظ العملية: $e");
    }
  }
}
