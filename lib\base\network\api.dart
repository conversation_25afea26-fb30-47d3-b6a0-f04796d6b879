// ignore_for_file: avoid_print

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:inventory_application/base/cache/locale_manager.dart';
import 'package:inventory_application/base/constants/enums/locale_keys_enum.dart';
import 'package:inventory_application/base/lang/language_notifier.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/main.dart';
import 'package:inventory_application/models/dto/response.dart';
import 'package:inventory_application/screens/auth/SignIn_screen.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';

class Api {
  static String baseUrl = AppController.getBaseUrlFromShared();
  static Dio api = Dio();

  // static Future<void> initBaseUrl() async {
  //   //SharedPreferences prefs = await SharedPreferences.getInstance();
  //   baseUrl = 'https://terp.pal4it.org/APIs';
  //   initDio();
  // }

  static void initDio() {
    // api.options.baseUrl = baseUrl;
    // print(baseUrl);
    api.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          var token =
              LocaleManager.instance.getStringValue(PreferencesKeys.TOKEN);
          //print(token);
          options.headers['Authorization'] = 'bearer $token';
          options.headers['langid'] =
              LanguageNotifier.languageVal == 'ar' ? '1' : '2';
          return handler.next(options);
        },
        onError: (error, handler) async {
          if ((error.response?.statusCode == 401)) {
            if (AppController.isThereConnection == true) {
              Navigator.of(navigatorKey.currentContext!).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const SignInScreen()),
                (Route<dynamic> route) => false,
              );
              return;
            } else {
              errorSnackBar(message: "لا يوجد اتصال بالانترنت");
              return;
            }

            // return;
          }

          return handler.next(error);
        },
      ),
    );
  }

  // static Future<bool> validateBaseUrl() async {
  //   try {
  //     final response = await api.get('/health-check');
  //     return response.statusCode == 200;
  //   } catch (e) {
  //     print('Base URL validation failed: $e');
  //     return false;
  //   }
  // }

  //************************************************************************** */
  static Future<ResponseResultModel?> getOne({required String action}) async {
    try {
      var url = '$baseUrl/$action';
      var result = await api.get(url);
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print(e);
      return null;
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> post(
      {required String action, dynamic body}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.post(url, data: body);
      var statusCode = result.statusCode ?? 0;
      // ignore: curly_braces_in_flow_control_structures
      if (statusCode >= 200 && statusCode <= 300) if (result.data == null) {
        return ResponseResultModel(isSuccess: true);
      }
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print('Error While Post Request, $url');
      print(e);
      return ResponseResultModel(isSuccess: false);
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> getWithBody(
      {required String action, dynamic body}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.get(url, data: body);
      var statusCode = result.statusCode ?? 0;
      // ignore: curly_braces_in_flow_control_structures
      if (statusCode >= 200 && statusCode <= 300) if (result.data == null) {
        return ResponseResultModel(isSuccess: true);
      }
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print('Error While Post Request, $url');
      print(e);
      return ResponseResultModel(isSuccess: false);
    }
  }

  //************************************************************************** */
  static Future<dynamic> postList(
      {required String action, required dynamic body}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.post(url, data: body);
      return result.data;
    } catch (e) {
      print('Error While Post Request, $url');
      print(e);
      return null;
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> put(
      {required String action, dynamic body}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.put(url, data: body);
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print('Error While put Request, $url');
      return null;
    }
  }

  //************************************************************************** */
  static Future<ResponseResultModel?> delete({required String action}) async {
    var url = '$baseUrl/$action';
    try {
      var result = await api.delete(url);
      var data = ResponseResultModel.fromJson(result.data);
      return data;
    } catch (e) {
      print('Error While delete Request, $url');
      return null;
    }
  }

  //************************************************************************** */

  // static Future<bool> refreshToken() async {
  //   final pref = await SharedPreferences.getInstance();

  //   final refreshToken = pref.getString('refreshToken');
  //   final userId = pref.getString('userId');
  //   final response = await api.post('$baseUrl/auth/RefreshToken', data: {
  //     'userId': userId,
  //     'refreshToken': refreshToken,
  //   });

  //   var result = ResponseResultModel.fromJson(response.data);
  //   if (result.isSuccess) {

  //      LocaleManager.instance.setStringValue(PreferencesKeys.TOKEN,result.data['token'])

  //     pref.setString('refreshToken', result.data['refreshToken']);
  //     return true;
  //   } else {
  //     // refresh token is wrong
  //     //AuthController.logOut();
  //     return false;
  //   }
  // }
}
