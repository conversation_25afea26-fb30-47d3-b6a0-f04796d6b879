class CardDetailReportDTO {
  final CardInfoDTO? cardInfo;
  final BalanceSummaryDTO? balanceSummary;
  final List<PaymentDetailDTO>? paymentDetails;
  final List<RechargeDetailDTO>? rechargeDetails;
  final List<DeviceSpendingSummaryDTO>? deviceSpendingSummary;
  final FinancialSummaryCardDTO? financialSummary;

  CardDetailReportDTO({
    this.cardInfo,
    this.balanceSummary,
    this.paymentDetails,
    this.rechargeDetails,
    this.deviceSpendingSummary,
    this.financialSummary,
  });

  factory CardDetailReportDTO.fromJson(Map<String, dynamic> json) {
    return CardDetailReportDTO(
      cardInfo: json['cardInfo'] != null
          ? CardInfoDTO.fromJson(json['cardInfo'])
          : null,
      balanceSummary: json['balanceSummary'] != null
          ? BalanceSummaryDTO.fromJson(json['balanceSummary'])
          : null,
      paymentDetails: json['paymentDetails'] != null
          ? (json['paymentDetails'] as List)
              .map((item) => PaymentDetailDTO.fromJson(item))
              .toList()
          : null,
      rechargeDetails: json['rechargeDetails'] != null
          ? (json['rechargeDetails'] as List)
              .map((item) => RechargeDetailDTO.fromJson(item))
              .toList()
          : null,
      deviceSpendingSummary: json['deviceSpendingSummary'] != null
          ? (json['deviceSpendingSummary'] as List)
              .map((item) => DeviceSpendingSummaryDTO.fromJson(item))
              .toList()
          : null,
      financialSummary: json['financialSummary'] != null
          ? FinancialSummaryCardDTO.fromJson(json['financialSummary'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'cardInfo': cardInfo?.toJson(),
      'balanceSummary': balanceSummary?.toJson(),
      'paymentDetails': paymentDetails?.map((item) => item.toJson()).toList(),
      'rechargeDetails': rechargeDetails?.map((item) => item.toJson()).toList(),
      'deviceSpendingSummary':
          deviceSpendingSummary?.map((item) => item.toJson()).toList(),
      'financialSummary': financialSummary?.toJson(),
    };
  }
}

class CardInfoDTO {
  final int? memberId;
  final String? cardNumber;
  final String? memberName;
  final String? mobile;
  final DateTime? memberSince;
  final double? currentBalance;
  final int? memberState;

  CardInfoDTO({
    this.memberId,
    this.cardNumber,
    this.memberName,
    this.mobile,
    this.memberSince,
    this.currentBalance,
    this.memberState,
  });

  factory CardInfoDTO.fromJson(Map<String, dynamic> json) {
    return CardInfoDTO(
      memberId: json['memberId'],
      cardNumber: json['cardNumber'],
      memberName: json['memberName'],
      mobile: json['mobile'],
      memberSince: json['memberSince'] != null
          ? DateTime.tryParse(json['memberSince'])
          : null,
      currentBalance: json['currentBalance']?.toDouble(),
      memberState: json['memberState'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'memberId': memberId,
      'cardNumber': cardNumber,
      'memberName': memberName,
      'mobile': mobile,
      'memberSince': memberSince?.toIso8601String(),
      'currentBalance': currentBalance,
      'memberState': memberState,
    };
  }
}

class BalanceSummaryDTO {
  final double? currentBalance;
  final double? totalRechargeAmount;
  final double? totalSpentAmount;
  final double? totalGiftAmount;
  final int? totalRechargeTransactions;
  final int? totalSpentTransactions;

  BalanceSummaryDTO({
    this.currentBalance,
    this.totalRechargeAmount,
    this.totalSpentAmount,
    this.totalGiftAmount,
    this.totalRechargeTransactions,
    this.totalSpentTransactions,
  });

  factory BalanceSummaryDTO.fromJson(Map<String, dynamic> json) {
    return BalanceSummaryDTO(
      currentBalance: json['currentBalance']?.toDouble(),
      totalRechargeAmount: json['totalRechargeAmount']?.toDouble(),
      totalSpentAmount: json['totalSpentAmount']?.toDouble(),
      totalGiftAmount: json['totalGiftAmount']?.toDouble(),
      totalRechargeTransactions: json['totalRechargeTransactions'],
      totalSpentTransactions: json['totalSpentTransactions'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentBalance': currentBalance,
      'totalRechargeAmount': totalRechargeAmount,
      'totalSpentAmount': totalSpentAmount,
      'totalGiftAmount': totalGiftAmount,
      'totalRechargeTransactions': totalRechargeTransactions,
      'totalSpentTransactions': totalSpentTransactions,
    };
  }
}

class PaymentDetailDTO {
  final int? paymentId;
  final DateTime? paymentDate;
  final int? deviceId;
  final String? deviceName;
  final String? deviceCode;
  final double? paymentAmount;
  final double? balanceAfter;
  final int? paymentType;
  final String? companyName;
  final String? storeName;
  final bool? isRefund;

  PaymentDetailDTO({
    this.paymentId,
    this.paymentDate,
    this.deviceId,
    this.deviceName,
    this.deviceCode,
    this.paymentAmount,
    this.balanceAfter,
    this.paymentType,
    this.companyName,
    this.storeName,
    this.isRefund,
  });

  factory PaymentDetailDTO.fromJson(Map<String, dynamic> json) {
    return PaymentDetailDTO(
      paymentId: json['paymentId'],
      paymentDate: json['paymentDate'] != null
          ? DateTime.tryParse(json['paymentDate'])
          : null,
      deviceId: json['deviceId'],
      deviceName: json['deviceName'],
      deviceCode: json['deviceCode'],
      paymentAmount: json['paymentAmount']?.toDouble(),
      balanceAfter: json['balanceAfter']?.toDouble(),
      paymentType: json['paymentType'],
      companyName: json['companyName'],
      storeName: json['storeName'],
      isRefund: json['isRefund'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'paymentId': paymentId,
      'paymentDate': paymentDate?.toIso8601String(),
      'deviceId': deviceId,
      'deviceName': deviceName,
      'deviceCode': deviceCode,
      'paymentAmount': paymentAmount,
      'balanceAfter': balanceAfter,
      'paymentType': paymentType,
      'companyName': companyName,
      'storeName': storeName,
      'isRefund': isRefund,
    };
  }
}

class RechargeDetailDTO {
  final int? rechargeId;
  final DateTime? date;
  final int? companyId;
  final String? companyName;
  final int? memberId;
  final String? cardNumber;
  final String? memberName;
  final double? rechargeAmount;
  final double? giftAmount;
  final double? totalAmount;
  final double? totalFees;
  final double? balanceAfter;
  final int? adminId;
  final String? adminName;
  final int? rechargeType;
  final int? paymentTypeId;

  RechargeDetailDTO({
    this.rechargeId,
    this.date,
    this.companyId,
    this.companyName,
    this.memberId,
    this.cardNumber,
    this.memberName,
    this.rechargeAmount,
    this.giftAmount,
    this.totalAmount,
    this.totalFees,
    this.balanceAfter,
    this.adminId,
    this.adminName,
    this.rechargeType,
    this.paymentTypeId,
  });

  factory RechargeDetailDTO.fromJson(Map<String, dynamic> json) {
    return RechargeDetailDTO(
      rechargeId: json['rechargeId'],
      date: json['date'] != null ? DateTime.tryParse(json['date']) : null,
      companyId: json['companyId'],
      companyName: json['companyName'],
      memberId: json['memberId'],
      cardNumber: json['cardNumber'],
      memberName: json['memberName'],
      rechargeAmount: json['rechargeAmount']?.toDouble(),
      giftAmount: json['giftAmount']?.toDouble(),
      totalAmount: json['totalAmount']?.toDouble(),
      totalFees: json['totalFees']?.toDouble(),
      balanceAfter: json['balanceAfter']?.toDouble(),
      adminId: json['adminId'],
      adminName: json['adminName'],
      rechargeType: json['rechargeType'],
      paymentTypeId: json['paymentTypeId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'rechargeId': rechargeId,
      'date': date?.toIso8601String(),
      'companyId': companyId,
      'companyName': companyName,
      'memberId': memberId,
      'cardNumber': cardNumber,
      'memberName': memberName,
      'rechargeAmount': rechargeAmount,
      'giftAmount': giftAmount,
      'totalAmount': totalAmount,
      'totalFees': totalFees,
      'balanceAfter': balanceAfter,
      'adminId': adminId,
      'adminName': adminName,
      'rechargeType': rechargeType,
      'paymentTypeId': paymentTypeId,
    };
  }
}

class DeviceSpendingSummaryDTO {
  final int? deviceId;
  final String? deviceName;
  final String? deviceCode;
  final double? totalSpent;
  final int? transactionCount;
  final DateTime? lastUsed;
  final double? averageSpent;
  final String? deviceCategory;

  DeviceSpendingSummaryDTO({
    this.deviceId,
    this.deviceName,
    this.deviceCode,
    this.totalSpent,
    this.transactionCount,
    this.lastUsed,
    this.averageSpent,
    this.deviceCategory,
  });

  factory DeviceSpendingSummaryDTO.fromJson(Map<String, dynamic> json) {
    return DeviceSpendingSummaryDTO(
      deviceId: json['deviceId'],
      deviceName: json['deviceName'],
      deviceCode: json['deviceCode'],
      totalSpent: json['totalSpent']?.toDouble(),
      transactionCount: json['transactionCount'],
      lastUsed:
          json['lastUsed'] != null ? DateTime.tryParse(json['lastUsed']) : null,
      averageSpent: json['averageSpent']?.toDouble(),
      deviceCategory: json['deviceCategory'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'deviceId': deviceId,
      'deviceName': deviceName,
      'deviceCode': deviceCode,
      'totalSpent': totalSpent,
      'transactionCount': transactionCount,
      'lastUsed': lastUsed?.toIso8601String(),
      'averageSpent': averageSpent,
      'deviceCategory': deviceCategory,
    };
  }
}

class FinancialSummaryCardDTO {
  final double? totalIncome;
  final double? totalSpending;
  final double? netBalance;
  final double? totalTransactionValue;
  final int? totalTransactions;
  final DateTime? firstTransaction;
  final DateTime? lastTransaction;
  final int? daysActive;
  final double? averageTransactionValue;

  FinancialSummaryCardDTO({
    this.totalIncome,
    this.totalSpending,
    this.netBalance,
    this.totalTransactionValue,
    this.totalTransactions,
    this.firstTransaction,
    this.lastTransaction,
    this.daysActive,
    this.averageTransactionValue,
  });

  factory FinancialSummaryCardDTO.fromJson(Map<String, dynamic> json) {
    return FinancialSummaryCardDTO(
      totalIncome: json['totalIncome']?.toDouble(),
      totalSpending: json['totalSpending']?.toDouble(),
      netBalance: json['netBalance']?.toDouble(),
      totalTransactionValue: json['totalTransactionValue']?.toDouble(),
      totalTransactions: json['totalTransactions'],
      firstTransaction: json['firstTransaction'] != null
          ? DateTime.tryParse(json['firstTransaction'])
          : null,
      lastTransaction: json['lastTransaction'] != null
          ? DateTime.tryParse(json['lastTransaction'])
          : null,
      daysActive: json['daysActive'],
      averageTransactionValue: json['averageTransactionValue']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalIncome': totalIncome,
      'totalSpending': totalSpending,
      'netBalance': netBalance,
      'totalTransactionValue': totalTransactionValue,
      'totalTransactions': totalTransactions,
      'firstTransaction': firstTransaction?.toIso8601String(),
      'lastTransaction': lastTransaction?.toIso8601String(),
      'daysActive': daysActive,
      'averageTransactionValue': averageTransactionValue,
    };
  }
}
