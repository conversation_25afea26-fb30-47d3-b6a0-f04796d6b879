import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';

class InvoiceProductListHeaderWidget extends StatelessWidget {
  const InvoiceProductListHeaderWidget(
      {super.key, required this.backgroundColor, required this.textColor});
  final Color backgroundColor;
  final Color textColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 40,
      decoration: BoxDecoration(
        color: backgroundColor,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Container(
              alignment: Alignment.center,
              child: Text(
                T("Quantity"),
                style: TextStyle(color: textColor, fontSize: 14),
              ),
            ),
          ),
          // Expanded(
          //   flex: 1,
          //   child: Container(
          //     alignment: Alignment.center,
          //     child: Text(
          //       T("Unit"),
          //       style: TextStyle(color: textColor, fontSize: 14),
          //     ),
          //   ),
          // ),
          Expanded(
            flex: 2,
            child: Container(
              alignment: Alignment.center,
              child: Text(
                T("Price"),
                style: TextStyle(color: textColor, fontSize: 14),
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Container(
              alignment: Alignment.center,
              child: Text(
                T("Total"),
                style: TextStyle(color: textColor, fontSize: 14),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
