class ProductTransactionDetailDTO {
  final String? date;
  final String? transactionType;
  final String? store;
  final int? branchId;
  final double? quantity;
  final bool? isIncoming;
  final String? attributes;

  ProductTransactionDetailDTO({
    this.date,
    this.transactionType,
    this.store,
    this.branchId,
    this.quantity,
    this.isIncoming,
    this.attributes,
  });

  factory ProductTransactionDetailDTO.fromJson(Map<String, dynamic> json) {
    return ProductTransactionDetailDTO(
      date: json['Date'],
      transactionType: json['TransactionType'],
      store: json['Store'],
      branchId: json['BranchId'],
      quantity: (json['Quantity'] as num?)?.toDouble(),
      isIncoming: json['IsIncoming'],
      attributes: json['Attributes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Date': date,
      'TransactionType': transactionType,
      'Store': store,
      'BranchId': branchId,
      'Quantity': quantity,
      'IsIncoming': isIncoming,
      'Attributes': attributes,
    };
  }

  String get transactionTypeDisplayName {
    switch (transactionType) {
      case 'SalesInvoice':
        return 'فاتورة مبيعات';
      case 'SalesReturnInvoice':
        return 'مرتجع مبيعات';
      case 'PurchaseInvoice':
        return 'فاتورة مشتريات';
      case 'PurchaseReturnInvoice':
        return 'مرتجع مشتريات';
      case 'Incoming':
        return 'وارد';
      case 'Outgoing':
        return 'صادر';
      case 'Transfer':
        return 'تحويل';
      case 'Damaged':
        return 'تالف';
      case 'Adjustment':
        return 'تسوية';
      default:
        return transactionType ?? 'غير محدد';
    }
  }
}

class ProductTransactionTotalDTO {
  final String? attributes;
  final int? branchId;
  final double? incomingTotal;
  final double? outgoingTotal;
  final double? available;

  ProductTransactionTotalDTO({
    this.attributes,
    this.branchId,
    this.incomingTotal,
    this.outgoingTotal,
    this.available,
  });

  factory ProductTransactionTotalDTO.fromJson(Map<String, dynamic> json) {
    return ProductTransactionTotalDTO(
      attributes: json['Attributes'],
      branchId: json['BranchId'],
      incomingTotal: (json['IncomingTotal'] as num?)?.toDouble(),
      outgoingTotal: (json['OutgoingTotal'] as num?)?.toDouble(),
      available: (json['Available'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Attributes': attributes,
      'BranchId': branchId,
      'IncomingTotal': incomingTotal,
      'OutgoingTotal': outgoingTotal,
      'Available': available,
    };
  }
}

class ProductTransactionReportDTO {
  final List<ProductTransactionDetailDTO>? details;
  final List<ProductTransactionTotalDTO>? totals;

  ProductTransactionReportDTO({
    this.details,
    this.totals,
  });

  factory ProductTransactionReportDTO.fromJson(Map<String, dynamic> json) {
    return ProductTransactionReportDTO(
      details: (json['Details'] as List?)
          ?.map((item) => ProductTransactionDetailDTO.fromJson(item))
          .toList(),
      totals: (json['Totals'] as List?)
          ?.map((item) => ProductTransactionTotalDTO.fromJson(item))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Details': details?.map((item) => item.toJson()).toList(),
      'Totals': totals?.map((item) => item.toJson()).toList(),
    };
  }
}
