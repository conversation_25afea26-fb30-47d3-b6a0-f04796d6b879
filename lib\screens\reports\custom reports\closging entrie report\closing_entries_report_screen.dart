import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/reports/custom%20reports/closging%20entrie%20report/widgets/closing_entries_report_admin_transactions_widget.dart';
import 'package:inventory_application/screens/reports/custom%20reports/closging%20entrie%20report/widgets/closing_entries_report_card_history_widget.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/controllers/accounting_report_helper_controller.dart';
import 'package:inventory_application/models/dto/reports/closing_entries_report_dto.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/components/common_combobox.dart';
import 'package:excel/excel.dart' as XL;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:io';

class ClosingEntriesReportScreen extends StatefulWidget {
  const ClosingEntriesReportScreen({Key? key}) : super(key: key);

  @override
  State<ClosingEntriesReportScreen> createState() =>
      _ClosingEntriesReportScreenState();
}

class _ClosingEntriesReportScreenState
    extends State<ClosingEntriesReportScreen> {
  DateTime? _fromDate;
  DateTime? _toDate;
  int? _selectedCompanyId;
  int? _selectedAdminId;

  // Time filters
  TimeOfDay? _fromTime;
  TimeOfDay? _toTime;
  bool _enableTimeFilter = false;

  // Sorting variables
  int _sortColumnIndex = 0;
  bool _sortAscending = true;
  List<RechargeTransactionDTO> _sortedRecharges = [];

  // Pagination variables
  int _currentPage = 0;
  int _itemsPerPage = 10;
  final List<int> _itemsPerPageOptions = [
    10,
    20,
    50,
    100,
    -1
  ]; // -1 means show all
  List<RechargeTransactionDTO> _paginatedRecharges = [];

  // Controllers for date fields
  final TextEditingController _fromDateController = TextEditingController();
  final TextEditingController _toDateController = TextEditingController();
  final TextEditingController _cardNumberController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _fromDate = DateTime.now().subtract(const Duration(days: 1));
    _toDate = DateTime.now();
    _selectedAdminId = 0; // Default to "All Admins"

    // تحميل التقرير الأولي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadReport();
    });
  }

  Future<void> _loadReport() async {
    final controller = context.read<AccountingReportHelperController>();

    // Apply time filters if enabled
    DateTime? adjustedFromDate = _fromDate;
    DateTime? adjustedToDate = _toDate;

    if (_enableTimeFilter && _fromDate != null && _toDate != null) {
      if (_fromTime != null) {
        adjustedFromDate = DateTime(
          _fromDate!.year,
          _fromDate!.month,
          _fromDate!.day,
          _fromTime!.hour,
          _fromTime!.minute,
        );
      }

      if (_toTime != null) {
        adjustedToDate = DateTime(
          _toDate!.year,
          _toDate!.month,
          _toDate!.day,
          _toTime!.hour,
          _toTime!.minute,
        );
      }
    }

    await controller.generateClosingEntriesReport(
      fromDate: adjustedFromDate,
      toDate: adjustedToDate,
      companyId: _selectedCompanyId,
      adminId:
          _selectedAdminId == 0 ? null : _selectedAdminId, // 0 means all admins
      cardNumber: _cardNumberController.text.trim().isEmpty
          ? null
          : _cardNumberController.text.trim(),
    );

    // Reset pagination to first page when loading new data
    _currentPage = 0;

    // Sort recharges after loading
    _sortRecharges(controller.getRechargeTransactions());
  }

  void _sortRecharges(List<RechargeTransactionDTO>? recharges) {
    if (recharges == null) {
      _sortedRecharges = [];
      _updatePagination();
      return;
    }

    _sortedRecharges = List.from(recharges);

    switch (_sortColumnIndex) {
      case 0: // Date
        _sortedRecharges.sort((a, b) {
          final dateA = DateTime.tryParse(a.date ?? '') ?? DateTime(1900);
          final dateB = DateTime.tryParse(b.date ?? '') ?? DateTime(1900);
          return _sortAscending
              ? dateA.compareTo(dateB)
              : dateB.compareTo(dateA);
        });
        break;
      case 1: // Card Number
        _sortedRecharges.sort((a, b) {
          final cardA = a.cardNumber ?? '';
          final cardB = b.cardNumber ?? '';
          return _sortAscending
              ? cardA.compareTo(cardB)
              : cardB.compareTo(cardA);
        });
        break;
      case 2: // Member Name
        _sortedRecharges.sort((a, b) {
          final memberA = a.memberName ?? '';
          final memberB = b.memberName ?? '';
          return _sortAscending
              ? memberA.compareTo(memberB)
              : memberB.compareTo(memberA);
        });
        break;
      case 3: // Admin ID
        _sortedRecharges.sort((a, b) {
          final adminA = a.adminId ?? 0;
          final adminB = b.adminId ?? 0;
          return _sortAscending
              ? adminA.compareTo(adminB)
              : adminB.compareTo(adminA);
        });
        break;
      case 4: // Amount
        _sortedRecharges.sort((a, b) {
          final amountA = a.totalAmount ?? 0;
          final amountB = b.totalAmount ?? 0;
          return _sortAscending
              ? amountA.compareTo(amountB)
              : amountB.compareTo(amountA);
        });
      case 5: // Member Name
        _sortedRecharges.sort((a, b) {
          final memberA = a.paymentTypeId ?? 0;
          final memberB = b.paymentTypeId ?? 0;
          return _sortAscending
              ? memberA.compareTo(memberB)
              : memberB.compareTo(memberA);
        });
        break;
    }

    _updatePagination();
  }

  // Update pagination after sorting or filtering
  void _updatePagination() {
    if (_itemsPerPage == -1) {
      // Show all items
      _paginatedRecharges = List.from(_sortedRecharges);
    } else {
      // Calculate pagination
      final totalItems = _sortedRecharges.length;
      final totalPages = (totalItems / _itemsPerPage).ceil();

      // Reset current page if it's out of bounds
      if (_currentPage >= totalPages && totalPages > 0) {
        _currentPage = totalPages - 1;
      } else if (_currentPage < 0) {
        _currentPage = 0;
      }

      final startIndex = _currentPage * _itemsPerPage;
      final endIndex = (startIndex + _itemsPerPage).clamp(0, totalItems);

      _paginatedRecharges = _sortedRecharges.sublist(startIndex, endIndex);
    }
  }

  // Change items per page
  void _changeItemsPerPage(int newItemsPerPage) {
    setState(() {
      _itemsPerPage = newItemsPerPage;
      _currentPage = 0; // Reset to first page
      _updatePagination();
    });
  }

  // Go to next page
  void _nextPage() {
    if (_itemsPerPage == -1) return; // No pagination when showing all

    final totalPages = (_sortedRecharges.length / _itemsPerPage).ceil();
    if (_currentPage < totalPages - 1) {
      setState(() {
        _currentPage++;
        _updatePagination();
      });
    }
  }

  // Go to previous page
  void _previousPage() {
    if (_itemsPerPage == -1) return; // No pagination when showing all

    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
        _updatePagination();
      });
    }
  }

  // Go to specific page
  void _goToPage(int page) {
    if (_itemsPerPage == -1) return; // No pagination when showing all

    final totalPages = (_sortedRecharges.length / _itemsPerPage).ceil();
    if (page >= 0 && page < totalPages) {
      setState(() {
        _currentPage = page;
        _updatePagination();
      });
    }
  }

  // Get total pages count
  int get _totalPages {
    if (_itemsPerPage == -1) return 1;
    return (_sortedRecharges.length / _itemsPerPage).ceil();
  }

  void _onSort(int columnIndex, bool ascending) {
    setState(() {
      _sortColumnIndex = columnIndex;
      _sortAscending = ascending;
      _sortRecharges(context
          .read<AccountingReportHelperController>()
          .getRechargeTransactions());
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(T('تقرير قيود الإقفال')),
        backgroundColor: Colors.blue[700],
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text(
                      'تقرير شامل لجميع معاملات الشحن مع إمكانية التصدير والفلترة'),
                  duration: Duration(seconds: 3),
                ),
              );
            },
          ),
        ],
      ),
      body: Consumer<AccountingReportHelperController>(
        builder: (context, controller, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // قسم الفلاتر
                _buildFiltersSection(controller),
                const SizedBox(height: 24),

                // قسم الملخص المالي
                if (controller.closingEntriesReport != null) ...[
                  _buildFinancialSummarySection(controller),
                  const SizedBox(height: 24),

                  // قسم الملخصات التفصيلية
                  _buildDetailedSummariesSection(controller),
                  const SizedBox(height: 24),

                  // قسم تفاصيل المعاملات
                  _buildTransactionDetailsSection(controller),
                ],

                // شاشة التحميل
                if (controller.isLoadingClosingEntries)
                  const Center(
                    child: CircularProgressIndicator(),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFiltersSection(AccountingReportHelperController controller) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.filter_alt,
                  color: Colors.blue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  T('الفلاتر'),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // فلتر التاريخ
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, true),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            _fromDate != null
                                ? controller.formatDate(_fromDate)
                                : 'من تاريخ',
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: InkWell(
                    onTap: () => _selectDate(context, false),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.calendar_today, size: 16),
                          const SizedBox(width: 8),
                          Text(
                            _toDate != null
                                ? controller.formatDate(_toDate)
                                : 'إلى تاريخ',
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // فلتر الوقت
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Switch(
                      value: _enableTimeFilter,
                      activeColor: Colors.orange,
                      onChanged: (value) {
                        setState(() {
                          _enableTimeFilter = value;
                          if (!value) {
                            _fromTime = null;
                            _toTime = null;
                          } else {
                            // Set default times if enabled
                            _fromTime ??= const TimeOfDay(hour: 0, minute: 0);
                            _toTime ??= const TimeOfDay(hour: 23, minute: 59);
                          }
                        });
                      },
                    ),
                    const SizedBox(width: 8),
                    Text(
                      T('تفعيل فلترة الوقت'),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                if (_enableTimeFilter) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectTime(context, true),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.orange),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.orange.withOpacity(0.1),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.access_time,
                                    size: 16, color: Colors.orange),
                                const SizedBox(width: 8),
                                Text(
                                  _fromTime != null
                                      ? 'من ${_formatTime(_fromTime)}'
                                      : 'من ساعة',
                                  style: const TextStyle(
                                    color: Colors.orange,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectTime(context, false),
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.orange),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.orange.withOpacity(0.1),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.access_time,
                                    size: 16, color: Colors.orange),
                                const SizedBox(width: 8),
                                Text(
                                  _toTime != null
                                      ? 'إلى ${_formatTime(_toTime)}'
                                      : 'إلى ساعة',
                                  style: const TextStyle(
                                    color: Colors.orange,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.blue.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(Icons.info_outline,
                            size: 16, color: Colors.blue[700]),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            T('🕐 فلترة دقيقة بالساعة والدقيقة للحصول على نتائج أكثر تحديداً'),
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: 16),

            // فلتر الادمن
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'اختر الادمن:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                controller.isLoadingAdmins
                    ? Container(
                        height: 50,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.blue),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Center(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 16,
                                height: 16,
                                child:
                                    CircularProgressIndicator(strokeWidth: 2),
                              ),
                              SizedBox(width: 8),
                              Text('جاري تحميل الادمنز...'),
                            ],
                          ),
                        ),
                      )
                    : MyComboBox(
                        caption: controller.getSelectedAdminName(),
                        labelText: 'الادمن',
                        selectedValue: _selectedAdminId,
                        modalTitle: 'اختيار الادمن',
                        data: controller.admins,
                        onSelect: (int id, String name) {
                          setState(() {
                            _selectedAdminId = id;
                          });
                        },
                        isShowLabel: true,
                        height: 50,
                        onRefresh: () {
                          controller.getAllAdmins();
                        },
                      ),
              ],
            ),
            const SizedBox(height: 16),

            // فلتر رقم البطاقة
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'رقم البطاقة:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
                TextField(
                  controller: _cardNumberController,
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    hintText: 'أدخل رقم البطاقة للفلترة (اختياري)',
                    prefixIcon: const Icon(Icons.credit_card),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10),
                      borderSide:
                          const BorderSide(color: Colors.blue, width: 2),
                    ),
                    suffixIcon: _cardNumberController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              setState(() {
                                _cardNumberController.clear();
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    setState(() {}); // لتحديث suffixIcon
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),

            // أزرار التحكم
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: _loadReport,
                  icon: Icon(_enableTimeFilter ? Icons.schedule : Icons.search),
                  label: Text(_enableTimeFilter ? 'بحث بالوقت' : 'بحث'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor:
                        _enableTimeFilter ? Colors.orange : Colors.blue,
                    foregroundColor: Colors.white,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    setState(() {
                      _fromDate =
                          DateTime.now().subtract(const Duration(days: 30));
                      _toDate = DateTime.now();
                      _selectedCompanyId = null;
                      _selectedAdminId = 0; // Reset to "All Admins"
                      _cardNumberController.clear(); // Clear card number
                      _enableTimeFilter = false; // Reset time filter
                      _fromTime = null;
                      _toTime = null;

                      // Reset pagination
                      _currentPage = 0;
                      _itemsPerPage = 10;
                      _sortedRecharges.clear();
                      _paginatedRecharges.clear();
                    });
                    controller.resetFilters();
                  },
                  icon: const Icon(Icons.refresh),
                  label: const Text('إعادة تعيين'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.grey,
                    foregroundColor: Colors.white,
                  ),
                ),
                if (_sortedRecharges.isNotEmpty)
                  ElevatedButton.icon(
                    onPressed: _exportToExcel,
                    icon: const Icon(Icons.file_download),
                    label: const Text('تصدير Excel'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                if (_sortedRecharges.isNotEmpty)
                  ElevatedButton.icon(
                    onPressed: () async {
                      var confirmation = await showConfirmDialog(
                        title: 'إنشاء قيد اقفال',
                        content: 'هل تريد إنشاء قيد اقفال للفترة المحددة؟',
                        confirmText: 'إنشاء',
                        backText: 'إلغاء',
                      );
                      if (confirmation == true) {
                        if (controller
                                .getFinancialSummary()
                                ?.rechargeSummary
                                ?.totalAmount ==
                            0) {
                          errorSnackBar(
                              message: 'لا يوجد بيانات للفترة المحددة');
                          return;
                        }
                        var result = await controller
                            .createCLosingEntryJournalForSkyLand(
                          controller.getFinancialSummary()?.rechargeSummary ??
                              RechargeSummaryDTO(),
                          _fromDate ?? DateTime.now(),
                        );
                        if (result) {
                          successSnackBar(
                              message: 'تم إنشاء قيد الإقفال بنجاح');
                        } else {
                          errorSnackBar(
                              message: 'حدث خطأ أثناء إنشاء قيد الإقفال');
                        }
                      }
                    },
                    icon: const Icon(Icons.task_outlined),
                    label: const Text('إنشاء قيد اقفال'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFinancialSummarySection(
      AccountingReportHelperController controller) {
    final summary = controller.getFinancialSummary();
    if (summary == null) return const SizedBox.shrink();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: Colors.green,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'الملخص المالي',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
              ],
            ),

            // عرض معلومات الفلترة المطبقة
            if (_fromDate != null || _toDate != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withOpacity(0.3)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.date_range,
                            size: 16, color: Colors.blue[700]),
                        const SizedBox(width: 6),
                        Text(
                          T('فترة التقرير:'),
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            color: Colors.blue[700],
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 6),
                    Text(
                      '${controller.formatDate(_fromDate)} - ${controller.formatDate(_toDate)}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 13,
                      ),
                    ),
                    if (_enableTimeFilter &&
                        (_fromTime != null || _toTime != null)) ...[
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          Icon(Icons.access_time,
                              size: 14, color: Colors.orange[700]),
                          const SizedBox(width: 4),
                          Text(
                            'الوقت: ${_formatTime(_fromTime) ?? '00:00'} - ${_formatTime(_toTime) ?? '23:59'}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange[700],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],

            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'اجمالي المبلغ',
                    controller.formatNumber(controller.getTotalRecive()),
                    controller.formatNumber(controller.getTotalReciveCard()),
                    controller.formatNumber(controller.getTotalReciveCash()),
                    Colors.orange,
                    Icons.monetization_on,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي الشحن',
                    controller
                        .formatNumber(controller.getTotalRechargeAmount()),
                    controller
                        .formatNumber(controller.getTotalRechargeAmountCard()),
                    controller
                        .formatNumber(controller.getTotalRechargeAmountCash()),
                    Colors.blue,
                    Icons.account_balance_wallet,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'رسوم الكروت',
                    controller.formatNumber(controller.getTotalFeesAmount()),
                    controller
                        .formatNumber(controller.getTotalFeesAmountCard()),
                    controller
                        .formatNumber(controller.getTotalFeesAmountCash()),
                    Colors.green,
                    Icons.shopping_cart,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildSummaryCard(
                    'رسوم الاباء',
                    (controller
                        .formatNumber(controller.getTotalParentFeeAmount())),
                    controller
                        .formatNumber(controller.getTotalParentFeeAmountCard()),
                    controller
                        .formatNumber(controller.getTotalParentFeeAmountCash()),
                    Colors.purple,
                    Icons.family_restroom,
                  ),
                ),
                // const SizedBox(width: 16),
                // Expanded(
                //   child: _buildSummaryCard(
                //     'اجمالي الهدايا',
                //     controller.formatNumber(controller.getGiftsAmount()),
                //     Colors.purple,
                //     Icons.account_balance,
                //   ),
                // ),
              ],
            ),
            // const SizedBox(height: 16),
            // Row(
            //   children: [
            //     Expanded(
            //       child: _buildSummaryCard(
            //         'الرصيد الحالي',
            //         controller.formatNumber(controller.getNetAmount() -
            //             controller.getTotalRechargeAmount()),
            //         Colors.indigo,
            //         Icons.account_balance,
            //       ),
            //     ),
            //   ],
            // ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, String valueCard,
      String valueCash, Color color, IconData icon) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: color.withOpacity(0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // Gradient overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      color.withOpacity(0.05),
                      Colors.transparent,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
              ),
            ),
            // Main content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with icon and title
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: color.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: color.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          icon,
                          color: color,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          title,
                          style: TextStyle(
                            color: const Color(0xFF2D3748),
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Main value
                  Text(
                    value,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: color,
                      letterSpacing: 0.5,
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Payment methods breakdown
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.grey.shade200,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        // Cards payment
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.blue.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.credit_card,
                                color: Colors.blue,
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'بطاقة:',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              valueCard,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.blue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        // Cash payment
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(6),
                              decoration: BoxDecoration(
                                color: Colors.green.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.payments,
                                color: Colors.green,
                                size: 16,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'نقداً:',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey.shade600,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const Spacer(),
                            Text(
                              valueCash,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedSummariesSection(
      AccountingReportHelperController controller) {
    final companySummaries = controller.getCompanySummaries();
    final adminSummaries = controller.getAdminSummaries();

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.pie_chart,
                  color: Colors.indigo,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'الملخصات التفصيلية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.indigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // ملخص حسب الشركة
            if (companySummaries != null && companySummaries.isNotEmpty) ...[
              const Text(
                'حسب الشركة:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...companySummaries
                  .map((company) => _buildCompanySummaryTile(company)),
              const SizedBox(height: 16),
            ],

            // ملخص حسب المدير
            if (adminSummaries != null && adminSummaries.isNotEmpty) ...[
              const Text(
                'حسب المدير:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...adminSummaries.map((admin) => _buildAdminSummaryTile(admin)),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCompanySummaryTile(CompanySummaryDTO company) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  company.companyName ?? 'غير محدد',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                    'عدد المعاملات: ${context.read<AccountingReportHelperController>().formatCount(company.transactionCount)}'),
                Text(
                    'عدد الأعضاء: ${context.read<AccountingReportHelperController>().formatCount(company.memberCount)}'),
              ],
            ),
          ),
          Text(
            context
                .read<AccountingReportHelperController>()
                .formatNumber(company.totalRecharge),
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAdminSummaryTile(AdminSummaryDTO admin) {
    return GestureDetector(
      onTap: () => _showAdminTransactionsDialog(admin),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    admin.adminName ?? 'غير محدد',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  Text('الحساب: ${admin.adminAccount ?? 'غير محدد'}'),
                  Text(
                      'عدد المعاملات: ${context.read<AccountingReportHelperController>().formatCount(admin.transactionCount)}'),
                ],
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  context
                      .read<AccountingReportHelperController>()
                      .formatNumber(admin.totalAmount),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.green,
                  ),
                ),
                Row(
                  children: [
                    const Text('بطاقة:'),
                    const SizedBox(width: 4),
                    Text(
                      context
                          .read<AccountingReportHelperController>()
                          .formatNumber(admin.totalAmountCard),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    const Text('نقداً:'),
                    const SizedBox(width: 4),
                    Text(
                      context
                          .read<AccountingReportHelperController>()
                          .formatNumber(admin.totalAmountCash),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.touch_app,
                      size: 16,
                      color: Colors.green.withOpacity(0.7),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'اضغط للتفاصيل',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green.withOpacity(0.7),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Show admin transactions dialog
  void _showAdminTransactionsDialog(AdminSummaryDTO admin) {
    showDialog(
      context: context,
      builder: (context) => AdminTransactionsDialog(
        admin: admin,
        fromDate: _fromDate,
        toDate: _toDate,
      ),
    );
  }

  // Show card history dialog
  void _showCardHistoryDialog(String cardNumber) {
    showDialog(
      context: context,
      builder: (context) => CardHistoryDialog(
        cardNumber: cardNumber,
      ),
    );
  }

  Widget _buildTransactionDetailsSection(
      AccountingReportHelperController controller) {
    final recharges = controller.getRechargeTransactions();
    final sales = controller.getSalesTransactions();

    // Sort recharges when they change
    if (recharges != null && _sortedRecharges.isEmpty) {
      _sortRecharges(recharges);
    }

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: Colors.red,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل المعاملات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (_sortedRecharges.isNotEmpty) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'معاملات الشحن:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _itemsPerPage == -1
                            ? 'عرض جميع المعاملات (${_sortedRecharges.length})'
                            : 'عرض ${_paginatedRecharges.length} من ${_sortedRecharges.length} معاملة • الصفحة ${_currentPage + 1} من ${_totalPages}',
                        style: const TextStyle(
                          fontSize: 12,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  ElevatedButton.icon(
                    onPressed: _exportToExcel,
                    icon: const Icon(Icons.download, size: 16),
                    label: const Text('تصدير Excel'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      textStyle: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: _buildRechargeDataTable(),
              ),
            ] else if (controller.getRechargeTransactions() != null &&
                controller.getRechargeTransactions()!.isEmpty) ...[
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(20.0),
                  child: Text(
                    'لا توجد معاملات شحن في الفترة المحددة',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRechargeDataTable() {
    return Column(
      children: [
        // Pagination Controls - Top
        _buildPaginationControls(),
        const SizedBox(height: 8),

        // Legend for sorting
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Text(
            'انقر على رأس العمود للترتيب • انقر على معاملة لعرض تاريخ الكارت',
            style: TextStyle(
              fontSize: 12,
              fontStyle: FontStyle.italic,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        const SizedBox(height: 8),

        // Data Table
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Container(
            width: MediaQuery.of(context).size.width -
                64, // Adjust for container padding
            child: DataTable(
              sortColumnIndex: _sortColumnIndex,
              sortAscending: _sortAscending,
              columnSpacing: 8,
              horizontalMargin: 4,
              headingRowColor:
                  MaterialStateProperty.all(Colors.blue.withOpacity(0.1)),
              columns: [
                DataColumn(
                  label: const Text(
                    'التاريخ',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'رقم البطاقة',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'اسم العضو',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'اسم الادمن',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'المبلغ',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  numeric: true,
                  onSort: _onSort,
                ),
                DataColumn(
                  label: const Text(
                    'نوع الدفع',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  numeric: false,
                  onSort: _onSort,
                ),
              ],
              rows: _paginatedRecharges.map((recharge) {
                final bool hasValidCardNumber = recharge.cardNumber != null &&
                    recharge.cardNumber!.trim().isNotEmpty;

                return DataRow(
                  cells: [
                    DataCell(
                      Text(
                        "".myDateFormatter(DateTime.parse(recharge.date ?? '')),
                        style: const TextStyle(fontSize: 12),
                      ),
                      onTap: hasValidCardNumber
                          ? () => _showCardHistoryDialog(recharge.cardNumber!)
                          : null,
                    ),
                    DataCell(
                      Row(
                        children: [
                          Text(
                            recharge.cardNumber != null &&
                                    recharge.cardNumber!.isNotEmpty
                                ? recharge.cardNumber!
                                : 'زائر',
                            style: TextStyle(
                              fontSize: 12,
                              color: hasValidCardNumber
                                  ? Colors.blue
                                  : Colors.grey[600],
                              fontWeight: hasValidCardNumber
                                  ? FontWeight.w500
                                  : FontWeight.normal,
                            ),
                          ),
                          if (hasValidCardNumber) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.launch,
                              size: 12,
                              color: Colors.blue.withOpacity(0.7),
                            ),
                          ],
                        ],
                      ),
                      onTap: hasValidCardNumber
                          ? () => _showCardHistoryDialog(recharge.cardNumber!)
                          : null,
                    ),
                    DataCell(
                      Text(
                        recharge.memberName != null &&
                                recharge.memberName!.isNotEmpty
                            ? recharge.memberName!
                            : 'غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                      onTap: hasValidCardNumber
                          ? () => _showCardHistoryDialog(recharge.cardNumber!)
                          : null,
                    ),
                    DataCell(
                      Text(
                        recharge.adminName?.toString() ?? 'غير محدد',
                        style: const TextStyle(fontSize: 12),
                      ),
                      onTap: hasValidCardNumber
                          ? () => _showCardHistoryDialog(recharge.cardNumber!)
                          : null,
                    ),
                    DataCell(
                      Text(
                        context
                            .read<AccountingReportHelperController>()
                            .formatNumber(recharge.totalAmount),
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                      onTap: hasValidCardNumber
                          ? () => _showCardHistoryDialog(recharge.cardNumber!)
                          : null,
                    ),
                    DataCell(
                      Text(
                        recharge.paymentTypeId == 1 ? 'نقدا' : 'بطاقة',
                        style: const TextStyle(fontSize: 12),
                      ),
                      onTap: hasValidCardNumber
                          ? () => _showCardHistoryDialog(recharge.cardNumber!)
                          : null,
                    ),
                  ],
                );
              }).toList(),
            ),
          ),
        ),

        // Pagination Controls - Bottom
        const SizedBox(height: 8),
        _buildPaginationControls(),
      ],
    );
  }

  // Build pagination controls widget
  Widget _buildPaginationControls() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        children: [
          // Items per page selection and info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Items per page dropdown
              Row(
                children: [
                  const Text(
                    'عرض:',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(6),
                      color: Colors.white,
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<int>(
                        value: _itemsPerPage,
                        items: _itemsPerPageOptions.map((int value) {
                          return DropdownMenuItem<int>(
                            value: value,
                            child: Text(
                              value == -1 ? 'الكل' : value.toString(),
                              style: const TextStyle(fontSize: 13),
                            ),
                          );
                        }).toList(),
                        onChanged: (int? newValue) {
                          if (newValue != null) {
                            _changeItemsPerPage(newValue);
                          }
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'عنصر لكل صفحة',
                    style: TextStyle(fontSize: 13, color: Colors.grey),
                  ),
                ],
              ),

              // Pagination info
              Text(
                _itemsPerPage == -1
                    ? 'عرض جميع العناصر (${_sortedRecharges.length})'
                    : 'عرض ${(_currentPage * _itemsPerPage) + 1} - ${((_currentPage + 1) * _itemsPerPage).clamp(0, _sortedRecharges.length)} من ${_sortedRecharges.length}',
                style: const TextStyle(
                  fontSize: 13,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Navigation controls (only show if pagination is enabled)
          if (_itemsPerPage != -1 && _totalPages > 1) ...[
            const SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Previous button
                IconButton(
                  onPressed: _currentPage > 0 ? _previousPage : null,
                  icon: const Icon(Icons.chevron_right), // Right arrow for RTL
                  tooltip: 'الصفحة السابقة',
                  style: IconButton.styleFrom(
                    backgroundColor: _currentPage > 0
                        ? Colors.blue.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    foregroundColor:
                        _currentPage > 0 ? Colors.blue : Colors.grey,
                  ),
                ),

                const SizedBox(width: 16),

                // Page numbers
                ..._buildPageNumbers(),

                const SizedBox(width: 16),

                // Next button
                IconButton(
                  onPressed: _currentPage < _totalPages - 1 ? _nextPage : null,
                  icon: const Icon(Icons.chevron_left), // Left arrow for RTL
                  tooltip: 'الصفحة التالية',
                  style: IconButton.styleFrom(
                    backgroundColor: _currentPage < _totalPages - 1
                        ? Colors.blue.withOpacity(0.1)
                        : Colors.grey.withOpacity(0.1),
                    foregroundColor: _currentPage < _totalPages - 1
                        ? Colors.blue
                        : Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  // Build page number buttons
  List<Widget> _buildPageNumbers() {
    List<Widget> pageButtons = [];

    // Show max 5 page numbers centered around current page
    int startPage =
        (_currentPage - 2).clamp(0, _totalPages - 5).clamp(0, _currentPage);
    int endPage = (startPage + 4).clamp(_currentPage, _totalPages - 1);

    // Adjust start if we're near the end
    if (endPage - startPage < 4) {
      startPage = (endPage - 4).clamp(0, startPage);
    }

    // First page + ellipsis if needed
    if (startPage > 0) {
      pageButtons.add(_buildPageButton(0));
      if (startPage > 1) {
        pageButtons.add(const Padding(
          padding: EdgeInsets.symmetric(horizontal: 4),
          child: Text('...', style: TextStyle(color: Colors.grey)),
        ));
      }
    }

    // Page numbers
    for (int i = startPage; i <= endPage; i++) {
      pageButtons.add(_buildPageButton(i));
    }

    // Ellipsis + last page if needed
    if (endPage < _totalPages - 1) {
      if (endPage < _totalPages - 2) {
        pageButtons.add(const Padding(
          padding: EdgeInsets.symmetric(horizontal: 4),
          child: Text('...', style: TextStyle(color: Colors.grey)),
        ));
      }
      pageButtons.add(_buildPageButton(_totalPages - 1));
    }

    return pageButtons;
  }

  // Build individual page button
  Widget _buildPageButton(int pageIndex) {
    final isCurrentPage = pageIndex == _currentPage;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 2),
      child: InkWell(
        onTap: () => _goToPage(pageIndex),
        borderRadius: BorderRadius.circular(6),
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(
            color: isCurrentPage ? Colors.blue : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: isCurrentPage ? Colors.blue : Colors.grey.shade300,
            ),
          ),
          child: Center(
            child: Text(
              '${pageIndex + 1}',
              style: TextStyle(
                color: isCurrentPage ? Colors.white : Colors.grey.shade700,
                fontWeight: isCurrentPage ? FontWeight.bold : FontWeight.normal,
                fontSize: 13,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _selectDate(BuildContext context, bool isFromDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      locale: const Locale('en'),
      initialDate: isFromDate
          ? (_fromDate ?? DateTime.now())
          : (_toDate ?? DateTime.now()),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null) {
      setState(() {
        if (isFromDate) {
          _fromDate = picked;
        } else {
          _toDate = picked;
        }
      });
    }
  }

  Future<void> _selectTime(BuildContext context, bool isFromTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isFromTime
          ? (_fromTime ?? TimeOfDay.now())
          : (_toTime ?? TimeOfDay.now()),
      builder: (BuildContext context, Widget? child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        if (isFromTime) {
          _fromTime = picked;
        } else {
          _toTime = picked;
        }
      });
    }
  }

  String _formatTime(TimeOfDay? time) {
    if (time == null) return '';
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  // دالة تصدير البيانات إلى Excel
  Future<void> _exportToExcel() async {
    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('جاري تصدير البيانات...'),
            ],
          ),
        );
      },
    );

    try {
      if (_sortedRecharges.isEmpty) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لا توجد بيانات للتصدير'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // طلب الصلاحيات
      var status = await Permission.storage.request();
      if (!status.isGranted) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('يرجى منح صلاحية الوصول للتخزين'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // إنشاء ملف Excel
      var excel = XL.Excel.createExcel();
      XL.Sheet sheet = excel['تقرير قيود الإقفال'];
      final controller = context.read<AccountingReportHelperController>();

      int currentRow = 0;

      // إضافة عنوان التقرير
      var titleCell = sheet.cell(
          XL.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      titleCell.value = XL.TextCellValue('تقرير قيود الإقفال');
      titleCell.cellStyle = XL.CellStyle(
        bold: true,
        fontSize: 18,
        horizontalAlign: XL.HorizontalAlign.Center,
        fontColorHex: XL.ExcelColor.blue700,
      );
      currentRow++;

      // إضافة معلومات الفترة
      var periodCell = sheet.cell(
          XL.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      String fromDateStr = _fromDate?.toString().split(' ')[0] ?? 'غير محدد';
      String toDateStr = _toDate?.toString().split(' ')[0] ?? 'غير محدد';

      String filterInfo = 'الفترة: من $fromDateStr إلى $toDateStr';
      if (_selectedAdminId != null && _selectedAdminId != 0) {
        filterInfo += ' - المشرف: ${controller.getSelectedAdminName()}';
      }
      if (_cardNumberController.text.trim().isNotEmpty) {
        filterInfo += ' - رقم البطاقة: ${_cardNumberController.text.trim()}';
      }
      if (_enableTimeFilter && (_fromTime != null || _toTime != null)) {
        filterInfo +=
            ' - الوقت: ${_formatTime(_fromTime) ?? '00:00'} - ${_formatTime(_toTime) ?? '23:59'}';
      }

      periodCell.value = XL.TextCellValue(filterInfo);
      periodCell.cellStyle = XL.CellStyle(
        fontSize: 12,
        fontColorHex: XL.ExcelColor.blue600,
      );
      currentRow++;

      // إضافة تاريخ إنشاء التقرير
      var dateCell = sheet.cell(
          XL.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      dateCell.value = XL.TextCellValue(
          'تاريخ إنشاء التقرير: ${DateTime.now().toString().split('.')[0]}');
      dateCell.cellStyle = XL.CellStyle(
        fontSize: 10,
        fontColorHex: XL.ExcelColor.grey600,
      );
      currentRow += 2;

      // قسم الملخص المالي
      var summaryTitleCell = sheet.cell(
          XL.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      summaryTitleCell.value = XL.TextCellValue('الملخص المالي');
      summaryTitleCell.cellStyle = XL.CellStyle(
        bold: true,
        fontSize: 16,
        backgroundColorHex: XL.ExcelColor.grey300,
        horizontalAlign: XL.HorizontalAlign.Center,
        fontColorHex: XL.ExcelColor.black,
      );
      currentRow++;

      // إضافة المجاميع المالية
      // حساب مجاميع طرق الدفع للملخص المالي
      double totalCardAmount = 0;
      double totalCashAmount = 0;

      for (var transaction in _sortedRecharges) {
        if (transaction.paymentTypeId == 1) {
          // نقد
          totalCashAmount += transaction.totalAmount ?? 0;
        } else {
          // كرت
          totalCardAmount += transaction.totalAmount ?? 0;
        }
      }

      List<Map<String, String>> financialSummary = [
        {
          'البيان': 'إجمالي المبلغ',
          'القيمة': controller.formatNumber(controller.getTotalRecive()),
        },
        {
          'البيان': '├─ دفع بطاقة',
          'القيمة': controller.formatNumber(totalCardAmount),
        },
        {
          'البيان': '└─ دفع نقداً',
          'القيمة': controller.formatNumber(totalCashAmount),
        },
        {
          'البيان': '',
          'القيمة': '',
        },
        {
          'البيان': 'إجمالي الشحن',
          'القيمة':
              controller.formatNumber(controller.getTotalRechargeAmount()),
        },
        {
          'البيان': 'رسوم الكروت',
          'القيمة': controller.formatNumber(controller.getTotalFeesAmount()),
        },
        {
          'البيان': 'رسوم دخول الآباء',
          'القيمة': controller.formatNumber(controller.getTotalRecive() -
              controller.getTotalRechargeAmount() -
              controller.getTotalFeesAmount()),
        },
      ];

      // رؤوس جدول الملخص المالي
      List<String> summaryHeaders = ['البيان', 'القيمة'];
      for (int i = 0; i < summaryHeaders.length; i++) {
        var headerCell = sheet.cell(XL.CellIndex.indexByColumnRow(
            columnIndex: i, rowIndex: currentRow));
        headerCell.value = XL.TextCellValue(summaryHeaders[i]);
        headerCell.cellStyle = XL.CellStyle(
          bold: true,
          backgroundColorHex: XL.ExcelColor.amber400,
          horizontalAlign: XL.HorizontalAlign.Center,
          fontColorHex: XL.ExcelColor.black,
        );
      }
      currentRow++;

      // بيانات الملخص المالي
      for (int i = 0; i < financialSummary.length; i++) {
        var summary = financialSummary[i];

        // تخطي الصفوف الفارغة
        if (summary['البيان']!.isEmpty && summary['القيمة']!.isEmpty) {
          currentRow++;
          continue;
        }

        var statementCell = sheet.cell(XL.CellIndex.indexByColumnRow(
            columnIndex: 0, rowIndex: currentRow));
        statementCell.value = XL.TextCellValue(summary['البيان']!);

        var valueCell = sheet.cell(XL.CellIndex.indexByColumnRow(
            columnIndex: 1, rowIndex: currentRow));
        valueCell.value = XL.TextCellValue(summary['القيمة']!);

        // تنسيق مختلف للعناصر الفرعية (التي تبدأ بـ ├─ أو └─)
        bool isSubItem = summary['البيان']!.startsWith('├─') ||
            summary['البيان']!.startsWith('└─');
        bool isMainTotal = summary['البيان']! == 'إجمالي المبلغ';

        if (isMainTotal) {
          // تنسيق خاص للإجمالي الرئيسي
          statementCell.cellStyle = XL.CellStyle(
            bold: true,
            horizontalAlign: XL.HorizontalAlign.Right,
            fontSize: 14,
            fontColorHex: XL.ExcelColor.blue700,
          );
          valueCell.cellStyle = XL.CellStyle(
            bold: true,
            horizontalAlign: XL.HorizontalAlign.Center,
            fontSize: 14,
            fontColorHex: XL.ExcelColor.blue700,
          );
        } else if (isSubItem) {
          // تنسيق للعناصر الفرعية
          statementCell.cellStyle = XL.CellStyle(
            bold: false,
            horizontalAlign: XL.HorizontalAlign.Right,
            fontSize: 12,
            fontColorHex: summary['البيان']!.contains('بطاقة')
                ? XL.ExcelColor.blue600
                : XL.ExcelColor.orange600,
          );
          valueCell.cellStyle = XL.CellStyle(
            bold: true,
            horizontalAlign: XL.HorizontalAlign.Center,
            fontSize: 12,
            fontColorHex: summary['البيان']!.contains('بطاقة')
                ? XL.ExcelColor.blue600
                : XL.ExcelColor.orange600,
          );
        } else {
          // تنسيق عادي
          statementCell.cellStyle = XL.CellStyle(
            bold: true,
            horizontalAlign: XL.HorizontalAlign.Right,
          );
          valueCell.cellStyle = XL.CellStyle(
            bold: true,
            horizontalAlign: XL.HorizontalAlign.Center,
            fontColorHex: XL.ExcelColor.blue700,
          );
        }

        currentRow++;
      }
      currentRow++;

      // قسم ملخص طرق الدفع
      var paymentSummaryTitleCell = sheet.cell(
          XL.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      paymentSummaryTitleCell.value = XL.TextCellValue('ملخص طرق الدفع');
      paymentSummaryTitleCell.cellStyle = XL.CellStyle(
        bold: true,
        fontSize: 16,
        backgroundColorHex: XL.ExcelColor.grey300,
        horizontalAlign: XL.HorizontalAlign.Center,
        fontColorHex: XL.ExcelColor.black,
      );
      currentRow++;

      // حساب مجاميع طرق الدفع
      double totalCard = 0;
      double totalCash = 0;
      int cardTransactions = 0;
      int cashTransactions = 0;

      for (var transaction in _sortedRecharges) {
        if (transaction.paymentTypeId == 1) {
          // نقد
          totalCash += transaction.totalAmount ?? 0;
          cashTransactions++;
        } else {
          // كرت
          totalCard += transaction.totalAmount ?? 0;
          cardTransactions++;
        }
      }

      // جدول ملخص طرق الدفع
      List<String> paymentHeaders = [
        'طريقة الدفع',
        'عدد المعاملات',
        'إجمالي المبلغ',
        'النسبة المئوية'
      ];
      for (int i = 0; i < paymentHeaders.length; i++) {
        var headerCell = sheet.cell(XL.CellIndex.indexByColumnRow(
            columnIndex: i, rowIndex: currentRow));
        headerCell.value = XL.TextCellValue(paymentHeaders[i]);
        headerCell.cellStyle = XL.CellStyle(
          bold: true,
          backgroundColorHex: XL.ExcelColor.amber400,
          horizontalAlign: XL.HorizontalAlign.Center,
          fontColorHex: XL.ExcelColor.black,
        );
      }
      currentRow++;

      // بيانات طرق الدفع
      double totalPayments = totalCard + totalCash;
      int totalTransactionCount = cardTransactions + cashTransactions;

      List<Map<String, String>> paymentData = [
        {
          'طريقة الدفع': 'دفع بطاقة',
          'عدد المعاملات': controller.formatCount(cardTransactions),
          'إجمالي المبلغ': controller.formatNumber(totalCard),
          'النسبة المئوية': totalPayments > 0
              ? '${((totalCard / totalPayments) * 100).toStringAsFixed(1)}%'
              : '0%',
        },
        {
          'طريقة الدفع': 'دفع نقداً',
          'عدد المعاملات': controller.formatCount(cashTransactions),
          'إجمالي المبلغ': controller.formatNumber(totalCash),
          'النسبة المئوية': totalPayments > 0
              ? '${((totalCash / totalPayments) * 100).toStringAsFixed(1)}%'
              : '0%',
        },
        {
          'طريقة الدفع': 'المجموع',
          'عدد المعاملات': controller.formatCount(totalTransactionCount),
          'إجمالي المبلغ': controller.formatNumber(totalPayments),
          'النسبة المئوية': '100%',
        },
      ];

      for (int i = 0; i < paymentData.length; i++) {
        var data = paymentData[i];
        bool isTotal = i == paymentData.length - 1;

        for (int colIndex = 0; colIndex < paymentHeaders.length; colIndex++) {
          var cell = sheet.cell(XL.CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: currentRow,
          ));

          String key = paymentHeaders[colIndex];
          cell.value = XL.TextCellValue(data[key]!);

          if (isTotal) {
            // تنسيق خاص لصف المجموع
            cell.cellStyle = XL.CellStyle(
              bold: true,
              backgroundColorHex: XL.ExcelColor.blue200,
              horizontalAlign: XL.HorizontalAlign.Center,
              fontColorHex: XL.ExcelColor.blue700,
            );
          } else {
            // تنسيق عادي
            cell.cellStyle = XL.CellStyle(
              horizontalAlign: colIndex == 0
                  ? XL.HorizontalAlign.Right
                  : XL.HorizontalAlign.Center,
              fontColorHex:
                  i == 0 ? XL.ExcelColor.blue700 : XL.ExcelColor.orange700,
              bold: colIndex > 0,
            );
          }
        }
        currentRow++;
      }
      currentRow++;

      // قسم ملخص الإدارة
      final adminSummaries = controller.getAdminSummaries();
      if (adminSummaries != null && adminSummaries.isNotEmpty) {
        var adminTitleCell = sheet.cell(XL.CellIndex.indexByColumnRow(
            columnIndex: 0, rowIndex: currentRow));
        adminTitleCell.value = XL.TextCellValue('ملخص الإدارة');
        adminTitleCell.cellStyle = XL.CellStyle(
          bold: true,
          fontSize: 16,
          backgroundColorHex: XL.ExcelColor.grey300,
          horizontalAlign: XL.HorizontalAlign.Center,
          fontColorHex: XL.ExcelColor.black,
        );
        currentRow++;

        // رؤوس جدول ملخص الإدارة
        List<String> adminHeaders = [
          'اسم الإدمن',
          'الحساب',
          'عدد المعاملات',
          'إجمالي المبلغ',
          'اجمالي بطاقة',
          'اجمالي نقداً',
          'اجمالي الشحن',
          'شحن بطاقة',
          'شحن نقداً',
          'اجمالي رسوم الكروت',
          'رسوم الكروت بطاقة',
          'رسوم الكروت نقدا',
          'اجمالي رسوم الاباء',
          'رسوم الاباء بطاقة',
          'رسوم الاباء نقدا',
        ];
        for (int i = 0; i < adminHeaders.length; i++) {
          var headerCell = sheet.cell(XL.CellIndex.indexByColumnRow(
              columnIndex: i, rowIndex: currentRow));
          headerCell.value = XL.TextCellValue(adminHeaders[i]);
          headerCell.cellStyle = XL.CellStyle(
            bold: true,
            horizontalAlign: XL.HorizontalAlign.Center,
            fontColorHex: XL.ExcelColor.black,
          );
        }
        currentRow++;

        // بيانات ملخص الإدارة
        for (var admin in adminSummaries) {
          List<String> adminData = [
            admin.adminName ?? 'غير محدد',
            admin.adminAccount ?? 'غير محدد',
            controller.formatCount(admin.transactionCount),
            controller.formatNumber(admin.totalAmount),
            controller.formatNumber(admin.totalAmountCard ?? 0),
            controller.formatNumber(admin.totalAmountCash ?? 0),
            controller.formatNumber(admin.totalRecharge ?? 0),
            controller.formatNumber(admin.totalRechargeCard ?? 0),
            controller.formatNumber(admin.totalRechargeCash ?? 0),
            controller.formatNumber(admin.totalFeesAmount ?? 0),
            controller.formatNumber(admin.totalFeesAmountCard ?? 0),
            controller.formatNumber(admin.totalFeesAmountCash ?? 0),
            controller.formatNumber((admin.totalAmount ?? 0) -
                (admin.totalRecharge ?? 0) -
                (admin.totalFeesAmount ?? 0)),
            controller.formatNumber((admin.totalAmountCard ?? 0) -
                (admin.totalRechargeCard ?? 0) -
                (admin.totalFeesAmountCard ?? 0)),
            controller.formatNumber((admin.totalAmountCash ?? 0) -
                (admin.totalRechargeCash ?? 0) -
                (admin.totalFeesAmountCash ?? 0)),
          ];

          for (int colIndex = 0; colIndex < adminData.length; colIndex++) {
            var cell = sheet.cell(XL.CellIndex.indexByColumnRow(
              columnIndex: colIndex,
              rowIndex: currentRow,
            ));
            cell.value = XL.TextCellValue(adminData[colIndex]);

            if (colIndex == 3) {
              // إجمالي المبلغ
              cell.cellStyle = XL.CellStyle(
                horizontalAlign: XL.HorizontalAlign.Center,
                fontColorHex: XL.ExcelColor.green700,
                bold: true,
              );
            } else if (colIndex == 2) {
              // عدد المعاملات
              cell.cellStyle = XL.CellStyle(
                horizontalAlign: XL.HorizontalAlign.Center,
                fontColorHex: XL.ExcelColor.blue700,
                bold: true,
              );
            } else if (colIndex == 4) {
              // دفع بالكرت
              cell.cellStyle = XL.CellStyle(
                horizontalAlign: XL.HorizontalAlign.Center,
                fontColorHex: XL.ExcelColor.blue700,
                bold: true,
              );
            } else if (colIndex == 5) {
              // دفع نقداً
              cell.cellStyle = XL.CellStyle(
                horizontalAlign: XL.HorizontalAlign.Center,
                fontColorHex: XL.ExcelColor.orange700,
                bold: true,
              );
            } else {
              cell.cellStyle = XL.CellStyle(
                horizontalAlign: XL.HorizontalAlign.Right,
              );
            }
          }
          currentRow++;
        }
        currentRow++;
      }

      // قسم تفاصيل المعاملات
      var transactionsTitleCell = sheet.cell(
          XL.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      transactionsTitleCell.value = XL.TextCellValue('تفاصيل معاملات الشحن');
      transactionsTitleCell.cellStyle = XL.CellStyle(
        bold: true,
        fontSize: 16,
        backgroundColorHex: XL.ExcelColor.grey300,
        horizontalAlign: XL.HorizontalAlign.Center,
        fontColorHex: XL.ExcelColor.black,
      );
      currentRow++;

      // إضافة ملخص المعاملات
      var summaryCell = sheet.cell(
          XL.CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow));
      double totalAmount = _sortedRecharges.fold(
          0, (sum, item) => sum + (item.totalAmount ?? 0));
      summaryCell.value = XL.TextCellValue(
          'إجمالي المعاملات: ${controller.formatCount(_sortedRecharges.length)} - إجمالي المبلغ: ${controller.formatNumber(totalAmount)}');
      summaryCell.cellStyle = XL.CellStyle(
        fontSize: 12,
        fontColorHex: XL.ExcelColor.red600,
        bold: true,
      );
      currentRow++;

      // إضافة الرؤوس
      List<String> headers = [
        'التاريخ',
        'رقم البطاقة',
        'اسم العضو',
        'اسم الادمن',
        'المبلغ',
        'نوع الدفع',
      ];

      for (int i = 0; i < headers.length; i++) {
        var cell = sheet.cell(XL.CellIndex.indexByColumnRow(
            columnIndex: i, rowIndex: currentRow));
        cell.value = XL.TextCellValue(headers[i]);
        cell.cellStyle = XL.CellStyle(
          bold: true,
          backgroundColorHex: XL.ExcelColor.blue200,
          horizontalAlign: XL.HorizontalAlign.Center,
          fontColorHex: XL.ExcelColor.blue700,
        );
      }
      currentRow++;

      // إضافة البيانات
      for (int rowIndex = 0; rowIndex < _sortedRecharges.length; rowIndex++) {
        final recharge = _sortedRecharges[rowIndex];

        List<String> rowData = [
          "".myDateFormatter(DateTime.parse(recharge.date ?? '')),
          recharge.cardNumber ?? 'غير محدد',
          recharge.memberName ?? 'غير محدد',
          recharge.adminName?.toString() ?? 'غير محدد',
          controller.formatNumber(recharge.totalAmount),
          recharge.paymentTypeId == 1 ? 'نقداً' : 'بطاقة',
        ];

        for (int colIndex = 0; colIndex < rowData.length; colIndex++) {
          var cell = sheet.cell(XL.CellIndex.indexByColumnRow(
            columnIndex: colIndex,
            rowIndex: currentRow,
          ));
          cell.value = XL.TextCellValue(rowData[colIndex]);

          // تنسيق خاص للمبلغ
          if (colIndex == 4) {
            cell.cellStyle = XL.CellStyle(
              horizontalAlign: XL.HorizontalAlign.Center,
              fontColorHex: XL.ExcelColor.blue700,
              bold: true,
            );
          }
          // تنسيق خاص لنوع الدفع
          else if (colIndex == 5) {
            cell.cellStyle = XL.CellStyle(
              horizontalAlign: XL.HorizontalAlign.Center,
              fontColorHex: recharge.paymentTypeId == 1
                  ? XL.ExcelColor.orange700
                  : XL.ExcelColor.blue700,
              bold: true,
            );
          } else {
            cell.cellStyle = XL.CellStyle(
              horizontalAlign: colIndex == 0
                  ? XL.HorizontalAlign.Center
                  : XL.HorizontalAlign.Right,
            );
          }
        }
        currentRow++;
      }

      // حفظ الملف
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'تقرير_قيود_الإقفال_${DateTime.now().millisecondsSinceEpoch}.xlsx';
      final filePath = '${directory.path}/$fileName';

      List<int>? fileBytes = excel.save();
      if (fileBytes != null) {
        File file = File(filePath);
        await file.writeAsBytes(fileBytes);

        Navigator.of(context).pop(); // إغلاق مؤشر التحميل

        // مشاركة الملف
        await Share.shareXFiles(
          [XFile(filePath)],
          text:
              'تقرير قيود الإقفال الشامل - يتضمن الملخص المالي، ملخص طرق الدفع، ملخص الإدارة وتفاصيل المعاملات',
          subject:
              'تقرير قيود الإقفال - ${DateTime.now().toString().split(' ')[0]}',
        );

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('✅ تم تصدير التقرير الشامل بنجاح!'),
                const SizedBox(height: 4),
                Text('📄 $fileName', style: const TextStyle(fontSize: 12)),
                const SizedBox(height: 4),
                const Text(
                    '📊 يتضمن: الملخص المالي + ملخص طرق الدفع + ملخص الإدارة + تفاصيل المعاملات',
                    style: TextStyle(fontSize: 11)),
                const SizedBox(height: 2),
                const Text('💳 مع تفاصيل الدفع بطاقة والنقد لكل قسم',
                    style: TextStyle(fontSize: 11, color: Colors.white70)),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 7),
            action: SnackBarAction(
              label: 'مشاركة مرة أخرى',
              textColor: Colors.white,
              onPressed: () async {
                await Share.shareXFiles([XFile(filePath)]);
              },
            ),
          ),
        );
      } else {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('❌ فشل في حفظ الملف'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء التصدير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  void dispose() {
    _fromDateController.dispose();
    _toDateController.dispose();
    _cardNumberController.dispose();
    super.dispose();
  }
}

// Admin Transactions Dialog Widget

