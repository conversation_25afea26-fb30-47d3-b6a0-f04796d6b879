import 'package:flutter/material.dart';
import 'package:vector_math/vector_math_64.dart' as vector;
import 'package:inventory_application/models/model/warehouse_3d_model.dart';

class Warehouse3DViewerWidget extends StatefulWidget {
  final Warehouse3D warehouse;
  final Function(Cabinet3D)? onCabinetTap;
  final Function(Shelf3D)? onShelfTap;
  final Function(double, double, double)? onEmptySpaceTap;
  final bool isAddingCabinet;
  final bool isAddingShelf;

  const Warehouse3DViewerWidget({
    Key? key,
    required this.warehouse,
    this.onCabinetTap,
    this.onShelfTap,
    this.onEmptySpaceTap,
    this.isAddingCabinet = false,
    this.isAddingShelf = false,
  }) : super(key: key);

  @override
  State<Warehouse3DViewerWidget> createState() =>
      _Warehouse3DViewerWidgetState();
}

class _Warehouse3DViewerWidgetState extends State<Warehouse3DViewerWidget>
    with TickerProviderStateMixin {
  // متحكمات الحركة والدوران
  late AnimationController _rotationController;
  late AnimationController _zoomController;

  // متغيرات التفاعل
  double _rotationX = -0.5;
  double _rotationY = 0.5;
  double _zoom = 0.8; // زوم أولي أصغر لرؤية المستودع كاملاً
  Offset? _lastPanOffset;

  // حدود الزوم
  final double _minZoom = 0.3;
  final double _maxZoom = 3.0;

  // إعدادات العرض
  final double _perspective = 0.001;
  final double _gridSize = 10.0;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _zoomController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF8F9FA),
      child: Stack(
        children: [
          // العرض ثلاثي الأبعاد
          GestureDetector(
            onScaleStart: (details) {
              _lastScale = 1.0;
              if (details.pointerCount == 1) {
                _lastPanOffset = details.focalPoint;
              }
            },
            onScaleUpdate: (details) {
              if (details.pointerCount == 1) {
                // التعامل مع السحب (الدوران)
                _handlePanUpdate(details.focalPoint);
              } else if (details.pointerCount == 2) {
                // التعامل مع الزوم
                _onScaleUpdate(details);
              }
            },
            onScaleEnd: (details) {
              _lastScale = 1.0;
              _lastPanOffset = null;
            },
            onTap: _onTap,
            child: CustomPaint(
              size: Size.infinite,
              painter: Warehouse3DPainter(
                warehouse: widget.warehouse,
                rotationX: _rotationX,
                rotationY: _rotationY,
                zoom: _zoom,
                perspective: _perspective,
                gridSize: _gridSize,
                isAddingCabinet: widget.isAddingCabinet,
                isAddingShelf: widget.isAddingShelf,
              ),
            ),
          ),

          // أزرار التحكم بالزوم
          Positioned(
            right: 16,
            bottom: 80,
            child: Column(
              children: [
                // زوم للداخل
                FloatingActionButton.small(
                  heroTag: "zoom_in_3d",
                  onPressed: _zoomIn,
                  backgroundColor: Colors.white,
                  child: const Icon(Icons.zoom_in, color: Colors.blue),
                ),
                const SizedBox(height: 8),

                // زوم للخارج
                FloatingActionButton.small(
                  heroTag: "zoom_out_3d",
                  onPressed: _zoomOut,
                  backgroundColor: Colors.white,
                  child: const Icon(Icons.zoom_out, color: Colors.blue),
                ),
                const SizedBox(height: 8),

                // إعادة تعيين العرض
                FloatingActionButton.small(
                  heroTag: "reset_view_3d",
                  onPressed: _resetView,
                  backgroundColor: Colors.white,
                  child: const Icon(Icons.refresh, color: Colors.green),
                ),
              ],
            ),
          ),

          // مؤشر الزوم
          Positioned(
            left: 16,
            bottom: 16,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'زوم: ${(_zoom * 100).toInt()}%',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

          // تعليمات التحكم
          Positioned(
            top: 16,
            left: 16,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: const [
                  Text(
                    '🎮 التحكم في العرض:',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '• اسحب للدوران',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                  Text(
                    '• قرب إصبعين للزوم',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                  Text(
                    '• استخدم الأزرار للتحكم الدقيق',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handlePanUpdate(Offset focalPoint) {
    if (_lastPanOffset != null) {
      final delta = focalPoint - _lastPanOffset!;
      setState(() {
        _rotationY += delta.dx * 0.01;
        _rotationX += delta.dy * 0.01;
        _rotationX = _rotationX.clamp(-1.5, 1.5);
      });
      _lastPanOffset = focalPoint;
    }
  }

  void _onTap() {
    // TODO: تحديد المكان المحدد في المساحة ثلاثية الأبعاد
    if (widget.onEmptySpaceTap != null &&
        (widget.isAddingCabinet || widget.isAddingShelf)) {
      // حساب الإحداثيات التقريبية
      final x = (widget.warehouse.length ?? 100) * 0.5;
      final y = (widget.warehouse.width ?? 80) * 0.5;
      final z = 0.0;

      widget.onEmptySpaceTap!(x, y, z);
    }
  }

  double _lastScale = 1.0;

  void _onScaleUpdate(ScaleUpdateDetails details) {
    if (details.pointerCount == 2) {
      setState(() {
        // تطبيق الزوم مع الحدود
        final scaleDelta = details.scale / _lastScale;
        final newZoom = _zoom * scaleDelta;
        _zoom = newZoom.clamp(_minZoom, _maxZoom);
        _lastScale = details.scale;
      });
    }
  }

  void _zoomIn() {
    setState(() {
      _zoom = (_zoom * 1.2).clamp(_minZoom, _maxZoom);
    });
  }

  void _zoomOut() {
    setState(() {
      _zoom = (_zoom / 1.2).clamp(_minZoom, _maxZoom);
    });
  }

  void _resetView() {
    setState(() {
      _rotationX = -0.5;
      _rotationY = 0.5;
      _zoom = 0.8;
    });
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _zoomController.dispose();
    super.dispose();
  }
}

class Warehouse3DPainter extends CustomPainter {
  final Warehouse3D warehouse;
  final double rotationX;
  final double rotationY;
  final double zoom;
  final double perspective;
  final double gridSize;
  final bool isAddingCabinet;
  final bool isAddingShelf;

  Warehouse3DPainter({
    required this.warehouse,
    required this.rotationX,
    required this.rotationY,
    required this.zoom,
    required this.perspective,
    required this.gridSize,
    required this.isAddingCabinet,
    required this.isAddingShelf,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);

    // إعداد التحويل ثلاثي الأبعاد
    final transform = _createTransform(center);

    // رسم الشبكة الأساسية
    _drawGrid(canvas, size, transform);

    // رسم أرضية المستودع
    _drawWarehouseFloor(canvas, transform);

    // رسم جدران المستودع
    _drawWarehouseWalls(canvas, transform);

    // رسم الخزائن
    _drawCabinets(canvas, transform);

    // رسم مؤشر الإضافة إذا كان في وضع الإضافة
    if (isAddingCabinet || isAddingShelf) {
      _drawAdditionCursor(canvas, size);
    }

    // رسم الأسطورة والمعلومات
    _drawLegend(canvas, size);
  }

  Matrix4 _createTransform(Offset center) {
    final transform = Matrix4.identity();

    // الانتقال إلى المركز
    transform.translate(center.dx, center.dy, 0.0);

    // التكبير
    transform.scale(zoom, zoom, zoom);

    // الدوران
    transform.rotateX(rotationX);
    transform.rotateY(rotationY);

    // المنظور
    transform.setEntry(3, 2, -perspective);

    return transform;
  }

  void _drawGrid(Canvas canvas, Size size, Matrix4 transform) {
    final paint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..strokeWidth = 0.5;

    final warehouseLength = warehouse.length ?? 100;
    final warehouseWidth = warehouse.width ?? 80;

    // خطوط الشبكة الأفقية
    for (double x = -warehouseLength / 2;
        x <= warehouseLength / 2;
        x += gridSize) {
      final start =
          _project3DPoint(vector.Vector3(x, -warehouseWidth / 2, 0), transform);
      final end =
          _project3DPoint(vector.Vector3(x, warehouseWidth / 2, 0), transform);

      if (start != null && end != null) {
        canvas.drawLine(start, end, paint);
      }
    }

    // خطوط الشبكة العمودية
    for (double y = -warehouseWidth / 2;
        y <= warehouseWidth / 2;
        y += gridSize) {
      final start = _project3DPoint(
          vector.Vector3(-warehouseLength / 2, y, 0), transform);
      final end =
          _project3DPoint(vector.Vector3(warehouseLength / 2, y, 0), transform);

      if (start != null && end != null) {
        canvas.drawLine(start, end, paint);
      }
    }
  }

  void _drawWarehouseFloor(Canvas canvas, Matrix4 transform) {
    final warehouseLength = warehouse.length ?? 100;
    final warehouseWidth = warehouse.width ?? 80;

    // رسم الأرضية بنقش مربعات احترافي
    _drawTiledFloor(canvas, transform, warehouseLength, warehouseWidth);

    // رسم حدود الأرضية مع تأثير ثلاثي الأبعاد
    _drawFloorBorder(canvas, transform, warehouseLength, warehouseWidth);

    // رسم خطوط إرشادية للمساعدة في التنسيق
    _drawGuideLines(canvas, transform, warehouseLength, warehouseWidth);
  }

  void _drawTiledFloor(
      Canvas canvas, Matrix4 transform, double length, double width) {
    final tileSize = 15.0; // حجم البلاطة الاحترافي
    final lightPaint = Paint()
      ..color = const Color(0xFFF8F9FA)
      ..style = PaintingStyle.fill;
    final darkPaint = Paint()
      ..color = const Color(0xFFECF0F1)
      ..style = PaintingStyle.fill;

    for (double x = -length / 2; x < length / 2; x += tileSize) {
      for (double y = -width / 2; y < width / 2; y += tileSize) {
        final isLight =
            ((x / tileSize).floor() + (y / tileSize).floor()) % 2 == 0;
        final paint = isLight ? lightPaint : darkPaint;

        final corners = [
          _project3DPoint(vector.Vector3(x, y, 0), transform),
          _project3DPoint(vector.Vector3(x + tileSize, y, 0), transform),
          _project3DPoint(
              vector.Vector3(x + tileSize, y + tileSize, 0), transform),
          _project3DPoint(vector.Vector3(x, y + tileSize, 0), transform),
        ];

        if (corners.every((corner) => corner != null)) {
          final path = Path();
          path.moveTo(corners[0]!.dx, corners[0]!.dy);
          for (int i = 1; i < corners.length; i++) {
            path.lineTo(corners[i]!.dx, corners[i]!.dy);
          }
          path.close();
          canvas.drawPath(path, paint);

          // رسم حدود خفيفة للبلاطات
          final tileBorderPaint = Paint()
            ..color = Colors.grey.withOpacity(0.2)
            ..style = PaintingStyle.stroke
            ..strokeWidth = 0.5;
          canvas.drawPath(path, tileBorderPaint);
        }
      }
    }
  }

  void _drawFloorBorder(
      Canvas canvas, Matrix4 transform, double length, double width) {
    final borderPaint = Paint()
      ..color = const Color(0xFF2C3E50)
      ..strokeWidth = 3.0
      ..style = PaintingStyle.stroke;

    final corners = [
      _project3DPoint(vector.Vector3(-length / 2, -width / 2, 0), transform),
      _project3DPoint(vector.Vector3(length / 2, -width / 2, 0), transform),
      _project3DPoint(vector.Vector3(length / 2, width / 2, 0), transform),
      _project3DPoint(vector.Vector3(-length / 2, width / 2, 0), transform),
    ];

    if (corners.every((corner) => corner != null)) {
      final path = Path();
      path.moveTo(corners[0]!.dx, corners[0]!.dy);
      for (int i = 1; i < corners.length; i++) {
        path.lineTo(corners[i]!.dx, corners[i]!.dy);
      }
      path.close();
      canvas.drawPath(path, borderPaint);
    }
  }

  void _drawGuideLines(
      Canvas canvas, Matrix4 transform, double length, double width) {
    final guidePaint = Paint()
      ..color = const Color(0xFF3498DB).withOpacity(0.3)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // خط وسط طولي
    final centerLineY = [
      _project3DPoint(vector.Vector3(-length / 2, 0, 0), transform),
      _project3DPoint(vector.Vector3(length / 2, 0, 0), transform),
    ];

    if (centerLineY.every((point) => point != null)) {
      canvas.drawLine(centerLineY[0]!, centerLineY[1]!, guidePaint);
    }

    // خط وسط عرضي
    final centerLineX = [
      _project3DPoint(vector.Vector3(0, -width / 2, 0), transform),
      _project3DPoint(vector.Vector3(0, width / 2, 0), transform),
    ];

    if (centerLineX.every((point) => point != null)) {
      canvas.drawLine(centerLineX[0]!, centerLineX[1]!, guidePaint);
    }
  }

  void _drawWarehouseWalls(Canvas canvas, Matrix4 transform) {
    final warehouseLength = warehouse.length ?? 100;
    final warehouseWidth = warehouse.width ?? 80;
    final warehouseHeight = warehouse.height ?? 40;

    // رسم الجدران مع تدرج لوني لإعطاء عمق
    _drawWallWithGradient(
        canvas, transform, warehouseLength, warehouseWidth, warehouseHeight);

    // رسم الأعمدة الداعمة
    _drawSupportColumns(
        canvas, transform, warehouseLength, warehouseWidth, warehouseHeight);

    // رسم السقف
    _drawCeiling(
        canvas, transform, warehouseLength, warehouseWidth, warehouseHeight);
  }

  void _drawWallWithGradient(Canvas canvas, Matrix4 transform, double length,
      double width, double height) {
    // الجدار الأمامي - لون فاتح
    final frontWallPaint = Paint()
      ..color = const Color(0xFFE8EAED)
      ..style = PaintingStyle.fill;

    // الجدار الجانبي - لون أغمق للعمق
    final sideWallPaint = Paint()
      ..color = const Color(0xFFD5DBDB)
      ..style = PaintingStyle.fill;

    final wallBorderPaint = Paint()
      ..color = const Color(0xFF7F8C8D)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    // الجدار الأمامي
    _drawSingleWall(
        canvas,
        transform,
        [
          vector.Vector3(-length / 2, -width / 2, 0),
          vector.Vector3(length / 2, -width / 2, 0),
          vector.Vector3(length / 2, -width / 2, height),
          vector.Vector3(-length / 2, -width / 2, height),
        ],
        frontWallPaint,
        wallBorderPaint);

    // الجدار الجانبي الأيمن
    _drawSingleWall(
        canvas,
        transform,
        [
          vector.Vector3(length / 2, -width / 2, 0),
          vector.Vector3(length / 2, width / 2, 0),
          vector.Vector3(length / 2, width / 2, height),
          vector.Vector3(length / 2, -width / 2, height),
        ],
        sideWallPaint,
        wallBorderPaint);

    // الجدار الخلفي (جزئي للرؤية)
    _drawSingleWall(
        canvas,
        transform,
        [
          vector.Vector3(length / 2, width / 2, 0),
          vector.Vector3(-length / 2, width / 2, 0),
          vector.Vector3(-length / 2, width / 2, height),
          vector.Vector3(length / 2, width / 2, height),
        ],
        sideWallPaint,
        wallBorderPaint,
        opacity: 0.3);

    // الجدار الجانبي الأيسر (جزئي للرؤية)
    _drawSingleWall(
        canvas,
        transform,
        [
          vector.Vector3(-length / 2, width / 2, 0),
          vector.Vector3(-length / 2, -width / 2, 0),
          vector.Vector3(-length / 2, -width / 2, height),
          vector.Vector3(-length / 2, width / 2, height),
        ],
        sideWallPaint,
        wallBorderPaint,
        opacity: 0.3);
  }

  void _drawSingleWall(Canvas canvas, Matrix4 transform,
      List<vector.Vector3> points, Paint wallPaint, Paint borderPaint,
      {double opacity = 1.0}) {
    final projectedPoints =
        points.map((point) => _project3DPoint(point, transform)).toList();

    if (projectedPoints.every((point) => point != null)) {
      final path = Path();
      path.moveTo(projectedPoints[0]!.dx, projectedPoints[0]!.dy);
      for (int i = 1; i < projectedPoints.length; i++) {
        path.lineTo(projectedPoints[i]!.dx, projectedPoints[i]!.dy);
      }
      path.close();

      // تطبيق الشفافية إذا لزم الأمر
      if (opacity < 1.0) {
        wallPaint = Paint()
          ..color = wallPaint.color.withOpacity(opacity)
          ..style = wallPaint.style;
        borderPaint = Paint()
          ..color = borderPaint.color.withOpacity(opacity)
          ..style = borderPaint.style
          ..strokeWidth = borderPaint.strokeWidth;
      }

      canvas.drawPath(path, wallPaint);
      canvas.drawPath(path, borderPaint);
    }
  }

  void _drawSupportColumns(Canvas canvas, Matrix4 transform, double length,
      double width, double height) {
    final columnPaint = Paint()
      ..color = const Color(0xFF95A5A6)
      ..style = PaintingStyle.fill;

    final columnPositions = [
      vector.Vector3(-length / 4, -width / 4, 0),
      vector.Vector3(length / 4, -width / 4, 0),
      vector.Vector3(length / 4, width / 4, 0),
      vector.Vector3(-length / 4, width / 4, 0),
    ];

    for (final pos in columnPositions) {
      _drawColumn(canvas, transform, pos, height, columnPaint);
    }
  }

  void _drawColumn(Canvas canvas, Matrix4 transform, vector.Vector3 position,
      double height, Paint paint) {
    final columnWidth = 2.0;
    final corners = [
      _project3DPoint(
          vector.Vector3(position.x - columnWidth, position.y - columnWidth, 0),
          transform),
      _project3DPoint(
          vector.Vector3(position.x + columnWidth, position.y - columnWidth, 0),
          transform),
      _project3DPoint(
          vector.Vector3(position.x + columnWidth, position.y + columnWidth, 0),
          transform),
      _project3DPoint(
          vector.Vector3(position.x - columnWidth, position.y + columnWidth, 0),
          transform),
    ];

    final topCorners = [
      _project3DPoint(
          vector.Vector3(
              position.x - columnWidth, position.y - columnWidth, height),
          transform),
      _project3DPoint(
          vector.Vector3(
              position.x + columnWidth, position.y - columnWidth, height),
          transform),
      _project3DPoint(
          vector.Vector3(
              position.x + columnWidth, position.y + columnWidth, height),
          transform),
      _project3DPoint(
          vector.Vector3(
              position.x - columnWidth, position.y + columnWidth, height),
          transform),
    ];

    if (corners.every((c) => c != null) && topCorners.every((c) => c != null)) {
      // رسم الجوانب
      for (int i = 0; i < 4; i++) {
        final next = (i + 1) % 4;
        final path = Path();
        path.moveTo(corners[i]!.dx, corners[i]!.dy);
        path.lineTo(corners[next]!.dx, corners[next]!.dy);
        path.lineTo(topCorners[next]!.dx, topCorners[next]!.dy);
        path.lineTo(topCorners[i]!.dx, topCorners[i]!.dy);
        path.close();
        canvas.drawPath(path, paint);
      }
    }
  }

  void _drawCeiling(Canvas canvas, Matrix4 transform, double length,
      double width, double height) {
    final ceilingPaint = Paint()
      ..color = const Color(0xFFF5F5F5)
      ..style = PaintingStyle.fill;

    final ceilingBorderPaint = Paint()
      ..color = const Color(0xFFBDC3C7)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    final corners = [
      _project3DPoint(
          vector.Vector3(-length / 2, -width / 2, height), transform),
      _project3DPoint(
          vector.Vector3(length / 2, -width / 2, height), transform),
      _project3DPoint(vector.Vector3(length / 2, width / 2, height), transform),
      _project3DPoint(
          vector.Vector3(-length / 2, width / 2, height), transform),
    ];

    if (corners.every((corner) => corner != null)) {
      final path = Path();
      path.moveTo(corners[0]!.dx, corners[0]!.dy);
      for (int i = 1; i < corners.length; i++) {
        path.lineTo(corners[i]!.dx, corners[i]!.dy);
      }
      path.close();
      canvas.drawPath(path, ceilingPaint);
      canvas.drawPath(path, ceilingBorderPaint);
    }
  }

  void _drawCabinets(Canvas canvas, Matrix4 transform) {
    if (warehouse.cabinets == null) return;

    for (final cabinet in warehouse.cabinets!) {
      _drawCabinet(canvas, transform, cabinet);
    }
  }

  void _drawCabinet(Canvas canvas, Matrix4 transform, Cabinet3D cabinet) {
    final baseColor = _getCabinetColor(cabinet.color);

    // ألوان متدرجة للواقعية
    final frontFacePaint = Paint()
      ..color = baseColor
      ..style = PaintingStyle.fill;

    final sideFacePaint = Paint()
      ..color = Color.fromARGB(
        baseColor.alpha,
        (baseColor.red * 0.8).round(),
        (baseColor.green * 0.8).round(),
        (baseColor.blue * 0.8).round(),
      )
      ..style = PaintingStyle.fill;

    final topFacePaint = Paint()
      ..color = Color.fromARGB(
        baseColor.alpha,
        (baseColor.red * 0.9).round(),
        (baseColor.green * 0.9).round(),
        (baseColor.blue * 0.9).round(),
      )
      ..style = PaintingStyle.fill;

    final cabinetBorderPaint = Paint()
      ..color = const Color(0xFF2C3E50)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    final x = (cabinet.xPosition ?? 0) - (warehouse.length ?? 100) / 2;
    final y = (cabinet.yPosition ?? 0) - (warehouse.width ?? 80) / 2;
    final z = cabinet.zPosition ?? 0;
    final length = cabinet.length ?? 20;
    final width = cabinet.width ?? 15;
    final height = cabinet.height ?? 25;

    // رسم الخزانة كصندوق ثلاثي الأبعاد احترافي
    _drawCabinetBox(canvas, transform, x, y, z, length, width, height,
        frontFacePaint, sideFacePaint, topFacePaint, cabinetBorderPaint);

    // رسم تفاصيل الخزانة (مقابض، أرقام، إلخ)
    _drawCabinetDetails(
        canvas, transform, cabinet, x, y, z, length, width, height);

    // رسم الأرفف
    _drawShelves(canvas, transform, cabinet, x, y, z);
  }

  void _drawCabinetBox(
      Canvas canvas,
      Matrix4 transform,
      double x,
      double y,
      double z,
      double length,
      double width,
      double height,
      Paint frontPaint,
      Paint sidePaint,
      Paint topPaint,
      Paint borderPaint) {
    // الوجه الأمامي
    _drawCabinetFace(
        canvas,
        transform,
        [
          vector.Vector3(x, y, z),
          vector.Vector3(x + length, y, z),
          vector.Vector3(x + length, y, z + height),
          vector.Vector3(x, y, z + height),
        ],
        frontPaint,
        borderPaint);

    // الوجه الجانبي الأيمن
    _drawCabinetFace(
        canvas,
        transform,
        [
          vector.Vector3(x + length, y, z),
          vector.Vector3(x + length, y + width, z),
          vector.Vector3(x + length, y + width, z + height),
          vector.Vector3(x + length, y, z + height),
        ],
        sidePaint,
        borderPaint);

    // الوجه العلوي
    _drawCabinetFace(
        canvas,
        transform,
        [
          vector.Vector3(x, y, z + height),
          vector.Vector3(x + length, y, z + height),
          vector.Vector3(x + length, y + width, z + height),
          vector.Vector3(x, y + width, z + height),
        ],
        topPaint,
        borderPaint);
  }

  void _drawCabinetFace(Canvas canvas, Matrix4 transform,
      List<vector.Vector3> points, Paint facePaint, Paint borderPaint) {
    final projectedPoints =
        points.map((point) => _project3DPoint(point, transform)).toList();

    if (projectedPoints.every((point) => point != null)) {
      final path = Path();
      path.moveTo(projectedPoints[0]!.dx, projectedPoints[0]!.dy);
      for (int i = 1; i < projectedPoints.length; i++) {
        path.lineTo(projectedPoints[i]!.dx, projectedPoints[i]!.dy);
      }
      path.close();

      canvas.drawPath(path, facePaint);
      canvas.drawPath(path, borderPaint);
    }
  }

  void _drawCabinetDetails(
      Canvas canvas,
      Matrix4 transform,
      Cabinet3D cabinet,
      double x,
      double y,
      double z,
      double length,
      double width,
      double height) {
    // رسم المقابض
    _drawCabinetHandles(canvas, transform, x, y, z, length, height);

    // رسم رقم/كود الخزانة
    _drawCabinetLabel(canvas, transform, cabinet, x, y, z, length, height);

    // رسم مؤشر حالة الامتلاء
    _drawCabinetStatusIndicator(
        canvas, transform, cabinet, x, y, z, length, width, height);
  }

  void _drawCabinetHandles(Canvas canvas, Matrix4 transform, double x, double y,
      double z, double length, double height) {
    final handlePaint = Paint()
      ..color = const Color(0xFF7F8C8D)
      ..style = PaintingStyle.fill;

    // مقبض علوي
    final handle1Center = _project3DPoint(
        vector.Vector3(x + length * 0.7, y - 0.5, z + height * 0.8), transform);

    // مقبض سفلي
    final handle2Center = _project3DPoint(
        vector.Vector3(x + length * 0.7, y - 0.5, z + height * 0.2), transform);

    if (handle1Center != null) {
      canvas.drawCircle(handle1Center, 2, handlePaint);
    }
    if (handle2Center != null) {
      canvas.drawCircle(handle2Center, 2, handlePaint);
    }
  }

  void _drawCabinetLabel(Canvas canvas, Matrix4 transform, Cabinet3D cabinet,
      double x, double y, double z, double length, double height) {
    final labelPosition = _project3DPoint(
        vector.Vector3(x + length / 2, y - 1, z + height / 2), transform);

    if (labelPosition != null) {
      final textPainter = TextPainter(
        text: TextSpan(
          text: cabinet.code ?? 'CAB',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 8,
            fontWeight: FontWeight.bold,
          ),
        ),
        textDirection: TextDirection.rtl,
      );
      textPainter.layout();

      // خلفية للنص
      final textBg = Paint()..color = Colors.black.withOpacity(0.7);
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromCenter(
            center: labelPosition,
            width: textPainter.width + 8,
            height: textPainter.height + 4,
          ),
          const Radius.circular(4),
        ),
        textBg,
      );

      textPainter.paint(
        canvas,
        Offset(
          labelPosition.dx - textPainter.width / 2,
          labelPosition.dy - textPainter.height / 2,
        ),
      );
    }
  }

  void _drawCabinetStatusIndicator(
      Canvas canvas,
      Matrix4 transform,
      Cabinet3D cabinet,
      double x,
      double y,
      double z,
      double length,
      double width,
      double height) {
    // حساب نسبة الامتلاء
    final shelfCount = cabinet.shelves?.length ?? 0;
    final maxShelves = (height / 8).floor(); // تقدير عدد الأرفف القصوى
    final fillRatio = shelfCount / maxShelves.clamp(1, 10);

    // لون المؤشر حسب الامتلاء
    Color indicatorColor;
    if (fillRatio < 0.3) {
      indicatorColor = Colors.red; // فارغ تقريباً
    } else if (fillRatio < 0.7) {
      indicatorColor = Colors.orange; // متوسط
    } else {
      indicatorColor = Colors.green; // ممتلئ
    }

    final indicatorPosition = _project3DPoint(
        vector.Vector3(x + length + 1, y + width / 2, z + height - 3),
        transform);

    if (indicatorPosition != null) {
      final indicatorPaint = Paint()
        ..color = indicatorColor
        ..style = PaintingStyle.fill;

      canvas.drawCircle(indicatorPosition, 3, indicatorPaint);

      // حدود بيضاء للوضوح
      final borderPaint = Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;
      canvas.drawCircle(indicatorPosition, 3, borderPaint);
    }
  }

  void _drawShelves(Canvas canvas, Matrix4 transform, Cabinet3D cabinet,
      double x, double y, double z) {
    if (cabinet.shelves == null) return;

    for (final shelf in cabinet.shelves!) {
      _drawShelfNew(canvas, transform, shelf, cabinet, x, y, z);
    }
  }

  void _drawShelfNew(Canvas canvas, Matrix4 transform, Shelf3D shelf,
      Cabinet3D cabinet, double cabinetX, double cabinetY, double cabinetZ) {
    final shelfColor = _getShelfStatusColor(shelf.status);
    final shelfPaint = Paint()
      ..color = shelfColor.withOpacity(0.8)
      ..style = PaintingStyle.fill;

    final shelfBorderPaint = Paint()
      ..color = const Color(0xFF34495E)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    final x = cabinetX + (shelf.xPosition ?? 2);
    final y = cabinetY + (shelf.yPosition ?? 2);
    final z = cabinetZ + (shelf.zPosition ?? 5);
    final length = shelf.length ?? 18;
    final width = shelf.width ?? 12;
    final height = shelf.height ?? 2;

    // رسم الرف كصفيحة رقيقة
    _drawCabinetFace(
        canvas,
        transform,
        [
          vector.Vector3(x, y, z),
          vector.Vector3(x + length, y, z),
          vector.Vector3(x + length, y + width, z),
          vector.Vector3(x, y + width, z),
        ],
        shelfPaint,
        shelfBorderPaint);

    // رسم الوجه العلوي للرف
    _drawCabinetFace(
        canvas,
        transform,
        [
          vector.Vector3(x, y, z + height),
          vector.Vector3(x + length, y, z + height),
          vector.Vector3(x + length, y + width, z + height),
          vector.Vector3(x, y + width, z + height),
        ],
        shelfPaint,
        shelfBorderPaint);

    // رسم الأدوية على الرف
    _drawMedicinesOnShelf(canvas, transform, shelf, x, y, z, length, width);
  }

  void _drawMedicinesOnShelf(Canvas canvas, Matrix4 transform, Shelf3D shelf,
      double x, double y, double z, double length, double width) {
    if (shelf.products == null) return;

    final medicineColors = [
      Colors.blue.shade300,
      Colors.green.shade300,
      Colors.orange.shade300,
      Colors.purple.shade300,
      Colors.red.shade300,
    ];

    for (int i = 0; i < shelf.products!.length; i++) {
      final color = medicineColors[i % medicineColors.length];

      final medicineX = x + (i % 4) * (length / 4) + 2;
      final medicineY = y + (i ~/ 4) * (width / 2) + 2;
      final medicineZ = z + 2;

      _drawMedicineBox(
          canvas, transform, medicineX, medicineY, medicineZ, color);
    }
  }

  void _drawMedicineBox(Canvas canvas, Matrix4 transform, double x, double y,
      double z, Color color) {
    final medicinePaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final size = 1.5;
    final height = 3.0;

    // رسم صندوق الدواء الصغير
    _drawCabinetFace(
        canvas,
        transform,
        [
          vector.Vector3(x, y, z),
          vector.Vector3(x + size, y, z),
          vector.Vector3(x + size, y, z + height),
          vector.Vector3(x, y, z + height),
        ],
        medicinePaint,
        Paint()..color = Colors.transparent);
  }

  void _drawShelf(
      Canvas canvas, Matrix4 transform, Shelf3D shelf, Cabinet3D cabinet) {
    final shelfColor = _getShelfStatusColor(shelf.status);
    final shelfPaint = Paint()
      ..color = shelfColor.withOpacity(0.7)
      ..style = PaintingStyle.fill;

    final cabinetX = (cabinet.xPosition ?? 0) - (warehouse.length ?? 100) / 2;
    final cabinetY = (cabinet.yPosition ?? 0) - (warehouse.width ?? 80) / 2;
    final cabinetZ = cabinet.zPosition ?? 0;

    final x = cabinetX + (shelf.xPosition ?? 0);
    final y = cabinetY + (shelf.yPosition ?? 0);
    final z = cabinetZ + (shelf.zPosition ?? 0);
    final length = shelf.length ?? 10;
    final width = shelf.width ?? 8;
    final height = shelf.height ?? 2;

    // رسم الرف كصندوق مسطح
    final corners = [
      _project3DPoint(vector.Vector3(x, y, z), transform),
      _project3DPoint(vector.Vector3(x + length, y, z), transform),
      _project3DPoint(vector.Vector3(x + length, y + width, z), transform),
      _project3DPoint(vector.Vector3(x, y + width, z), transform),
      _project3DPoint(vector.Vector3(x, y, z + height), transform),
      _project3DPoint(vector.Vector3(x + length, y, z + height), transform),
      _project3DPoint(
          vector.Vector3(x + length, y + width, z + height), transform),
      _project3DPoint(vector.Vector3(x, y + width, z + height), transform),
    ];

    if (corners.every((corner) => corner != null)) {
      // رسم الوجه العلوي للرف
      final path = Path();
      path.moveTo(corners[4]!.dx, corners[4]!.dy);
      path.lineTo(corners[5]!.dx, corners[5]!.dy);
      path.lineTo(corners[6]!.dx, corners[6]!.dy);
      path.lineTo(corners[7]!.dx, corners[7]!.dy);
      path.close();

      canvas.drawPath(path, shelfPaint);
    }
  }

  void _drawCubeFace(
      Canvas canvas, List<Offset> corners, Paint fillPaint, Paint borderPaint) {
    final path = Path();
    path.moveTo(corners[0].dx, corners[0].dy);
    for (int i = 1; i < corners.length; i++) {
      path.lineTo(corners[i].dx, corners[i].dy);
    }
    path.close();

    canvas.drawPath(path, fillPaint);
    canvas.drawPath(path, borderPaint);
  }

  void _drawAdditionCursor(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = isAddingCabinet ? Colors.blue : Colors.green
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final center = Offset(size.width / 2, size.height / 2);
    const radius = 20.0;

    // رسم دائرة مع علامة زائد
    canvas.drawCircle(center, radius, paint);
    canvas.drawLine(
      Offset(center.dx - 10, center.dy),
      Offset(center.dx + 10, center.dy),
      paint,
    );
    canvas.drawLine(
      Offset(center.dx, center.dy - 10),
      Offset(center.dx, center.dy + 10),
      paint,
    );
  }

  void _drawLegend(Canvas canvas, Size size) {
    final legendPaint = Paint()
      ..color = Colors.white.withOpacity(0.9)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    const legendRect = Rect.fromLTWH(10, 10, 150, 80);
    canvas.drawRRect(
      RRect.fromRectAndRadius(legendRect, const Radius.circular(8)),
      legendPaint,
    );
    canvas.drawRRect(
      RRect.fromRectAndRadius(legendRect, const Radius.circular(8)),
      borderPaint,
    );

    // رسم العناصر
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'المستودع ثلاثي الأبعاد\n• اسحب للدوران\n• انقر للإضافة',
        style: TextStyle(
          color: Color(0xFF2C3E50),
          fontSize: 10,
        ),
      ),
      textDirection: TextDirection.rtl,
    );

    textPainter.layout();
    textPainter.paint(canvas, const Offset(20, 20));
  }

  Offset? _project3DPoint(vector.Vector3 point, Matrix4 transform) {
    final vector4 = vector.Vector4(point.x, point.y, point.z, 1.0);
    final transformed = transform * vector4;

    if (transformed.w != 0) {
      return Offset(
        transformed.x / transformed.w,
        transformed.y / transformed.w,
      );
    }
    return null;
  }

  Color _getCabinetColor(String? colorString) {
    if (colorString == null) return const Color(0xFF3498DB);

    try {
      if (colorString.startsWith('#')) {
        return Color(
            int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
    } catch (e) {
      // في حالة فشل التحويل، استخدم اللون الافتراضي
    }

    return const Color(0xFF3498DB);
  }

  Color _getShelfStatusColor(ShelfStatus? status) {
    switch (status) {
      case ShelfStatus.empty:
        return Colors.grey;
      case ShelfStatus.partial:
        return Colors.orange;
      case ShelfStatus.full:
        return Colors.green;
      case ShelfStatus.overloaded:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true; // إعادة الرسم دائماً للتفاعل السلس
  }
}
