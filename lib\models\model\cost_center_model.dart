class CostCenterModel {
  String? code;
  String? customCode;
  String? name;
  String? nameEn;
  int? iD;
  int? localId;

  CostCenterModel({
    this.code,
    this.customCode,
    this.name,
    this.nameEn,
    this.iD,
    this.localId,
  });

  CostCenterModel.fromJson(Map<String, dynamic> json) {
    code = json['Code'];
    customCode = json['CustomCode'];
    name = json['Name_Ar'] ?? json['Name'];
    nameEn = json['Name_En'];
    iD = json['ID'];
    localId = json['local_Id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Code'] = code;
    data['CustomCode'] = customCode;
    data['Name'] = name;
    data['Name_En'] = nameEn;
    data['local_Id'] = localId;
    data['ID'] = iD;
    return data;
  }
}
