enum CustomerSyncStatus { pending, syncing, synced }

class CustomerModel {
  String? code;
  String? name;
  String? nameEn;
  String? nameTr;
  String? nameLocalized;
  String? customerTypeName;
  String? accountingName;
  String? accountingNumber;
  String? address;
  String? notes;
  int? iD;
  int? localId;

  // CustomerSyncStatus? status;
  String? phone;

  CustomerModel(
      {this.code,
      this.name,
      this.notes,
      this.address,
      this.nameEn,
      this.nameTr,
      this.nameLocalized,
      this.customerTypeName,
      this.accountingName,
      this.accountingNumber,
      this.iD,
      this.localId,
      // this.status,
      this.phone});

  CustomerModel.fromJson(Map<String, dynamic> json) {
    notes = json['Notes'];
    code = json['Code'];
    address = json['Address'];
    name = json['Name'];
    nameEn = json['Name_En'];
    nameTr = json['Name_Tr'];
    nameLocalized = json['Name_Localized'];
    customerTypeName = json['Customer_Type_Name'];
    accountingName = json['Accounting_Name'];
    accountingNumber = json['Accounting_Number'];
    // status = json['status'] != null
    //     ? CustomerSyncStatus.values[json['status']]
    //     : CustomerSyncStatus.synced;

    iD = json['ID'];
    localId = json['local_Id'];
    phone = json['Phone'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['Code'] = code;
    data['Notes'] = notes;
    data['Address'] = address;
    data['Name'] = name;
    data['Name_En'] = nameEn;
    data['Name_Tr'] = nameTr;
    data['Name_Localized'] = nameLocalized;
    data['Customer_Type_Name'] = customerTypeName;
    data['Accounting_Name'] = accountingName;
    data['Accounting_Number'] = accountingNumber;
    // data['status'] = status?.index;
    data['local_Id'] = localId;
    data['ID'] = iD;
    data['Phone'] = phone;
    return data;
  }
}
