import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/controllers/report_controller.dart';
import 'package:inventory_application/controllers/printer_settings_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/reports/report_filter_dto.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/reports/base_report_screen.dart';
import 'package:inventory_application/screens/reports/widgets/daily_sales_report_widget.dart';
import 'package:inventory_application/screens/reports/widgets/report_filter_dialog.dart';
import 'package:inventory_application/screens/reports/widgets/report_print_helper.dart';
import 'package:provider/provider.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'dart:io';
import 'package:inventory_application/helpers/arabic_font_loader.dart';

class DailySalesReportScreen extends BaseReportScreen {
  const DailySalesReportScreen({Key? key}) : super(key: key);

  @override
  State<DailySalesReportScreen> createState() => _DailySalesReportScreenState();
}

class _DailySalesReportScreenState
    extends BaseReportScreenState<DailySalesReportScreen> {
  @override
  ReportType get reportType => ReportType.dailySales;

  @override
  String getReportTitle() => T('Daily Sales');

  @override
  IconData getReportIcon() => Icons.calendar_today;

  @override
  Color getHeaderColor() => const Color(0xFF4361EE);

  @override
  Color getAccentColor() => const Color(0xFF3A0CA3);

  @override
  Future<void> generateReport() async {
    setLoading(true);

    try {
      final reportController =
          Provider.of<ReportController>(context, listen: false);

      // استخدام الفلتر من ReportController
      await reportController.generateDailySalesReport(
        fromDate: reportController.filter.fromDate,
        toDate: reportController.filter.toDate,
      );
    } catch (e) {
      errorSnackBar(message: 'Error generating daily sales report: $e');
    } finally {
      setLoading(false);
    }
  }

  // دالة معاينة وطباعة التقرير
  Future<void> _printReport() async {
    final reportController =
        Provider.of<ReportController>(context, listen: false);

    if (reportController.dailySalesReport == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('لا توجد بيانات للطباعة'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // عرض خيارات المعاينة والطباعة
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.print, color: Colors.blue),
              SizedBox(width: 8),
              Text('طباعة تقرير المبيعات اليومية'),
            ],
          ),
          content: const Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('اختر العملية المطلوبة:'),
              SizedBox(height: 8),
              Text(
                '• المعاينة: لعرض التقرير قبل الطباعة\n• الطباعة المباشرة: للطباعة فوراً',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
              },
              icon: const Icon(Icons.cancel, color: Colors.grey),
              label: const Text('إلغاء'),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _previewReport();
              },
              icon: const Icon(Icons.preview),
              label: const Text('معاينة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).pop();
                _directPrint();
              },
              icon: const Icon(Icons.print),
              label: const Text('طباعة مباشرة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        );
      },
    );
  }

  // دالة معاينة التقرير
  Future<void> _previewReport() async {
    final reportController =
        Provider.of<ReportController>(context, listen: false);

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('جاري تحضير معاينة التقرير...'),
            ],
          ),
        );
      },
    );

    try {
      final printerSettings = context.read<PrinterSettingsController>();

      // إنشاء PDF
      final pdf =
          await _createDailySalesReportPdf(reportController, printerSettings);

      Navigator.of(context).pop(); // إغلاق مؤشر التحميل

      // عرض معاينة التقرير
      await Printing.layoutPdf(
        onLayout: (_) async => await pdf.save(),
        name:
            'معاينة تقرير المبيعات اليومية - ${DateFormat('yyyy-MM-dd').format(DateTime.now())}',
        format: PdfPageFormat.a4,
      );

      // إظهار رسالة نجاح
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('تم عرض معاينة التقرير بنجاح'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء معاينة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // دالة الطباعة المباشرة
  Future<void> _directPrint() async {
    final reportController =
        Provider.of<ReportController>(context, listen: false);

    // عرض مؤشر التحميل
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('جاري تحضير التقرير للطباعة...'),
            ],
          ),
        );
      },
    );

    try {
      final printerSettings = context.read<PrinterSettingsController>();

      // إنشاء PDF
      final pdf =
          await _createDailySalesReportPdf(reportController, printerSettings);

      Navigator.of(context).pop(); // إغلاق مؤشر التحميل

      // طباعة التقرير مباشرة
      await _printReportPdf(pdf, printerSettings);
    } catch (e) {
      print(e);
      Navigator.of(context).pop(); // إغلاق مؤشر التحميل
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('حدث خطأ أثناء طباعة التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Show filter dialog
  Future<void> _showFilterDialog() async {
    final reportController =
        Provider.of<ReportController>(context, listen: false);

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => ReportFilterDialog(
        initialFilter: reportController.filter,
        selectedReportType: reportType,
      ),
    );

    if (result == true) {
      generateReport();
    }
  }

  // Helper method to get date range text
  String _getDateRangeText(ReportFilterDTO filter) {
    if (filter.fromDate == null || filter.toDate == null) {
      return T('Date range not set');
    }

    final fromDate = DateFormat('yyyy-MM-dd').format(filter.fromDate!);
    final toDate = DateFormat('yyyy-MM-dd').format(filter.toDate!);

    return '$fromDate ${T('to')} $toDate';
  }

  // Build date range header
  Widget _buildDateRangeHeader(ReportController reportController) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            getHeaderColor().withOpacity(0.8),
            getAccentColor().withOpacity(0.8)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.date_range,
              size: 20,
              color: Colors.white,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  T('Report Period'),
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.white70,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  _getDateRangeText(reportController.filter),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          InkWell(
            onTap: _showFilterDialog,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.filter_alt,
                    size: 16,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    T('Filter'),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget buildReportContent(ReportController reportController) {
    final report = reportController.dailySalesReport;
    if (report == null) {
      return Center(
        child: Text(T('No daily report data available')),
      );
    }
    return DailySalesReportWidget(report: report);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReportController>(
      builder: (context, reportController, child) {
        final report = reportController.dailySalesReport;

        return Scaffold(
          appBar: AppBar(
            title: Row(
              children: [
                Icon(getReportIcon(), color: Colors.white),
                const SizedBox(width: 8),
                Text(
                  getReportTitle(),
                  style: const TextStyle(color: Colors.white),
                ),
              ],
            ),
            backgroundColor: getHeaderColor(),
            elevation: 0,
            actions: [
              // Filter Button
              IconButton(
                icon: const Icon(Icons.filter_alt, color: Colors.white),
                onPressed: _showFilterDialog,
                tooltip: T('Filter Report'),
              ),
              // Print and Refresh Actions
              Consumer<ReportController>(
                builder: (context, controller, child) {
                  if (controller.dailySalesReport != null) {
                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: const Icon(Icons.preview, color: Colors.white),
                          tooltip: 'معاينة التقرير',
                          onPressed: _previewReport,
                        ),
                        IconButton(
                          icon: const Icon(Icons.print, color: Colors.white),
                          tooltip: 'طباعة التقرير',
                          onPressed: _printReport,
                        ),
                        IconButton(
                          icon: const Icon(Icons.refresh, color: Colors.white),
                          tooltip: 'تحديث التقرير',
                          onPressed: generateReport,
                        ),
                      ],
                    );
                  }
                  return IconButton(
                    icon: const Icon(Icons.refresh, color: Colors.white),
                    tooltip: 'تحديث التقرير',
                    onPressed: generateReport,
                  );
                },
              ),
            ],
          ),
          body: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  getHeaderColor(),
                  getAccentColor(),
                  Colors.white,
                ],
                stops: const [0.0, 0.1, 0.3],
              ),
            ),
            child: Column(
              children: [
                // Date Range Display
                _buildDateRangeHeader(reportController),

                // Main content
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 10,
                          offset: Offset(0, -2),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                      ),
                      child: Column(
                        children: [
                          // Content based on loading state
                          Expanded(
                            child: buildReportContent(reportController),
                          ),

                          // Print Button Section
                          if (report != null)
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.grey.shade50,
                                border: Border(
                                  top: BorderSide(color: Colors.grey.shade300),
                                ),
                              ),
                              child: SizedBox(
                                width: double.infinity,
                                child: ElevatedButton.icon(
                                  onPressed: _printReport,
                                  icon: const Icon(Icons.print),
                                  label: const Text(
                                      'طباعة تقرير المبيعات اليومية'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: getHeaderColor(),
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        vertical: 12),
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Floating Print Button
          floatingActionButton: report != null
              ? FloatingActionButton(
                  onPressed: _printReport,
                  backgroundColor: getHeaderColor(),
                  tooltip: 'طباعة التقرير',
                  child: const Icon(Icons.print, color: Colors.white),
                )
              : null,
        );
      },
    );
  }

  // إنشاء PDF لتقرير المبيعات اليومية
  Future<pw.Document> _createDailySalesReportPdf(
    ReportController reportController,
    PrinterSettingsController printerSettings,
  ) async {
    final pdf = pw.Document();
    final arabicFont = await _loadArabicFont();
    final pageFormat = PdfPageFormat.a4;
    final report = reportController.dailySalesReport!;
    final calculatedReport = report.withCalculatedValues();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: pageFormat,
        margin: const pw.EdgeInsets.all(20),
        build: (context) => [
          // رأس التقرير
          _buildReportHeader(arabicFont, printerSettings),
          pw.SizedBox(height: 15),

          // معلومات فترة التقرير
          _buildReportPeriodSection(arabicFont, calculatedReport),
          pw.SizedBox(height: 20),

          // ملخص المبيعات والتدفق النقدي
          _buildSalesSummarySection(arabicFont, calculatedReport),
          pw.SizedBox(height: 20),

          // جدول الفواتير
          ..._buildInvoicesTable(arabicFont, calculatedReport),

          pw.SizedBox(height: 20),

          // ذيل التقرير
          _buildReportFooter(arabicFont, printerSettings),
        ],
      ),
    );

    return pdf;
  }

  // بناء رأس التقرير
  pw.Widget _buildReportHeader(
      pw.Font arabicFont, PrinterSettingsController printerSettings) {
    return pw.Column(
      children: [
        // شعار الشركة إذا كان مفعلاً
        if (printerSettings.printLogo && printerSettings.logoPath != null)
          pw.Center(
            child: pw.Container(
              width: 100,
              height: 40,
              decoration: pw.BoxDecoration(
                border: pw.Border.all(width: 1),
                borderRadius: pw.BorderRadius.circular(8),
              ),
              child: pw.Center(
                child: pw.Text(
                  'الشعار',
                  style: pw.TextStyle(
                    font: arabicFont,
                    fontSize: 12,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  textDirection: pw.TextDirection.rtl,
                ),
              ),
            ),
          ),

        if (printerSettings.printLogo) pw.SizedBox(height: 10),

        // رأس مخصص
        if (printerSettings.receiptHeader.isNotEmpty)
          pw.Center(
            child: pw.Text(
              printerSettings.receiptHeader,
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),

        if (printerSettings.receiptHeader.isNotEmpty) pw.SizedBox(height: 8),

        // عنوان التقرير
        pw.Center(
          child: pw.Text(
            'تقرير المبيعات اليومية',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),

        pw.SizedBox(height: 5),

        // خط فاصل
        pw.Container(
          width: double.infinity,
          height: 1,
          color: PdfColors.grey400,
        ),
      ],
    );
  }

  // بناء قسم فترة التقرير
  pw.Widget _buildReportPeriodSection(
      pw.Font arabicFont, dynamic calculatedReport) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.blue200),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'فترة التقرير',
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue700,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                calculatedReport.fromDate != null &&
                        calculatedReport.toDate != null
                    ? '${DateFormat('yyyy-MM-dd').format(calculatedReport.fromDate!)} إلى ${DateFormat('yyyy-MM-dd').format(calculatedReport.toDate!)}'
                    : 'غير محدد',
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 12,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'تاريخ الطباعة',
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue700,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now()),
                style: pw.TextStyle(
                  font: arabicFont,
                  fontSize: 12,
                ),
                textDirection: pw.TextDirection.rtl,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء قسم ملخص المبيعات
  pw.Widget _buildSalesSummarySection(
      pw.Font arabicFont, dynamic calculatedReport) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.green50,
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: PdfColors.green200),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص المبيعات والتدفق النقدي',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.green700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
          pw.SizedBox(height: 12),

          // جدول الملخص
          pw.Table(
            border: pw.TableBorder.all(color: PdfColors.green200),
            columnWidths: {
              0: const pw.FlexColumnWidth(2),
              1: const pw.FlexColumnWidth(1.5),
              2: const pw.FlexColumnWidth(2),
              3: const pw.FlexColumnWidth(1.5),
            },
            children: [
              // الصف الأول
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.green100),
                children: [
                  _buildSummaryCell(
                      'إجمالي المبيعات (قيمة الفواتير)', arabicFont,
                      isHeader: true),
                  _buildSummaryCell(
                      '${calculatedReport.totalSales.toStringAsFixed(2)}',
                      arabicFont,
                      isHeader: true),
                  _buildSummaryCell('إجمالي المرتجعات', arabicFont,
                      isHeader: true),
                  _buildSummaryCell(
                      '${calculatedReport.totalReturns.toStringAsFixed(2)}',
                      arabicFont,
                      isHeader: true),
                ],
              ),
              // الصف الثاني
              pw.TableRow(
                children: [
                  _buildSummaryCell('صافي المبيعات', arabicFont),
                  _buildSummaryCell(
                      '${calculatedReport.netSales.toStringAsFixed(2)}',
                      arabicFont,
                      color: PdfColors.green700),
                  _buildSummaryCell('إجمالي الخصم', arabicFont),
                  _buildSummaryCell(
                      '${calculatedReport.totalDiscount.toStringAsFixed(2)}',
                      arabicFont,
                      color: PdfColors.orange700),
                ],
              ),
              // الصف الثالث
              pw.TableRow(
                children: [
                  _buildSummaryCell(
                      'المدفوع فعلياً (داخل الخزينة)', arabicFont),
                  _buildSummaryCell(
                      '${calculatedReport.totalPaid.toStringAsFixed(2)}',
                      arabicFont,
                      color: PdfColors.blue700),
                  _buildSummaryCell('المتبقي (غير مدفوع)', arabicFont),
                  _buildSummaryCell(
                      '${calculatedReport.totalRemaining.toStringAsFixed(2)}',
                      arabicFont,
                      color: PdfColors.red700),
                ],
              ),
              // الصف الرابع - التدفق النقدي
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.blue50),
                children: [
                  _buildSummaryCell('الداخل للخزينة', arabicFont),
                  _buildSummaryCell(
                      '${calculatedReport.cashInflow.toStringAsFixed(2)}',
                      arabicFont,
                      color: PdfColors.green700),
                  _buildSummaryCell('الخارج من الخزينة', arabicFont),
                  _buildSummaryCell(
                      '${calculatedReport.cashOutflow.toStringAsFixed(2)}',
                      arabicFont,
                      color: PdfColors.red700),
                ],
              ),
              // الصف الخامس
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.purple50),
                children: [
                  _buildSummaryCell('صافي الحركة النقدية', arabicFont),
                  _buildSummaryCell(
                      '${calculatedReport.netCashFlow.toStringAsFixed(2)}',
                      arabicFont,
                      color: PdfColors.purple700),
                  _buildSummaryCell('عدد الفواتير', arabicFont),
                  _buildSummaryCell(
                      '${calculatedReport.invoiceCount} مبيعات | ${calculatedReport.returnInvoiceCount} مرتجعات',
                      arabicFont,
                      color: PdfColors.indigo700),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء جدول الفواتير
  List<pw.Widget> _buildInvoicesTable(
      pw.Font arabicFont, dynamic calculatedReport) {
    final invoices = calculatedReport.invoices ?? [];

    if (invoices.isEmpty) {
      return [
        pw.Center(
          child: pw.Text(
            'لا توجد فواتير',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 14,
              color: PdfColors.grey600,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      ];
    }

    List<pw.Widget> widgets = [];

    // عنوان القسم
    widgets.add(
      pw.Text(
        'تفاصيل الفواتير',
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: 16,
          fontWeight: pw.FontWeight.bold,
          color: PdfColors.indigo700,
        ),
        textDirection: pw.TextDirection.rtl,
      ),
    );

    widgets.add(pw.SizedBox(height: 5));

    // تحديد العدد الأقصى للفواتير في PDF
    const int maxInvoicesInPdf = 100;
    final invoicesToShow = invoices.length > maxInvoicesInPdf
        ? invoices.take(maxInvoicesInPdf).toList()
        : invoices;

    widgets.add(
      pw.Text(
        invoices.length > maxInvoicesInPdf
            ? 'عرض أحدث $maxInvoicesInPdf فاتورة من إجمالي ${invoices.length} فاتورة'
            : 'إجمالي الفواتير: ${invoices.length}',
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: 12,
          color: PdfColors.indigo600,
          fontWeight: pw.FontWeight.bold,
        ),
        textDirection: pw.TextDirection.rtl,
      ),
    );

    widgets.add(pw.SizedBox(height: 10));

    // تقسيم الفواتير إلى مجموعات صغيرة
    const int itemsPerTable = 20;

    for (int i = 0; i < invoicesToShow.length; i += itemsPerTable) {
      final endIndex = (i + itemsPerTable).clamp(0, invoicesToShow.length);
      final chunk = invoicesToShow.sublist(i, endIndex);

      // إضافة عنوان فرعي للمجموعات إذا كان العدد كبيراً
      if (invoicesToShow.length > itemsPerTable && i > 0) {
        widgets.add(pw.SizedBox(height: 15));
        widgets.add(
          pw.Text(
            'الفواتير ${i + 1} - $endIndex',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue700,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        );
        widgets.add(pw.SizedBox(height: 8));
      }

      // إنشاء الجدول للمجموعة الحالية
      widgets.add(
        pw.Table(
          border: pw.TableBorder.all(color: PdfColors.grey400),
          columnWidths: {
            0: const pw.FlexColumnWidth(2),
            1: const pw.FlexColumnWidth(2),
            2: const pw.FlexColumnWidth(1.5),
            3: const pw.FlexColumnWidth(1.5),
            4: const pw.FlexColumnWidth(1.5),
            5: const pw.FlexColumnWidth(1.5),
            6: const pw.FlexColumnWidth(2),
          },
          children: [
            // Header row
            if (i == 0 || i % (itemsPerTable * 2) == 0)
              pw.TableRow(
                decoration: const pw.BoxDecoration(color: PdfColors.indigo100),
                children: [
                  _buildTableCell('رقم الفاتورة', arabicFont, isHeader: true),
                  _buildTableCell('التاريخ', arabicFont, isHeader: true),
                  _buildTableCell('النوع', arabicFont, isHeader: true),
                  _buildTableCell('الإجمالي', arabicFont, isHeader: true),
                  _buildTableCell('المدفوع', arabicFont, isHeader: true),
                  _buildTableCell('المتبقي', arabicFont, isHeader: true),
                  _buildTableCell('العميل', arabicFont, isHeader: true),
                ],
              ),
            // Data rows
            ...chunk.map((invoice) {
              final isReturn =
                  invoice.transactionsType?.contains('مرتجع') ?? false;
              return pw.TableRow(
                decoration: pw.BoxDecoration(
                  color: isReturn ? PdfColors.red50 : PdfColors.green50,
                ),
                children: [
                  _buildTableCell(invoice.code ?? '', arabicFont),
                  _buildTableCell(
                    invoice.entryDate != null
                        ? DateFormat('yyyy-MM-dd').format(invoice.entryDate!)
                        : '',
                    arabicFont,
                  ),
                  _buildTableCell(
                    isReturn ? 'مرتجع' : 'مبيعات',
                    arabicFont,
                    color: isReturn ? PdfColors.red700 : PdfColors.green700,
                  ),
                  _buildTableCell(
                    '${(invoice.total ?? 0).toStringAsFixed(2)}',
                    arabicFont,
                    color: isReturn ? PdfColors.red700 : PdfColors.green700,
                  ),
                  _buildTableCell(
                    '${(invoice.paidAmount ?? 0).toStringAsFixed(2)}',
                    arabicFont,
                    color: PdfColors.blue700,
                  ),
                  _buildTableCell(
                    '${((invoice.total ?? 0) - (invoice.paidAmount ?? 0)).toStringAsFixed(2)}',
                    arabicFont,
                    color: PdfColors.orange700,
                  ),
                  _buildTableCell(
                    invoice.customerName ?? '',
                    arabicFont,
                    fontSize: 8,
                  ),
                ],
              );
            }),
          ],
        ),
      );
    }

    return widgets;
  }

  // بناء خلية الملخص
  pw.Widget _buildSummaryCell(
    String text,
    pw.Font arabicFont, {
    bool isHeader = false,
    PdfColor? color,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: isHeader ? 11 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: color ?? (isHeader ? PdfColors.black : PdfColors.black),
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // بناء خلية الجدول
  pw.Widget _buildTableCell(
    String text,
    pw.Font arabicFont, {
    bool isHeader = false,
    PdfColor? color,
    double? fontSize,
  }) {
    return pw.Padding(
      padding: const pw.EdgeInsets.all(6),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: arabicFont,
          fontSize: fontSize ?? (isHeader ? 10 : 9),
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: color ?? (isHeader ? PdfColors.black : PdfColors.black),
        ),
        textDirection: pw.TextDirection.rtl,
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  // بناء ذيل التقرير
  pw.Widget _buildReportFooter(
      pw.Font arabicFont, PrinterSettingsController printerSettings) {
    return pw.Column(
      children: [
        pw.Container(
          width: double.infinity,
          height: 1,
          color: PdfColors.grey400,
        ),
        pw.SizedBox(height: 10),
        pw.Text(
          'تاريخ الطباعة: ${DateTime.now().toString().split('.')[0]}',
          style: pw.TextStyle(
            font: arabicFont,
            fontSize: 10,
            color: PdfColors.grey600,
          ),
          textDirection: pw.TextDirection.rtl,
        ),
        if (printerSettings.receiptFooter.isNotEmpty) ...[
          pw.SizedBox(height: 8),
          pw.Center(
            child: pw.Text(
              printerSettings.receiptFooter,
              style: pw.TextStyle(
                font: arabicFont,
                fontSize: 10,
              ),
              textDirection: pw.TextDirection.rtl,
              textAlign: pw.TextAlign.center,
            ),
          ),
        ],
        pw.SizedBox(height: 8),
        pw.Center(
          child: pw.Text(
            'شكراً لاستخدام نظامنا Pal4it',
            style: pw.TextStyle(
              font: arabicFont,
              fontSize: 9,
              color: PdfColors.grey600,
            ),
            textDirection: pw.TextDirection.rtl,
          ),
        ),
      ],
    );
  }

  // تحميل الخط العربي
  Future<pw.Font> _loadArabicFont() async {
    try {
      return await ArabicFontLoader.getSafeArabicFont();
    } catch (e) {
      print('⚠️ Arabic font loading failed in DailySalesReport: $e');
      return pw.Font.helvetica();
    }
  }

  // طباعة PDF
  Future<void> _printReportPdf(
      pw.Document pdf, PrinterSettingsController printerSettings) async {
    try {
      final bool isMobile =
          !Platform.isWindows && !Platform.isLinux && !Platform.isMacOS;

      // إذا كان على الهاتف وإعدادات الطابعة غير مكونة
      if (isMobile && printerSettings.printerType == null) {
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name:
              'تقرير المبيعات اليومية - ${DateFormat('yyyy-MM-dd').format(DateTime.now())}',
          format: PdfPageFormat.a4,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال التقرير للطباعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
        return;
      }

      // طباعة حسب نوع الطابعة
      if (printerSettings.printerType == 'usb') {
        final printers = await Printing.listPrinters();
        final selectedPrinter = printers.firstWhere(
          (printer) => printer.name == printerSettings.selectedPrinterName,
          orElse: () => throw Exception('الطابعة المحددة غير متوفرة'),
        );

        final result = await Printing.directPrintPdf(
          printer: selectedPrinter,
          onLayout: (_) async => await pdf.save(),
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  result ? 'تم طباعة التقرير بنجاح' : 'فشل في طباعة التقرير'),
              backgroundColor: result ? Colors.green : Colors.red,
            ),
          );
        }
      } else {
        // طباعة عادية أو شبكة أو بلوتوث
        await Printing.layoutPdf(
          onLayout: (_) async => await pdf.save(),
          name:
              'تقرير المبيعات اليومية - ${DateFormat('yyyy-MM-dd').format(DateTime.now())}',
          format: PdfPageFormat.a4,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم إرسال التقرير للطباعة بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في طباعة التقرير: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في طباعة التقرير: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
      rethrow;
    }
  }
}
