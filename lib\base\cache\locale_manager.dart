import 'dart:async';

import 'package:shared_preferences/shared_preferences.dart';

import '../constants/enums/locale_keys_enum.dart';

class LocaleManager {
  LocaleManager._init() {
    SharedPreferences.getInstance().then((value) {
      _preferences = value;
    });
  }
  static final LocaleManager _instance = LocaleManager._init();

  SharedPreferences? _preferences;

  static LocaleManager get instance => _instance;

  static Future prefrencesInit() async {
    instance._preferences ??= await SharedPreferences.getInstance();
  }

  Future<bool> checkKey(PreferencesKeys key) async {
    if (!_preferences!.containsKey(key.toString())) {
      return false;
    }
    return true;
  }

  Future<bool> checkKeyByStringKey(String key) async {
    if (!_preferences!.containsKey(key.toString())) {
      return false;
    }
    return true;
  }

  void setString(PreferencesKeys key, String value) async {
    await _preferences?.setString(key.toString(), value);
  }

  void setInt(PreferencesKeys key, int value) async {
    await _preferences?.setInt(key.toString(), value);
  }

  void removeKey(PreferencesKeys key) async {
    await _preferences?.remove(key.toString());
  }

  void removeKeyByStringKey(String key) async {
    await _preferences?.remove(key.toString());
  }

  Future<void> setStringValue(PreferencesKeys key, String value) async {
    await _preferences!.setString(key.toString(), value);
  }

  Future<void> setStringValueByStringKey(String key, String value) async {
    print("setStringValueByStringKey: $key, $value");

    await _preferences!.setString(key.toString(), value);
  }

  String getStringValue(PreferencesKeys key) =>
      _preferences?.getString(key.toString()) ?? '';

  String getStringValueByStringKey(String key) =>
      _preferences?.getString(key.toString()) ?? '';

  void setBool(PreferencesKeys key, bool value) async {
    await _preferences?.setBool(key.toString(), value);
  }

  bool getBool(PreferencesKeys key) {
    var value = _preferences?.getBool(key.toString());
    return value ?? true;
  }
}
