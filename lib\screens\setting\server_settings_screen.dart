import 'package:flutter/material.dart';
import 'package:inventory_application/base/database/database_helper%20.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/auth/%C4%B0magebgWidget.dart';
import 'package:inventory_application/screens/auth/SignIn_screen.dart';
import 'package:inventory_application/screens/components/back_button_header.dart';
import 'package:inventory_application/screens/components/common_button.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:provider/provider.dart';

class ServerSettingsScreen extends StatefulWidget {
  const ServerSettingsScreen({super.key, required this.isFirstTime});
  final bool isFirstTime;
  @override
  State<ServerSettingsScreen> createState() => _ServerSettingsScreenState();
}

class _ServerSettingsScreenState extends State<ServerSettingsScreen> {
  final TextEditingController _serverController = TextEditingController();
  final TextEditingController _skylanAccountingHelperController =
      TextEditingController();
  final TextEditingController _ecommerceServerController =
      TextEditingController();

  bool isConnectionTested = false;
  bool isConnectionTestedOnEcommerce = false;
  bool isConnectionTestedOnSkylanAccountingHelper = false;
  var baseUrl = "";
  var ecommerceBaseUrl = "";
  var skyLandHelperBaseUrl = "";
  var isUsingEcommerce = false;

  @override
  void initState() {
    baseUrl = AppController.getBaseUrlFromShared();
    if (baseUrl.isNotEmpty) {
      isConnectionTested = true;
      _serverController.text = baseUrl;
    }
    ecommerceBaseUrl = AppController.getEcommerceBaseUrlFromShared();
    if (ecommerceBaseUrl.isNotEmpty) {
      isConnectionTestedOnEcommerce = true;
      _ecommerceServerController.text = ecommerceBaseUrl;
    }
    isUsingEcommerce = AppController.getIsUsingEcommerceFromShared();

    skyLandHelperBaseUrl =
        AppController.getSkylanAccountingHelperURLFromShared();
    if (skyLandHelperBaseUrl.isNotEmpty) {
      isConnectionTestedOnSkylanAccountingHelper = true;
      _skylanAccountingHelperController.text = skyLandHelperBaseUrl;
    }

    _skylanAccountingHelperController.text = skyLandHelperBaseUrl;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: Stack(
        children: [
          Column(
            children: [
              if (!widget
                  .isFirstTime) // Only show back button if not first time setup
                BackButtonHeader(
                  title: T('Server Settings'),
                  icon: Icons.settings_remote,
                ),
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color.fromARGB(255, 200, 183, 245),
                          Color(0xFF895FF9),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                    child: Column(
                      children: [
                        ImageBgWidget(
                          height: 300,
                          image: "assets/images/base_images/219.jpg",
                        ),
                        const SizedBox(
                          height: 15,
                        ),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Text(
                                    T('Server Settings'),
                                    // textAlign: TextAlign.center,
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 20,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Row(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 15),
                                    child: Container(
                                      width: context.width - 50,
                                      decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          border:
                                              Border.all(color: Colors.white),
                                          borderRadius:
                                              BorderRadius.circular(15)),
                                      child: TextField(
                                        controller: _serverController,
                                        onChanged: (value) {
                                          baseUrl = value;
                                          setState(() {
                                            isConnectionTested = false;
                                          });
                                        },
                                        style: const TextStyle(
                                            color: Colors.white),
                                        decoration: InputDecoration(
                                          prefixIcon: const Icon(Icons.dataset,
                                              color: Colors.white),
                                          hintText: T('Server ID'),
                                          hintStyle: const TextStyle(
                                              color: Colors.white),
                                          border: InputBorder.none,
                                          filled: true,
                                          fillColor:
                                              Colors.white.withOpacity(0.1),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              isConnectionTested
                                  ? CommonMaterialButton(
                                      width: context.width - 40,
                                      label: T("Connected"),
                                      onPressed: () {},
                                    )
                                  : CommonMaterialButton(
                                      width: context.width - 40,
                                      label: T("Try connection"),
                                      backgroundColor: context.onSecondary,
                                      borderColor: context.onSecondary,
                                      onPressed: () async {
                                        var result =
                                            await Provider.of<AppController>(
                                                    context,
                                                    listen: false)
                                                .testServerConnection(baseUrl);

                                        if (result == false) {
                                          errorSnackBar(
                                              message: T(
                                                  "The server address is incorrect!"));
                                        }
                                        setState(() {
                                          isConnectionTested = result;
                                        });
                                      },
                                    ),
                              const SizedBox(height: 15),
                              Row(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 15),
                                    child: Container(
                                      width: context.width - 50,
                                      decoration: BoxDecoration(
                                          color: Colors.white.withOpacity(0.2),
                                          border:
                                              Border.all(color: Colors.white),
                                          borderRadius:
                                              BorderRadius.circular(15)),
                                      child: TextField(
                                        controller:
                                            _skylanAccountingHelperController,
                                        onChanged: (value) {
                                          skyLandHelperBaseUrl = value;
                                          setState(() {
                                            isConnectionTestedOnSkylanAccountingHelper =
                                                false;
                                          });
                                        },
                                        style: const TextStyle(
                                            color: Colors.white),
                                        decoration: InputDecoration(
                                          prefixIcon: const Icon(Icons.dataset,
                                              color: Colors.white),
                                          hintText:
                                              T('Skyland helper Server ID'),
                                          hintStyle: const TextStyle(
                                              color: Colors.white),
                                          border: InputBorder.none,
                                          filled: true,
                                          fillColor:
                                              Colors.white.withOpacity(0.1),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 10),
                              isConnectionTestedOnSkylanAccountingHelper
                                  ? CommonMaterialButton(
                                      width: context.width - 40,
                                      label: T("Connected"),
                                      onPressed: () {},
                                    )
                                  : CommonMaterialButton(
                                      width: context.width - 40,
                                      label: T("Try connection"),
                                      backgroundColor: context.onSecondary,
                                      borderColor: context.onSecondary,
                                      onPressed: () async {
                                        var result = await Provider.of<
                                                    AppController>(context,
                                                listen: false)
                                            .testSkylanAccountingHelperConnection(
                                                skyLandHelperBaseUrl);

                                        if (result == false) {
                                          errorSnackBar(
                                              message: T(
                                                  "The server address is incorrect!"));
                                        }
                                        setState(() {
                                          isConnectionTestedOnSkylanAccountingHelper =
                                              result;
                                        });
                                      },
                                    ),
                              SwitchListTile(
                                title: Text(
                                  T('هل يستخدم Ecommerce?'),
                                  style: TextStyle(color: Colors.white),
                                ),
                                subtitle: Text(
                                    T(
                                      'عند تفعيل هذا الخيار يكون التطبيق متصل ايضا بالمتجر الالكتروني',
                                    ),
                                    style: TextStyle(color: Colors.white)),
                                value: isUsingEcommerce,
                                onChanged: (value) async {
                                  await Provider.of<AppController>(context,
                                          listen: false)
                                      .setIsUsingEcommerceStatus(value);
                                  setState(() {
                                    isUsingEcommerce = value;
                                  });
                                },
                                activeColor: context.newPrimaryColor,
                              ),
                              if (isUsingEcommerce)
                                Column(
                                  children: [
                                    Row(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 15),
                                          child: Container(
                                            width: context.width - 50,
                                            decoration: BoxDecoration(
                                                color: Colors.white
                                                    .withOpacity(0.2),
                                                border: Border.all(
                                                    color: Colors.white),
                                                borderRadius:
                                                    BorderRadius.circular(15)),
                                            child: TextField(
                                              controller:
                                                  _ecommerceServerController,
                                              onChanged: (value) {
                                                ecommerceBaseUrl = value;
                                                setState(() {
                                                  isConnectionTestedOnEcommerce =
                                                      false;
                                                });
                                              },
                                              style: const TextStyle(
                                                  color: Colors.white),
                                              decoration: InputDecoration(
                                                prefixIcon: const Icon(
                                                    Icons.dataset,
                                                    color: Colors.white),
                                                hintText:
                                                    T('Ecommerce Server ID'),
                                                hintStyle: const TextStyle(
                                                    color: Colors.white),
                                                border: InputBorder.none,
                                                filled: true,
                                                fillColor: Colors.white
                                                    .withOpacity(0.1),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 10),
                                    isConnectionTestedOnEcommerce
                                        ? CommonMaterialButton(
                                            width: context.width - 40,
                                            label: T("Connected"),
                                            onPressed: () {},
                                          )
                                        : CommonMaterialButton(
                                            width: context.width - 40,
                                            label: T("Try connection"),
                                            backgroundColor:
                                                context.onSecondary,
                                            borderColor: context.onSecondary,
                                            onPressed: () async {
                                              var result = await Provider.of<
                                                          AppController>(
                                                      context,
                                                      listen: false)
                                                  .testEcommerceServerConnection(
                                                      _ecommerceServerController
                                                          .text);

                                              if (result == false) {
                                                errorSnackBar(
                                                    message: T(
                                                        "The server address is incorrect!"));
                                              }
                                              setState(() {
                                                isConnectionTestedOnEcommerce =
                                                    result;
                                              });
                                            },
                                          ),
                                  ],
                                ),

                              const SizedBox(height: 10),
                              widget.isFirstTime == false
                                  ? Column(
                                      children: [
                                        Row(
                                          children: [
                                            Text(
                                              T("Local Server Settings"),
                                              // textAlign: TextAlign.center,
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 20,
                                              ),
                                            ),
                                          ],
                                        ),
                                        const SizedBox(height: 10),
                                        InkWell(
                                          onTap: () async {
                                            pleaseWaitDialog(
                                                context: context,
                                                isShown: true);

                                            try {
                                              await DatabaseHelper()
                                                  .createBackup();
                                              successSnackBar(
                                                message: T(
                                                    "Backup completed successfully"),
                                                context: context,
                                              );
                                            } catch (e) {
                                              errorSnackBar(
                                                message: T("Backup failed"),
                                                context: context,
                                              );
                                            } finally {
                                              pleaseWaitDialog(
                                                  context: context,
                                                  isShown: false);
                                            }
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 10, horizontal: 15),
                                            width: context.width - 40,
                                            decoration: BoxDecoration(
                                              color: context.colors.secondary,
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                const Icon(Icons.backup,
                                                    color: Colors.white),
                                                const SizedBox(width: 10),
                                                Text(
                                                  T("Backup"),
                                                  style: const TextStyle(
                                                      fontSize: 16,
                                                      color: Colors.white),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        InkWell(
                                          onTap: () async {
                                            pleaseWaitDialog(
                                                context: context,
                                                isShown: true);
                                            try {
                                              await DatabaseHelper()
                                                  .selectAndRestoreDatabase();
                                              successSnackBar(
                                                  message: T(
                                                      "Backup completed successfully"),
                                                  // ignore: use_build_context_synchronously
                                                  context: context);
                                            } catch (e) {
                                              errorSnackBar(
                                                  message: T("Backup failed"),
                                                  // ignore: use_build_context_synchronously
                                                  context: context);
                                            } finally {
                                              pleaseWaitDialog(
                                                  context: context,
                                                  isShown: false);
                                            }
                                          },
                                          child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 10, horizontal: 15),
                                            width: context.width - 40,
                                            decoration: BoxDecoration(
                                              color: context.colors.secondary,
                                              borderRadius:
                                                  BorderRadius.circular(20),
                                            ),
                                            child: Row(
                                              children: [
                                                const Icon(
                                                  Icons.cloud_download,
                                                  color: Colors.white,
                                                ),
                                                const SizedBox(width: 10),
                                                Text(
                                                  T("Restore Backup"),
                                                  style: const TextStyle(
                                                      fontSize: 16,
                                                      color: Colors.white),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                        const SizedBox(height: 10),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            FutureBuilder(
                                              future: DatabaseHelper()
                                                  .getBackupFolder(),
                                              builder: (context, snapshot) {
                                                return Container(
                                                  width: context.width - 50,
                                                  decoration: BoxDecoration(
                                                      color: Colors.white
                                                          .withOpacity(0.2),
                                                      border: Border.all(
                                                          color: Colors.white),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              15)),
                                                  child: TextField(
                                                    controller:
                                                        TextEditingController(
                                                            text:
                                                                snapshot.data),
                                                    onChanged: (value) {
                                                      baseUrl = value;
                                                      setState(() {
                                                        isConnectionTested =
                                                            false;
                                                      });
                                                    },
                                                    style: const TextStyle(
                                                        color: Colors.white),
                                                    decoration: InputDecoration(
                                                      prefixIcon: InkWell(
                                                        onTap: () async {
                                                          await DatabaseHelper()
                                                              .setBackupFolder();
                                                          setState(() {});
                                                        },
                                                        child: const Icon(
                                                            Icons.edit,
                                                            color:
                                                                Colors.white),
                                                      ),
                                                      hintText: T("Server ID"),
                                                      hintStyle:
                                                          const TextStyle(
                                                              color:
                                                                  Colors.white),
                                                      border: InputBorder.none,
                                                      filled: true,
                                                      fillColor: Colors.white
                                                          .withOpacity(0.1),
                                                    ),
                                                  ),
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ],
                                    )
                                  : const SizedBox(),
                              // const SizedBox(height: 5),

                              const SizedBox(height: 10),
                              CommonMaterialButton(
                                onPressed: () {
                                  if (widget.isFirstTime) {
                                    if (isConnectionTested) {
                                      Navigator.of(context).pushReplacement(
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              const SignInScreen(),
                                        ),
                                      );
                                    } else {
                                      errorSnackBar(
                                          message: T(
                                              "You must connect to the server first."));
                                      return;
                                    }
                                  } else {
                                    Navigator.of(context).pop();
                                  }
                                },
                                width: context.width - 40,
                                backgroundColor: context.colors.onPrimary,
                                borderColor: context.colors.onPrimary,
                                label: T("Finish"),
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
