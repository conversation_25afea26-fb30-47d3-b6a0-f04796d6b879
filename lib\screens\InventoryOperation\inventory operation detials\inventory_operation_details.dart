import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/base/extension/string_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';

import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/model/inventory_operation_model.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20detials/widget/inventory_details_base_info_widget.dart';
import 'package:inventory_application/screens/InventoryOperation/inventory%20operation%20edit/inventory_operation_edit_screen.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/services/printer_service.dart';
import 'package:provider/provider.dart';

class InvetoryDetailsPage extends StatefulWidget {
  final int id;
  final InventoryOperationType operationType;
  final bool? isFromLocal;
  const InvetoryDetailsPage(
      {super.key,
      required this.id,
      required this.operationType,
      this.isFromLocal = false});

  @override
  State<InvetoryDetailsPage> createState() => _InvetoryDetailsPageState();
}

class _InvetoryDetailsPageState extends State<InvetoryDetailsPage> {
  @override
  void initState() {
    super.initState();
  }

  void _showConfirmDialog({
    required String title,
    required String content,
    required String confirmText,
    required Function delete,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15),
          ),
          title: Text(
            title,
            style: TextStyle(
              color: context.newSecondaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: Text(
            content,
            style: TextStyle(
              color: context.newTextColor,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
              child: Text(
                T("Cancel"),
                style: TextStyle(
                  color: context.newTextColor,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(dialogContext).pop();
                delete();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text(confirmText),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: SingleChildScrollView(
        child: Column(
          children: [
            // Modern Header
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: context.newBackgroundColor,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonHeader(
                    icon: Icons.receipt_long,
                    title: T("Inventory details"),
                  ),
                ],
              ),
            ),

            // Invoice Data Section
            FutureBuilder<InventoryOperationModel?>(
              future: widget.isFromLocal == true
                  ? Provider.of<InventoryOperationController>(context,
                          listen: false)
                      .getOperationById(
                      widget.id,
                    )
                  : Provider.of<InventoryOperationController>(context,
                          listen: false)
                      .getInvoiceByTypeAndID(
                      type: widget.operationType.name,
                      id: widget.id.toString(),
                    ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return Container(
                    height: 300,
                    alignment: Alignment.center,
                    child: CircularProgressIndicator(
                      color: context.newPrimaryColor,
                    ),
                  );
                } else if (snapshot.hasError) {
                  return Container(
                    margin: const EdgeInsets.all(20),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.red.withOpacity(0.1),
                          spreadRadius: 1,
                          blurRadius: 3,
                          offset: const Offset(0, 1),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          T("Error loading invoice data"),
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: context.newTextColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${snapshot.error}',
                          style: TextStyle(
                            color: context.newTextColor.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  );
                } else if (snapshot.hasData && snapshot.data != null) {
                  return Column(
                    children: [
                      // Base Info Section
                      Container(
                        margin: const EdgeInsets.only(
                            top: 15, left: 15, right: 15, bottom: 5),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: context.newPrimaryColor.withOpacity(0.15),
                              spreadRadius: 1,
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section Header
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 15),
                              decoration: BoxDecoration(
                                color: context.newSecondaryColor,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(15),
                                  topRight: Radius.circular(15),
                                ),
                              ),
                              child: Text(
                                T("Inventory Information"),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),

                            // Base Info Widget
                            InventoryDetailsBaseInfoWidget(data: snapshot.data),
                          ],
                        ),
                      ),

                      // Items Section

                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 13, vertical: 8),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 15, vertical: 8),
                          decoration: BoxDecoration(
                            color: context.newBackgroundColor.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(10),
                            border: Border.all(
                              color: context.newPrimaryColor.withOpacity(0.3),
                            ),
                          ),
                          child: Column(
                            children: [
                              // Section Header
                              Container(
                                width: double.infinity,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 8),
                                margin: const EdgeInsets.only(bottom: 12),
                                decoration: BoxDecoration(
                                  color:
                                      context.newPrimaryColor.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  T("Inventory items"),
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    color: context.newSecondaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: snapshot.data
                                        ?.inventoryOperationItems?.length ??
                                    0,
                                itemBuilder: (context, index) {
                                  final item = snapshot
                                      .data?.inventoryOperationItems?[index];

                                  return Card(
                                    margin: const EdgeInsets.symmetric(
                                        vertical: 10),
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "${T("Code")} : ${T(item?.itemCode.toString() ?? "")}",
                                            style: TextStyle(
                                              color: context.newSecondaryColor,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          SizedBox(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width /
                                                1.5,
                                            child: Text(
                                              "${T("name")} : ${T(item?.itemName ?? "")} ",
                                              style: TextStyle(
                                                color:
                                                    context.newSecondaryColor,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          item?.batchNumber == null
                                              ? const SizedBox()
                                              : SizedBox(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width /
                                                      1.5,
                                                  child: Text(
                                                    "${"Batch Number"} : ${T(item?.batchNumber ?? "")} ",
                                                    style: TextStyle(
                                                      color: context
                                                          .newSecondaryColor,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                          item?.expirationDate == null
                                              ? const SizedBox()
                                              : SizedBox(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width /
                                                      1.5,
                                                  child: Text(
                                                    "${T("Expiration Date")} : ${"".myDateFormatter(item?.expirationDate, isShowTime: false)} ",
                                                    style: TextStyle(
                                                      color: context
                                                          .newSecondaryColor,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                    ),
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ),
                                          SizedBox(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width /
                                                1.5,
                                            child: Text(
                                              " ${item?.attribute?.map((e) => e.itemsAttributeOptions?.map((e) => e.optionName).join(",") ?? "").join(", ") ?? T("No Attributes")} ",
                                              style: TextStyle(
                                                color: context.onSurface,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          Text(
                                            "${T("Quantity")} : ${T(item?.quantity.toString() ?? "")}",
                                            style: TextStyle(
                                              color: context.newSecondaryColor,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          Text(
                                            "${T("Price")} : ${T(item?.unitPrice.toString() ?? "")}",
                                            style: TextStyle(
                                              color: context.newSecondaryColor,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              ),

                              const Divider(),
                            ],
                          ),
                        ),
                      ),
                      // Action Buttons Section
                      Container(
                        margin: const EdgeInsets.only(
                            left: 15, right: 15, bottom: 25),
                        padding: const EdgeInsets.all(15),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(15),
                          boxShadow: [
                            BoxShadow(
                              color: context.newPrimaryColor.withOpacity(0.15),
                              spreadRadius: 1,
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Action Buttons Header
                            Container(
                              width: double.infinity,
                              margin: const EdgeInsets.only(bottom: 15),
                              padding: const EdgeInsets.symmetric(vertical: 8),
                              decoration: BoxDecoration(
                                color: context.newBackgroundColor,
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color:
                                      context.newPrimaryColor.withOpacity(0.3),
                                ),
                              ),
                              child: Text(
                                T("Inventory Actions"),
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: context.newSecondaryColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),

                            // First Row Buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Duplicate Button

                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () async {
                                      if (snapshot.data?.isPosted == true) {
                                        errorSnackBar(
                                            message: T(
                                                "This inventory is already posted and cannot be edited."));
                                        return;
                                      } else {
                                        snapshot.data?.submit = "Post";
                                        final controller = Provider.of<
                                                InventoryOperationController>(
                                            context,
                                            listen: false);
                                        final success = await controller
                                            .editInventoryOperation(
                                          model: snapshot.data ??
                                              InventoryOperationModel(),
                                          type: getOperationTypeFromCode(
                                                      snapshot.data?.code ?? '')
                                                  ?.name ??
                                              '',
                                        );

                                        if (success) {
                                          successSnackBar(
                                              message: T(
                                                  'Shipment Posted successfully.'),
                                              context: context);

                                          snapshot.data?.isPosted = true;
                                          setState(() {});
                                        } else {
                                          errorSnackBar(
                                              message: T(
                                                  'Failed to update shipment.'),
                                              context: context);
                                        }
                                      }
                                    },
                                    icon: const Icon(Icons.output_outlined,
                                        size: 18),
                                    label: snapshot.data?.isPosted == true
                                        ? Text(T("Posted"))
                                        : Text(T("Post")),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          snapshot.data?.isPosted == true
                                              ? context.onPrimary
                                              : context.onSecondary,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),
                                // Edit Button
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      final data = snapshot.data;

                                      if (data == null) return;

                                      if (data.isPosted == true) {
                                        errorSnackBar(
                                            message: T(
                                                "This inventory is already posted and cannot be edited."));
                                        return;
                                      }
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder: (context) =>
                                              InventoryOperationEditScreen(
                                            model: data,
                                          ),
                                        ),
                                      );
                                    },
                                    icon: const Icon(Icons.edit, size: 18),
                                    label: Text(T("Edit")),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor:
                                          context.newSecondaryColor,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 10),

                            // Second Row Buttons
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Print Button
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () async {
                                      try {
                                        // Print directly using PrinterService
                                        await PrinterServiceforinventory
                                            .printInventoryOperation(
                                          snapshot.data!,
                                          context,
                                        );
                                      } catch (e) {
                                        errorSnackBar(
                                          message:
                                              'حدث خطأ أثناء طباعة الفاتورة: $e',
                                        );
                                      }
                                    },
                                    icon: const Icon(Icons.print, size: 18),
                                    label: Text(T("Print")),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.green,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 10),

                                // Delete Button
                                Expanded(
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      // _showConfirmDialog(
                                      //   title: T("Warning"),
                                      //   content: T(
                                      //       "Are you sure you want to delete this invoice?"),
                                      //   confirmText: T("Delete"),
                                      //   delete: () async {
                                      //     bool result = await Provider.of<
                                      //                 InvoiceController>(
                                      //             context,
                                      //             listen: false)
                                      //         .deleteSyncInvoice(
                                      //             widget.model.id ?? 0,
                                      //             widget.model.invoiceCode !=
                                      //                     null
                                      //                 ? widget.model
                                      //                         .invoiceCode!
                                      //                         .startsWith("SI")
                                      //                     ? "Invoice"
                                      //                     : "RetrunInvoice"
                                      //                 : "");
                                      //     if (result) {
                                      //       // ignore: use_build_context_synchronously
                                      //       Navigator.of(context)
                                      //           .pushReplacement(
                                      //               MaterialPageRoute(
                                      //         builder: (context) =>
                                      //             const InvoicesListScreenScreen(),
                                      //       ));
                                      //       successSnackBar(
                                      //           message:
                                      //               T("Successfully deleted"));
                                      //     } else {
                                      //       errorSnackBar(
                                      //           message: T(
                                      //               "Delete failed. Please try again later"));
                                      //     }
                                      //   },
                                      // );
                                    },
                                    icon: const Icon(Icons.delete_outline,
                                        size: 18),
                                    label: Text(T("Delete")),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }

                return Container(
                  margin: const EdgeInsets.all(20),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: context.newTextColor.withOpacity(0.5),
                        size: 48,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        T("No data available"),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: context.newTextColor,
                        ),
                      ),
                    ],
                  ),
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
