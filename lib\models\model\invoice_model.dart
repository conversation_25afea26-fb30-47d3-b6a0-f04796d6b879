import 'package:inventory_application/helpers/function.dart';
// ignore: unused_import
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';

class InvoiceModel {
  int? iD;
  int? localId;
  int? pyamentMethodId;
  String? code;
  String? appReferanceCode;
  String? entryDateFormated;
  DateTime? entryDate;
  String? receiptNo;
  String? proformaInvoiceID;
  int? customerID;
  String? customerName;
  int? salesmanId;
  String? salesmanName;
  String? salesOrderID;
  int? storeID;
  int? pyamentTypeId;
  String? storeName;
  String? notes;
  String? itemsCategory;
  String? ddlItems;
  String? transactionsType;
  List<SalesItems>? salesItems;
  String? driverName;
  String? driverCarNumber;
  String? salesInvoiceID;
  String? shippingPeriod;
  double? discountValue;
  double? vATPercent;
  double? paidAmount;
  double? total;

  bool? isFullyPaid;

  InvoiceModel(
      {this.iD,
      this.localId,
      this.pyamentMethodId,
      this.pyamentTypeId,
      this.code,
      this.appReferanceCode,
      this.entryDate,
      this.entryDateFormated,
      this.receiptNo,
      this.proformaInvoiceID,
      this.customerID,
      this.salesOrderID,
      this.storeID,
      this.storeName,
      this.salesmanId,
      this.salesmanName,
      this.notes,
      this.itemsCategory,
      this.ddlItems,
      this.transactionsType,
      this.salesItems,
      this.driverName,
      this.driverCarNumber,
      this.shippingPeriod,
      this.salesInvoiceID,
      this.discountValue,
      this.vATPercent,
      this.paidAmount,
      this.customerName,
      this.isFullyPaid,
      this.total});

  InvoiceModel.fromJson(Map<String, dynamic> json) {
    iD = json['ID'];
    code = json['Code'];
    appReferanceCode = json['APP_Referance_Code'];
    entryDateFormated = json['Entry_Date_Formated'];
    entryDate = json['entryDate'] != null
        ? json['entryDate'].toString().contains("-")
            ? DateTime.parse(json['entryDate'])
            : formatDate(json['Entry_Date'])
        : DateTime.now();
    receiptNo = json['Receipt_No'];
    proformaInvoiceID = json['Proforma_Invoice_ID'];
    customerID = json['Customer_ID'];
    customerName = json['Customer_Name'];
    salesmanId = json['Salesman_ID'];
    salesmanName = json['Salesman_Name'];
    pyamentMethodId = json['Pyament_Method_Id'];
    pyamentTypeId = json['Pyament_Type_Id'];

    salesOrderID = json['Sales_Order_ID'];
    salesInvoiceID = json['Sales_Invoice_ID'];
    storeID = json['Store_ID'];
    storeName = json['storeName'];
    notes = json['Notes'];
    itemsCategory = json['ItemsCategory'];
    ddlItems = json['ddlItems'];
    transactionsType = json['transactions_type'];
    if (json['SalesItems'] != null) {
      salesItems = <SalesItems>[];
      json['SalesItems'].forEach((v) {
        salesItems!.add(new SalesItems.fromJson(v));
      });
    }
    driverName = json['Driver_Name'];
    driverCarNumber = json['Driver_Car_Number'];
    total = json['total'];
    shippingPeriod = json['Shipping_Period'];
    discountValue = json['Discount_Value'] != null
        ? double.parse((json['Discount_Value'].toString()))
        : 0.0;
    vATPercent = json['VAT_Percent'] != null
        ? double.parse((json['VAT_Percent'].toString()))
        : 0.0;

    paidAmount = json['Paid_Amount'];
    isFullyPaid = json['Is_Fully_Paid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ID'] = iD;
    data['Code'] = code;
    data['APP_Referance_Code'] = appReferanceCode;
    data['Pyament_Method_Id'] = pyamentMethodId;
    data['Pyament_Type_Id'] = pyamentTypeId;

    data['Entry_Date_Formated'] = entryDateFormated;
    data['entryDate'] = entryDate?.toIso8601String();
    data['Receipt_No'] = receiptNo;
    data['Proforma_Invoice_ID'] = proformaInvoiceID;
    data['Customer_ID'] = customerID;
    data['Customer_Name'] = customerName;
    data['Salesman_ID'] = salesmanId;
    data['Salesman_Name'] = salesmanName;
    data['Sales_Invoice_ID'] = salesInvoiceID;
    data['Sales_Order_ID'] = salesOrderID;
    data['Store_ID'] = storeID;
    data['storeName'] = storeName;
    data['Notes'] = notes;
    data['ItemsCategory'] = itemsCategory;
    data['ddlItems'] = ddlItems;
    data['transactions_type'] = transactionsType;
    if (salesItems != null) {
      data['SalesItems'] = salesItems!.map((v) => v.toJson()).toList();
    }
    data['Driver_Name'] = driverName;
    data['Driver_Car_Number'] = driverCarNumber;

    data['Shipping_Period'] = shippingPeriod;
    data['Discount_Value'] = discountValue;

    data['Paid_Amount'] = paidAmount;
    data['total'] = total;

    return data;
  }
}

class SalesItems {
  int? itemID;
  String? itemName;
  String? barcode;
  String? itemCode;
  int? salesItemID;
  int? storeID;
  String? storeName;
  double? unitPrice;
  double? quantity;
  int? unitID;
  String? unitName;
  double? discountValue;
  List<Inventory>? inventory;
  String? virtualProductId;
  bool? hasSelectedAttributes;
  List<ItemAttribute>? itemAttributes;
  Map<String, String>? selectedAttributesMap;
  List<int>? selectedOptionIds;
  String? selectedAttribute;
  String? batchNumber;
  String? serialNumber;
  DateTime? expirationDate;
  List<ItemAttribute>? attribute;
  List<ItemAttribute>? itemAttribute;

  SalesItems(
      {this.itemID,
      this.itemName,
      this.barcode,
      this.itemCode,
      this.salesItemID,
      this.storeID,
      this.storeName,
      this.unitPrice,
      this.quantity,
      this.unitID,
      this.discountValue,
      this.inventory,
      this.unitName,
      this.virtualProductId,
      this.hasSelectedAttributes,
      this.itemAttributes,
      this.selectedAttributesMap,
      this.selectedAttribute,
      this.itemAttribute,
      this.attribute,
      this.batchNumber,
      this.serialNumber,
      this.expirationDate,
      this.selectedOptionIds}) {
    storeName = storeID != null
        ? inventory
            ?.firstWhere(
              (element) => element.iD == storeID,
            )
            .storeName
        : "";
  }

  SalesItems.fromJson(Map<String, dynamic> json) {
    itemID = json['Item_ID'];
    itemName = json['Item_Name'];
    itemCode = json['Item_Code'];

    barcode = json['barcode'];
    salesItemID = json['Sales_Item_ID'];
    storeID = json['Store_ID'];
    unitPrice = json['Unit_Price'];
    quantity = json['Quantity'];
    unitID = json['Unit_ID'];
    unitName = json['Unit_Name'];
    discountValue = json['Unit_Discount_Price'];
    virtualProductId = json['Virtual_Product_ID'];
    hasSelectedAttributes = json['Has_Selected_Attributes'];
    selectedAttribute = json['SelectedAttribute'];
    batchNumber = json['Batch_Number'];
    serialNumber = json['Serial_Number'];
    expirationDate = json['Expiration_Date'] != null
        ? json['Expiration_Date'].toString().contains("-")
            ? DateTime.parse(json['Expiration_Date'])
            : parseAspNetDate(json['Expiration_Date'])
        : DateTime.now();

    if (json['Item_Attributes'] != null) {
      itemAttributes = <ItemAttribute>[];
      json['Item_Attributes'].forEach((v) {
        itemAttributes!.add(ItemAttribute.fromJson(v));
      });
    }

    if (json['Selected_Attributes_Map'] != null) {
      selectedAttributesMap =
          Map<String, String>.from(json['Selected_Attributes_Map']);
    }

    if (json['Selected_Option_Ids'] != null) {
      selectedOptionIds = List<int>.from(json['Selected_Option_Ids']);
    }
    if (json['ItemAttribute'] != null) {
      // Deserialize ItemAttributes
      itemAttribute = <ItemAttribute>[];
      json['ItemAttribute'].forEach((v) {
        itemAttribute!.add(ItemAttribute.fromJson(v));
      });
    }
    if (json['Attribute'] != null) {
      // Deserialize ItemAttributes
      attribute = <ItemAttribute>[];
      json['Attribute'].forEach((v) {
        attribute!.add(ItemAttribute.fromJson(v));
      });
    }

    if (json['Inventory'] != null) {
      inventory = <Inventory>[];
      json['Inventory'].forEach((v) {
        inventory!.add(Inventory.fromJson(v));
      });
    }

    storeName = storeID != null
        ? inventory
            ?.firstWhere(
              (element) => element.iD == storeID,
            )
            .storeName
        : "";
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Item_ID'] = itemID;
    data['Item_Name'] = itemName;
    data['barcode'] = barcode;
    data['Item_Code'] = itemCode;
    data['Sales_Item_ID'] = salesItemID;
    data['Store_ID'] = storeID;
    data['Unit_Price'] = unitPrice;
    data['Quantity'] = quantity;
    data['Unit_ID'] = unitID;
    data['Unit_Name'] = unitName;
    data['Discount_Value'] = discountValue;
    data['Virtual_Product_ID'] = virtualProductId;
    data['Has_Selected_Attributes'] = hasSelectedAttributes;
    data['Batch_Number'] = batchNumber;
    data['Serial_Number'] = serialNumber;
    data['Expiration_Date'] = expirationDate?.toIso8601String();
    data['SelectedAttribute'] = selectedAttribute;
    if (itemAttribute != null) {
      data['ItemAttribute'] = itemAttribute!.map((v) => v.toJson()).toList();
    }
    if (attribute != null) {
      data['Attribute'] = attribute!.map((v) => v.toJson()).toList();
    }
    if (itemAttributes != null) {
      data['Item_Attributes'] = itemAttributes!.map((v) => v.toJson()).toList();
    }

    if (selectedAttributesMap != null) {
      data['Selected_Attributes_Map'] = selectedAttributesMap;
    }

    if (selectedOptionIds != null) {
      data['Selected_Option_Ids'] = selectedOptionIds;
    }

    if (inventory != null) {
      data['Inventory'] = inventory!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

//----------------------------------------------------------------------------

