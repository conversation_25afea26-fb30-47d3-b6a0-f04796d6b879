import 'package:inventory_application/models/model/warehouse_3d_model.dart';

class Warehouse3DDemoData {
  /// إنشاء بيانات تجريبية للمستودع ثلاثي الأبعاد
  static Warehouse3D createDemoWarehouse() {
    // إنشاء المستودع الرئيسي - أكبر وأكثر احترافية
    final warehouse = Warehouse3D(
      id: 1,
      name: 'المستودع الرئيسي - مستشفى الأمل',
      description:
          'مستودع الأدوية والمعدات الطبية الرئيسي - تصميم احترافي ثلاثي الأبعاد',
      length: 200.0, // أكبر للمزيد من الخزائن
      width: 120.0, // أوسع للممرات
      height: 50.0, // أعلى للأرفف الطويلة
      xPosition: 0.0,
      yPosition: 0.0,
      zPosition: 0.0,
      cabinets: [],
    );

    // إنشاء الخزائن بتصميم احترافي ومنظم
    final cabinets = <Cabinet3D>[
      // الصف الأول - الجانب الأيسر (الأدوية الأساسية)
      Cabinet3D(
        id: 1,
        name: 'خزانة الأدوية الأساسية A1',
        code: 'MED-A1-001',
        warehouseId: 1,
        length: 35.0,
        width: 20.0,
        height: 40.0,
        xPosition: -60.0,
        yPosition: -30.0,
        zPosition: 0.0,
        color: '#3498DB',
        shelves: [],
      ),

      Cabinet3D(
        id: 2,
        name: 'خزانة الأدوية الأساسية A2',
        code: 'MED-A2-001',
        warehouseId: 1,
        length: 35.0,
        width: 20.0,
        height: 40.0,
        xPosition: -60.0,
        yPosition: 10.0,
        zPosition: 0.0,
        color: '#3498DB',
        shelves: [],
      ),

      // الصف الثاني - الوسط (المضادات الحيوية)
      Cabinet3D(
        id: 3,
        name: 'خزانة المضادات الحيوية B1',
        code: 'ANT-B1-001',
        warehouseId: 1,
        length: 30.0,
        width: 18.0,
        height: 35.0,
        xPosition: 0.0,
        yPosition: -30.0,
        zPosition: 0.0,
        color: '#E74C3C',
        shelves: [],
      ),

      Cabinet3D(
        id: 4,
        name: 'خزانة المضادات الحيوية B2',
        code: 'ANT-B2-001',
        warehouseId: 1,
        length: 30.0,
        width: 18.0,
        height: 35.0,
        xPosition: 0.0,
        yPosition: 10.0,
        zPosition: 0.0,
        color: '#E74C3C',
        shelves: [],
      ),

      // الصف الثالث - الجانب الأيمن (الفيتامينات والمكملات)
      Cabinet3D(
        id: 5,
        name: 'خزانة الفيتامينات C1',
        code: 'VIT-C1-001',
        warehouseId: 1,
        length: 32.0,
        width: 20.0,
        height: 38.0,
        xPosition: 60.0,
        yPosition: -30.0,
        zPosition: 0.0,
        color: '#2ECC71',
        shelves: [],
      ),

      Cabinet3D(
        id: 6,
        name: 'خزانة الفيتامينات C2',
        code: 'VIT-C2-001',
        warehouseId: 1,
        length: 32.0,
        width: 20.0,
        height: 38.0,
        xPosition: 60.0,
        yPosition: 10.0,
        zPosition: 0.0,
        color: '#2ECC71',
        shelves: [],
      ),

      // خزائن متخصصة
      Cabinet3D(
        id: 7,
        name: 'خزانة الأدوية المبردة',
        code: 'COLD-001',
        warehouseId: 1,
        length: 25.0,
        width: 15.0,
        height: 30.0,
        xPosition: -30.0,
        yPosition: 35.0,
        zPosition: 0.0,
        color: '#1ABC9C',
        shelves: [],
      ),

      Cabinet3D(
        id: 8,
        name: 'خزانة المعدات الطبية',
        code: 'EQUIP-001',
        warehouseId: 1,
        length: 40.0,
        width: 25.0,
        height: 45.0,
        xPosition: 30.0,
        yPosition: 35.0,
        zPosition: 0.0,
        color: '#9B59B6',
        shelves: [],
      ),
    ];

    // إضافة الأرفف لكل خزانة
    for (var i = 0; i < cabinets.length; i++) {
      final cabinet = cabinets[i];
      cabinet.shelves = _createShelvesForCabinet(cabinet);
    }

    warehouse.cabinets = cabinets;
    return warehouse;
  }

  /// إنشاء الأرفف لخزانة معينة
  static List<Shelf3D> _createShelvesForCabinet(Cabinet3D cabinet) {
    final shelves = <Shelf3D>[];
    final shelfHeight = 5.0;
    final numberOfShelves =
        ((cabinet.height ?? 30) / (shelfHeight + 2)).floor();

    for (int i = 0; i < numberOfShelves; i++) {
      final shelf = Shelf3D(
        id: (cabinet.id! * 10) + i + 1,
        name: '${cabinet.name} - رف ${i + 1}',
        code: '${cabinet.code}-S${i + 1}',
        cabinetId: cabinet.id,
        length: (cabinet.length ?? 25) - 2,
        width: (cabinet.width ?? 15) - 2,
        height: shelfHeight,
        xPosition: 1.0,
        yPosition: 1.0,
        zPosition: (i * (shelfHeight + 2)) + 5.0,
        status: _getRandomShelfStatus(),
        maxCapacity: 50,
        currentOccupancy: _getRandomOccupancy(),
        products: [],
      );

      // إضافة منتجات تجريبية لبعض الأرفف
      if (shelf.currentOccupancy! > 0) {
        shelf.products = _createDemoProducts(shelf);
      }

      shelves.add(shelf);
    }

    return shelves;
  }

  /// إنشاء منتجات تجريبية للرف
  static List<ProductLocation3D> _createDemoProducts(Shelf3D shelf) {
    final products = <ProductLocation3D>[];
    final numProducts = (shelf.currentOccupancy! / 10).ceil();

    final demoProductNames = [
      'باراسيتامول 500 مج', // مسكن ألم شائع
      'أيبوبروفين 400 مج', // مضاد التهاب
      'أموكسيسيلين 250 مج', // مضاد حيوي
      'فيتامين د 1000 وحدة', // فيتامين أساسي
      'حقن أنسولين', // لمرضى السكري
      'مطهر طبي يدوي', // نظافة اليدين
      'شاش طبي معقم', // ضمادات
      'قفازات طبية مطاطية', // حماية
      'محلول ملحي للحقن', // سوائل وريدية
      'كريم مضاد للالتهاب', // استخدام خارجي
      'أقراص ضغط الدم', // أدوية القلب
      'شراب كحة للأطفال', // أدوية الجهاز التنفسي
      'كبسولات فيتامين ب12', // مكملات غذائية
      'مرهم مضاد حيوي', // علاج الجروح
      'أكياس ملح الإمهاء', // علاج الجفاف
    ];

    for (int i = 0; i < numProducts; i++) {
      final product = ProductLocation3D(
        id: (shelf.id! * 100) + i + 1,
        productId: 1000 + i,
        productName: demoProductNames[i % demoProductNames.length],
        productCode: 'MED-${1000 + i}',
        shelfId: shelf.id,
        cabinetId: shelf.cabinetId,
        warehouseId: 1,
        quantity: _getRandomQuantity(),
        xPosition: (i % 3) * 5.0,
        yPosition: (i ~/ 3) * 3.0,
        zPosition: 1.0,
        lastUpdated: DateTime.now().subtract(Duration(days: i)),
        batchNumber:
            'BATCH-${DateTime.now().year}-${(i + 1).toString().padLeft(3, '0')}',
        expiryDate: DateTime.now().add(Duration(days: 365 + (i * 30))),
      );

      products.add(product);
    }

    return products;
  }

  /// إنشاء حالة رف عشوائية
  static ShelfStatus _getRandomShelfStatus() {
    final statuses = [
      ShelfStatus.empty,
      ShelfStatus.partial,
      ShelfStatus.full,
      ShelfStatus.overloaded,
    ];
    return statuses[DateTime.now().millisecond % statuses.length];
  }

  /// إنشاء إشغال عشوائي للرف
  static int _getRandomOccupancy() {
    final occupancies = [0, 15, 25, 35, 45, 50, 55];
    return occupancies[DateTime.now().millisecond % occupancies.length];
  }

  /// إنشاء كمية عشوائية للمنتج
  static double _getRandomQuantity() {
    final quantities = [5.0, 10.0, 15.0, 20.0, 25.0, 30.0];
    return quantities[DateTime.now().millisecond % quantities.length];
  }

  /// مصطلحات البحث التجريبية
  static List<String> getDemoSearchTerms() {
    return [
      'باراسيتامول', // ابحث عن مسكن الألم الشائع
      'أيبوبروفين', // مضاد التهاب قوي
      'أموكسيسيلين', // مضاد حيوي واسع المجال
      'فيتامين د', // فيتامين مهم للعظام
      'أنسولين', // دواء السكري
      'MED-1001', // كود منتج محدد
      'MED-1005', // منتج آخر بالكود
      'BATCH-2024-001', // دفعة محددة
      'مطهر يدوي', // منتج النظافة
      'شاش معقم', // مستلزم طبي
      'ضغط الدم', // فئة أدوية القلب
      'شراب كحة', // أدوية الأطفال
      'CAB-A01', // كود خزانة محددة
      'قفازات مطاطية', // معدات الحماية
      'ملح الإمهاء', // علاج الجفاف
    ];
  }

  /// نصائح للاستخدام
  static List<String> getUsageTips() {
    return [
      '🎮 التحكم في العرض: اسحب بإصبعك لدوران المستودع ثلاثي الأبعاد',
      '🔍 البحث الذكي: ابحث بالاسم أو الكود أو رقم الدفعة للعثور على أي منتج',
      '📍 تحديد المواقع: انقر على المنتج ليضيء موقعه في المستودع',
      '🎨 رموز الألوان: أزرق = أدوية عامة، أحمر = مضادات حيوية، أخضر = فيتامينات',
      '⚡ البحث السريع: جرب البحث عن "باراسيتامول" أو "فيتامين د"',
      '🔍 أزرار الزوم: استخدم + و - للتكبير والتصغير للحصول على رؤية أفضل',
      '↺ إعادة التعيين: اضغط زر الإعادة لاستعادة المنظور الأساسي',
      '📊 نصيحة: ابدأ بالزوم للخارج لرؤية المستودع كاملاً، ثم كبر للتفاصيل',
      '🎯 للتجربة: ابحث عن "MED-1001" أو "CAB-A01" لرؤية النتائج مباشرة',
      '🔄 النظام محدث: المواقع تتحدث تلقائياً مع حركة المخزون الفعلية',
    ];
  }
}
