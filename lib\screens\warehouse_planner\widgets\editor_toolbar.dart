import 'package:flutter/material.dart';
import '../../../models/warehouse_planner/editor_state.dart';

/// شريط أدوات المحرر
class EditorToolbar extends StatelessWidget {
  final EditMode currentMode;
  final Function(EditMode) onModeChanged;
  final VoidCallback onUndo;
  final VoidCallback onRedo;
  final VoidCallback onZoomFit;
  final VoidCallback onToggleGrid;
  final VoidCallback onToggleMeasurements;

  const EditorToolbar({
    Key? key,
    required this.currentMode,
    required this.onModeChanged,
    required this.onUndo,
    required this.onRedo,
    required this.onZoomFit,
    required this.onToggleGrid,
    required this.onToggleMeasurements,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Text(
            'أدوات الرسم',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 16),

          // أزرار الأوضاع
          _buildModeSection(),

          const SizedBox(height: 20),
          const Divider(),
          const SizedBox(height: 20),

          // أدوات التحكم
          _buildControlSection(),
        ],
      ),
    );
  }

  Widget _buildModeSection() {
    return Column(
      children: [
        // وضع التحديد
        _buildModeButton(
          mode: EditMode.select,
          icon: Icons.pan_tool,
          label: 'تحديد',
          description: 'تحديد وتحريك العناصر',
        ),

        const SizedBox(height: 8),

        // وضع رسم الجدران
        _buildModeButton(
          mode: EditMode.drawWall,
          icon: Icons.linear_scale,
          label: 'رسم جدار',
          description: 'انقر لإضافة نقاط الجدار',
        ),

        const SizedBox(height: 8),

        // وضع مسح الجدران
        _buildModeButton(
          mode: EditMode.deleteWall,
          icon: Icons.delete_outline,
          label: 'مسح جدار',
          description: 'انقر على جدار لحذفه',
          color: Colors.red,
        ),

        const SizedBox(height: 8),

        // وضع إضافة المداخل
        _buildModeButton(
          mode: EditMode.addEntrance,
          icon: Icons.door_front_door,
          label: 'إضافة مدخل',
          description: 'انقر على جدار لإضافة مدخل',
        ),

        const SizedBox(height: 8),

        // وضع وضع الخزائن
        _buildModeButton(
          mode: EditMode.placeShelf,
          icon: Icons.add_box,
          label: 'وضع خزانة',
          description: 'اختر قالب وانقر للوضع',
        ),

        const SizedBox(height: 8),

        // وضع تعديل الخزائن
        _buildModeButton(
          mode: EditMode.editShelf,
          icon: Icons.edit,
          label: 'تعديل خزانة',
          description: 'تعديل وتدوير الخزائن',
        ),
      ],
    );
  }

  Widget _buildModeButton({
    required EditMode mode,
    required IconData icon,
    required String label,
    required String description,
    Color? color,
  }) {
    final isActive = currentMode == mode;

    return Container(
      decoration: BoxDecoration(
        gradient: isActive
            ? LinearGradient(
                colors: [
                  color ?? _getModeColor(mode),
                  (color ?? _getModeColor(mode)).withOpacity(0.8)
                ],
              )
            : null,
        color: isActive ? null : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isActive ? (color ?? _getModeColor(mode)) : Colors.grey.shade300,
          width: isActive ? 2 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () => onModeChanged(mode),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: isActive
                        ? Colors.white.withOpacity(0.2)
                        : (color ?? _getModeColor(mode)).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: isActive
                        ? Colors.white
                        : (color ?? _getModeColor(mode)),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        label,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: isActive ? Colors.white : Colors.grey.shade800,
                        ),
                      ),
                      Text(
                        description,
                        style: TextStyle(
                          fontSize: 11,
                          color:
                              isActive ? Colors.white70 : Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildControlSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          'أدوات التحكم',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: Colors.grey.shade700,
          ),
        ),
        const SizedBox(height: 12),

        // صف الأزرار الأول
        Row(
          children: [
            Expanded(
              child: _buildControlButton(
                icon: Icons.undo,
                label: 'تراجع',
                onPressed: onUndo,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildControlButton(
                icon: Icons.redo,
                label: 'إعادة',
                onPressed: onRedo,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // صف الأزرار الثاني
        Row(
          children: [
            Expanded(
              child: _buildControlButton(
                icon: Icons.zoom_out_map,
                label: 'تكبير كامل',
                onPressed: onZoomFit,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildControlButton(
                icon: Icons.grid_on,
                label: 'الشبكة',
                onPressed: onToggleGrid,
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),

        // زر القياسات
        _buildControlButton(
          icon: Icons.straighten,
          label: 'القياسات',
          onPressed: onToggleMeasurements,
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
  }) {
    return Container(
      height: 40,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 16),
        label: Text(
          label,
          style: const TextStyle(fontSize: 11),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.grey.shade100,
          foregroundColor: Colors.grey.shade700,
          elevation: 0,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Color _getModeColor(EditMode mode) {
    switch (mode) {
      case EditMode.select:
        return Colors.grey.shade600;
      case EditMode.drawWall:
        return const Color(0xFF34495E);
      case EditMode.deleteWall:
        return Colors.red;
      case EditMode.addEntrance:
        return const Color(0xFFE67E22);
      case EditMode.placeShelf:
        return const Color(0xFF3498DB);
      case EditMode.editShelf:
        return const Color(0xFF9B59B6);
    }
  }
}
