{"v": "5.6.5", "fr": 24, "ip": 0, "op": 28, "w": 30, "h": 30, "nm": "walk", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "lottie Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [10.949, 13.123, 0], "ix": 2}, "a": {"a": 0, "k": [4.338, 4.288, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0.45, 0.887], [0.495, -0.241], [0, 0], [0.098, -0.215], [0, 0], [-0.514, -0.202], [-0.168, 0.369], [0, 0], [0, 0]], "o": [[-0.238, -0.499], [0, 0], [-0.212, 0.101], [0, 0], [-0.228, 0.503], [0.637, 0.25], [0, 0], [0, 0], [1.165, -0.303]], "v": [[3.638, -3.327], [2.305, -3.798], [-1.612, -1.923], [-2.091, -1.434], [-3.86, 2.464], [-3.362, 3.788], [-2.038, 3.29], [-0.592, 0.105], [2.86, -0.924]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0.45, 0.887], [0.495, -0.241], [0, 0], [0.064, -0.313], [0, 0], [-0.712, -0.193], [-0.083, 0.555], [0, 0], [0, 0]], "o": [[-0.238, -0.499], [0, 0], [-0.212, 0.223], [0, 0], [-0.151, 0.631], [0.726, 0.057], [0, 0], [0, 0], [1.165, -0.303]], "v": [[3.638, -3.327], [2.305, -3.798], [-0.831, -0.892], [-1.294, -0.075], [-1.798, 3.746], [-1.018, 5.07], [0.087, 4.072], [0.533, 0.636], [2.86, -0.924]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0.846, 0.524], [0.297, -0.463], [0, 0], [-0.108, -0.301], [0, 0], [-0.708, 0.206], [0.218, 0.517], [0, 0], [0, 0]], "o": [[-0.463, -0.302], [0, 0], [-0.065, 0.3], [0, 0], [0.199, 0.618], [0.65, -0.328], [0, 0], [0, 0], [0.837, -0.865]], "v": [[3.156, -3.348], [1.773, -3.057], [0.606, 1.056], [0.635, 1.995], [2.192, 5.52], [3.546, 6.245], [3.971, 4.818], [2.565, 1.651], [3.741, -0.891]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[0.97, 0.221], [0.131, -0.535], [0, 0], [-0.2, -0.249], [0, 0], [-0.603, 0.425], [0.374, 0.419], [0, 0], [0, 0]], "o": [[-0.536, -0.136], [0, 0], [0.036, 0.305], [0, 0], [0.389, 0.52], [0.508, -0.521], [0, 0], [0, 0], [0.511, -1.09]], "v": [[2.8, -2.881], [1.586, -2.156], [1.818, 2.113], [2.151, 2.991], [4.768, 5.82], [6.285, 6.066], [6.223, 4.578], [3.865, 2.04], [4.151, -0.747]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0.971, -0.216], [-0.11, -0.539], [0, 0], [-0.288, -0.14], [0, 0], [-0.363, 0.642], [0.517, 0.218], [0, 0], [0, 0]], "o": [[-0.542, 0.107], [0, 0], [0.163, 0.261], [0, 0], [0.574, 0.304], [0.236, -0.689], [0, 0], [0, 0], [-0.004, -1.204]], "v": [[2.645, -2.592], [1.859, -1.418], [3.894, 2.341], [4.571, 2.993], [8.147, 4.43], [9.623, 4.004], [8.931, 2.685], [5.713, 1.4], [4.78, -1.242]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[0.89, 0.444], [0.254, -0.489], [0, 0], [-0.136, -0.29], [0, 0], [-0.686, 0.27], [0.265, 0.495], [0, 0], [0, 0]], "o": [[-0.489, -0.259], [0, 0], [-0.037, 0.305], [0, 0], [0.255, 0.597], [0.617, -0.387], [0, 0], [0, 0], [0.754, -0.938]], "v": [[3.774, -3.663], [2.424, -3.245], [1.64, 0.957], [1.755, 1.89], [3.63, 5.257], [5.045, 5.855], [5.337, 4.394], [3.646, 1.37], [4.583, -1.27]], "c": true}]}, {"t": 28, "s": [{"i": [[0.45, 0.887], [0.495, -0.241], [0, 0], [0.098, -0.215], [0, 0], [-0.514, -0.202], [-0.168, 0.369], [0, 0], [0, 0]], "o": [[-0.238, -0.499], [0, 0], [-0.212, 0.101], [0, 0], [-0.228, 0.503], [0.637, 0.25], [0, 0], [0, 0], [1.165, -0.303]], "v": [[3.638, -3.327], [2.305, -3.798], [-1.612, -1.923], [-2.091, -1.434], [-3.86, 2.464], [-3.362, 3.788], [-2.038, 3.29], [-0.592, 0.105], [2.86, -0.924]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.338, 4.288], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "lottie Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [16.5, 5.5, 0], "ix": 2}, "a": {"a": 0, "k": [2.75, 2.75, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[-1.381, 0], [0, 1.381], [1.381, 0], [0, -1.38]], "o": [[1.381, 0], [0, -1.38], [-1.381, 0], [0, 1.381]], "v": [[0, 2.5], [2.5, 0], [0, -2.5], [-2.5, 0]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[-1.381, 0], [0, 1.381], [1.381, 0], [0, -1.38]], "o": [[1.381, 0], [0, -1.38], [-1.381, 0], [0, 1.381]], "v": [[-0.344, 2.5], [2.156, 0], [-0.344, -2.5], [-2.844, 0]], "c": true}]}, {"t": 28, "s": [{"i": [[-1.381, 0], [0, 1.381], [1.381, 0], [0, -1.38]], "o": [[1.381, 0], [0, -1.38], [-1.381, 0], [0, 1.381]], "v": [[0, 2.5], [2.5, 0], [0, -2.5], [-2.5, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [2.75, 2.75], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "lottie Outlines", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15.001, 18, 0], "ix": 2}, "a": {"a": 0, "k": [4.25, 9.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0.064, 0.101], [0, 0], [0, 0], [-0.001, 0.006], [0, 0], [0, 0], [0, 0.193], [1.382, 0.001], [0, 0], [0.253, -1.113], [0, 0], [0, 0], [0, 0], [0, -0.171], [-0.55, -0.459], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.485, 0], [0, 0.552], [0.014, 0.064]], "o": [[0, 0], [-0.023, -0.118], [0, 0], [0, 0], [0.001, -0.006], [0, 0], [0, 0], [0.042, -0.18], [0, -1.38], [0, 0], [-1.19, 0], [0, 0], [-0.3, 1.5], [0, 0], [-0.033, 0.161], [0, 0.772], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.092, 0.457], [0.552, 0], [0, -0.068], [0, 0]], "v": [[3.98, 7.804], [2.98, 2.804], [2.834, 2.479], [2.837, 2.459], [0.949, -0.998], [0.951, -1.016], [1.933, -5.938], [1.931, -5.941], [2, -6.5], [-0.5, -9], [-0.501, -9], [-2.935, -7.053], [-2.945, -7.016], [-3.951, -2], [-3.95, -1.999], [-4, -1.5], [-3.093, 0.412], [-3.085, 0.433], [0.807, 3.5], [1.918, 7.695], [2.019, 8.197], [2.039, 8.193], [3, 9], [4, 8], [3.96, 7.808]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0, 0], [0, 0], [0.064, 0.101], [0, 0], [0, 0], [-0.001, 0.006], [0, 0], [0, 0], [0, 0.193], [1.382, 0.001], [0, 0], [0.253, -1.113], [0, 0], [0, 0], [0, 0], [0, -0.171], [-0.55, -0.459], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.626, 0.141], [0, 0.552], [0.014, 0.064]], "o": [[0, 0], [-0.023, -0.118], [0, 0], [0, 0], [0.001, -0.006], [0, 0], [0, 0], [0.042, -0.18], [0, -1.38], [0, 0], [-1.19, 0], [0, 0], [-0.3, 1.5], [0, 0], [-0.033, 0.161], [0, 0.772], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.226, 0.401], [0.421, -0.188], [0, -0.068], [0, 0]], "v": [[3.98, 7.804], [2.355, 3.023], [2.209, 2.698], [2.212, 2.678], [0.949, -0.998], [0.951, -1.016], [1.933, -5.938], [1.931, -5.941], [2, -6.5], [-0.5, -9], [-0.501, -9], [-2.935, -7.053], [-2.949, -7.004], [-3.951, -2], [-3.95, -1.999], [-4, -1.5], [-3.093, 0.412], [-3.085, 0.433], [0.026, 3.813], [1.871, 7.883], [2.019, 8.197], [2.039, 8.193], [3.375, 8.922], [4, 8], [3.96, 7.808]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0.001, 0.13], [0, 0], [0, 0], [-0.001, 0.006], [0, 0], [0, 0], [0, 0.193], [1.382, 0.001], [0, 0], [0.253, -1.113], [0, 0], [0, 0], [0, 0], [0, -0.171], [-0.55, -0.459], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.485, 0], [0, 0.552], [0.014, 0.064]], "o": [[0, 0], [-0.004, -0.124], [0, 0], [0, 0], [0.001, -0.006], [0, 0], [0, 0], [0.042, -0.18], [0, -1.38], [0, 0], [-1.19, 0], [0, 0], [-0.3, 1.5], [0, 0], [-0.033, 0.161], [0, 0.772], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.07, 0.51], [0.552, 0], [0, -0.068], [0, 0]], "v": [[0.925, 7.851], [0.933, 3.445], [0.928, 3.073], [0.931, 3.053], [0.949, -0.998], [0.951, -1.016], [1.933, -5.938], [1.931, -5.941], [2, -6.5], [-0.5, -9], [-0.501, -9], [-2.935, -7.053], [-2.953, -7.004], [-3.951, -2], [-3.95, -1.999], [-4, -1.5], [-3.093, 0.412], [-3.085, 0.433], [-1.693, 3.531], [-1.223, 7.765], [-1.184, 8.15], [-1.148, 8.334], [-0.078, 9.109], [0.922, 8.125], [0.929, 7.988]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [-0.038, 0.208], [0, 0], [0, 0], [-0.001, 0.006], [0, 0], [0, 0], [0, 0.193], [1.382, 0.001], [0, 0], [0.253, -1.113], [0, 0], [0, 0], [0, 0], [0, -0.171], [-0.55, -0.459], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.485, 0], [-0.43, 0.586], [-0.07, 0.098]], "o": [[0, 0], [0.066, -0.179], [0, 0], [0, 0], [0.001, -0.006], [0, 0], [0, 0], [0.042, -0.18], [0, -1.38], [0, 0], [-1.19, 0], [0, 0], [-0.3, 1.5], [0, 0], [-0.033, 0.161], [0, 0.772], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.165, 0.525], [0.552, 0], [0.078, -0.118], [0, 0]], "v": [[-1.934, 7.945], [-0.395, 4.382], [-0.213, 3.854], [-0.194, 3.834], [0.949, -0.998], [0.951, -1.016], [1.933, -5.938], [1.931, -5.941], [2, -6.5], [-0.5, -9], [-0.501, -9], [-2.935, -7.053], [-2.953, -6.984], [-3.951, -2], [-3.95, -1.999], [-4, -1.5], [-3.093, 0.412], [-3.085, 0.433], [-2.755, 4.156], [-4.02, 7.453], [-4.09, 7.619], [-4.148, 7.771], [-3.476, 9.016], [-2.227, 8.461], [-2.056, 8.199]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [-0.038, 0.208], [0, 0], [0, 0], [-0.001, 0.006], [0, 0], [0, 0], [0, 0.193], [1.382, 0.001], [0, 0], [0.253, -1.113], [0, 0], [0, 0], [0, 0], [0, -0.171], [-0.55, -0.459], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.485, 0], [-0.43, 0.586], [-0.07, 0.098]], "o": [[0, 0], [0.066, -0.179], [0, 0], [0, 0], [0.001, -0.006], [0, 0], [0, 0], [0.042, -0.18], [0, -1.38], [0, 0], [-1.19, 0], [0, 0], [-0.3, 1.5], [0, 0], [-0.033, 0.161], [0, 0.772], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.165, 0.69], [0.552, 0], [0.078, -0.118], [0, 0]], "v": [[-2.403, 7.78], [-0.833, 4.695], [-0.65, 4.167], [-0.632, 4.146], [0.949, -0.998], [0.951, -1.016], [1.933, -5.938], [1.931, -5.941], [2, -6.5], [-0.5, -9], [-0.501, -9], [-2.935, -7.053], [-2.949, -7.004], [-3.951, -2], [-3.95, -1.999], [-4, -1.5], [-3.093, 0.412], [-3.085, 0.433], [-3.349, 4.156], [-4.441, 7.257], [-4.512, 7.423], [-4.57, 7.576], [-3.898, 8.977], [-2.695, 8.312], [-2.524, 8.034]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[0, 0], [0, 0], [0.001, 0.193], [0, 0], [0, 0], [-0.001, 0.006], [0, 0], [0, 0], [0, 0.193], [1.382, 0.001], [0, 0], [0.253, -1.113], [0, 0], [0, 0], [0, 0], [0, -0.171], [-0.55, -0.459], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.485, -0.407], [-0.305, 0.242], [-0.086, 0.091]], "o": [[0, 0], [0.137, -0.124], [0, 0], [0, 0], [0.001, -0.006], [0, 0], [0, 0], [0.042, -0.18], [0, -1.38], [0, 0], [-1.19, 0], [0, 0], [-0.3, 1.5], [0, 0], [-0.033, 0.161], [0, 0.772], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.438, 0.479], [0.39, 0.304], [0.093, -0.039], [0, 0]], "v": [[-2.645, 7.718], [0.886, 4.413], [1.092, 3.901], [1.087, 3.865], [0.949, -0.998], [0.951, -1.016], [1.933, -5.938], [1.931, -5.941], [2, -6.5], [-0.5, -9], [-0.501, -9], [-2.935, -7.053], [-2.953, -6.984], [-3.951, -2], [-3.95, -1.999], [-4, -1.5], [-3.093, 0.412], [-3.085, 0.433], [-1.74, 3.641], [-4.332, 6.445], [-4.379, 6.509], [-4.391, 6.521], [-4.312, 8.086], [-2.984, 8.062], [-2.751, 7.839]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0.025, 0.287], [0, 0], [0, 0], [-0.001, 0.006], [0, 0], [0, 0], [0, 0.193], [1.382, 0.001], [0, 0], [0.253, -1.113], [0, 0], [0, 0], [0, 0], [0, -0.171], [-0.55, -0.459], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.423, -0.164], [-0.219, 0.297], [-0.078, 0.177]], "o": [[0, 0], [0.066, -0.179], [0, 0], [0, 0], [0.001, -0.006], [0, 0], [0, 0], [0.042, -0.18], [0, -1.38], [0, 0], [-1.19, 0], [0, 0], [-0.3, 1.5], [0, 0], [-0.033, 0.161], [0, 0.772], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.079, 0.635], [0.343, 0.164], [0.062, -0.094], [0, 0]], "v": [[1.574, 7.64], [2.855, 4.101], [2.99, 3.338], [2.915, 3.146], [0.949, -0.998], [0.951, -1.016], [1.933, -5.938], [1.931, -5.941], [2, -6.5], [-0.5, -9], [-0.501, -9], [-2.935, -7.053], [-2.953, -6.984], [-3.951, -2], [-3.95, -1.999], [-4, -1.5], [-3.093, 0.412], [-3.085, 0.433], [0.338, 3.563], [-0.457, 6.945], [-0.489, 7.087], [-0.516, 7.208], [0.11, 8.367], [1.297, 8.172], [1.468, 7.855]], "c": true}]}, {"t": 28, "s": [{"i": [[0, 0], [0, 0], [0.064, 0.101], [0, 0], [0, 0], [-0.001, 0.006], [0, 0], [0, 0], [0, 0.193], [1.382, 0.001], [0, 0], [0.253, -1.113], [0, 0], [0, 0], [0, 0], [0, -0.171], [-0.55, -0.459], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.485, 0], [0, 0.552], [0.014, 0.064]], "o": [[0, 0], [-0.023, -0.118], [0, 0], [0, 0], [0.001, -0.006], [0, 0], [0, 0], [0.042, -0.18], [0, -1.38], [0, 0], [-1.19, 0], [0, 0], [-0.3, 1.5], [0, 0], [-0.033, 0.161], [0, 0.772], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.092, 0.457], [0.552, 0], [0, -0.068], [0, 0]], "v": [[3.98, 7.804], [2.98, 2.804], [2.834, 2.479], [2.837, 2.459], [0.949, -0.998], [0.951, -1.016], [1.933, -5.938], [1.931, -5.941], [2, -6.5], [-0.5, -9], [-0.501, -9], [-2.935, -7.053], [-2.953, -6.977], [-3.951, -2], [-3.95, -1.999], [-4, -1.5], [-3.093, 0.412], [-3.085, 0.433], [0.807, 3.5], [1.918, 7.695], [2.019, 8.197], [2.039, 8.193], [3, 9], [4, 8], [3.96, 7.808]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [4.25, 9.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "lottie Outlines", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15, 18, 0], "ix": 2}, "a": {"a": 0, "k": [6.25, 11.25, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0.014, 0.095], [0.017, 0.086], [0, 0], [0.104, 0.197], [0.03, 0.055], [0, 0], [0, 0], [-0.004, 0.02], [0, 0.295], [2.483, 0.002], [0.618, -1.809], [0.041, -0.202], [0.096, -0.784], [0, 0], [0, -0.3], [-0.919, -0.853], [-0.085, -0.067], [0, 0], [0, 0], [0, 0], [-0.067, -0.142], [-1.252, 0], [0, 1.654]], "o": [[-0.006, -0.084], [0, 0], [-0.06, -0.299], [-0.024, -0.058], [0, 0], [0, 0], [0.005, -0.02], [0.063, -0.305], [0, -2.479], [-1.948, 0], [-0.098, 0.177], [-0.562, 2.813], [0, 0], [-0.06, 0.295], [0, 1.248], [0.073, 0.079], [0, 0], [0, 0], [0, 0], [0.032, 0.16], [0.431, 1.157], [1.655, 0], [0, -0.125]], "v": [[5.976, 7.667], [5.942, 7.411], [4.943, 2.415], [4.675, 1.669], [4.593, 1.501], [3.052, -1.32], [3.895, -5.547], [3.907, -5.607], [4, -6.501], [-0.501, -11.001], [-4.751, -7.965], [-4.961, -7.393], [-5.899, -2.395], [-5.909, -2.396], [-6, -1.499], [-4.558, 1.785], [-4.322, 2.005], [-0.955, 4.658], [-0.029, 8.148], [0.059, 8.589], [0.209, 9.044], [3, 11.001], [6, 8.001]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0.014, 0.095], [0.017, 0.086], [0, 0], [0.104, 0.197], [0.03, 0.055], [0, 0], [0, 0], [-0.004, 0.02], [0, 0.295], [2.483, 0.002], [0.618, -1.809], [0.041, -0.202], [0.096, -0.784], [0, 0], [0, -0.3], [-0.919, -0.853], [-0.085, -0.067], [0, 0], [0, 0], [0, 0], [-0.067, -0.142], [-1.252, 0], [0, 1.654]], "o": [[-0.006, -0.084], [0, 0], [-0.06, -0.299], [-0.024, -0.058], [0, 0], [0, 0], [0.005, -0.02], [0.063, -0.305], [0, -2.479], [-1.948, 0], [-0.098, 0.177], [-0.562, 2.813], [0, 0], [-0.06, 0.295], [0, 1.248], [0.073, 0.079], [0, 0], [0, 0], [0, 0], [0.032, 0.16], [0.431, 1.157], [1.655, 0], [0, -0.125]], "v": [[5.976, 7.667], [5.942, 7.411], [4.162, 2.79], [3.894, 2.044], [3.812, 1.876], [3.052, -1.32], [3.895, -5.547], [3.907, -5.607], [4, -6.501], [-0.501, -11.001], [-4.751, -7.965], [-4.961, -7.393], [-5.899, -2.395], [-5.909, -2.396], [-6, -1.499], [-4.558, 1.785], [-4.322, 2.005], [-1.736, 5.033], [-0.029, 8.148], [0.059, 8.589], [0.209, 9.044], [3, 11.001], [6, 8.001]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0.014, 0.095], [0.017, 0.086], [0, 0], [0.104, 0.197], [0.03, 0.055], [0, 0], [0, 0], [-0.004, 0.02], [0, 0.295], [2.483, 0.002], [0.618, -1.809], [0.041, -0.202], [0.096, -0.784], [0, 0], [0, -0.3], [-0.919, -0.853], [-0.085, -0.067], [0, 0], [0, 0], [0, 0], [-0.067, -0.142], [-1.252, 0], [0, 1.654]], "o": [[-0.006, -0.084], [0, 0], [-0.06, -0.299], [-0.024, -0.058], [0, 0], [0, 0], [0.005, -0.02], [0.063, -0.305], [0, -2.479], [-1.948, 0], [-0.098, 0.177], [-0.562, 2.813], [0, 0], [-0.06, 0.295], [0, 1.248], [0.073, 0.079], [0, 0], [0, 0], [0, 0], [0.032, 0.16], [0.431, 1.157], [1.655, 0], [0, -0.125]], "v": [[2.82, 7.792], [2.786, 7.536], [2.974, 3.072], [3.019, 1.857], [2.937, 1.688], [3.052, -1.32], [3.895, -5.547], [3.907, -5.607], [4, -6.501], [-0.501, -11.001], [-4.751, -7.965], [-4.961, -7.393], [-5.899, -2.395], [-5.909, -2.396], [-6, -1.499], [-4.558, 1.785], [-4.322, 2.005], [-3.174, 4.033], [-2.935, 8.398], [-2.847, 8.839], [-2.697, 9.294], [-0.156, 11.126], [2.844, 8.126]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[0.014, 0.095], [0.017, 0.086], [0, 0], [0.104, 0.197], [0.03, 0.055], [0, 0], [0, 0], [-0.004, 0.02], [0, 0.295], [2.483, 0.002], [0.618, -1.809], [0.041, -0.202], [0.096, -0.784], [0, 0], [0, -0.3], [-0.919, -0.853], [-0.085, -0.067], [0, 0], [0, 0], [0, 0], [-0.067, -0.142], [-1.252, 0], [0, 1.654]], "o": [[-0.006, -0.084], [0, 0], [-0.06, -0.299], [-0.024, -0.058], [0, 0], [0, 0], [0.005, -0.02], [0.063, -0.305], [0, -2.479], [-1.948, 0], [-0.098, 0.177], [-0.562, 2.813], [0, 0], [-0.06, 0.295], [0, 1.248], [0.073, 0.079], [0, 0], [0, 0], [0, 0], [0.032, 0.16], [0.431, 1.157], [1.655, 0], [0, -0.125]], "v": [[0.257, 8.354], [0.348, 8.036], [1.505, 5.134], [3.019, 1.857], [2.937, 1.688], [3.052, -1.32], [3.895, -5.547], [3.907, -5.607], [4, -6.501], [-0.501, -11.001], [-4.751, -7.965], [-4.961, -7.393], [-5.899, -2.395], [-5.909, -2.396], [-6, -1.499], [-4.558, 1.785], [-4.322, 2.005], [-4.58, 4.064], [-6.092, 8.648], [-5.66, 9.277], [-5.51, 9.731], [-2.719, 11.688], [0.281, 8.688]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0.014, 0.095], [0.017, 0.086], [0, 0], [0.097, 0.163], [0.03, 0.055], [0, 0], [0, 0], [-0.004, 0.02], [0, 0.295], [2.483, 0.002], [0.618, -1.809], [0.041, -0.202], [0.096, -0.784], [0, 0], [0, -0.3], [-0.919, -0.853], [-0.085, -0.067], [0, 0], [0, 0], [0, 0], [-0.067, -0.142], [-1.252, 0], [0, 1.654]], "o": [[-0.006, -0.084], [0, 0], [0.357, -0.938], [-0.024, -0.058], [0, 0], [0, 0], [0.005, -0.02], [0.063, -0.305], [0, -2.479], [-1.948, 0], [-0.098, 0.177], [-0.562, 2.813], [0, 0], [-0.06, 0.295], [0, 1.248], [0.073, 0.079], [0, 0], [0, 0], [0, 0], [0.032, 0.16], [0.431, 1.157], [1.655, 0], [0, -0.125]], "v": [[-0.953, 9.6], [-0.862, 9.282], [0.925, 4.844], [2.269, 1.326], [2.499, 0.875], [3.052, -1.32], [3.895, -5.547], [3.907, -5.607], [4, -6.501], [-0.501, -11.001], [-4.751, -7.965], [-4.961, -7.393], [-5.899, -2.395], [-5.909, -2.396], [-6, -1.499], [-4.558, 1.785], [-4.322, 2.005], [-4.701, 3.912], [-6.717, 7.719], [-6.285, 8.348], [-6.135, 8.802], [-2.719, 11.688], [-0.929, 9.934]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[-0.288, 0.365], [0.017, 0.086], [0, 0], [0.091, 0.138], [0.03, 0.055], [0, 0], [0, 0], [-0.004, 0.02], [0, 0.295], [2.483, 0.002], [0.618, -1.809], [0.041, -0.202], [0.096, -0.784], [0, 0], [0, -0.3], [-0.919, -0.853], [-0.085, -0.067], [0, 0], [0, 0], [0, 0], [0.166, -0.137], [-1.383, -1.59], [-0.968, 0.781]], "o": [[-0.006, -0.084], [0, 0], [-0.13, -1.072], [-0.024, -0.058], [0, 0], [0, 0], [0.005, -0.02], [0.063, -0.305], [0, -2.479], [-1.948, 0], [-0.098, 0.177], [-0.562, 2.813], [0, 0], [-0.06, 0.295], [0, 1.248], [0.073, 0.079], [0, 0], [0, 0], [0, 0], [-0.184, 0.192], [-0.677, 0.675], [1.25, 1.437], [0.097, -0.078]], "v": [[-0.93, 9.167], [0.161, 8.224], [3.224, 5.228], [3.019, 1.857], [2.937, 1.688], [3.052, -1.32], [3.895, -5.547], [3.907, -5.607], [4, -6.501], [-0.501, -11.001], [-4.751, -7.965], [-4.961, -7.393], [-5.899, -2.395], [-5.909, -2.396], [-6, -1.499], [-4.558, 1.785], [-4.322, 2.005], [-3.424, 3.47], [-5.248, 5.148], [-5.503, 5.371], [-5.916, 5.794], [-6, 9.688], [-1.563, 9.782]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0.014, 0.095], [0.017, 0.086], [0, 0], [0.091, 0.138], [0.03, 0.055], [0, 0], [0, 0], [-0.004, 0.02], [0, 0.295], [2.483, 0.002], [0.618, -1.809], [0.041, -0.202], [0.096, -0.784], [0, 0], [0, -0.3], [-0.919, -0.853], [-0.085, -0.067], [0, 0], [0, 0], [0, 0], [0.041, -0.231], [-1.843, -0.469], [-0.781, 1.5]], "o": [[-0.006, -0.084], [0, 0], [-0.13, -1.072], [-0.024, -0.058], [0, 0], [0, 0], [0.005, -0.02], [0.063, -0.305], [0, -2.479], [-1.948, 0], [-0.098, 0.177], [-0.562, 2.813], [0, 0], [-0.06, 0.295], [0, 1.248], [0.073, 0.079], [0, 0], [0, 0], [0, 0], [-0.152, 0.317], [-0.209, 0.832], [1.604, 0.408], [0.058, -0.111]], "v": [[3.288, 7.979], [3.38, 7.661], [5.099, 2.853], [3.519, 0.419], [3.437, 0.251], [3.052, -1.32], [3.895, -5.547], [3.907, -5.607], [4, -6.501], [-0.501, -11.001], [-4.751, -7.965], [-4.961, -7.393], [-5.899, -2.395], [-5.909, -2.396], [-6, -1.499], [-4.558, 1.786], [-4.322, 2.005], [-1.393, 3.97], [-1.935, 5.929], [-2.097, 6.839], [-2.229, 7.387], [-0.344, 10.157], [3.156, 8.376]], "c": true}]}, {"t": 28, "s": [{"i": [[0.014, 0.095], [0.017, 0.086], [0, 0], [0.104, 0.197], [0.03, 0.055], [0, 0], [0, 0], [-0.004, 0.02], [0, 0.295], [2.483, 0.002], [0.618, -1.809], [0.041, -0.202], [0.096, -0.784], [0, 0], [0, -0.3], [-0.919, -0.853], [-0.085, -0.067], [0, 0], [0, 0], [0, 0], [-0.067, -0.142], [-1.252, 0], [0, 1.654]], "o": [[-0.006, -0.084], [0, 0], [-0.06, -0.299], [-0.024, -0.058], [0, 0], [0, 0], [0.005, -0.02], [0.063, -0.305], [0, -2.479], [-1.948, 0], [-0.098, 0.177], [-0.562, 2.813], [0, 0], [-0.06, 0.295], [0, 1.248], [0.073, 0.079], [0, 0], [0, 0], [0, 0], [0.032, 0.16], [0.431, 1.157], [1.655, 0], [0, -0.125]], "v": [[5.976, 7.667], [5.942, 7.411], [4.943, 2.415], [4.675, 1.669], [4.593, 1.501], [3.052, -1.32], [3.895, -5.547], [3.907, -5.607], [4, -6.501], [-0.501, -11.001], [-4.751, -7.965], [-4.961, -7.393], [-5.899, -2.395], [-5.909, -2.396], [-6, -1.499], [-4.558, 1.785], [-4.322, 2.005], [-0.955, 4.658], [-0.029, 8.148], [0.059, 8.589], [0.209, 9.044], [3, 11.001], [6, 8.001]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [6.25, 11.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "lottie Outlines", "tt": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [15.499, 18.906, 0], "ix": 2}, "a": {"a": 0, "k": [7.751, 8.345, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0.346, 0.158], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.092, -0.041], [0, 0], [0, 0], [-0.149, 0], [0, 0.552]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.075, 0.068], [0, 0], [0, 0], [0.126, 0.057], [0.553, 0], [0, -0.405]], "v": [[3.021, 1.189], [3.023, 1.184], [2.999, 1.174], [2.997, 1.173], [0.161, -0.117], [-3.141, -3.094], [-3.609, -0.829], [-1.059, 1.475], [-0.805, 1.641], [2.195, 3.003], [2.197, 3.001], [2.609, 3.094], [3.609, 2.094]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0.29, 0.246], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.077, -0.065], [0, 0], [0, 0], [-0.143, -0.041], [-0.151, 0.531]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.054, 0.086], [0, 0], [0, 0], [0.106, 0.089], [0.532, 0.151], [0.11, -0.39]], "v": [[1.488, 2.342], [1.491, 2.339], [1.471, 2.322], [1.47, 2.32], [-0.907, 0.307], [-3.271, -3.459], [-4.339, -1.407], [-2.514, 1.506], [-2.316, 1.734], [0.199, 3.863], [0.201, 3.861], [0.572, 4.064], [1.807, 3.374]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0.119, 0.361], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.032, -0.095], [0, 0], [0, 0], [-0.101, -0.109], [-0.405, 0.375]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.001, 0.101], [0, 0], [0, 0], [0.044, 0.131], [0.375, 0.406], [0.297, -0.275]], "v": [[-2.463, 3.43], [-2.459, 3.429], [-2.467, 3.404], [-2.467, 3.402], [-3.446, 0.445], [-3.5, -4.001], [-5.481, -2.808], [-5.443, 0.63], [-5.392, 0.927], [-4.357, 4.056], [-4.353, 4.055], [-4.142, 4.421], [-2.729, 4.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[-0.213, 0.315], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.056, -0.084], [0, 0], [0, 0], [0.025, -0.147], [-0.544, -0.091]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.079, 0.063], [0, 0], [0, 0], [-0.077, 0.115], [-0.091, 0.545], [0.399, 0.067]], "v": [[-9.277, 2.442], [-9.274, 2.445], [-9.259, 2.423], [-9.258, 2.422], [-7.519, -0.163], [-4.037, -2.929], [-6.194, -3.764], [-8.888, -1.628], [-9.092, -1.406], [-10.931, 1.328], [-10.929, 1.331], [-11.089, 1.722], [-10.268, 2.873]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[-0.292, 0.243], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.077, -0.065], [0, 0], [0, 0], [0.065, -0.134], [-0.497, -0.239]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.094, 0.038], [0, 0], [0, 0], [-0.106, 0.089], [-0.24, 0.498], [0.365, 0.176]], "v": [[-11.164, 1.487], [-11.162, 1.491], [-11.141, 1.474], [-11.14, 1.474], [-8.748, -0.523], [-4.633, -2.206], [-6.47, -3.61], [-9.653, -2.312], [-9.912, -2.156], [-12.441, -0.044], [-12.439, -0.041], [-12.703, 0.289], [-12.236, 1.624]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[-0.078, 0.372], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.02, -0.099], [0, 0], [0, 0], [-0.033, -0.145], [-0.539, 0.121]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.05, 0.088], [0, 0], [0, 0], [-0.028, 0.135], [0.121, 0.54], [0.395, -0.089]], "v": [[-6.29, 3.446], [-6.286, 3.447], [-6.281, 3.421], [-6.28, 3.42], [-5.644, 0.37], [-3.463, -3.505], [-5.776, -3.464], [-7.465, -0.471], [-7.571, -0.188], [-8.242, 3.038], [-8.239, 3.039], [-8.24, 3.462], [-7.045, 4.218]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0.22, 0.31], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.059, -0.082], [0, 0], [0, 0], [-0.129, -0.075], [-0.277, 0.477]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.031, 0.096], [0, 0], [0, 0], [0.08, 0.113], [0.478, 0.277], [0.203, -0.35]], "v": [[-0.932, 2.722], [-0.929, 2.72], [-0.944, 2.698], [-0.944, 2.697], [-2.751, 0.159], [-4.113, -4.074], [-5.654, -2.35], [-4.605, 0.924], [-4.469, 1.193], [-2.557, 3.878], [-2.554, 3.876], [-2.245, 4.164], [-0.878, 3.801]], "c": true}]}, {"t": 28, "s": [{"i": [[0.346, 0.158], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.092, -0.041], [0, 0], [0, 0], [-0.149, 0], [0, 0.552]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.075, 0.068], [0, 0], [0, 0], [0.126, 0.057], [0.553, 0], [0, -0.405]], "v": [[3.021, 1.189], [3.023, 1.184], [2.999, 1.174], [2.997, 1.173], [0.161, -0.117], [-3.141, -3.094], [-3.609, -0.829], [-1.059, 1.475], [-0.805, 1.641], [2.195, 3.003], [2.197, 3.001], [2.609, 3.094], [3.609, 2.094]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [11.643, 3.345], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.001, -0.301], [-0.552, 0], [-0.182, 0.25], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.003, 0.552], [0.333, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.415, -2.749], [0.479, -5.238], [-0.971, -0.34], [-3.234, 3.663], [-3.415, 4.238], [-2.415, 5.238], [-1.617, 4.844], [-1.605, 4.826], [1.1, 0.723]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.001, -0.301], [-0.552, 0], [-0.182, 0.25], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.003, 0.552], [0.333, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.415, -2.749], [0.479, -5.238], [0.311, -0.122], [-3.016, 2.881], [-3.196, 3.457], [-2.196, 4.457], [-1.399, 4.062], [-1.386, 4.044], [2.382, 0.942]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.001, -0.301], [-0.552, 0], [-0.182, 0.25], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.003, 0.552], [0.333, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.415, -2.749], [0.479, -5.238], [2.717, -0.809], [-0.609, 2.194], [-0.79, 2.77], [0.21, 3.77], [1.008, 3.375], [1.02, 3.357], [4.788, 0.255]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.001, -0.301], [-0.552, 0], [-0.182, 0.25], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.003, 0.552], [0.333, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.165, -5.562], [0.479, -5.238], [3.498, 0.003], [2.016, 3.35], [1.835, 3.926], [2.835, 4.926], [3.633, 4.531], [3.646, 4.513], [5.819, 0.38]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.078, -0.406], [-0.562, 0.125], [0.063, 0.676], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.094, 0.562], [0.333, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.165, -5.562], [0.479, -5.238], [4.217, 0.503], [5.172, 3.928], [5.257, 4.504], [6.445, 5.301], [7.273, 4.281], [7.224, 3.826], [6.663, -0.339]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 19, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.078, -0.406], [-0.562, 0.125], [0.063, 0.676], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.094, 0.562], [0.333, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.171, -6.468], [0.485, -6.145], [2.316, 0.503], [3.271, 3.928], [3.356, 4.504], [4.544, 5.301], [5.372, 4.281], [5.323, 3.825], [4.762, -0.339]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.117, -0.397], [-0.557, -0.147], [-0.254, 0.63], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.174, 0.543], [0.296, 0.153], [0, 0], [0, 0], [0, 0]], "v": [[3.963, -5.803], [1.427, -6.747], [0.006, 0], [-0.747, 3.56], [-0.903, 4.033], [-0.213, 5.285], [1.037, 4.634], [1.155, 4.331], [2.566, 0.373]], "c": true}]}, {"t": 28, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-0.001, -0.301], [-0.552, 0], [-0.182, 0.25], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.003, 0.552], [0.333, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.415, -2.749], [0.479, -5.238], [-0.971, -0.34], [-3.234, 3.663], [-3.415, 4.238], [-2.415, 5.238], [-1.617, 4.844], [-1.605, 4.826], [1.1, 0.723]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [3.666, 11.201], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 28, "st": 0, "bm": 0}], "markers": []}