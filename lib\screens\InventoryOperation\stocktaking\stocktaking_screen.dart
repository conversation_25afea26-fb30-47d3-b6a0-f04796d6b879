import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/Inventory_operation_controller.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:inventory_application/controllers/inventory_operation_incoming_controller.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/inventoryOperations/stocktaking_draft_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/screens/InventoryOperation/stocktaking/comparison/inventory_comparison_screen.dart';
import 'package:inventory_application/screens/InventoryOperation/stocktaking/comparison/inventory_comparison_with_ecommerce_screen.dart';
import 'package:inventory_application/screens/components/common_header.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:inventory_application/screens/shared/add_product_with_selected_attributes_widget.dart';
import 'package:inventory_application/screens/shared/product_attribute_selection_dialog_widget.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:inventory_application/models/model/product_model.dart';

class StocktakingScreen extends StatefulWidget {
  final String? existingStocktakingId; // For editing existing stocktaking
  final String? existingWarehouseId;
  final String? existingWarehouseName;

  const StocktakingScreen({
    super.key,
    this.existingStocktakingId,
    this.existingWarehouseId,
    this.existingWarehouseName,
  });

  @override
  _StocktakingScreenState createState() => _StocktakingScreenState();
}

class _StocktakingScreenState extends State<StocktakingScreen>
    with TickerProviderStateMixin {
  AnimationController? animationController;
  final TextEditingController _barcodeController = TextEditingController();
  final TextEditingController _quantityController =
      TextEditingController(text: "1");
  final FocusNode _barcodeFocusNode = FocusNode();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  final InventoryOperationController _controller =
      InventoryOperationController();
  final List<ProductDTO> _scannedItems = [];
  final Map<String, TextEditingController> _quantityControllers = {};

  int? _selectedWarehouseId;
  String? _selectedWarehouseName;
  String _selectedView = "Add Items"; // scanner, manual, list

  String? _currentStocktakingId; // Current stocktaking session ID
  bool _isExistingStocktaking = false;
  DateTime? _stocktakingStartDate;
  Timer? _autoSaveTimer;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _barcodeFocusNode.requestFocus();
    _loadWarehouses();

    // Check if we're editing an existing stocktaking
    if (widget.existingStocktakingId != null) {
      _loadExistingStocktaking();
    } else {
      _createNewStocktaking();
    }
  }

  @override
  void dispose() {
    animationController?.dispose();
    _barcodeController.dispose();
    _quantityController.dispose();
    _barcodeFocusNode.dispose();
    _refreshController.dispose();
    _scrollController.dispose();
    _autoSaveTimer?.cancel();
    // Dispose all quantity controllers
    for (var controller in _quantityControllers.values) {
      controller.dispose();
    }
    _quantityControllers.clear();
    super.dispose();
  }

  void _loadWarehouses() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<WarehouseController>(context, listen: false)
          .fetchWarehouses();
    });
  }

  // Open continuous barcode scanner dialog

  String _getProductKey(ProductDTO product) {
    if (product.virtualProductId != null) {
      return product.virtualProductId!;
    }
    if (product.barcode != null && product.barcode!.isNotEmpty) {
      return 'barcode_${product.barcode}';
    }
    return 'id_${product.id}';
  }

  Future<void> _selectWarehouse() async {
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);

    if (warehouseController.warehouses.isEmpty) {
      await warehouseController.fetchWarehouses();
    }

    if (warehouseController.warehouses.isEmpty) {
      errorSnackBar(message: T("No warehouses available"));
      return;
    }

    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          width: double.maxFinite,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(T("Select Warehouse"),
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              ...warehouseController.warehouses
                  .map(
                    (warehouse) => ListTile(
                      title: Text(warehouse.name),
                      leading:
                          Icon(Icons.warehouse, color: context.newPrimaryColor),
                      onTap: () {
                        setState(() {
                          _selectedWarehouseId = warehouse.id;
                          _selectedWarehouseName = warehouse.name;
                        });
                        Navigator.pop(context);
                        // Auto-save when warehouse is selected
                        _autoSaveDraft();
                      },
                    ),
                  )
                  .toList(),
            ],
          ),
        ),
      ),
    );
  }

  // Load existing stocktaking session
  Future<void> _loadExistingStocktaking() async {
    try {
      setState(() {
        _isExistingStocktaking = true;
        _currentStocktakingId = widget.existingStocktakingId;
        _selectedWarehouseId = int.tryParse(widget.existingWarehouseId ?? '');
        _selectedWarehouseName = widget.existingWarehouseName;
      });

      // Load existing items from database
      final existingItems =
          await _controller.getStocktakingDraftItems(_currentStocktakingId!);
      setState(() {
        _scannedItems.clear();
        _scannedItems.addAll(existingItems);
      });

      successSnackBar(message: T("Stocktaking session loaded successfully"));
    } catch (e) {
      errorSnackBar(message: T("Error loading stocktaking session: $e"));
    }
  }

  // Create new stocktaking session
  Future<void> _createNewStocktaking() async {
    try {
      _currentStocktakingId = DateTime.now().millisecondsSinceEpoch.toString();
      _stocktakingStartDate = DateTime.now();

      setState(() {
        _isExistingStocktaking = false;
      });

      // Only save initial draft if we have a warehouse selected
      if (_selectedWarehouseId != null) {
        await _saveDraft();
      }
    } catch (e) {
      errorSnackBar(message: T("Error creating stocktaking session: $e"));
    }
  }

  // Save current state as draft
  Future<void> _saveDraft() async {
    if (_currentStocktakingId == null) return;

    try {
      var data = StocktakingDraftDTO(
        lastModified: DateTime.now(),
        startDate: _stocktakingStartDate ?? DateTime.now(),
        stocktakingId: _currentStocktakingId,
        warehouseId: _selectedWarehouseId,
        warehouseName: _selectedWarehouseName,
        items: _scannedItems,
        status: 'draft',
      );

      final result = await _controller.saveStocktakingDraft(data.toJson());
      if (result > 0) {
        print("Draft saved successfully: $result");
      } else {
        print("Failed to save draft: $result");
      }
    } catch (e) {
      print("Error saving draft: $e");
      // Only show error to user if it's critical
      if (mounted) {
        errorSnackBar(message: T("Failed to save draft: ${e.toString()}"));
      }
    }
  }

  // Auto-save draft when items change
  void _autoSaveDraft() {
    // Cancel previous timer if exists
    _autoSaveTimer?.cancel();

    // Set new timer for auto-save
    _autoSaveTimer = Timer(const Duration(seconds: 2), () async {
      if (mounted && _currentStocktakingId != null) {
        await _saveDraft();
      }
    });
  }

  // Add method to delete current draft
  Future<void> _deleteDraft() async {
    bool? confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(T("Delete Stocktaking")),
        content: Text(T(
            "Are you sure you want to delete this stocktaking session? All data will be lost.")),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(T("Cancel")),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text(T("Delete")),
          ),
        ],
      ),
    );

    if (confirm == true && _currentStocktakingId != null) {
      try {
        await _controller.deleteStocktakingDraft(_currentStocktakingId!);
        successSnackBar(message: T("Stocktaking session deleted"));
        Navigator.pop(context);
      } catch (e) {
        errorSnackBar(message: T("Error deleting stocktaking: $e"));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ApplicationLayout(
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Header
          SliverToBoxAdapter(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    offset: const Offset(0, 2),
                    blurRadius: 6,
                  )
                ],
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonHeader(
                    icon: Icons.inventory_2_outlined,
                    title: _isExistingStocktaking
                        ? T("Edit Stocktaking")
                        : T("New Stocktaking"),
                    actions: [
                      // Manual Save Draft Button

                      // Delete button (only for existing stocktaking)
                      if (_isExistingStocktaking)
                        IconButton(
                          icon: const Icon(Icons.delete_outline),
                          color: Colors.red,
                          onPressed: _deleteDraft,
                          tooltip: T("Delete Stocktaking"),
                        ),
                    ],
                  ),

                  // Stats Row
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildStatCard(
                            T("Items Scanned"),
                            _scannedItems.length.toString(),
                            Icons.qr_code_scanner,
                            context.newPrimaryColor,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildStatCard(
                            T("Total Quantity"),
                            _scannedItems
                                .fold<int>(
                                    0,
                                    (sum, item) =>
                                        sum + (item.quantity?.toInt() ?? 0))
                                .toString(),
                            Icons.inventory,
                            context.newSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Warehouse Selection
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.1),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: InkWell(
                onTap: _selectWarehouse,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: _selectedWarehouseId != null
                          ? context.newPrimaryColor
                          : Colors.grey.shade300,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.warehouse,
                        color: _selectedWarehouseId != null
                            ? context.newPrimaryColor
                            : Colors.grey,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          _selectedWarehouseName ?? T("Select Warehouse"),
                          style: TextStyle(
                            color: _selectedWarehouseName == null
                                ? Colors.grey
                                : context.newTextColor,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      Icon(
                        Icons.arrow_drop_down,
                        color: _selectedWarehouseId != null
                            ? context.newPrimaryColor
                            : Colors.grey,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // View Toggle Buttons
          SliverToBoxAdapter(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              child: Row(
                children: [
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewButton(
                      "Add Items",
                      T("Add Items"),
                      Icons.keyboard,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: _buildViewButton(
                      "list",
                      T("Items List"),
                      Icons.list,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Content based on selected view
          if (_selectedView == "Add Items") ...[
            // Manual Input View
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, 1),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),

                    // Product Selection Button in Manual View
                    // SizedBox(
                    //   width: double.infinity,
                    //   child: ElevatedButton.icon(
                    //     onPressed: _openProductSelection,
                    //     icon: const Icon(Icons.shopping_cart),
                    //     label: Text(T("Browse & Select Products")),
                    //     style: ElevatedButton.styleFrom(
                    //       backgroundColor: Colors.green,
                    //       foregroundColor: Colors.white,
                    //       padding: const EdgeInsets.symmetric(vertical: 16),
                    //       shape: RoundedRectangleBorder(
                    //         borderRadius: BorderRadius.circular(8),
                    //       ),
                    //     ),
                    //   ),
                    // ),
                    InvoiceSelectProductWidget(
                      selectedProducts: _scannedItems,
                      oncannotAddProduct: () {
                        errorSnackBar(
                            message: "يرجى اختيار المستودع",
                            // ignore: use_build_context_synchronously
                            context: context);
                      },
                      onChange: () {
                        // Force UI refresh when products change
                        setState(() {});
                      },
                      onAddProduct: (ProductDTO product) {
                        _scannedItems.add(product);
                        // Force UI refresh
                        setState(() {});
                      },
                      onRemoveProduct: (int id) {
                        _scannedItems.removeWhere(
                          (element) => element.id == id,
                        );
                        // Force UI refresh
                        setState(() {});
                      },
                      onSearchByBarcode: (String barcode) async {
                        // Get the controller
                        final inventoryIncomingController =
                            Provider.of<InventoryOperationIncomingController>(
                          context,
                          listen: false,
                        );

                        // Process the barcode
                        var result =
                            await inventoryIncomingController.getItemByBarcode(
                          barcode: barcode,
                        );

                        // If result is a ProductDTO object rather than a boolean,
                        // it means we need to show the attribute selection dialog
                        if (result is ProductDTO) {
                          final selectedOptions =
                              await showDialog<Map<int, ItemAttributeOption>>(
                            context: context,
                            barrierDismissible: false,
                            builder: (BuildContext context) {
                              return AttributeSelectionDialog(product: result);
                            },
                          );

                          if (selectedOptions != null) {
                            final provider = Provider.of<
                                    InventoryOperationIncomingController>(
                                context,
                                listen: false);
                            addProductWithSelectedAttributes(
                              product: result,
                              selectedOptions: selectedOptions,
                              currentProducts: provider.selectedIncomingProduct,
                              onUpdateExisting: (updatedProduct, index) {
                                provider.selectedIncomingProduct[index] =
                                    updatedProduct;
                              },
                              onAddNew: (newProduct) {
                                provider.addProductToSelectedList(newProduct);
                              },
                              onAfterChange: () {
                                provider.calculateInvoiceTotal();
                                // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
                                provider.notifyListeners();
                              },
                            );
                          }

                          return true;
                        }

                        // Force UI refresh immediately after barcode scan
                        setState(() {
                          // This will trigger a rebuild with the updated product list
                        });

                        // Add a small delay and refresh again to ensure UI updates
                        await Future.delayed(const Duration(milliseconds: 100));
                        setState(() {});

                        // Show error if product not found
                        if (result == false) {
                          errorSnackBar(
                            message: T(
                                "There is no product associated with the barcode"),
                            // ignore: use_build_context_synchronously
                            context: context,
                          );
                        }

                        return result;
                      },
                    ),
                  ],
                ),
              ),
            ),
          ],

          // Items List
          if (_selectedView == "list" || _scannedItems.isNotEmpty) ...[
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      T("Scanned Items (${_scannedItems.length})"),
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: context.newTextColor,
                      ),
                    ),
                    if (_scannedItems.isNotEmpty)
                      TextButton.icon(
                        onPressed: () {
                          setState(() {
                            // Dispose all quantity controllers
                            for (var controller
                                in _quantityControllers.values) {
                              controller.dispose();
                            }
                            _quantityControllers.clear();
                            _scannedItems.clear();
                          });
                        },
                        icon: const Icon(Icons.clear_all, color: Colors.red),
                        label: Text(T("Clear All"),
                            style: TextStyle(color: Colors.red)),
                      ),
                  ],
                ),
              ),
            ),
            _scannedItems.isEmpty
                ? SliverToBoxAdapter(
                    child: Container(
                      margin: const EdgeInsets.all(16),
                      padding: const EdgeInsets.all(32),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade50,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.inventory_2_outlined,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            T("No items scanned yet"),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            T("Use scanner or manual entry to add items"),
                            style: TextStyle(
                              color: Colors.grey.shade500,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  )
                : SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final product = _scannedItems[index];
                        return Container(
                          margin: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 2,
                                offset: const Offset(0, 1),
                              ),
                            ],
                          ),
                          child: ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: context.newPrimaryColor.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.inventory,
                                color: context.newPrimaryColor,
                              ),
                            ),
                            title: Text(
                              product.title ?? '',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: context.newTextColor,
                              ),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  T("Code: ${product.code ?? product.barcode}"),
                                  style: TextStyle(
                                    color:
                                        context.newTextColor.withOpacity(0.7),
                                  ),
                                ),
                                if (product.barcodeName != null &&
                                    product.barcodeName!.isNotEmpty)
                                  Text(
                                    T("Attributes: ${product.barcodeName}"),
                                    style: TextStyle(
                                      color: Colors.blue,
                                      fontSize: 12,
                                    ),
                                  ),
                              ],
                            ),
                            trailing: Container(
                              width: 180,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Quantity Input Field
                                  Expanded(
                                    child: Container(
                                      height: 40,
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                            color: Colors.grey.shade300),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Row(
                                        children: [
                                          // Minus button
                                          InkWell(
                                            onTap: () {
                                              String productKey =
                                                  _getProductKey(product);
                                              setState(() {
                                                if ((product.quantity ?? 0) >
                                                    1) {
                                                  _scannedItems[index]
                                                          .quantity =
                                                      (_scannedItems[index]
                                                                  .quantity ??
                                                              0) -
                                                          1;
                                                  _quantityControllers[
                                                              productKey]!
                                                          .text =
                                                      _scannedItems[index]
                                                          .quantity!
                                                          .toInt()
                                                          .toString();
                                                } else {
                                                  _quantityControllers
                                                      .remove(productKey)
                                                      ?.dispose();
                                                  _scannedItems.removeAt(index);
                                                }
                                              });
                                              _autoSaveDraft();
                                            },
                                            child: Container(
                                              width: 35,
                                              height: 40,
                                              decoration: BoxDecoration(
                                                color: Colors.orange
                                                    .withOpacity(0.1),
                                                borderRadius:
                                                    const BorderRadius.only(
                                                  topLeft: Radius.circular(5),
                                                  bottomLeft:
                                                      Radius.circular(5),
                                                ),
                                              ),
                                              child: const Icon(
                                                Icons.remove,
                                                color: Colors.orange,
                                                size: 18,
                                              ),
                                            ),
                                          ),
                                          // Quantity TextField
                                          Expanded(
                                            child: TextField(
                                              controller: () {
                                                String productKey =
                                                    _getProductKey(product);
                                                if (!_quantityControllers
                                                    .containsKey(productKey)) {
                                                  _quantityControllers[
                                                          productKey] =
                                                      TextEditingController(
                                                          text: (product
                                                                      .quantity
                                                                      ?.toInt() ??
                                                                  0)
                                                              .toString());
                                                }
                                                return _quantityControllers[
                                                    productKey]!;
                                              }(),
                                              keyboardType:
                                                  TextInputType.number,
                                              textAlign: TextAlign.center,
                                              style: const TextStyle(
                                                fontSize: 14,
                                                fontWeight: FontWeight.bold,
                                              ),
                                              decoration: const InputDecoration(
                                                border: InputBorder.none,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 4),
                                              ),
                                              onSubmitted: (value) {
                                                int? newQuantity =
                                                    int.tryParse(value);
                                                if (newQuantity != null &&
                                                    newQuantity > 0) {
                                                  setState(() {
                                                    _scannedItems[index]
                                                            .quantity =
                                                        newQuantity.toDouble();
                                                  });
                                                  _autoSaveDraft();
                                                } else {
                                                  // Reset to previous value if invalid
                                                  String productKey =
                                                      _getProductKey(product);
                                                  _quantityControllers[
                                                          productKey]!
                                                      .text = (product.quantity
                                                              ?.toInt() ??
                                                          1)
                                                      .toString();
                                                }
                                              },
                                              onChanged: (value) {
                                                int? newQuantity =
                                                    int.tryParse(value);
                                                if (newQuantity != null &&
                                                    newQuantity > 0) {
                                                  setState(() {
                                                    _scannedItems[index]
                                                            .quantity =
                                                        newQuantity.toDouble();
                                                  });
                                                  _autoSaveDraft();
                                                }
                                              },
                                            ),
                                          ),
                                          // Plus button
                                          InkWell(
                                            onTap: () {
                                              String productKey =
                                                  _getProductKey(product);
                                              setState(() {
                                                _scannedItems[index].quantity =
                                                    (_scannedItems[index]
                                                                .quantity ??
                                                            0) +
                                                        1;
                                                _quantityControllers[
                                                            productKey]!
                                                        .text =
                                                    _scannedItems[index]
                                                        .quantity!
                                                        .toInt()
                                                        .toString();
                                              });
                                              _autoSaveDraft();
                                            },
                                            child: Container(
                                              width: 35,
                                              height: 40,
                                              decoration: BoxDecoration(
                                                color: Colors.green
                                                    .withOpacity(0.1),
                                                borderRadius:
                                                    const BorderRadius.only(
                                                  topRight: Radius.circular(5),
                                                  bottomRight:
                                                      Radius.circular(5),
                                                ),
                                              ),
                                              child: const Icon(
                                                Icons.add,
                                                color: Colors.green,
                                                size: 18,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  // Delete button
                                  IconButton(
                                    icon: const Icon(Icons.delete_outline),
                                    color: Colors.red,
                                    onPressed: () {
                                      String productKey =
                                          _getProductKey(product);
                                      setState(() {
                                        _quantityControllers
                                            .remove(productKey)
                                            ?.dispose();
                                        _scannedItems.removeAt(index);
                                      });
                                      _autoSaveDraft();
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      childCount: _scannedItems.length,
                    ),
                  ),
          ],

          // Save Button
          if (_scannedItems.isNotEmpty)
            SliverToBoxAdapter(
              child: Container(
                margin: const EdgeInsets.all(16),
                child: ElevatedButton(
                  onPressed: () {
                    if (AppController.getIsUsingEcommerceFromShared()) {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) =>
                            InventoryComparisonWithEcommerceScreen(
                          stocktakingData: StocktakingDraftDTO(
                            lastModified: DateTime.now(),
                            startDate: _stocktakingStartDate ?? DateTime.now(),
                            stocktakingId: _currentStocktakingId,
                            warehouseId: _selectedWarehouseId,
                            warehouseName: _selectedWarehouseName,
                            items: _scannedItems,
                            status: 'draft',
                          ),
                        ),
                      ));
                    } else {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (context) => InventoryComparisonScreen(
                          stocktakingData: StocktakingDraftDTO(
                            lastModified: DateTime.now(),
                            startDate: _stocktakingStartDate ?? DateTime.now(),
                            stocktakingId: _currentStocktakingId,
                            warehouseId: _selectedWarehouseId,
                            warehouseName: _selectedWarehouseName,
                            items: _scannedItems,
                            status: 'draft',
                          ),
                        ),
                      ));
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: context.newPrimaryColor,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(double.infinity, 56),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.compare),
                      const SizedBox(width: 8),
                      Text(
                        T("Compare Stocktaking"),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

          // Bottom spacing
          const SliverToBoxAdapter(
            child: SizedBox(height: 32),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: context.newTextColor.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildViewButton(String view, String label, IconData icon) {
    final isSelected = _selectedView == view;
    return InkWell(
      onTap: () {
        setState(() {
          _selectedView = view;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        decoration: BoxDecoration(
          color: isSelected ? context.newPrimaryColor : Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? context.newPrimaryColor : Colors.grey.shade300,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: context.newPrimaryColor.withOpacity(0.2),
                    spreadRadius: 1,
                    blurRadius: 3,
                    offset: const Offset(0, 1),
                  ),
                ]
              : null,
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? Colors.white
                  : context.newTextColor.withOpacity(0.7),
              size: 20,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected
                    ? Colors.white
                    : context.newTextColor.withOpacity(0.7),
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
