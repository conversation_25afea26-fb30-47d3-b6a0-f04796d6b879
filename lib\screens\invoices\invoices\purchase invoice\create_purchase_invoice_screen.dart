import 'package:flutter/material.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/purchase_controller.dart';

import 'package:inventory_application/controllers/invoice_settings_controller.dart';
import 'package:inventory_application/controllers/salesmen_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/invoice/invoice_product_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:inventory_application/models/dto/purchase_invoice/purchase_invoice_dto.dart';
import 'package:inventory_application/models/model/product_model.dart';
import 'package:inventory_application/screens/components/common_snackbar.dart';
import 'package:inventory_application/screens/invoices/invoices/invoice%20details/invoice_details_for_local_screen.dart';
import 'package:inventory_application/screens/invoices/invoices/purchase%20invoice/widgets/create_purchase_invoice_dialog_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/purchase%20invoice/widgets/purchase_invoice_bottom_navbar_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_list_items_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/purchase%20invoice/widgets/purchase_product_list_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_product_list_header_widget.dart';
import 'package:inventory_application/screens/invoices/invoices/widgets/invoice_select_product_widget.dart';
import 'package:inventory_application/screens/shared/add_product_with_selected_attributes_widget.dart';
import 'package:inventory_application/screens/shared/product_attribute_selection_dialog_widget.dart';
import 'package:provider/provider.dart';
import 'package:inventory_application/controllers/warehouse_controller.dart';
import 'package:inventory_application/controllers/customer_controller.dart';

class CreatePurchaseInvoiceScreen extends StatefulWidget {
  final InvoiceDto? invoiceData;
  const CreatePurchaseInvoiceScreen({super.key, this.invoiceData});

  @override
  State<CreatePurchaseInvoiceScreen> createState() =>
      _CreatePurchaseInvoiceScreenState();
}

class _CreatePurchaseInvoiceScreenState
    extends State<CreatePurchaseInvoiceScreen> {
  // New color palette to match home screen
  final Color primaryColor = const Color(0xFF667EEA);
  final Color secondaryColor = const Color(0xFF764BA2);
  final Color backgroundColor = const Color(0xFFF8F9FD);
  final Color textColor = const Color(0xFF2C3E50);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setDefaultValues();
    });
  }

  void _setDefaultValues() async {
    final purchaseInvoiceController =
        Provider.of<PurchaseInvoiceController>(context, listen: false);
    final warehouseController =
        Provider.of<WarehouseController>(context, listen: false);
    final salesmenController =
        Provider.of<SalesmenController>(context, listen: false);
    final customerController =
        Provider.of<CustomerController>(context, listen: false);
    final invoiceSettingsController =
        Provider.of<InvoiceSettingsController>(context, listen: false);

    // Get invoice number
    await purchaseInvoiceController.getPurchaseInvoiceNumber();

    // Try to load warehouses with retry logic
    int retryCount = 0;
    while (warehouseController.warehouses.isEmpty && retryCount < 3) {
      try {
        await warehouseController.fetchWarehouses();
        await salesmenController.fetchSalesmen();
        retryCount++;
        if (warehouseController.warehouses.isEmpty && retryCount < 3) {
          // Wait a moment before retrying
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        retryCount++;
      }
    }

    // Try to load customers with retry logic
    retryCount = 0;
    while (customerController.customers.isEmpty && retryCount < 3) {
      try {
        await customerController.fetchCustomersFromServer();
        retryCount++;
        if (customerController.customers.isEmpty && retryCount < 3) {
          // Wait a moment before retrying
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        print("Error loading customers: $e");
        retryCount++;
      }
    }

    // First check if we have a default warehouse in settings
    if (invoiceSettingsController.defaultWarehouseId != null) {
      purchaseInvoiceController.invoice.warehouseId =
          invoiceSettingsController.defaultWarehouseId;
      purchaseInvoiceController.invoice.warehouseName =
          invoiceSettingsController.defaultWarehouseName;
    }
    // If not, set default warehouse from the first available warehouse
    else if (warehouseController.warehouses.isNotEmpty) {
      try {
        final firstWarehouse = warehouseController.warehouses.first;
        purchaseInvoiceController.invoice.warehouseId = firstWarehouse.id;
        purchaseInvoiceController.invoice.warehouseName = firstWarehouse.name;
      } catch (e) {
        print("Error setting default warehouse: $e");
      }
    } else {
      // Handle case where no warehouses are available
      print("Warning: No warehouses available");
      errorSnackBar(
        message:
            T("No warehouses available. Please add warehouses to the system."),
        context: context,
      );
    }

    // First check if we have a default customer in settings

    // Set today's date
    purchaseInvoiceController.invoice.invoiceDate = DateTime.now();

    // Set payment type to cash
    // purchaseInvoiceController.invoice.invoicePaymentType =
    //     InvoicePaymentType.chash;

    // Calculate the invoice total (this will set the total and totalAfterDiscount)
    purchaseInvoiceController.calculateInvoiceTotal();

    // Set payment value to the total after discount (full payment)
    if (purchaseInvoiceController.invoice.totalAfterDiscount != null) {
      double totalAfterDiscount =
          purchaseInvoiceController.invoice.totalAfterDiscount ?? 0;

      // Use the controller method to set the payment value
      purchaseInvoiceController.setPaymentValue(totalAfterDiscount);
    }

    // Notify listeners to update UI
    // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
    purchaseInvoiceController.notifyListeners();

    // If critical data is missing, show dialog to let user select manually
    if (purchaseInvoiceController.invoice.warehouseId == null ||
        purchaseInvoiceController.invoice.supplierId == null) {
      // Show dialog for manual selection
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).push(DialogRoute(
          context: context,
          barrierDismissible: false,
          builder: (context) => CreatePurchaseInvoiceDialogWidget(),
        ));
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var products = Provider.of<PurchaseInvoiceController>(context)
        .selectedPurchaseInvoiceProduct;

    var provider =
        Provider.of<PurchaseInvoiceController>(context, listen: false);

    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        var result = await showConfirmDialog(
          title: T('Exit'),
          content: T('Are you sure?'),
          backText: T("back"),
          confirmText: T("exit"),
        );
        if (result == true) {
          if (provider.invoice.id != null) {
            provider.invoice = PurchaseInvoiceDto();
            provider.selectedPurchaseInvoiceProduct = [];
          }
        }

        return result ?? false;
      },
      child: Scaffold(
        backgroundColor: backgroundColor,
        appBar: AppBar(
          elevation: 0,
          toolbarHeight: 60,
          shadowColor: Colors.transparent,
          backgroundColor: Colors.white,
          automaticallyImplyLeading: false,
          title: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      InkWell(
                        onTap: () => Navigator.of(context).pop(),
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: context.colors.primary.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.arrow_back,
                            color: context.colors.primary,
                            size: 24,
                          ),
                        ),
                      ),
                      const SizedBox(width: 5),
                      Text(
                        T("Purchase Invoice"),
                        style: TextStyle(
                          color: textColor,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 8),
                  _buildCompactButton(
                    label: T("Save & New"),
                    icon: Icons.save_outlined,
                    isMain: true,
                    onPressed: () async {
                      pleaseWaitDialog(context: context, isShown: true);

                      // Only set payment to full amount if it hasn't been manually set
                      if (provider.invoice.paymentValue == null) {
                        if (provider.invoice.total != null) {
                          double totalAmount = provider.invoice.total ?? 0;
                          double totalDiscount =
                              provider.invoice.totalDiscount ?? 0;
                          provider.setPaymentValue(totalAmount - totalDiscount);
                        }
                      }

                      var result = await provider.savePurchaseInvoice();
                      if (result.isSuccess) {
                        // ignore: use_build_context_synchronously
                        pleaseWaitDialog(context: context, isShown: false);
                        successSnackBar(
                            message: T("The operation has been saved"));

                        // Reset the invoice and set default values
                        provider.selectedPurchaseInvoiceProduct.clear();
                        provider.invoice = PurchaseInvoiceDto();
                        _setDefaultValues();

                        return;
                      }
                      // ignore: use_build_context_synchronously
                      pleaseWaitDialog(context: context, isShown: false);
                      errorSnackBar(
                          message: result.message != null
                              ? result.message!.first.toString()
                              : T("Not saved"));
                    },
                  )
                ],
              ),
            ],
          ),
          toolbarOpacity: 1,
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(1.0),
            child: Container(
              color: Colors.grey.withOpacity(0.2),
              height: 1.0,
            ),
          ),
        ),
        bottomNavigationBar: const PurchaseInvoiceBottomNavbarWidget(),
        body: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
          ),
          child: SingleChildScrollView(
            padding: EdgeInsets.zero,
            physics: const BouncingScrollPhysics(),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InvoiceSelectProductWidget(
                  selectedProducts: products,
                  onChange: () {
                    // Force UI refresh when products change
                    setState(() {});
                  },
                  onAddProduct: (ProductDTO product) {
                    provider.addProductToSelectedList(product);
                    // Force UI refresh
                    setState(() {});
                  },
                  onRemoveProduct: (int id) {
                    provider.deleteProductFromSelectedList(id);
                    // Force UI refresh
                    setState(() {});
                  },
                  onSearchByBarcode: (String barcode) async {
                    // Get the controller
                    final purchaseInvoiceController =
                        Provider.of<PurchaseInvoiceController>(
                      context,
                      listen: false,
                    );

                    // Process the barcode
                    var result =
                        await purchaseInvoiceController.getItemByBarcode(
                      barcode: barcode,
                    );

                    // If result is a ProductDTO object rather than a boolean,
                    // it means we need to show the attribute selection dialog
                    if (result is ProductDTO) {
                      final selectedOptions =
                          await showDialog<Map<int, ItemAttributeOption>>(
                        context: context,
                        barrierDismissible: false,
                        builder: (BuildContext context) {
                          return AttributeSelectionDialog(product: result);
                        },
                      );

                      if (selectedOptions != null) {
                        final provider = Provider.of<PurchaseInvoiceController>(
                            context,
                            listen: false);
                        addProductWithSelectedAttributes(
                          product: result,
                          selectedOptions: selectedOptions,
                          currentProducts:
                              provider.selectedPurchaseInvoiceProduct,
                          onUpdateExisting: (updatedProduct, index) {
                            provider.selectedPurchaseInvoiceProduct[index] =
                                updatedProduct;
                          },
                          onAddNew: (newProduct) {
                            provider.addProductToSelectedList(newProduct);
                          },
                          onAfterChange: () {
                            provider.calculateInvoiceTotal();
                            // ignore: invalid_use_of_protected_member, invalid_use_of_visible_for_testing_member
                            provider.notifyListeners();
                          },
                        );
                      }

                      return true;
                    }

                    // Force UI refresh immediately after barcode scan
                    setState(() {
                      // This will trigger a rebuild with the updated product list
                    });

                    // Add a small delay and refresh again to ensure UI updates
                    await Future.delayed(const Duration(milliseconds: 100));
                    setState(() {});

                    // Show error if product not found
                    if (result == false) {
                      errorSnackBar(
                        message: T(
                            "There is no product associated with the barcode"),
                        // ignore: use_build_context_synchronously
                        context: context,
                      );
                    }

                    return result;
                  },
                ),
                if (products.isNotEmpty) ...[
                  Consumer<PurchaseInvoiceController>(
                    builder: (context, purchaseInvoiceController, child) {
                      return ListView.separated(
                        itemCount: products.length,
                        shrinkWrap: true,
                        padding: EdgeInsets.zero,
                        separatorBuilder: (context, index) => Divider(
                          color: Colors.grey.withOpacity(0.2),
                          height: 1,
                          indent: 0,
                          endIndent: 0,
                        ),
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (context, index) {
                          return PurchaseProductListWidget(
                            id: products[index].id ?? 0,
                            barcode: products[index].barcode,
                            virtualProductId: products[index].virtualProductId,
                            selectedInvoiceProduct: purchaseInvoiceController
                                .selectedPurchaseInvoiceProduct,
                            onChangeWarehouse: (int productId, int warehouseId,
                                String warehouseName,
                                [String? virtualProductId]) {
                              provider.setProductWarehouse(productId,
                                  warehouseId, warehouseName, virtualProductId);
                              setState(() {});
                            },
                            onDeleteProduct: (int productId,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                // If we have a virtual ID, use it when deleting
                                provider.deleteProductFromSelectedList(
                                    productId,
                                    virtualProductId: virtualProductId);
                              } else {
                                // Otherwise use the regular product ID
                                provider
                                    .deleteProductFromSelectedList(productId);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdatePrice: (int productId, double price,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                provider.updateProductPrice(
                                    productId, price, virtualProductId);
                              } else {
                                provider.updateProductPrice(productId, price);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdateQuantity: (int productId, double quantity,
                                [String? virtualProductId]) {
                              if (virtualProductId != null) {
                                provider.updateProductQuantity(
                                    productId, quantity, virtualProductId);
                              } else {
                                provider.updateProductQuantity(
                                    productId, quantity);
                              }
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            onUpdateUnit: (int productId, ItemPriceDTO unit,
                                [String? virtualProductId]) {
                              provider.updateProductUnit(
                                  productId, unit, virtualProductId);
                              provider.calculateInvoiceTotal();
                              setState(() {});
                            },
                            // New callbacks for additional fields
                            onUpdateBatchNumber:
                                (int productId, String batchNumber,
                                    [String? virtualProductId]) {
                              provider.updateProductBatchNumber(
                                  productId, batchNumber, virtualProductId);
                              setState(() {});
                            },
                            onUpdateSerialNumber:
                                (int productId, String serialNumber,
                                    [String? virtualProductId]) {
                              provider.updateProductSerialNumber(
                                  productId, serialNumber, virtualProductId);
                              setState(() {});
                            },
                            onUpdateExpirationDate:
                                (int productId, DateTime expirationDate,
                                    [String? virtualProductId]) {
                              provider.updateProductExpirationDate(
                                  productId, expirationDate, virtualProductId);
                              setState(() {});
                            },
                            onUpdateUnitCost: (int productId, double unitCost,
                                [String? virtualProductId]) {
                              provider.updateProductUnitCost(
                                  productId, unitCost, virtualProductId);
                              setState(() {});
                            },
                            onUpdateVatPercent:
                                (int productId, double vatPercent,
                                    [String? virtualProductId]) {
                              provider.updateProductVatPercent(
                                  productId, vatPercent, virtualProductId);
                              setState(() {});
                            },
                          );
                        },
                      );
                    },
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    bool isMain = false,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isMain ? primaryColor : Colors.white,
        foregroundColor: isMain ? Colors.white : primaryColor,
        elevation: isMain ? 2 : 0,
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: isMain ? Colors.transparent : primaryColor.withOpacity(0.5),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  // Format the selected options as a string (e.g., "S/Red")
}
