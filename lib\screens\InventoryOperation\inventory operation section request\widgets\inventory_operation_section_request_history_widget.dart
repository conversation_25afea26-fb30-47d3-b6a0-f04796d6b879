import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/inventory_operation_section_request_controller.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/models/dto/inventoryOperations/inventory_operation_section_request_history_dto.dart';
import 'package:inventory_application/models/dto/products/product_dto.dart';
import 'package:provider/provider.dart';

class InventoryOperationSectionRequestHistoryWidget extends StatefulWidget {
  const InventoryOperationSectionRequestHistoryWidget({super.key});

  @override
  State<InventoryOperationSectionRequestHistoryWidget> createState() =>
      _InventoryOperationSectionRequestHistoryWidgetState();
}

class _InventoryOperationSectionRequestHistoryWidgetState
    extends State<InventoryOperationSectionRequestHistoryWidget> {
  DateTime _fromDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _toDate = DateTime.now();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadHistory();
  }

  Future<void> _loadHistory() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<InventoryOperationSectionRequestController>(
        context,
        listen: false,
      ).getSectionRequestHistory(_fromDate, _toDate);
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: DateTimeRange(start: _fromDate, end: _toDate),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: context.newPrimaryColor,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: Colors.black,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _fromDate = picked.start;
        _toDate = picked.end;
      });
      await _loadHistory();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InventoryOperationSectionRequestController>(
      builder: (context, controller, child) {
        final historyItems = controller.sectionRequestHistory;

        return Column(
          children: [
            // Date Filter Header
            _buildDateFilterHeader(),
            const SizedBox(height: 16),

            // Content
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : historyItems.isEmpty
                      ? _buildEmptyState()
                      : _buildHistoryGrid(historyItems),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDateFilterHeader() {
    return Container(
      padding: const EdgeInsets.all(5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.history,
                color: context.newPrimaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                T('Request History'),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: context.newPrimaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.withOpacity(0.3)),
                  ),
                  child: Row(
                    children: [
                      context.width > 600
                          ? Icon(
                              Icons.calendar_today,
                              size: 16,
                              color: Colors.grey[600],
                            )
                          : const SizedBox(),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${DateFormat('yyyy-MM-dd').format(_fromDate)} - ${DateFormat('yyyy-MM-dd').format(_toDate)}',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(width: 5),
              ElevatedButton.icon(
                onPressed: _selectDateRange,
                icon: const Icon(Icons.date_range, size: 18),
                label: Text(T('')),
                style: ElevatedButton.styleFrom(
                  backgroundColor: context.newPrimaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inventory_2_outlined,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            T('No history found'),
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            T('No items found for the selected date range'),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          // const SizedBox(height: 24),
          // ElevatedButton.icon(
          //   onPressed: _selectDateRange,
          //   icon: const Icon(Icons.refresh, size: 18),
          //   label: Text(T('Try Different Dates')),
          //   style: ElevatedButton.styleFrom(
          //     backgroundColor: context.newPrimaryColor,
          //     foregroundColor: Colors.white,
          //     shape: RoundedRectangleBorder(
          //       borderRadius: BorderRadius.circular(8),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget _buildHistoryGrid(
      List<InventoryOperationSectionRequestHistoryDto> items) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      margin: const EdgeInsets.all(5),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: context.newPrimaryColor,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    T('#'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    T('Item Name'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    T('Balance'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    T('Action'),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          // Data rows
          Expanded(
            child: ListView.builder(
              itemCount: items.length,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                return _buildDataRow(items[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataRow(
      InventoryOperationSectionRequestHistoryDto item, int index) {
    final balance = item.balance ?? 0.0;
    final isEven = index % 2 == 0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isEven ? Colors.grey[50] : Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          // Index
          Expanded(
            flex: 1,
            child: Text(
              '${index + 1}',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: context.newPrimaryColor,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          // Item Name
          Expanded(
            flex: 3,
            child: Text(
              item.itemName ?? T('Unknown Item'),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
          ),

          // Balance
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getBalanceColor(balance).withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                balance.toStringAsFixed(2),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: _getBalanceColor(balance),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

          // Action Button
          Expanded(
            flex: 2,
            child: Center(
              child: ElevatedButton.icon(
                onPressed:
                    balance > 0 ? () => _addItemToSelectedList(item) : null,
                icon: const Icon(Icons.add, size: 16),
                label: Text(T('')),
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      balance > 0 ? context.newPrimaryColor : Colors.grey,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  minimumSize: const Size(60, 32),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getBalanceColor(double balance) {
    if (balance <= 0) {
      return Colors.red;
    } else if (balance < 10) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  void _addItemToSelectedList(InventoryOperationSectionRequestHistoryDto item) {
    if (item.itemId == null || item.balance == null || item.balance! <= 0) {
      return;
    }
    Provider.of<InventoryOperationSectionRequestController>(context,
            listen: false)
        .addProductToSelectedListFromHistory(
            item.itemId ?? 0, item.balance ?? 0);
  }
}
