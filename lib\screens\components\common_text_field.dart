import 'package:flutter/material.dart';
import 'package:inventory_application/base/theme/app_theme_light.dart';

class CommonTextField extends StatefulWidget {
  const CommonTextField({
    super.key,
    required this.label,
    this.hint,
    this.obscureText,
    this.keyboardType,
    this.controller,
    this.validator,
    this.onSaved,
    this.onChanged,
    this.onTap,
    this.isPassword = false,
    this.enabled,
    this.suffixIcon,
    this.initialValue,
    this.floatingLabelBehavior,
  });
  final String? initialValue;
  final String label;
  final String? hint;
  final bool? obscureText;
  final TextInputType? keyboardType;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final Function(String?)? onSaved;
  final Function(String)? onChanged;
  final Function()? onTap;
  final bool? enabled;
  final Widget? suffixIcon;
  final bool isPassword;
  final FloatingLabelBehavior? floatingLabelBehavior;

  @override
  State<CommonTextField> createState() => _CommonTextFieldState();
}

class _CommonTextFieldState extends State<CommonTextField> {
  bool _obscureText = true;
  void _toggle() {
    setState(() {
      _obscureText = !_obscureText;
    });
  }

  @override
  void setState(VoidCallback fn) {
    super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      initialValue: widget.initialValue,
      keyboardType: widget.keyboardType,
      onFieldSubmitted: widget.onSaved,
      textAlign: TextAlign.center,
      decoration: commonInputDecoration(
        widget.label,
        widget.hint,
        widget.isPassword
            ? Container(
                padding: const EdgeInsets.only(top: 20),
                margin: const EdgeInsets.only(right: 20),
                width: 1,
                height: 2,
                child: IconButton(
                  focusNode: FocusNode(skipTraversal: true),
                  icon: Icon(
                    _obscureText ? Icons.visibility : Icons.visibility_off,
                    color: const Color(0xff42474d),
                    size: 15,
                  ),
                  onPressed: () {
                    _toggle();
                  },
                ),
              )
            : null,
        widget.floatingLabelBehavior,
      ),
      obscureText: widget.isPassword && _obscureText,
      controller: widget.controller,
      validator: widget.validator,
      onSaved: widget.onSaved,
      onChanged: widget.onChanged,
      onTap: widget.onTap,
      enabled: widget.enabled,
    );
  }
}

InputDecoration commonInputDecoration(String label, String? hint,
    Widget? suffixIcon, FloatingLabelBehavior? floatingLabelBehavior) {
  return InputDecoration(
    alignLabelWithHint: true,

    contentPadding: const EdgeInsets.symmetric(
        vertical: 10, horizontal: 0), // Increase padding for better appearance
    border: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10), // Set border radius
      borderSide: BorderSide(
        color: AppThemeLight.instance.theme.colorScheme.primary,
        width: 1.2,
      ),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.circular(10), // Apply radius to enabled border
      borderSide: BorderSide(
        color: AppThemeLight.instance.theme.colorScheme.primary,
        width: 1.2,
      ),
    ),
    disabledBorder: OutlineInputBorder(
      borderRadius:
          BorderRadius.circular(10), // Apply radius to disabled border
      borderSide: BorderSide(
        color: AppThemeLight.instance.theme.colorScheme.primary,
        width: 1.2,
      ),
    ),
    labelStyle: TextStyle(
      color: AppThemeLight
          .instance.theme.colorScheme.primary, // Customize label text color
      fontSize: 16.0,
    ),
    filled: true,
    fillColor: Colors.transparent,
    label: Center(
      child: Text(
        label,
        style: const TextStyle(fontSize: 16),
      ),
    ),
    hintText: hint,

    suffixIcon: suffixIcon,
    hintStyle: TextStyle(
      color: AppThemeLight.instance.theme.colorScheme.primary,
    ),
    floatingLabelBehavior: floatingLabelBehavior ?? FloatingLabelBehavior.auto,
  );
}
