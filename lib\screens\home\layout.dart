import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:inventory_application/helpers/function.dart';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/home/<USER>';
import 'package:inventory_application/screens/invoices/home/<USER>';
import 'package:inventory_application/screens/setting/my_profile_screen.dart';
import 'package:inventory_application/screens/setting/setting_page.dart';
import '../components/common_snackbar.dart';
import 'package:inventory_application/base/extension/context_extension.dart';
import 'package:inventory_application/controllers/auth_controller.dart';
import 'package:inventory_application/controllers/app_controller.dart';
import 'package:provider/provider.dart';

enum BottomNavbarItems { inventory, accounting, report, settings, none }

class HomeLayout extends StatefulWidget {
  const HomeLayout({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  State<HomeLayout> createState() => _HomeLayoutState();
}

class _HomeLayoutState extends State<HomeLayout> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  Widget body = const InvoicesHomeScreen();

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) {
          return;
        }

        final bool shouldPop = await showConfirmDialog(
              title: T('Exit'),
              content: T("Are you sure?"),
              backText: T("back"),
              confirmText: T("exit"),
            ) ??
            false;

        if (shouldPop) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        appBar: AppBar(
          toolbarHeight: 40,
          shadowColor: Colors.transparent,
          backgroundColor: context.backgroundColor,
          automaticallyImplyLeading: false,
          title: MyAppBar(
            onClick: ToggleDrawer,
          ),
        ),
        body: Scaffold(
          key: _scaffoldKey,
          drawer: _buildDrawer(),
          body: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: widget.child,
          ),
        ),
      ),
    );
  }

  Widget _buildDrawer() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildDrawerHeader(),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.home_outlined,
                  title: T('Home'),
                  subtitle: 'Back to dashboard',
                  onTap: () {
                    _scaffoldKey.currentState?.closeDrawer();
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(
                        builder: (context) => const HomeScreen(),
                      ),
                    );
                  },
                  isSelected: true,
                ),
                // _buildSectionHeader(T('Important')),
                // _buildDrawerItem(
                //   icon: Icons.notifications_active_outlined,
                //   title: T('Notifications'),
                //   subtitle: '3 new messages',
                //   onTap: () {
                //     _scaffoldKey.currentState?.closeDrawer();
                //   },
                //   badge: '3',
                // ),
                // _buildDrawerItem(
                //   icon: Icons.sync_problem_outlined,
                //   title: T('Pending Sync'),
                //   subtitle: '5 items pending',
                //   onTap: () {
                //     _scaffoldKey.currentState?.closeDrawer();
                //   },
                //   badge: '5',
                // ),
                _buildSectionHeader(T('Management')),
                _buildDrawerItem(
                  icon: Icons.person_outline,
                  title: T('My Profile'),
                  subtitle: 'View your profile',
                  onTap: () {
                    _scaffoldKey.currentState?.closeDrawer();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const MyProfileScreen(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.settings_outlined,
                  title: T('Settings'),
                  subtitle: 'App configuration',
                  onTap: () {
                    _scaffoldKey.currentState?.closeDrawer();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const SettingPage(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.sync_outlined,
                  title: T('Sync Data'),
                  subtitle: 'Last sync: 2h ago',
                  onTap: () {
                    _scaffoldKey.currentState?.closeDrawer();
                  },
                ),
                // _buildSectionHeader(T('Support')),
                // _buildDrawerItem(
                //   icon: Icons.help_outline,
                //   title: T('Help & Support'),
                //   subtitle: 'Get assistance',
                //   onTap: () {
                //     _scaffoldKey.currentState?.closeDrawer();
                //   },
                // ),
                // _buildDrawerItem(
                //   icon: Icons.feedback_outlined,
                //   title: T('Feedback'),
                //   subtitle: 'Send us feedback',
                //   onTap: () {
                //     _scaffoldKey.currentState?.closeDrawer();
                //   },
                // ),
                const Divider(height: 32),
                _buildDrawerItem(
                  icon: Icons.logout,
                  title: T('Logout'),
                  subtitle: 'Sign out from app',
                  onTap: () async {
                    _scaffoldKey.currentState?.closeDrawer();
                    final bool shouldLogout = await showConfirmDialog(
                          title: T('Logout'),
                          content: T("Are you sure you want to logout?"),
                          backText: T("Cancel"),
                          confirmText: T("Logout"),
                        ) ??
                        false;

                    if (shouldLogout) {
                      await context.read<AuthController>().logOut();
                    }
                  },
                  isLogout: true,
                ),
              ],
            ),
          ),
          _buildDrawerFooter(),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 32, 16, 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.colors.primary,
            context.colors.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Image.asset(
                  "assets/images/base_images/main_logo.png",
                  width: 40,
                  height: 40,
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppController.currentBranchName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      AppController.deviceId ?? 'Unknown Device',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_tethering,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  AppController.isThereConnection == true
                      ? T('Online')
                      : T('Offline'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          color: context.colors.primary,
          fontSize: 14,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    bool isLogout = false,
    String? badge,
    bool isSelected = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? context.colors.primary.withOpacity(0.1)
            : isLogout
                ? Colors.red.withOpacity(0.05)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? context.colors.primary
                        : isLogout
                            ? Colors.red.withOpacity(0.1)
                            : context.colors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected
                        ? Colors.white
                        : isLogout
                            ? Colors.red
                            : context.colors.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: isSelected
                              ? context.colors.primary
                              : isLogout
                                  ? Colors.red
                                  : Colors.black87,
                          fontSize: 16,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.w500,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            color: isSelected
                                ? context.colors.primary.withOpacity(0.7)
                                : isLogout
                                    ? Colors.red.withOpacity(0.7)
                                    : Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (badge != null)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: context.colors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      badge,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    width: 4,
                    height: 24,
                    decoration: BoxDecoration(
                      color: context.colors.primary,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: context.colors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.info_outline,
              color: context.colors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Version 1.0.0',
                style: TextStyle(
                  color: Colors.grey.shade800,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '© 2025 All rights reserved Pal4it',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // ignore: non_constant_identifier_names
  void ToggleDrawer() {
    if (_scaffoldKey.currentState!.isDrawerOpen) {
      _scaffoldKey.currentState!.closeDrawer();
      //close drawer, if drawer is open
    } else {
      _scaffoldKey.currentState!.openDrawer();
      //open drawer, if drawer is closed
    }
  }
}

class ApplicationLayout extends StatefulWidget {
  const ApplicationLayout(
      {super.key,
      required this.child,
      this.floatingActionButtonLocation,
      this.floatingActionButton});

  final Widget child;

  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final Widget? floatingActionButton;

  @override
  State<ApplicationLayout> createState() => _ApplicationLayoutState();
}

class _ApplicationLayoutState extends State<ApplicationLayout> {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    // Usar un enfoque diferente para escritorio para evitar problemas de mouse_tracker
    return _buildScaffold();
  }

  Widget _buildScaffold() {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        toolbarHeight: 40,
        shadowColor: Colors.transparent,
        backgroundColor: context.backgroundColor,
        automaticallyImplyLeading: false,
        title: MyAppBar(
          onClick: ToggleDrawer,
        ),
      ),
      drawer: _buildDrawer(),
      backgroundColor: Colors.white,
      floatingActionButtonLocation: widget.floatingActionButtonLocation,
      floatingActionButton: widget.floatingActionButton,
      resizeToAvoidBottomInset: true,
      body: Builder(
        builder: (context) {
          if (kIsWeb ||
              (Platform.isWindows || Platform.isMacOS || Platform.isLinux)) {
            // Versión optimizada para escritorio
            return Material(
              color: Colors.white,
              child: widget.child,
            );
          } else {
            // Versión normal para móvil
            return widget.child;
          }
        },
      ),
    );
  }

  Widget _buildDrawer() {
    return Container(
      width: MediaQuery.of(context).size.width * 0.85,
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildDrawerHeader(),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                _buildDrawerItem(
                  icon: Icons.home_outlined,
                  title: T('Home'),
                  subtitle: 'Back to dashboard',
                  onTap: () {
                    _scaffoldKey.currentState?.closeDrawer();
                    Navigator.of(context).pushReplacement(
                      MaterialPageRoute(
                        builder: (context) => const HomeScreen(),
                      ),
                    );
                  },
                  isSelected: false,
                ),
                _buildSectionHeader(T('Management')),
                _buildDrawerItem(
                  icon: Icons.person_outline,
                  title: T('My Profile'),
                  subtitle: 'View your profile',
                  onTap: () {
                    _scaffoldKey.currentState?.closeDrawer();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const MyProfileScreen(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.settings_outlined,
                  title: T('Settings'),
                  subtitle: 'App configuration',
                  onTap: () {
                    _scaffoldKey.currentState?.closeDrawer();
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const SettingPage(),
                      ),
                    );
                  },
                ),
                _buildDrawerItem(
                  icon: Icons.sync_outlined,
                  title: T('Sync Data'),
                  subtitle: 'Last sync: 2h ago',
                  onTap: () {
                    _scaffoldKey.currentState?.closeDrawer();
                  },
                ),
                const Divider(height: 32),
                _buildDrawerItem(
                  icon: Icons.logout,
                  title: T('Logout'),
                  subtitle: 'Sign out from app',
                  onTap: () async {
                    _scaffoldKey.currentState?.closeDrawer();
                    final bool shouldLogout = await showConfirmDialog(
                          title: T('Logout'),
                          content: T("Are you sure you want to logout?"),
                          backText: T("Cancel"),
                          confirmText: T("Logout"),
                        ) ??
                        false;

                    if (shouldLogout) {
                      await context.read<AuthController>().logOut();
                    }
                  },
                  isLogout: true,
                ),
              ],
            ),
          ),
          _buildDrawerFooter(),
        ],
      ),
    );
  }

  Widget _buildDrawerHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 32, 16, 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            context.colors.primary,
            context.colors.primary.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Image.asset(
                  "assets/images/base_images/main_logo.png",
                  width: 40,
                  height: 40,
                  fit: BoxFit.contain,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppController.currentBranchName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      AppController.deviceId ?? 'Unknown Device',
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_tethering,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  AppController.isThereConnection == true
                      ? T('Online')
                      : T('Offline'),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          color: context.colors.primary,
          fontSize: 14,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
      ),
    );
  }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
    bool isLogout = false,
    String? badge,
    bool isSelected = false,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: isSelected
            ? context.colors.primary.withOpacity(0.1)
            : isLogout
                ? Colors.red.withOpacity(0.05)
                : Colors.transparent,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? context.colors.primary
                        : isLogout
                            ? Colors.red.withOpacity(0.1)
                            : context.colors.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected
                        ? Colors.white
                        : isLogout
                            ? Colors.red
                            : context.colors.primary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: isSelected
                              ? context.colors.primary
                              : isLogout
                                  ? Colors.red
                                  : Colors.black87,
                          fontSize: 16,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.w500,
                        ),
                      ),
                      if (subtitle != null) ...[
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            color: isSelected
                                ? context.colors.primary.withOpacity(0.7)
                                : isLogout
                                    ? Colors.red.withOpacity(0.7)
                                    : Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                if (badge != null)
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: context.colors.primary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      badge,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                if (isSelected)
                  Container(
                    margin: const EdgeInsets.only(left: 8),
                    width: 4,
                    height: 24,
                    decoration: BoxDecoration(
                      color: context.colors.primary,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerFooter() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: Colors.grey.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: context.colors.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.info_outline,
              color: context.colors.primary,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Version 1.0.0',
                style: TextStyle(
                  color: Colors.grey.shade800,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '© 2025 All rights reserved Pal4it',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // ignore: non_constant_identifier_names
  void ToggleDrawer() {
    if (_scaffoldKey.currentState!.isDrawerOpen) {
      _scaffoldKey.currentState!.closeDrawer();
      //close drawer, if drawer is open
    } else {
      _scaffoldKey.currentState!.openDrawer();
      //open drawer, if drawer is closed
    }
  }
}
